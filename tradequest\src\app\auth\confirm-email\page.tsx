'use client'

import Link from 'next/link'
import { useUserStore } from '@/lib/stores/user-store'

export default function ConfirmEmailPage() {
  const { interfaceMode } = useUserStore()
  const isAdolescentMode = interfaceMode === 'adolescent'

  return (
    <div className={`min-h-screen flex items-center justify-center ${
      isAdolescentMode 
        ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' 
        : 'bg-gray-900'
    }`}>
      <div className={`max-w-md w-full mx-4 p-8 rounded-lg shadow-lg text-center ${
        isAdolescentMode 
          ? 'bg-white/10 backdrop-blur-sm border border-white/20' 
          : 'bg-gray-800 border border-green-400'
      }`}>
        {/* Icon */}
        <div className="mb-6">
          <div className={`text-6xl mb-4 ${
            isAdolescentMode ? 'text-yellow-300' : 'text-green-400'
          }`}>
            {isAdolescentMode ? '📧' : '📨'}
          </div>
        </div>

        {/* Header */}
        <h1 className={`text-2xl font-bold mb-4 ${
          isAdolescentMode ? 'text-white' : 'text-green-400'
        }`}>
          {isAdolescentMode ? '🎉 Almost There!' : 'EMAIL_VERIFICATION_REQUIRED'}
        </h1>

        {/* Message */}
        <div className={`mb-6 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
          <p className="mb-4">
            {isAdolescentMode 
              ? 'We\'ve sent a magical confirmation link to your email! Click it to activate your TradeQuest account and start your trading adventure.'
              : 'A verification link has been sent to your email address. Please click the link to activate your account and access the trading platform.'
            }
          </p>
          
          <p className="text-sm">
            {isAdolescentMode 
              ? '✨ Don\'t forget to check your spam folder if you don\'t see it!'
              : 'Check your spam folder if the email is not in your inbox.'
            }
          </p>
        </div>

        {/* Instructions */}
        <div className={`p-4 rounded mb-6 ${
          isAdolescentMode 
            ? 'bg-blue-500/20 border border-blue-400' 
            : 'bg-gray-700 border border-green-400'
        }`}>
          <h3 className={`font-bold mb-2 ${
            isAdolescentMode ? 'text-blue-100' : 'text-green-400'
          }`}>
            {isAdolescentMode ? '🔍 What to do next:' : 'NEXT_STEPS:'}
          </h3>
          <ol className={`text-sm text-left space-y-1 ${
            isAdolescentMode ? 'text-blue-100' : 'text-green-300'
          }`}>
            <li>1. {isAdolescentMode ? 'Open your email app' : 'Check your email inbox'}</li>
            <li>2. {isAdolescentMode ? 'Find the TradeQuest email' : 'Locate the verification email'}</li>
            <li>3. {isAdolescentMode ? 'Click the confirmation link' : 'Click the verification link'}</li>
            <li>4. {isAdolescentMode ? 'Start your adventure!' : 'Access your account'}</li>
          </ol>
        </div>

        {/* Actions */}
        <div className="space-y-4">
          <Link
            href="/auth/login"
            className={`block w-full py-3 px-4 rounded font-bold transition-colors ${
              isAdolescentMode
                ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'
                : 'bg-green-400 text-gray-900 hover:bg-green-300'
            }`}
          >
            {isAdolescentMode ? '🚀 Go to Login' : 'PROCEED_TO_LOGIN'}
          </Link>

          <Link
            href="/"
            className={`block w-full py-2 px-4 rounded font-medium transition-colors ${
              isAdolescentMode
                ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30'
                : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'
            }`}
          >
            {isAdolescentMode ? '🏠 Back to Home' : 'RETURN_HOME'}
          </Link>
        </div>

        {/* Help */}
        <div className={`mt-6 text-sm ${
          isAdolescentMode ? 'text-white/70' : 'text-green-400'
        }`}>
          {isAdolescentMode ? 'Need help? ' : 'SUPPORT_NEEDED? '}
          <a 
            href="mailto:<EMAIL>" 
            className={`hover:underline ${
              isAdolescentMode ? 'text-yellow-300' : 'text-green-300'
            }`}
          >
            {isAdolescentMode ? 'Contact Support' : 'CONTACT_SUPPORT'}
          </a>
        </div>
      </div>
    </div>
  )
}
