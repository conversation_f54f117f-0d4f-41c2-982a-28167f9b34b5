-- TradeQuest Database Schema
-- This file contains the database schema for the TradeQuest platform

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    age INTEGER,
    is_minor BOOLEAN DEFAULT FALSE,
    interface_mode VARCHAR(20) DEFAULT 'adolescent' CHECK (interface_mode IN ('adolescent', 'adult')),
    avatar_url TEXT,
    total_quest_coins INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    experience_points INTEGER DEFAULT 0,
    guild_id UUID,
    preferred_language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Achievements table
CREATE TABLE public.achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    icon VARCHAR(50),
    category VARCHAR(20) CHECK (category IN ('trading', 'learning', 'social', 'special')),
    points INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User achievements (many-to-many relationship)
CREATE TABLE public.user_achievements (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    achievement_id UUID REFERENCES public.achievements(id) ON DELETE CASCADE,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, achievement_id)
);

-- Game sessions table
CREATE TABLE public.game_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    game_type VARCHAR(50) NOT NULL CHECK (game_type IN ('scalper_sprint', 'candle_strike', 'chain_maze', 'swing_trader_odyssey', 'day_trader_arena', 'portfolio_survivor')),
    difficulty VARCHAR(20) CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    score INTEGER DEFAULT 0,
    quest_coins_earned INTEGER DEFAULT 0,
    duration_seconds INTEGER,
    game_data JSONB, -- Store game-specific data
    completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Learning modules table
CREATE TABLE public.learning_modules (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    category VARCHAR(20) CHECK (category IN ('crypto', 'stocks', 'forex', 'fundamentals')),
    difficulty VARCHAR(20) CHECK (difficulty IN ('beginner', 'intermediate', 'advanced')),
    estimated_duration_minutes INTEGER,
    content JSONB, -- Store structured learning content
    quiz_questions JSONB, -- Store quiz questions
    prerequisites TEXT[], -- Array of prerequisite module IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User progress in learning modules
CREATE TABLE public.user_learning_progress (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    module_id UUID REFERENCES public.learning_modules(id) ON DELETE CASCADE,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    completed_at TIMESTAMP WITH TIME ZONE,
    quiz_score INTEGER,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, module_id)
);

-- Guilds table
CREATE TABLE public.guilds (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    member_count INTEGER DEFAULT 0,
    total_quest_coins INTEGER DEFAULT 0,
    level INTEGER DEFAULT 1,
    is_public BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Guild memberships
CREATE TABLE public.guild_memberships (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    guild_id UUID REFERENCES public.guilds(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(guild_id, user_id)
);

-- Quest coins transactions
CREATE TABLE public.quest_coin_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    type VARCHAR(20) CHECK (type IN ('earned', 'spent', 'bonus', 'penalty')),
    source VARCHAR(100), -- Description of the source/reason
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE public.notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    type VARCHAR(20) CHECK (type IN ('achievement', 'game_result', 'social', 'system')),
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Leaderboards (materialized view for performance)
CREATE MATERIALIZED VIEW public.leaderboards AS
SELECT 
    'daily' as period,
    gs.game_type,
    up.id as user_id,
    up.username,
    up.avatar_url,
    MAX(gs.score) as best_score,
    SUM(gs.quest_coins_earned) as total_coins,
    COUNT(*) as games_played,
    ROW_NUMBER() OVER (PARTITION BY gs.game_type ORDER BY MAX(gs.score) DESC) as rank
FROM public.game_sessions gs
JOIN public.user_profiles up ON gs.user_id = up.id
WHERE gs.completed_at >= CURRENT_DATE
GROUP BY gs.game_type, up.id, up.username, up.avatar_url

UNION ALL

SELECT 
    'weekly' as period,
    gs.game_type,
    up.id as user_id,
    up.username,
    up.avatar_url,
    MAX(gs.score) as best_score,
    SUM(gs.quest_coins_earned) as total_coins,
    COUNT(*) as games_played,
    ROW_NUMBER() OVER (PARTITION BY gs.game_type ORDER BY MAX(gs.score) DESC) as rank
FROM public.game_sessions gs
JOIN public.user_profiles up ON gs.user_id = up.id
WHERE gs.completed_at >= DATE_TRUNC('week', CURRENT_DATE)
GROUP BY gs.game_type, up.id, up.username, up.avatar_url

UNION ALL

SELECT 
    'monthly' as period,
    gs.game_type,
    up.id as user_id,
    up.username,
    up.avatar_url,
    MAX(gs.score) as best_score,
    SUM(gs.quest_coins_earned) as total_coins,
    COUNT(*) as games_played,
    ROW_NUMBER() OVER (PARTITION BY gs.game_type ORDER BY MAX(gs.score) DESC) as rank
FROM public.game_sessions gs
JOIN public.user_profiles up ON gs.user_id = up.id
WHERE gs.completed_at >= DATE_TRUNC('month', CURRENT_DATE)
GROUP BY gs.game_type, up.id, up.username, up.avatar_url;

-- Create indexes for better performance
CREATE INDEX idx_user_profiles_username ON public.user_profiles(username);
CREATE INDEX idx_user_profiles_guild_id ON public.user_profiles(guild_id);
CREATE INDEX idx_game_sessions_user_id ON public.game_sessions(user_id);
CREATE INDEX idx_game_sessions_game_type ON public.game_sessions(game_type);
CREATE INDEX idx_game_sessions_completed_at ON public.game_sessions(completed_at);
CREATE INDEX idx_user_achievements_user_id ON public.user_achievements(user_id);
CREATE INDEX idx_quest_coin_transactions_user_id ON public.quest_coin_transactions(user_id);
CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);

-- Row Level Security (RLS) policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_learning_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guild_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quest_coin_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view their own achievements" ON public.user_achievements
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own game sessions" ON public.game_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own game sessions" ON public.game_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view their own learning progress" ON public.user_learning_progress
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own learning progress" ON public.user_learning_progress
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own transactions" ON public.quest_coin_transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own notifications" ON public.notifications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications" ON public.notifications
    FOR UPDATE USING (auth.uid() = user_id);

-- Functions and triggers
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, username, age, is_minor)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
        COALESCE((NEW.raw_user_meta_data->>'age')::integer, NULL),
        COALESCE((NEW.raw_user_meta_data->>'age')::integer < 18, FALSE)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile when user signs up
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.learning_modules
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.guilds
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Function to refresh leaderboards
CREATE OR REPLACE FUNCTION public.refresh_leaderboards()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW public.leaderboards;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert some initial achievements
INSERT INTO public.achievements (name, description, icon, category, points) VALUES
('First Steps', 'Complete your first trading game', '🎮', 'trading', 50),
('Rising Trader', 'Score 1000+ points in a single game', '📈', 'trading', 100),
('Expert Trader', 'Score 5000+ points in a single game', '🏆', 'trading', 250),
('Speed Demon', 'Complete Scalper Sprint in under 30 seconds', '⚡', 'trading', 150),
('Pattern Master', 'Identify 10 candlestick patterns correctly', '🕯️', 'learning', 200),
('Social Butterfly', 'Join your first guild', '👥', 'social', 75),
('Team Player', 'Complete 5 guild challenges', '🤝', 'social', 300),
('Knowledge Seeker', 'Complete your first learning module', '🎓', 'learning', 100),
('Dedicated Student', 'Complete 10 learning modules', '📚', 'learning', 500),
('Early Adopter', 'One of the first 100 users', '⭐', 'special', 1000);
