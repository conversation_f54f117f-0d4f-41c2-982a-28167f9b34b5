'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { useUserStore } from '@/lib/stores/user-store'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()
  const { interfaceMode, setLoading: setUserLoading } = useUserStore()
  const supabase = createClient()

  const isAdolescentMode = interfaceMode === 'adolescent'

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setUserLoading(true)

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        setError(error.message)
        return
      }

      if (data.user) {
        // Fetch user profile
        const { data: profile } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('id', data.user.id)
          .single()

        if (profile) {
          useUserStore.getState().setUser({
            id: profile.id,
            email: data.user.email!,
            username: profile.username,
            age: profile.age,
            is_minor: profile.is_minor,
            interface_mode: profile.interface_mode,
            avatar_url: profile.avatar_url,
            total_quest_coins: profile.total_quest_coins,
            level: profile.level,
            experience_points: profile.experience_points,
            achievements: [],
            guild_id: profile.guild_id,
            preferred_language: profile.preferred_language,
            created_at: profile.created_at,
            updated_at: profile.updated_at,
          })
        }

        router.push('/')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
      setUserLoading(false)
    }
  }

  const handleSocialLogin = async (provider: 'google' | 'github') => {
    setLoading(true)
    setError('')

    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      })

      if (error) {
        setError(error.message)
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className={`min-h-screen flex items-center justify-center ${
      isAdolescentMode 
        ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' 
        : 'bg-gray-900'
    }`}>
      <div className={`max-w-md w-full mx-4 p-8 rounded-lg shadow-lg ${
        isAdolescentMode 
          ? 'bg-white/10 backdrop-blur-sm border border-white/20' 
          : 'bg-gray-800 border border-green-400'
      }`}>
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className={`text-3xl font-bold mb-2 ${
            isAdolescentMode ? 'text-white' : 'text-green-400'
          }`}>
            {isAdolescentMode ? '🏰 Welcome Back, Trader!' : '📊 Terminal Access'}
          </h1>
          <p className={`${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
            {isAdolescentMode 
              ? 'Continue your trading adventure!' 
              : 'Authenticate to access trading systems'
            }
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className={`mb-4 p-3 rounded ${
            isAdolescentMode 
              ? 'bg-red-500/20 border border-red-400 text-red-100' 
              : 'bg-red-900/50 border border-red-400 text-red-300'
          }`}>
            {error}
          </div>
        )}

        {/* Login Form */}
        <form onSubmit={handleLogin} className="space-y-6">
          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isAdolescentMode ? 'text-white' : 'text-green-300'
            }`}>
              {isAdolescentMode ? 'Email Address' : 'EMAIL_ADDRESS'}
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
                isAdolescentMode
                  ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
                  : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
              }`}
              placeholder={isAdolescentMode ? 'Enter your email' : '<EMAIL>'}
            />
          </div>

          <div>
            <label className={`block text-sm font-medium mb-2 ${
              isAdolescentMode ? 'text-white' : 'text-green-300'
            }`}>
              {isAdolescentMode ? 'Password' : 'PASSWORD'}
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
                isAdolescentMode
                  ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
                  : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
              }`}
              placeholder={isAdolescentMode ? 'Enter your password' : '••••••••'}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className={`w-full py-3 px-4 rounded font-bold transition-colors disabled:opacity-50 ${
              isAdolescentMode
                ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'
                : 'bg-green-400 text-gray-900 hover:bg-green-300'
            }`}
          >
            {loading 
              ? (isAdolescentMode ? '🔄 Logging in...' : 'AUTHENTICATING...') 
              : (isAdolescentMode ? '🚀 Start Adventure!' : 'LOGIN')
            }
          </button>
        </form>

        {/* Social Login */}
        <div className="mt-6">
          <div className={`text-center text-sm mb-4 ${
            isAdolescentMode ? 'text-white/70' : 'text-green-300'
          }`}>
            {isAdolescentMode ? 'Or continue with' : 'ALTERNATIVE_AUTH'}
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={() => handleSocialLogin('google')}
              disabled={loading}
              className={`py-2 px-4 rounded font-medium transition-colors disabled:opacity-50 ${
                isAdolescentMode
                  ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30'
                  : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'
              }`}
            >
              {isAdolescentMode ? '🔍 Google' : 'GOOGLE'}
            </button>
            
            <button
              onClick={() => handleSocialLogin('github')}
              disabled={loading}
              className={`py-2 px-4 rounded font-medium transition-colors disabled:opacity-50 ${
                isAdolescentMode
                  ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30'
                  : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'
              }`}
            >
              {isAdolescentMode ? '🐙 GitHub' : 'GITHUB'}
            </button>
          </div>
        </div>

        {/* Register Link */}
        <div className={`text-center mt-6 ${
          isAdolescentMode ? 'text-white/80' : 'text-green-300'
        }`}>
          {isAdolescentMode ? "New to TradeQuest? " : "NO_ACCOUNT? "}
          <Link 
            href="/auth/register" 
            className={`font-medium hover:underline ${
              isAdolescentMode ? 'text-yellow-300' : 'text-green-400'
            }`}
          >
            {isAdolescentMode ? 'Join the Adventure!' : 'CREATE_ACCOUNT'}
          </Link>
        </div>
      </div>
    </div>
  )
}
