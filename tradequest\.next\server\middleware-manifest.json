{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kKv0fp95WkJi7j6F/QdtZiLhope0cAZ3c8taB8S9siE=", "__NEXT_PREVIEW_MODE_ID": "5d98ed8ee043cf576d79cda175fe1dea", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1f20a0827752f3fe337cd309ab55b8b68a657c139d6ed96b34d7ff83ecf7cbd0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ad20d736cc442c3b05e705e1d49406741e3f5f027b7a48321684fb3f9e71bc6d"}}}, "sortedMiddleware": ["/"], "functions": {}}