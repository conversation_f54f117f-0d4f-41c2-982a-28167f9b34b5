import { GameConfig, GameState, Position, GameType } from '@/types'
import { generateSessionId, calculatePnL } from '@/lib/utils'
import { GAME_CONFIGS, QUEST_COIN_MULTIPLIERS } from '@/lib/constants'
import { marketDataService } from '@/lib/services/market-data'

export class BaseGame {
  protected config: GameConfig
  protected state: GameState
  protected startTime: number
  protected endTime: number
  protected isActive: boolean = false
  protected marketData: Map<string, number> = new Map()

  constructor(gameType: GameType, difficulty: 'beginner' | 'intermediate' | 'advanced') {
    const gameConfig = GAME_CONFIGS[gameType]

    this.config = {
      type: gameType,
      difficulty,
      duration_seconds: gameConfig.duration_seconds,
      starting_balance: gameConfig.starting_balance,
      available_pairs: [], // Will be set by specific game implementations
      special_rules: {},
    }

    this.state = {
      session_id: generateSessionId(),
      current_balance: this.config.starting_balance,
      positions: [],
      time_remaining: this.config.duration_seconds,
      score: 0,
      multiplier: QUEST_COIN_MULTIPLIERS[difficulty],
    }

    this.startTime = Date.now()
    this.endTime = this.startTime + (this.config.duration_seconds * 1000)
  }

  // Methods that can be overridden by specific games
  async initialize(): Promise<void> {
    // Default implementation - can be overridden
  }

  update(): void {
    // Default implementation - can be overridden
  }

  calculateScore(): number {
    // Default implementation - can be overridden
    return 0
  }

  getGameSpecificData(): any {
    // Default implementation - can be overridden
    return {}
  }

  // Common game lifecycle methods
  async start(): Promise<void> {
    await this.initialize()
    this.isActive = true
    this.startGameLoop()
  }

  pause(): void {
    this.isActive = false
  }

  resume(): void {
    this.isActive = true
    this.startGameLoop()
  }

  end(): GameState {
    this.isActive = false
    this.state.score = this.calculateScore()
    this.state.time_remaining = 0
    return this.state
  }

  // Trading operations
  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {
    if (!this.isActive) return false

    const currentPrice = this.marketData.get(symbol)
    if (!currentPrice) return false

    const tradeValue = currentPrice * quantity
    const requiredBalance = side === 'buy' ? tradeValue : 0

    if (this.state.current_balance < requiredBalance) {
      return false // Insufficient balance
    }

    // Check position limits
    const maxPositions = this.getMaxPositions()
    if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {
      return false // Too many positions
    }

    // Execute the trade
    const position: Position = {
      id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      symbol,
      side,
      quantity,
      entry_price: currentPrice,
      current_price: currentPrice,
      pnl: 0,
      timestamp: new Date().toISOString(),
    }

    // Update balance
    if (side === 'buy') {
      this.state.current_balance -= tradeValue
    } else {
      this.state.current_balance += tradeValue
    }

    // Add or update position
    const existingPositionIndex = this.state.positions.findIndex(p => p.symbol === symbol)
    if (existingPositionIndex >= 0) {
      // Update existing position (average price calculation would go here)
      this.state.positions[existingPositionIndex] = position
    } else {
      this.state.positions.push(position)
    }

    return true
  }

  async closePosition(positionId: string): Promise<boolean> {
    if (!this.isActive) return false

    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)
    if (positionIndex === -1) return false

    const position = this.state.positions[positionIndex]
    const currentPrice = this.marketData.get(position.symbol)
    if (!currentPrice) return false

    // Calculate final P&L
    const pnl = calculatePnL(position.entry_price, currentPrice, position.quantity, position.side)
    
    // Update balance with P&L
    this.state.current_balance += pnl
    if (position.side === 'sell') {
      // Return the initial trade value for short positions
      this.state.current_balance += position.entry_price * position.quantity
    }

    // Remove position
    this.state.positions.splice(positionIndex, 1)

    return true
  }

  // Market data updates
  async updateMarketData(): Promise<void> {
    try {
      const symbols = this.config.available_pairs.map(pair => pair.symbol)
      
      // In a real implementation, you'd fetch from different services based on asset type
      const cryptoSymbols = symbols.filter(s => this.isCryptoSymbol(s))
      const stockSymbols = symbols.filter(s => this.isStockSymbol(s))
      const forexSymbols = symbols.filter(s => this.isForexSymbol(s))

      const [cryptoData, stockData, forexData] = await Promise.all([
        cryptoSymbols.length > 0 ? marketDataService.getCryptoPrices(cryptoSymbols) : [],
        stockSymbols.length > 0 ? marketDataService.getStockPrices(stockSymbols) : [],
        forexSymbols.length > 0 ? marketDataService.getForexPrices(forexSymbols) : [],
      ])

      // Update market data map
      const allData = cryptoData.concat(stockData).concat(forexData)
      allData.forEach(data => {
        this.marketData.set(data.symbol, data.price)
      })

      // Update position P&L
      this.updatePositionPnL()
    } catch (error) {
      console.error('Error updating market data:', error)
    }
  }

  // Game state getters
  getState(): GameState {
    return { ...this.state }
  }

  getConfig(): GameConfig {
    return { ...this.config }
  }

  isGameActive(): boolean {
    return this.isActive && this.state.time_remaining > 0
  }

  getTimeRemaining(): number {
    if (!this.isActive) return 0
    const remaining = Math.max(0, this.endTime - Date.now())
    this.state.time_remaining = Math.floor(remaining / 1000)
    return this.state.time_remaining
  }

  // Protected helper methods
  protected startGameLoop(): void {
    if (!this.isActive) return

    const gameLoop = () => {
      if (!this.isActive) return

      this.update()
      this.getTimeRemaining()

      if (this.state.time_remaining <= 0) {
        this.end()
        return
      }

      setTimeout(gameLoop, 1000) // Update every second
    }

    gameLoop()
  }

  protected updatePositionPnL(): void {
    this.state.positions.forEach(position => {
      const currentPrice = this.marketData.get(position.symbol)
      if (currentPrice) {
        position.current_price = currentPrice
        position.pnl = calculatePnL(
          position.entry_price,
          currentPrice,
          position.quantity,
          position.side
        )
      }
    })
  }

  protected getTotalPnL(): number {
    return this.state.positions.reduce((total, position) => total + position.pnl, 0)
  }

  protected getMaxPositions(): number {
    const gameConfig = GAME_CONFIGS[this.config.type]
    return (gameConfig as any).max_positions || 5
  }

  protected hasExistingPosition(symbol: string): boolean {
    return this.state.positions.some(p => p.symbol === symbol)
  }

  protected isCryptoSymbol(symbol: string): boolean {
    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']
    return cryptoSymbols.some(crypto => symbol.includes(crypto))
  }

  protected isStockSymbol(symbol: string): boolean {
    const stockSymbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN', 'META', 'NVDA']
    return stockSymbols.includes(symbol)
  }

  protected isForexSymbol(symbol: string): boolean {
    return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY')
  }

  protected generateRandomPrice(basePrice: number, volatility: number = 0.02): number {
    const change = (Math.random() - 0.5) * 2 * volatility
    return basePrice * (1 + change)
  }

  protected simulateMarketMovement(): void {
    // Simulate realistic market movements for game purposes
    this.marketData.forEach((price, symbol) => {
      const volatility = this.getSymbolVolatility(symbol)
      const newPrice = this.generateRandomPrice(price, volatility)
      this.marketData.set(symbol, newPrice)
    })
  }

  protected getSymbolVolatility(symbol: string): number {
    if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto
    if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks
    if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex
    return 0.02 // Default 2%
  }
}

export { BaseGame }
