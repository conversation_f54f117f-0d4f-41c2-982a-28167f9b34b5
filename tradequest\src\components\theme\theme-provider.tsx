'use client'

import { useEffect } from 'react'
import { useThemeStore } from '@/lib/stores/theme-store'
import { useUserStore } from '@/lib/stores/user-store'

interface ThemeProviderProps {
  children: React.ReactNode
}

export default function ThemeProvider({ children }: ThemeProviderProps) {
  const { 
    applyThemeToDOM, 
    setInterfaceMode,
    autoThemeSwitch,
    getRecommendedTheme,
    setTheme,
    currentThemeId
  } = useThemeStore()
  
  const { interfaceMode, user } = useUserStore()

  // Initialize theme system
  useEffect(() => {
    // Apply current theme to DOM
    applyThemeToDOM()

    // Sync interface mode with user store
    setInterfaceMode(interfaceMode)

    // Auto theme switching based on time
    if (autoThemeSwitch) {
      const recommendedTheme = getRecommendedTheme()
      if (recommendedTheme !== currentThemeId) {
        setTheme(recommendedTheme)
      }
    }

    // Listen for system preference changes
    const mediaQueries = [
      window.matchMedia('(prefers-reduced-motion: reduce)'),
      window.matchMedia('(prefers-contrast: high)'),
      window.matchMedia('(prefers-color-scheme: dark)'),
    ]

    const handleSystemPreferenceChange = () => {
      // Re-apply theme to respect system preferences
      applyThemeToDOM()
    }

    mediaQueries.forEach(mq => {
      mq.addEventListener('change', handleSystemPreferenceChange)
    })

    return () => {
      mediaQueries.forEach(mq => {
        mq.removeEventListener('change', handleSystemPreferenceChange)
      })
    }
  }, [])

  // Auto theme switching interval
  useEffect(() => {
    if (!autoThemeSwitch) return

    const interval = setInterval(() => {
      const recommendedTheme = getRecommendedTheme()
      if (recommendedTheme !== currentThemeId) {
        setTheme(recommendedTheme)
      }
    }, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [autoThemeSwitch, currentThemeId, setTheme, getRecommendedTheme])

  // Sync interface mode changes
  useEffect(() => {
    setInterfaceMode(interfaceMode)
  }, [interfaceMode, setInterfaceMode])

  // Apply theme changes to DOM
  useEffect(() => {
    applyThemeToDOM()
  }, [currentThemeId, interfaceMode, applyThemeToDOM])

  return <>{children}</>
}

// Hook for accessing theme colors in components
export function useThemeColors() {
  const { colors } = useThemeStore()
  return colors
}

// Hook for theme-aware styling
export function useThemeClasses() {
  const { currentThemeId, interfaceMode } = useThemeStore()
  
  const getButtonClass = (variant: 'primary' | 'secondary' | 'success' | 'warning' | 'error' = 'primary') => {
    const baseClass = 'px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2'
    
    const variantClasses = {
      primary: 'bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] active:bg-[var(--color-primary-active)] text-white focus:ring-[var(--color-focus)]',
      secondary: 'bg-[var(--color-secondary)] hover:bg-[var(--color-secondary-hover)] active:bg-[var(--color-secondary-active)] text-white focus:ring-[var(--color-focus)]',
      success: 'bg-[var(--color-success)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',
      warning: 'bg-[var(--color-warning)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',
      error: 'bg-[var(--color-error)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',
    }
    
    return `${baseClass} ${variantClasses[variant]}`
  }
  
  const getCardClass = () => {
    return 'bg-[var(--color-background-secondary)] border border-[var(--color-border)] rounded-lg p-4'
  }
  
  const getTextClass = (variant: 'primary' | 'secondary' | 'muted' = 'primary') => {
    const variantClasses = {
      primary: 'text-[var(--color-text-primary)]',
      secondary: 'text-[var(--color-text-secondary)]',
      muted: 'text-[var(--color-text-muted)]',
    }
    
    return variantClasses[variant]
  }
  
  const getBullishClass = () => {
    return 'text-[var(--color-bullish)] bg-[var(--color-bullish-background)]'
  }
  
  const getBearishClass = () => {
    return 'text-[var(--color-bearish)] bg-[var(--color-bearish-background)]'
  }
  
  const getNeutralClass = () => {
    return 'text-[var(--color-neutral)] bg-[var(--color-neutral-background)]'
  }
  
  return {
    getButtonClass,
    getCardClass,
    getTextClass,
    getBullishClass,
    getBearishClass,
    getNeutralClass,
    currentThemeId,
    interfaceMode,
  }
}

// Component for theme-aware market condition indicators
export function MarketConditionBadge({ 
  condition, 
  children, 
  className = '' 
}: { 
  condition: 'bullish' | 'bearish' | 'neutral'
  children: React.ReactNode
  className?: string 
}) {
  const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses()
  
  const conditionClasses = {
    bullish: getBullishClass(),
    bearish: getBearishClass(),
    neutral: getNeutralClass(),
  }
  
  return (
    <span className={`px-2 py-1 rounded text-sm font-medium ${conditionClasses[condition]} ${className}`}>
      {children}
    </span>
  )
}

// Component for theme-aware trading buttons
export function TradingButton({ 
  action, 
  onClick, 
  children, 
  disabled = false,
  className = '' 
}: { 
  action: 'buy' | 'sell' | 'neutral'
  onClick: () => void
  children: React.ReactNode
  disabled?: boolean
  className?: string 
}) {
  const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses()
  
  const actionClasses = {
    buy: getBullishClass(),
    sell: getBearishClass(),
    neutral: getNeutralClass(),
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[var(--color-focus)] disabled:opacity-50 disabled:cursor-not-allowed ${actionClasses[action]} ${className}`}
    >
      {children}
    </button>
  )
}
