'use client'

import { useState } from 'react'
import { useThemeStore } from '@/lib/stores/theme-store'
import { themes } from '@/lib/themes/color-psychology'
import { useUserStore } from '@/lib/stores/user-store'

interface ThemeSelectorProps {
  className?: string
  showPsychologyInfo?: boolean
}

export default function ThemeSelector({ 
  className = '', 
  showPsychologyInfo = true 
}: ThemeSelectorProps) {
  const { 
    currentThemeId, 
    setTheme, 
    interfaceMode,
    autoThemeSwitch,
    toggleAutoThemeSwitch,
    highContrast,
    toggleHighContrast,
    reduceMotion,
    toggleReduceMotion
  } = useThemeStore()
  
  const { user } = useUserStore()
  const [showAdvanced, setShowAdvanced] = useState(false)
  
  const isAdolescentMode = interfaceMode === 'adolescent'

  const handleThemeChange = (themeId: string) => {
    setTheme(themeId)
  }

  const getThemePreview = (themeId: string) => {
    const theme = themes.find(t => t.id === themeId)
    if (!theme) return null

    const colors = theme[interfaceMode]
    
    return (
      <div className="flex space-x-1">
        <div 
          className="w-4 h-4 rounded-full border"
          style={{ backgroundColor: colors.primary }}
        />
        <div 
          className="w-4 h-4 rounded-full border"
          style={{ backgroundColor: colors.bullish }}
        />
        <div 
          className="w-4 h-4 rounded-full border"
          style={{ backgroundColor: colors.bearish }}
        />
        <div 
          className="w-4 h-4 rounded-full border"
          style={{ backgroundColor: colors.background }}
        />
      </div>
    )
  }

  const getPsychologyBars = (theme: any) => {
    const { psychologyProfile } = theme
    return (
      <div className="space-y-1 text-xs">
        <div className="flex items-center justify-between">
          <span className={isAdolescentMode ? 'text-white/80' : 'text-green-300'}>
            {isAdolescentMode ? '😌 Stress Relief' : 'STRESS_REDUCTION'}
          </span>
          <div className="flex space-x-1">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className={`w-1 h-2 ${
                  i < psychologyProfile.stressReduction
                    ? 'bg-green-400'
                    : isAdolescentMode ? 'bg-white/20' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className={isAdolescentMode ? 'text-white/80' : 'text-green-300'}>
            {isAdolescentMode ? '🎯 Focus' : 'FOCUS_ENHANCEMENT'}
          </span>
          <div className="flex space-x-1">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className={`w-1 h-2 ${
                  i < psychologyProfile.focusEnhancement
                    ? 'bg-blue-400'
                    : isAdolescentMode ? 'bg-white/20' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
        </div>
        <div className="flex items-center justify-between">
          <span className={isAdolescentMode ? 'text-white/80' : 'text-green-300'}>
            {isAdolescentMode ? '♿ Accessibility' : 'ACCESSIBILITY'}
          </span>
          <div className="flex space-x-1">
            {Array.from({ length: 10 }, (_, i) => (
              <div
                key={i}
                className={`w-1 h-2 ${
                  i < psychologyProfile.accessibility
                    ? 'bg-purple-400'
                    : isAdolescentMode ? 'bg-white/20' : 'bg-gray-600'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="mb-6">
        <h3 className={`text-lg font-bold mb-2 ${
          isAdolescentMode ? 'text-white' : 'text-green-400'
        }`}>
          {isAdolescentMode ? '🎨 Choose Your Adventure Theme' : '🎨 INTERFACE_THEME_SELECTION'}
        </h3>
        <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
          {isAdolescentMode 
            ? 'Each theme is scientifically designed to enhance your trading experience!'
            : 'Evidence-based color psychology themes optimized for trading performance.'
          }
        </p>
      </div>

      {/* Theme Grid */}
      <div className="grid gap-4 mb-6">
        {themes.map((theme) => (
          <div
            key={theme.id}
            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
              currentThemeId === theme.id
                ? (isAdolescentMode 
                    ? 'border-yellow-400 bg-yellow-400/10' 
                    : 'border-green-400 bg-green-400/10'
                  )
                : (isAdolescentMode 
                    ? 'border-white/20 hover:border-white/40 bg-white/5' 
                    : 'border-gray-600 hover:border-green-400/50 bg-gray-800/50'
                  )
            }`}
            onClick={() => handleThemeChange(theme.id)}
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h4 className={`font-bold mb-1 ${
                  isAdolescentMode ? 'text-white' : 'text-green-400'
                }`}>
                  {isAdolescentMode ? theme.name : theme.name.toUpperCase()}
                </h4>
                <p className={`text-sm ${
                  isAdolescentMode ? 'text-white/80' : 'text-green-300'
                }`}>
                  {theme.description}
                </p>
              </div>
              <div className="ml-4">
                {getThemePreview(theme.id)}
              </div>
            </div>

            {showPsychologyInfo && (
              <div className="mt-3 pt-3 border-t border-current/20">
                {getPsychologyBars(theme)}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Advanced Options */}
      <div className={`border-t pt-4 ${
        isAdolescentMode ? 'border-white/20' : 'border-gray-600'
      }`}>
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`text-sm font-medium mb-4 hover:underline ${
            isAdolescentMode ? 'text-white/80' : 'text-green-300'
          }`}
        >
          {isAdolescentMode ? '⚙️ Advanced Options' : '⚙️ ADVANCED_SETTINGS'} 
          {showAdvanced ? ' ▼' : ' ▶'}
        </button>

        {showAdvanced && (
          <div className="space-y-4">
            {/* Auto Theme Switch */}
            <div className="flex items-center justify-between">
              <div>
                <label className={`font-medium ${
                  isAdolescentMode ? 'text-white' : 'text-green-400'
                }`}>
                  {isAdolescentMode ? '🕐 Auto Theme Switch' : '🕐 AUTO_THEME_SWITCHING'}
                </label>
                <p className={`text-xs ${
                  isAdolescentMode ? 'text-white/70' : 'text-green-300'
                }`}>
                  {isAdolescentMode 
                    ? 'Automatically change themes based on time of day'
                    : 'Automatic theme selection based on circadian rhythms'
                  }
                </p>
              </div>
              <button
                onClick={toggleAutoThemeSwitch}
                className={`w-12 h-6 rounded-full transition-colors ${
                  autoThemeSwitch
                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')
                    : (isAdolescentMode ? 'bg-white/20' : 'bg-gray-600')
                }`}
              >
                <div className={`w-5 h-5 rounded-full bg-white transition-transform ${
                  autoThemeSwitch ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>

            {/* High Contrast */}
            <div className="flex items-center justify-between">
              <div>
                <label className={`font-medium ${
                  isAdolescentMode ? 'text-white' : 'text-green-400'
                }`}>
                  {isAdolescentMode ? '👁️ High Contrast' : '👁️ HIGH_CONTRAST_MODE'}
                </label>
                <p className={`text-xs ${
                  isAdolescentMode ? 'text-white/70' : 'text-green-300'
                }`}>
                  {isAdolescentMode 
                    ? 'Enhanced visibility for better readability'
                    : 'WCAG AAA compliance for visual accessibility'
                  }
                </p>
              </div>
              <button
                onClick={toggleHighContrast}
                className={`w-12 h-6 rounded-full transition-colors ${
                  highContrast
                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')
                    : (isAdolescentMode ? 'bg-white/20' : 'bg-gray-600')
                }`}
              >
                <div className={`w-5 h-5 rounded-full bg-white transition-transform ${
                  highContrast ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>

            {/* Reduce Motion */}
            <div className="flex items-center justify-between">
              <div>
                <label className={`font-medium ${
                  isAdolescentMode ? 'text-white' : 'text-green-400'
                }`}>
                  {isAdolescentMode ? '🎭 Reduce Motion' : '🎭 REDUCE_MOTION'}
                </label>
                <p className={`text-xs ${
                  isAdolescentMode ? 'text-white/70' : 'text-green-300'
                }`}>
                  {isAdolescentMode 
                    ? 'Minimize animations for comfort'
                    : 'Disable animations for vestibular disorders'
                  }
                </p>
              </div>
              <button
                onClick={toggleReduceMotion}
                className={`w-12 h-6 rounded-full transition-colors ${
                  reduceMotion
                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')
                    : (isAdolescentMode ? 'bg-white/20' : 'bg-gray-600')
                }`}
              >
                <div className={`w-5 h-5 rounded-full bg-white transition-transform ${
                  reduceMotion ? 'translate-x-6' : 'translate-x-0.5'
                }`} />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Current Theme Info */}
      {currentThemeId && (
        <div className={`mt-6 p-4 rounded-lg ${
          isAdolescentMode ? 'bg-white/10' : 'bg-gray-800'
        }`}>
          <h4 className={`font-bold mb-2 ${
            isAdolescentMode ? 'text-white' : 'text-green-400'
          }`}>
            {isAdolescentMode ? '✨ Current Theme Benefits' : '📊 CURRENT_THEME_ANALYSIS'}
          </h4>
          {(() => {
            const currentTheme = themes.find(t => t.id === currentThemeId)
            if (!currentTheme) return null
            
            return (
              <div className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                <p className="mb-2">{currentTheme.description}</p>
                {showPsychologyInfo && (
                  <div className="text-xs">
                    <p>
                      {isAdolescentMode ? '😌 Stress Relief: ' : 'STRESS_REDUCTION: '}
                      <span className="font-bold">{currentTheme.psychologyProfile.stressReduction}/10</span>
                    </p>
                    <p>
                      {isAdolescentMode ? '🎯 Focus Enhancement: ' : 'FOCUS_ENHANCEMENT: '}
                      <span className="font-bold">{currentTheme.psychologyProfile.focusEnhancement}/10</span>
                    </p>
                    <p>
                      {isAdolescentMode ? '♿ Accessibility: ' : 'ACCESSIBILITY: '}
                      <span className="font-bold">{currentTheme.psychologyProfile.accessibility}/10</span>
                    </p>
                  </div>
                )}
              </div>
            )
          })()}
        </div>
      )}
    </div>
  )
}
