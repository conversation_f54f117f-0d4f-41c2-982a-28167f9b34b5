'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { useUserStore } from '@/lib/stores/user-store'
import { validateAge, sanitizeUsername } from '@/lib/utils'

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: '',
    age: '',
    parentalConsent: false,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [step, setStep] = useState(1) // 1: Basic info, 2: Age verification, 3: Parental consent (if minor)
  const router = useRouter()
  const { interfaceMode, setLoading: setUserLoading } = useUserStore()
  const supabase = createClient()

  const isAdolescentMode = interfaceMode === 'adolescent'
  const age = parseInt(formData.age)
  const isMinor = age > 0 && age < 18

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const validateStep1 = () => {
    if (!formData.email || !formData.password || !formData.confirmPassword || !formData.username) {
      setError('Please fill in all fields')
      return false
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return false
    }

    if (formData.username.length < 3) {
      setError('Username must be at least 3 characters long')
      return false
    }

    return true
  }

  const validateStep2 = () => {
    if (!formData.age) {
      setError('Please enter your age')
      return false
    }

    if (!validateAge(age)) {
      setError('You must be at least 13 years old to use TradeQuest')
      return false
    }

    return true
  }

  const handleNextStep = () => {
    setError('')
    
    if (step === 1 && validateStep1()) {
      setStep(2)
    } else if (step === 2 && validateStep2()) {
      if (isMinor) {
        setStep(3) // Parental consent required
      } else {
        handleRegister()
      }
    } else if (step === 3) {
      if (!formData.parentalConsent) {
        setError('Parental consent is required for users under 18')
        return
      }
      handleRegister()
    }
  }

  const handleRegister = async () => {
    setLoading(true)
    setError('')
    setUserLoading(true)

    try {
      // Create auth user
      const { data, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            username: sanitizeUsername(formData.username),
            age: age,
            is_minor: isMinor,
            interface_mode: isMinor ? 'adolescent' : 'adult',
          }
        }
      })

      if (authError) {
        setError(authError.message)
        return
      }

      if (data.user) {
        // The user profile will be created automatically by the database trigger
        // Redirect to confirmation page or dashboard
        router.push('/auth/confirm-email')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
      setUserLoading(false)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isAdolescentMode ? 'text-white' : 'text-green-300'
        }`}>
          {isAdolescentMode ? 'Email Address' : 'EMAIL_ADDRESS'}
        </label>
        <input
          type="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          required
          className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
            isAdolescentMode
              ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
              : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
          }`}
          placeholder={isAdolescentMode ? 'Enter your email' : '<EMAIL>'}
        />
      </div>

      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isAdolescentMode ? 'text-white' : 'text-green-300'
        }`}>
          {isAdolescentMode ? 'Username' : 'USERNAME'}
        </label>
        <input
          type="text"
          name="username"
          value={formData.username}
          onChange={handleInputChange}
          required
          className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
            isAdolescentMode
              ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
              : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
          }`}
          placeholder={isAdolescentMode ? 'Choose a username' : 'trader_username'}
        />
      </div>

      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isAdolescentMode ? 'text-white' : 'text-green-300'
        }`}>
          {isAdolescentMode ? 'Password' : 'PASSWORD'}
        </label>
        <input
          type="password"
          name="password"
          value={formData.password}
          onChange={handleInputChange}
          required
          className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
            isAdolescentMode
              ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
              : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
          }`}
          placeholder={isAdolescentMode ? 'Create a strong password' : '••••••••'}
        />
      </div>

      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isAdolescentMode ? 'text-white' : 'text-green-300'
        }`}>
          {isAdolescentMode ? 'Confirm Password' : 'CONFIRM_PASSWORD'}
        </label>
        <input
          type="password"
          name="confirmPassword"
          value={formData.confirmPassword}
          onChange={handleInputChange}
          required
          className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
            isAdolescentMode
              ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
              : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
          }`}
          placeholder={isAdolescentMode ? 'Confirm your password' : '••••••••'}
        />
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className={`text-center mb-6 ${isAdolescentMode ? 'text-white' : 'text-green-300'}`}>
        <h3 className="text-lg font-bold mb-2">
          {isAdolescentMode ? '🎂 Age Verification' : 'AGE_VERIFICATION'}
        </h3>
        <p className="text-sm">
          {isAdolescentMode 
            ? 'We need to verify your age to provide the best experience and comply with regulations.'
            : 'Age verification required for regulatory compliance and optimal user experience.'
          }
        </p>
      </div>

      <div>
        <label className={`block text-sm font-medium mb-2 ${
          isAdolescentMode ? 'text-white' : 'text-green-300'
        }`}>
          {isAdolescentMode ? 'Your Age' : 'AGE_INPUT'}
        </label>
        <input
          type="number"
          name="age"
          value={formData.age}
          onChange={handleInputChange}
          min="13"
          max="120"
          required
          className={`w-full px-3 py-2 rounded border focus:outline-none focus:ring-2 ${
            isAdolescentMode
              ? 'bg-white/20 border-white/30 text-white placeholder-white/60 focus:ring-pink-400'
              : 'bg-gray-700 border-green-400 text-green-100 placeholder-green-400/60 focus:ring-green-400 font-mono'
          }`}
          placeholder={isAdolescentMode ? 'Enter your age' : 'AGE_NUMERIC'}
        />
      </div>

      {age > 0 && (
        <div className={`p-4 rounded ${
          isMinor 
            ? (isAdolescentMode ? 'bg-blue-500/20 border border-blue-400' : 'bg-blue-900/50 border border-blue-400')
            : (isAdolescentMode ? 'bg-green-500/20 border border-green-400' : 'bg-green-900/50 border border-green-400')
        }`}>
          <p className={`text-sm ${
            isMinor 
              ? (isAdolescentMode ? 'text-blue-100' : 'text-blue-300')
              : (isAdolescentMode ? 'text-green-100' : 'text-green-300')
          }`}>
            {isMinor 
              ? (isAdolescentMode 
                  ? '🌟 You\'ll get the Adventure Mode experience with special protections for young traders!'
                  : 'MINOR_DETECTED: Enhanced privacy protections and parental consent required.'
                )
              : (isAdolescentMode 
                  ? '🎯 You can choose between Adventure Mode and Professional Mode!'
                  : 'ADULT_USER: Full access to all features and interface modes.'
                )
            }
          </p>
        </div>
      )}
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-6">
      <div className={`text-center mb-6 ${isAdolescentMode ? 'text-white' : 'text-green-300'}`}>
        <h3 className="text-lg font-bold mb-2">
          {isAdolescentMode ? '👨‍👩‍👧‍👦 Parental Consent' : 'PARENTAL_CONSENT_REQUIRED'}
        </h3>
        <p className="text-sm">
          {isAdolescentMode 
            ? 'Since you\'re under 18, we need parental consent to create your account. This helps us keep you safe!'
            : 'COPPA compliance requires parental consent for users under 18 years of age.'
          }
        </p>
      </div>

      <div className={`p-4 rounded border ${
        isAdolescentMode 
          ? 'bg-yellow-500/20 border-yellow-400 text-yellow-100' 
          : 'bg-yellow-900/50 border-yellow-400 text-yellow-300'
      }`}>
        <h4 className="font-bold mb-2">
          {isAdolescentMode ? '🛡️ Special Protections for Young Traders:' : 'MINOR_USER_PROTECTIONS:'}
        </h4>
        <ul className="text-sm space-y-1">
          <li>• {isAdolescentMode ? 'Enhanced privacy settings' : 'Enhanced privacy controls'}</li>
          <li>• {isAdolescentMode ? 'Educational content filtering' : 'Age-appropriate content filtering'}</li>
          <li>• {isAdolescentMode ? 'Limited social features' : 'Restricted social interactions'}</li>
          <li>• {isAdolescentMode ? 'Parental oversight options' : 'Parental monitoring capabilities'}</li>
        </ul>
      </div>

      <div className="flex items-start space-x-3">
        <input
          type="checkbox"
          name="parentalConsent"
          checked={formData.parentalConsent}
          onChange={handleInputChange}
          className="mt-1"
        />
        <label className={`text-sm ${isAdolescentMode ? 'text-white' : 'text-green-300'}`}>
          {isAdolescentMode 
            ? 'I confirm that I have parental permission to create this account and that my parent/guardian agrees to the Terms of Service and Privacy Policy.'
            : 'PARENTAL_CONSENT: I confirm parental authorization for account creation and agreement to Terms of Service and Privacy Policy.'
          }
        </label>
      </div>
    </div>
  )

  return (
    <div className={`min-h-screen flex items-center justify-center ${
      isAdolescentMode 
        ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' 
        : 'bg-gray-900'
    }`}>
      <div className={`max-w-md w-full mx-4 p-8 rounded-lg shadow-lg ${
        isAdolescentMode 
          ? 'bg-white/10 backdrop-blur-sm border border-white/20' 
          : 'bg-gray-800 border border-green-400'
      }`}>
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className={`text-3xl font-bold mb-2 ${
            isAdolescentMode ? 'text-white' : 'text-green-400'
          }`}>
            {isAdolescentMode ? '🏰 Join TradeQuest!' : '📊 Create Account'}
          </h1>
          <p className={`${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
            {isAdolescentMode 
              ? 'Start your trading adventure today!' 
              : 'Initialize new user account'
            }
          </p>
          
          {/* Step indicator */}
          <div className="flex justify-center mt-4 space-x-2">
            {[1, 2, 3].map((stepNum) => (
              <div
                key={stepNum}
                className={`w-3 h-3 rounded-full ${
                  stepNum <= step 
                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')
                    : (isAdolescentMode ? 'bg-white/30' : 'bg-gray-600')
                } ${stepNum === 3 && !isMinor ? 'hidden' : ''}`}
              />
            ))}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className={`mb-4 p-3 rounded ${
            isAdolescentMode 
              ? 'bg-red-500/20 border border-red-400 text-red-100' 
              : 'bg-red-900/50 border border-red-400 text-red-300'
          }`}>
            {error}
          </div>
        )}

        {/* Form Steps */}
        {step === 1 && renderStep1()}
        {step === 2 && renderStep2()}
        {step === 3 && renderStep3()}

        {/* Navigation Buttons */}
        <div className="mt-8 flex space-x-4">
          {step > 1 && (
            <button
              onClick={() => setStep(step - 1)}
              className={`flex-1 py-3 px-4 rounded font-bold transition-colors ${
                isAdolescentMode
                  ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30'
                  : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'
              }`}
            >
              {isAdolescentMode ? '⬅️ Back' : 'BACK'}
            </button>
          )}
          
          <button
            onClick={handleNextStep}
            disabled={loading}
            className={`${step > 1 ? 'flex-1' : 'w-full'} py-3 px-4 rounded font-bold transition-colors disabled:opacity-50 ${
              isAdolescentMode
                ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'
                : 'bg-green-400 text-gray-900 hover:bg-green-300'
            }`}
          >
            {loading 
              ? (isAdolescentMode ? '🔄 Creating...' : 'PROCESSING...') 
              : (step === 1 
                  ? (isAdolescentMode ? 'Next ➡️' : 'NEXT')
                  : step === 2 && !isMinor
                    ? (isAdolescentMode ? '🚀 Create Account!' : 'CREATE_ACCOUNT')
                    : step === 2 && isMinor
                      ? (isAdolescentMode ? 'Next ➡️' : 'NEXT')
                      : (isAdolescentMode ? '🚀 Create Account!' : 'CREATE_ACCOUNT')
                )
            }
          </button>
        </div>

        {/* Login Link */}
        <div className={`text-center mt-6 ${
          isAdolescentMode ? 'text-white/80' : 'text-green-300'
        }`}>
          {isAdolescentMode ? "Already have an account? " : "EXISTING_USER? "}
          <Link 
            href="/auth/login" 
            className={`font-medium hover:underline ${
              isAdolescentMode ? 'text-yellow-300' : 'text-green-400'
            }`}
          >
            {isAdolescentMode ? 'Sign In!' : 'LOGIN'}
          </Link>
        </div>
      </div>
    </div>
  )
}
