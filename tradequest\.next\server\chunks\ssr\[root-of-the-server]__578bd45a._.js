module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
;
;
const defaultThemeConfig = {
    mode: 'adolescent',
    primary_color: '#8B5CF6',
    secondary_color: '#EC4899',
    background_style: 'fantasy',
    font_family: 'fantasy'
};
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        interfaceMode: 'adolescent',
        themeConfig: defaultThemeConfig,
        currentGameSession: null,
        recentSessions: [],
        // User management actions
        setUser: (user)=>{
            set({
                user,
                isAuthenticated: !!user
            });
            // Update interface mode based on user preference or age
            if (user) {
                const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult');
                get().switchInterfaceMode(mode);
            }
        },
        setAuthenticated: (authenticated)=>set({
                isAuthenticated: authenticated
            }),
        setLoading: (loading)=>set({
                isLoading: loading
            }),
        // Theme and UI actions
        switchInterfaceMode: (mode)=>{
            const newThemeConfig = mode === 'adolescent' ? {
                mode: 'adolescent',
                primary_color: '#8B5CF6',
                secondary_color: '#EC4899',
                background_style: 'fantasy',
                font_family: 'fantasy'
            } : {
                mode: 'adult',
                primary_color: '#1F2937',
                secondary_color: '#3B82F6',
                background_style: 'professional',
                font_family: 'monospace'
            };
            set({
                interfaceMode: mode,
                themeConfig: newThemeConfig
            });
            // Update user preference in database
            const { user } = get();
            if (user) {
                get().updateUserProfile({
                    interface_mode: mode
                });
            }
        },
        updateThemeConfig: (config)=>{
            set((state)=>({
                    themeConfig: {
                        ...state.themeConfig,
                        ...config
                    }
                }));
        },
        // Quest coins management
        addQuestCoins: (amount, source)=>{
            const { user } = get();
            if (!user) return;
            const updatedUser = {
                ...user,
                total_quest_coins: user.total_quest_coins + amount
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd also update the database and create a transaction record
            console.log(`Added ${amount} QuestCoins from ${source}`);
        },
        spendQuestCoins: (amount, purpose)=>{
            const { user } = get();
            if (!user || user.total_quest_coins < amount) {
                return false;
            }
            const updatedUser = {
                ...user,
                total_quest_coins: user.total_quest_coins - amount
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd also update the database and create a transaction record
            console.log(`Spent ${amount} QuestCoins on ${purpose}`);
            return true;
        },
        // Experience and leveling
        addExperience: (points)=>{
            const { user } = get();
            if (!user) return;
            const newExperience = user.experience_points + points;
            const newLevel = calculateLevel(newExperience);
            const leveledUp = newLevel > user.level;
            const updatedUser = {
                ...user,
                experience_points: newExperience,
                level: newLevel
            };
            set({
                user: updatedUser
            });
            if (leveledUp) {
                console.log(`Level up! Now level ${newLevel}`);
            // In a real app, you'd trigger level up effects, notifications, etc.
            }
        },
        // Achievement system
        unlockAchievement: (achievement)=>{
            const { user } = get();
            if (!user) return;
            // Check if achievement is already unlocked
            const alreadyUnlocked = user.achievements.some((a)=>a.id === achievement.id);
            if (alreadyUnlocked) return;
            const updatedUser = {
                ...user,
                achievements: [
                    ...user.achievements,
                    {
                        ...achievement,
                        unlocked_at: new Date().toISOString()
                    }
                ]
            };
            set({
                user: updatedUser
            });
            // Award quest coins for achievement
            get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`);
            console.log(`Achievement unlocked: ${achievement.name}`);
        // In a real app, you'd show a notification, play sound effects, etc.
        },
        // Game session management
        startGameSession: (session)=>{
            set({
                currentGameSession: session
            });
        },
        endGameSession: (session)=>{
            const { recentSessions } = get();
            // Add to recent sessions (keep last 10)
            const updatedSessions = [
                session,
                ...recentSessions
            ].slice(0, 10);
            set({
                currentGameSession: null,
                recentSessions: updatedSessions
            });
            // Award quest coins and experience
            get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`);
            get().addExperience(Math.floor(session.score / 10));
            // Check for achievements
            checkGameAchievements(session, get());
        },
        // Profile updates
        updateUserProfile: (updates)=>{
            const { user } = get();
            if (!user) return;
            const updatedUser = {
                ...user,
                ...updates
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd sync with the database
            console.log('User profile updated:', updates);
        },
        // Cleanup
        clearUserData: ()=>{
            set({
                user: null,
                isAuthenticated: false,
                currentGameSession: null,
                recentSessions: [],
                interfaceMode: 'adolescent',
                themeConfig: defaultThemeConfig
            });
        }
    }), {
    name: 'tradequest-user-storage',
    partialize: (state)=>({
            user: state.user,
            interfaceMode: state.interfaceMode,
            themeConfig: state.themeConfig,
            recentSessions: state.recentSessions
        })
}));
// Helper functions
function calculateLevel(experience) {
    const LEVEL_THRESHOLDS = [
        0,
        100,
        250,
        500,
        1000,
        1750,
        2750,
        4000,
        5500,
        7500,
        10000,
        13000,
        16500,
        20500,
        25000,
        30000,
        35500,
        41500,
        48000,
        55000,
        62500
    ];
    for(let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--){
        if (experience >= LEVEL_THRESHOLDS[i]) {
            return i + 1;
        }
    }
    return 1;
}
function checkGameAchievements(session, store) {
    const achievements = [];
    // First game achievement
    if (store.recentSessions.length === 0) {
        achievements.push({
            id: 'first_game',
            name: 'First Steps',
            description: 'Complete your first trading game',
            icon: '🎮',
            category: 'trading',
            points: 50
        });
    }
    // High score achievements
    if (session.score >= 1000) {
        achievements.push({
            id: 'high_score_1000',
            name: 'Rising Trader',
            description: 'Score 1000+ points in a single game',
            icon: '📈',
            category: 'trading',
            points: 100
        });
    }
    if (session.score >= 5000) {
        achievements.push({
            id: 'high_score_5000',
            name: 'Expert Trader',
            description: 'Score 5000+ points in a single game',
            icon: '🏆',
            category: 'trading',
            points: 250
        });
    }
    // Game-specific achievements
    if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {
        achievements.push({
            id: 'speed_scalper',
            name: 'Lightning Fast',
            description: 'Complete Scalper Sprint in under 30 seconds',
            icon: '⚡',
            category: 'trading',
            points: 150
        });
    }
    // Unlock achievements
    achievements.forEach((achievement)=>{
        store.unlockAchievement(achievement);
    });
}
}}),
"[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/lib/game-engine/base-game.ts'

Expression expected`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),
"[project]/src/lib/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACHIEVEMENT_CATEGORIES": (()=>ACHIEVEMENT_CATEGORIES),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "GAME_CONFIGS": (()=>GAME_CONFIGS),
    "INTERFACE_MODES": (()=>INTERFACE_MODES),
    "LEVEL_THRESHOLDS": (()=>LEVEL_THRESHOLDS),
    "QUEST_COIN_MULTIPLIERS": (()=>QUEST_COIN_MULTIPLIERS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TRADING_PAIRS": (()=>TRADING_PAIRS),
    "UPDATE_INTERVALS": (()=>UPDATE_INTERVALS),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
const GAME_CONFIGS = {
    scalper_sprint: {
        name: 'Scalper Sprint',
        description: '60-second trading challenges with rapid-fire decisions',
        difficulty: 'beginner',
        duration_seconds: 60,
        starting_balance: 10000,
        min_trade_size: 100,
        max_positions: 3,
        quest_coins_base: 50
    },
    candle_strike: {
        name: 'CandleStrike',
        description: 'Pattern recognition game with candlestick charts',
        difficulty: 'beginner',
        duration_seconds: 120,
        starting_balance: 0,
        patterns_to_identify: 5,
        quest_coins_base: 75
    },
    chain_maze: {
        name: 'ChainMaze',
        description: 'Navigate blockchain puzzles and learn consensus mechanisms',
        difficulty: 'intermediate',
        duration_seconds: 300,
        starting_balance: 1000,
        puzzles_to_solve: 3,
        quest_coins_base: 100
    },
    swing_trader_odyssey: {
        name: "Swing Trader's Odyssey",
        description: 'Multi-day position management with risk/reward balancing',
        difficulty: 'intermediate',
        duration_seconds: 600,
        starting_balance: 50000,
        max_positions: 5,
        quest_coins_base: 150
    },
    day_trader_arena: {
        name: 'Day Trader Arena',
        description: 'Real-time multiplayer trading competitions',
        difficulty: 'advanced',
        duration_seconds: 900,
        starting_balance: 100000,
        max_positions: 10,
        quest_coins_base: 200
    },
    portfolio_survivor: {
        name: 'Portfolio Survivor',
        description: 'Crisis management with diversification challenges',
        difficulty: 'advanced',
        duration_seconds: 1200,
        starting_balance: 500000,
        max_positions: 20,
        quest_coins_base: 300
    }
};
const TRADING_PAIRS = [
    {
        base: 'BTC',
        quote: 'USD',
        symbol: 'BTCUSD',
        exchange: 'virtual'
    },
    {
        base: 'ETH',
        quote: 'USD',
        symbol: 'ETHUSD',
        exchange: 'virtual'
    },
    {
        base: 'ADA',
        quote: 'USD',
        symbol: 'ADAUSD',
        exchange: 'virtual'
    },
    {
        base: 'SOL',
        quote: 'USD',
        symbol: 'SOLUSD',
        exchange: 'virtual'
    },
    {
        base: 'AAPL',
        quote: 'USD',
        symbol: 'AAPL',
        exchange: 'virtual'
    },
    {
        base: 'GOOGL',
        quote: 'USD',
        symbol: 'GOOGL',
        exchange: 'virtual'
    },
    {
        base: 'TSLA',
        quote: 'USD',
        symbol: 'TSLA',
        exchange: 'virtual'
    },
    {
        base: 'EUR',
        quote: 'USD',
        symbol: 'EURUSD',
        exchange: 'virtual'
    },
    {
        base: 'GBP',
        quote: 'USD',
        symbol: 'GBPUSD',
        exchange: 'virtual'
    },
    {
        base: 'JPY',
        quote: 'USD',
        symbol: 'JPYUSD',
        exchange: 'virtual'
    }
];
const ACHIEVEMENT_CATEGORIES = {
    trading: {
        name: 'Trading Mastery',
        color: '#10B981',
        icon: '📈'
    },
    learning: {
        name: 'Knowledge Seeker',
        color: '#3B82F6',
        icon: '🎓'
    },
    social: {
        name: 'Community Builder',
        color: '#8B5CF6',
        icon: '👥'
    },
    special: {
        name: 'Special Events',
        color: '#F59E0B',
        icon: '⭐'
    }
};
const LEVEL_THRESHOLDS = [
    0,
    100,
    250,
    500,
    1000,
    1750,
    2750,
    4000,
    5500,
    7500,
    10000,
    13000,
    16500,
    20500,
    25000,
    30000,
    35500,
    41500,
    48000,
    55000,
    62500
];
const QUEST_COIN_MULTIPLIERS = {
    beginner: 1.0,
    intermediate: 1.5,
    advanced: 2.0
};
const INTERFACE_MODES = {
    adolescent: {
        name: 'Adventure Mode',
        description: 'Fantasy-themed interface with quests and adventures',
        primaryColor: '#8B5CF6',
        secondaryColor: '#EC4899',
        fontFamily: 'fantasy'
    },
    adult: {
        name: 'Professional Mode',
        description: 'Bloomberg Terminal-style professional interface',
        primaryColor: '#1F2937',
        secondaryColor: '#3B82F6',
        fontFamily: 'monospace'
    }
};
const UPDATE_INTERVALS = {
    real_time: 1000,
    fast: 5000,
    normal: 15000,
    slow: 60000
};
const API_ENDPOINTS = {
    coingecko: {
        base: 'https://api.coingecko.com/api/v3',
        prices: '/simple/price',
        history: '/coins/{id}/market_chart'
    },
    alpha_vantage: {
        base: 'https://www.alphavantage.co/query',
        intraday: '?function=TIME_SERIES_INTRADAY',
        forex: '?function=FX_INTRADAY'
    }
};
const VALIDATION_RULES = {
    username: {
        minLength: 3,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_-]+$/
    },
    age: {
        min: 13,
        max: 120
    },
    trade: {
        minAmount: 1,
        maxAmount: 1000000
    }
};
const ERROR_MESSAGES = {
    auth: {
        invalid_credentials: 'Invalid email or password',
        user_not_found: 'User not found',
        email_already_exists: 'Email already registered',
        weak_password: 'Password must be at least 8 characters',
        age_verification_failed: 'Age verification required'
    },
    game: {
        session_expired: 'Game session has expired',
        invalid_trade: 'Invalid trade parameters',
        insufficient_balance: 'Insufficient balance for this trade',
        max_positions_reached: 'Maximum number of positions reached'
    },
    general: {
        network_error: 'Network error, please try again',
        server_error: 'Server error, please try again later',
        validation_error: 'Please check your input and try again'
    }
};
const SUCCESS_MESSAGES = {
    auth: {
        registration_complete: 'Account created successfully!',
        login_success: 'Welcome back!',
        logout_success: 'Logged out successfully'
    },
    game: {
        session_complete: 'Game session completed!',
        achievement_unlocked: 'Achievement unlocked!',
        level_up: 'Level up! Congratulations!'
    },
    general: {
        save_success: 'Changes saved successfully',
        update_success: 'Updated successfully'
    }
};
}}),
"[project]/src/lib/game-engine/games/scalper-sprint.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScalperSprintGame": (()=>ScalperSprintGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
;
;
class ScalperSprintGame extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseGame"] {
    gameData;
    tradeHistory = [];
    constructor(difficulty){
        super('scalper_sprint', difficulty);
        this.gameData = {
            trades_executed: 0,
            successful_trades: 0,
            largest_gain: 0,
            largest_loss: 0,
            average_hold_time: 0,
            speed_bonus: 0
        };
        // Set available trading pairs for scalping (high volatility pairs)
        this.config.available_pairs = this.getScalpingPairs(difficulty);
    }
    async initialize() {
        // Initialize market data with realistic scalping prices
        const initialPrices = this.generateInitialPrices();
        initialPrices.forEach((price, symbol)=>{
            this.marketData.set(symbol, price);
        });
        // Start market data updates more frequently for scalping
        this.startMarketDataUpdates();
    }
    update() {
        // Update market data with high frequency for scalping simulation
        this.simulateScalpingMarketMovement();
        this.updatePositionPnL();
        // Check for auto-close conditions (stop loss, take profit)
        this.checkAutoCloseConditions();
        // Update game-specific metrics
        this.updateGameMetrics();
    }
    calculateScore() {
        const totalPnL = this.getTotalPnL();
        const balanceChange = this.state.current_balance - this.config.starting_balance + totalPnL;
        const balanceChangePercentage = balanceChange / this.config.starting_balance * 100;
        // Base score from P&L percentage
        let score = Math.max(0, balanceChangePercentage * 10);
        // Bonus for number of successful trades
        const successRate = this.gameData.trades_executed > 0 ? this.gameData.successful_trades / this.gameData.trades_executed : 0;
        score += successRate * 50;
        // Speed bonus for quick decision making
        score += this.gameData.speed_bonus;
        // Penalty for holding positions too long (this is scalping!)
        const avgHoldTimePenalty = Math.max(0, (this.gameData.average_hold_time - 10) * 2);
        score -= avgHoldTimePenalty;
        // Difficulty multiplier
        score *= this.state.multiplier;
        return Math.round(Math.max(0, score));
    }
    getGameSpecificData() {
        return {
            ...this.gameData
        };
    }
    // Override trade execution to add scalping-specific logic
    async executeTrade(symbol, side, quantity) {
        const success = await super.executeTrade(symbol, side, quantity);
        if (success) {
            this.gameData.trades_executed++;
            // Record trade for analytics
            this.tradeHistory.push({
                timestamp: Date.now(),
                symbol,
                side,
                entry_price: this.marketData.get(symbol)
            });
            // Speed bonus for quick trades
            const timeSinceStart = (Date.now() - this.startTime) / 1000;
            if (timeSinceStart < 10) {
                this.gameData.speed_bonus += 5;
            }
        }
        return success;
    }
    // Override position closing to track scalping metrics
    async closePosition(positionId) {
        const position = this.state.positions.find((p)=>p.id === positionId);
        if (!position) return false;
        const success = await super.closePosition(positionId);
        if (success && position) {
            const tradeRecord = this.tradeHistory.find((t)=>t.symbol === position.symbol && t.entry_price === position.entry_price && !t.exit_price);
            if (tradeRecord) {
                const holdTime = (Date.now() - tradeRecord.timestamp) / 1000;
                const exitPrice = this.marketData.get(position.symbol);
                const pnl = position.pnl;
                // Update trade record
                tradeRecord.exit_price = exitPrice;
                tradeRecord.hold_time = holdTime;
                tradeRecord.pnl = pnl;
                // Update game metrics
                if (pnl > 0) {
                    this.gameData.successful_trades++;
                    this.gameData.largest_gain = Math.max(this.gameData.largest_gain, pnl);
                } else {
                    this.gameData.largest_loss = Math.min(this.gameData.largest_loss, pnl);
                }
                this.updateAverageHoldTime();
            }
        }
        return success;
    }
    getScalpingPairs(difficulty) {
        const allPairs = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRADING_PAIRS"];
        switch(difficulty){
            case 'beginner':
                // Major crypto pairs with high liquidity
                return allPairs.filter((pair)=>[
                        'BTCUSD',
                        'ETHUSD'
                    ].includes(pair.symbol));
            case 'intermediate':
                // Add some altcoins and major stocks
                return allPairs.filter((pair)=>[
                        'BTCUSD',
                        'ETHUSD',
                        'ADAUSD',
                        'AAPL',
                        'GOOGL'
                    ].includes(pair.symbol));
            case 'advanced':
                // All available pairs including forex
                return allPairs;
            default:
                return allPairs.slice(0, 3);
        }
    }
    generateInitialPrices() {
        const prices = new Map();
        // Realistic starting prices for scalping simulation
        const basePrices = {
            'BTCUSD': 45000 + (Math.random() - 0.5) * 2000,
            'ETHUSD': 3000 + (Math.random() - 0.5) * 200,
            'ADAUSD': 0.5 + (Math.random() - 0.5) * 0.1,
            'SOLUSD': 100 + (Math.random() - 0.5) * 20,
            'AAPL': 150 + (Math.random() - 0.5) * 10,
            'GOOGL': 2500 + (Math.random() - 0.5) * 100,
            'TSLA': 800 + (Math.random() - 0.5) * 50,
            'EURUSD': 1.1 + (Math.random() - 0.5) * 0.02,
            'GBPUSD': 1.3 + (Math.random() - 0.5) * 0.02,
            'JPYUSD': 0.009 + (Math.random() - 0.5) * 0.0002
        };
        this.config.available_pairs.forEach((pair)=>{
            prices.set(pair.symbol, basePrices[pair.symbol] || 100);
        });
        return prices;
    }
    startMarketDataUpdates() {
        // Update market data every 2 seconds for realistic scalping
        const updateInterval = setInterval(()=>{
            if (!this.isActive) {
                clearInterval(updateInterval);
                return;
            }
            this.simulateScalpingMarketMovement();
        }, 2000);
    }
    simulateScalpingMarketMovement() {
        // Simulate high-frequency price movements typical in scalping
        this.marketData.forEach((price, symbol)=>{
            // Higher volatility and more frequent small movements
            const volatility = this.getScalpingVolatility(symbol);
            const direction = Math.random() > 0.5 ? 1 : -1;
            const change = direction * Math.random() * volatility * price;
            // Add some momentum (trending behavior)
            const momentum = this.calculateMomentum(symbol);
            const newPrice = price + change + momentum;
            this.marketData.set(symbol, Math.max(0.01, newPrice));
        });
    }
    getScalpingVolatility(symbol) {
        // Higher volatility for scalping simulation
        if (this.isCryptoSymbol(symbol)) return 0.008 // 0.8% per update
        ;
        if (this.isStockSymbol(symbol)) return 0.003 // 0.3% per update
        ;
        if (this.isForexSymbol(symbol)) return 0.001 // 0.1% per update
        ;
        return 0.005;
    }
    calculateMomentum(symbol) {
        // Simple momentum calculation based on recent price history
        // In a real implementation, this would use actual price history
        return (Math.random() - 0.5) * 0.001 * (this.marketData.get(symbol) || 0);
    }
    checkAutoCloseConditions() {
        // Auto-close positions that hit stop loss or take profit levels
        this.state.positions.forEach((position)=>{
            const currentPrice = position.current_price;
            const entryPrice = position.entry_price;
            const pnlPercentage = position.pnl / (entryPrice * position.quantity) * 100;
            // Auto-close on 5% loss (stop loss) or 3% gain (take profit) for scalping
            if (pnlPercentage <= -5 || pnlPercentage >= 3) {
                this.closePosition(position.id);
            }
        });
    }
    updateGameMetrics() {
        // Update average hold time
        this.updateAverageHoldTime();
        // Update speed bonus based on quick decision making
        const recentTrades = this.tradeHistory.filter((t)=>Date.now() - t.timestamp < 5000 // Last 5 seconds
        );
        if (recentTrades.length >= 2) {
            this.gameData.speed_bonus += 2 // Bonus for rapid trading
            ;
        }
    }
    updateAverageHoldTime() {
        const completedTrades = this.tradeHistory.filter((t)=>t.hold_time !== undefined);
        if (completedTrades.length > 0) {
            const totalHoldTime = completedTrades.reduce((sum, trade)=>sum + (trade.hold_time || 0), 0);
            this.gameData.average_hold_time = totalHoldTime / completedTrades.length;
        }
    }
}
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$scalper$2d$sprint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/games/scalper-sprint.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function Home() {
    const { interfaceMode, switchInterfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserStore"])();
    const [currentGame, setCurrentGame] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const startScalperSprint = async ()=>{
        const game = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$scalper$2d$sprint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ScalperSprintGame"]('beginner');
        setCurrentGame(game);
        await game.start();
        // Update game state every second
        const interval = setInterval(()=>{
            if (game.isGameActive()) {
                setGameState(game.getState());
            } else {
                clearInterval(interval);
                setCurrentGame(null);
                setGameState(null);
            }
        }, 1000);
    };
    const executeTrade = async (symbol, side)=>{
        if (currentGame) {
            await currentGame.executeTrade(symbol, side, 1);
            setGameState(currentGame.getState());
        }
    };
    const isAdolescentMode = interfaceMode === 'adolescent';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen ${isAdolescentMode ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' : 'bg-gray-900 text-green-400 font-mono'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: `p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: `text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                            children: isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 47,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent'),
                                className: `px-4 py-2 rounded-lg transition-colors ${isAdolescentMode ? 'bg-white/20 hover:bg-white/30 text-white' : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'}`,
                                children: [
                                    "Switch to ",
                                    isAdolescentMode ? 'Professional' : 'Adventure',
                                    " Mode"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 52,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 51,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 46,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 45,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-7xl mx-auto p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: `mb-8 p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: `text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 74,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: `text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Embark on epic trading adventures and master the markets through exciting mini-games!' : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 77,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: `mb-8 p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm' : 'bg-gray-800 border border-green-400'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: `text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: isAdolescentMode ? '⚡ Scalper Sprint Challenge' : '📈 High-Frequency Trading Simulation'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 91,
                                columnNumber: 11
                            }, this),
                            !currentGame ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                        children: isAdolescentMode ? 'Test your speed and reflexes in this 60-second trading challenge!' : 'Execute rapid trades in a simulated high-frequency environment.'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 97,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: startScalperSprint,
                                        className: `px-6 py-3 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600' : 'bg-green-400 text-gray-900 hover:bg-green-300'}`,
                                        children: isAdolescentMode ? '🚀 Start Adventure!' : 'INITIALIZE TRADING SESSION'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 103,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: [
                                    gameState && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `grid grid-cols-2 md:grid-cols-4 gap-4 p-4 rounded ${isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Quest Coins' : 'BALANCE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 122,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                        children: [
                                                            "$",
                                                            gameState.current_balance.toFixed(2)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 125,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 121,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Score' : 'SCORE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 130,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: gameState.score
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 133,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 129,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 138,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`,
                                                        children: [
                                                            gameState.time_remaining,
                                                            "s"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 141,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 137,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Positions' : 'POSITIONS'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 146,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: gameState.positions.length
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 149,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 145,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 118,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>executeTrade('BTCUSD', 'buy'),
                                                className: `p-4 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-green-400 hover:bg-green-300 text-gray-900'}`,
                                                children: isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 158,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>executeTrade('BTCUSD', 'sell'),
                                                className: `p-4 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-red-400 hover:bg-red-300 text-gray-900'}`,
                                                children: isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 168,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 157,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "grid md:grid-cols-3 gap-6",
                        children: [
                            {
                                title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',
                                description: isAdolescentMode ? 'Six exciting games to master different trading skills' : 'Comprehensive trading simulation modules',
                                features: [
                                    'Scalper Sprint',
                                    'CandleStrike',
                                    'ChainMaze'
                                ]
                            },
                            {
                                title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',
                                description: isAdolescentMode ? 'Unlock badges and level up your trading hero' : 'Advanced performance tracking and analytics',
                                features: [
                                    'Progress Tracking',
                                    'Leaderboards',
                                    'Statistics'
                                ]
                            },
                            {
                                title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',
                                description: isAdolescentMode ? 'Join guilds and compete with friends' : 'Professional networking and strategy sharing',
                                features: [
                                    'Team Challenges',
                                    'Social Features',
                                    'Competitions'
                                ]
                            }
                        ].map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: `text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                        children: feature.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 216,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                        children: feature.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 219,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: `space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`,
                                        children: feature.features.map((item, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: `mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                        children: isAdolescentMode ? '✨' : '▶'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 225,
                                                        columnNumber: 21
                                                    }, this),
                                                    item
                                                ]
                                            }, i, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 224,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 208,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 184,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
} else {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else {
                "TURBOPACK unreachable";
            }
        } else {
            "TURBOPACK unreachable";
        }
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),
"[project]/node_modules/zustand/esm/vanilla.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createStore": (()=>createStore)
});
const createStoreImpl = (createState)=>{
    let state;
    const listeners = /* @__PURE__ */ new Set();
    const setState = (partial, replace)=>{
        const nextState = typeof partial === "function" ? partial(state) : partial;
        if (!Object.is(nextState, state)) {
            const previousState = state;
            state = (replace != null ? replace : typeof nextState !== "object" || nextState === null) ? nextState : Object.assign({}, state, nextState);
            listeners.forEach((listener)=>listener(state, previousState));
        }
    };
    const getState = ()=>state;
    const getInitialState = ()=>initialState;
    const subscribe = (listener)=>{
        listeners.add(listener);
        return ()=>listeners.delete(listener);
    };
    const api = {
        setState,
        getState,
        getInitialState,
        subscribe
    };
    const initialState = state = createState(setState, getState, api);
    return api;
};
const createStore = (createState)=>createState ? createStoreImpl(createState) : createStoreImpl;
;
}}),
"[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "create": (()=>create),
    "useStore": (()=>useStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/vanilla.mjs [app-ssr] (ecmascript)");
;
;
const identity = (arg)=>arg;
function useStore(api, selector = identity) {
    const slice = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useSyncExternalStore(api.subscribe, ()=>selector(api.getState()), ()=>selector(api.getInitialState()));
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useDebugValue(slice);
    return slice;
}
const createImpl = (createState)=>{
    const api = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$vanilla$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createStore"])(createState);
    const useBoundStore = (selector)=>useStore(api, selector);
    Object.assign(useBoundStore, api);
    return useBoundStore;
};
const create = (createState)=>createState ? createImpl(createState) : createImpl;
;
}}),
"[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "combine": (()=>combine),
    "createJSONStorage": (()=>createJSONStorage),
    "devtools": (()=>devtools),
    "persist": (()=>persist),
    "redux": (()=>redux),
    "subscribeWithSelector": (()=>subscribeWithSelector)
});
const __TURBOPACK__import$2e$meta__ = {
    get url () {
        return `file://${__turbopack_context__.P("node_modules/zustand/esm/middleware.mjs")}`;
    }
};
const reduxImpl = (reducer, initial)=>(set, _get, api)=>{
        api.dispatch = (action)=>{
            set((state)=>reducer(state, action), false, action);
            return action;
        };
        api.dispatchFromDevtools = true;
        return {
            dispatch: (...args)=>api.dispatch(...args),
            ...initial
        };
    };
const redux = reduxImpl;
const trackedConnections = /* @__PURE__ */ new Map();
const getTrackedConnectionState = (name)=>{
    const api = trackedConnections.get(name);
    if (!api) return {};
    return Object.fromEntries(Object.entries(api.stores).map(([key, api2])=>[
            key,
            api2.getState()
        ]));
};
const extractConnectionInformation = (store, extensionConnector, options)=>{
    if (store === void 0) {
        return {
            type: "untracked",
            connection: extensionConnector.connect(options)
        };
    }
    const existingConnection = trackedConnections.get(options.name);
    if (existingConnection) {
        return {
            type: "tracked",
            store,
            ...existingConnection
        };
    }
    const newConnection = {
        connection: extensionConnector.connect(options),
        stores: {}
    };
    trackedConnections.set(options.name, newConnection);
    return {
        type: "tracked",
        store,
        ...newConnection
    };
};
const removeStoreFromTrackedConnections = (name, store)=>{
    if (store === void 0) return;
    const connectionInfo = trackedConnections.get(name);
    if (!connectionInfo) return;
    delete connectionInfo.stores[store];
    if (Object.keys(connectionInfo.stores).length === 0) {
        trackedConnections.delete(name);
    }
};
const findCallerName = (stack)=>{
    var _a, _b;
    if (!stack) return void 0;
    const traceLines = stack.split("\n");
    const apiSetStateLineIndex = traceLines.findIndex((traceLine)=>traceLine.includes("api.setState"));
    if (apiSetStateLineIndex < 0) return void 0;
    const callerLine = ((_a = traceLines[apiSetStateLineIndex + 1]) == null ? void 0 : _a.trim()) || "";
    return (_b = /.+ (.+) .+/.exec(callerLine)) == null ? void 0 : _b[1];
};
const devtoolsImpl = (fn, devtoolsOptions = {})=>(set, get, api)=>{
        const { enabled, anonymousActionType, store, ...options } = devtoolsOptions;
        let extensionConnector;
        try {
            extensionConnector = (enabled != null ? enabled : (__TURBOPACK__import$2e$meta__.env ? __TURBOPACK__import$2e$meta__.env.MODE : void 0) !== "production") && window.__REDUX_DEVTOOLS_EXTENSION__;
        } catch (e) {}
        if (!extensionConnector) {
            return fn(set, get, api);
        }
        const { connection, ...connectionInformation } = extractConnectionInformation(store, extensionConnector, options);
        let isRecording = true;
        api.setState = (state, replace, nameOrAction)=>{
            const r = set(state, replace);
            if (!isRecording) return r;
            const action = nameOrAction === void 0 ? {
                type: anonymousActionType || findCallerName(new Error().stack) || "anonymous"
            } : typeof nameOrAction === "string" ? {
                type: nameOrAction
            } : nameOrAction;
            if (store === void 0) {
                connection == null ? void 0 : connection.send(action, get());
                return r;
            }
            connection == null ? void 0 : connection.send({
                ...action,
                type: `${store}/${action.type}`
            }, {
                ...getTrackedConnectionState(options.name),
                [store]: api.getState()
            });
            return r;
        };
        api.devtools = {
            cleanup: ()=>{
                if (connection && typeof connection.unsubscribe === "function") {
                    connection.unsubscribe();
                }
                removeStoreFromTrackedConnections(options.name, store);
            }
        };
        const setStateFromDevtools = (...a)=>{
            const originalIsRecording = isRecording;
            isRecording = false;
            set(...a);
            isRecording = originalIsRecording;
        };
        const initialState = fn(api.setState, get, api);
        if (connectionInformation.type === "untracked") {
            connection == null ? void 0 : connection.init(initialState);
        } else {
            connectionInformation.stores[connectionInformation.store] = api;
            connection == null ? void 0 : connection.init(Object.fromEntries(Object.entries(connectionInformation.stores).map(([key, store2])=>[
                    key,
                    key === connectionInformation.store ? initialState : store2.getState()
                ])));
        }
        if (api.dispatchFromDevtools && typeof api.dispatch === "function") {
            let didWarnAboutReservedActionType = false;
            const originalDispatch = api.dispatch;
            api.dispatch = (...args)=>{
                if ((__TURBOPACK__import$2e$meta__.env ? __TURBOPACK__import$2e$meta__.env.MODE : void 0) !== "production" && args[0].type === "__setState" && !didWarnAboutReservedActionType) {
                    console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.');
                    didWarnAboutReservedActionType = true;
                }
                originalDispatch(...args);
            };
        }
        connection.subscribe((message)=>{
            var _a;
            switch(message.type){
                case "ACTION":
                    if (typeof message.payload !== "string") {
                        console.error("[zustand devtools middleware] Unsupported action format");
                        return;
                    }
                    return parseJsonThen(message.payload, (action)=>{
                        if (action.type === "__setState") {
                            if (store === void 0) {
                                setStateFromDevtools(action.state);
                                return;
                            }
                            if (Object.keys(action.state).length !== 1) {
                                console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);
                            }
                            const stateFromDevtools = action.state[store];
                            if (stateFromDevtools === void 0 || stateFromDevtools === null) {
                                return;
                            }
                            if (JSON.stringify(api.getState()) !== JSON.stringify(stateFromDevtools)) {
                                setStateFromDevtools(stateFromDevtools);
                            }
                            return;
                        }
                        if (!api.dispatchFromDevtools) return;
                        if (typeof api.dispatch !== "function") return;
                        api.dispatch(action);
                    });
                case "DISPATCH":
                    switch(message.payload.type){
                        case "RESET":
                            setStateFromDevtools(initialState);
                            if (store === void 0) {
                                return connection == null ? void 0 : connection.init(api.getState());
                            }
                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
                        case "COMMIT":
                            if (store === void 0) {
                                connection == null ? void 0 : connection.init(api.getState());
                                return;
                            }
                            return connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
                        case "ROLLBACK":
                            return parseJsonThen(message.state, (state)=>{
                                if (store === void 0) {
                                    setStateFromDevtools(state);
                                    connection == null ? void 0 : connection.init(api.getState());
                                    return;
                                }
                                setStateFromDevtools(state[store]);
                                connection == null ? void 0 : connection.init(getTrackedConnectionState(options.name));
                            });
                        case "JUMP_TO_STATE":
                        case "JUMP_TO_ACTION":
                            return parseJsonThen(message.state, (state)=>{
                                if (store === void 0) {
                                    setStateFromDevtools(state);
                                    return;
                                }
                                if (JSON.stringify(api.getState()) !== JSON.stringify(state[store])) {
                                    setStateFromDevtools(state[store]);
                                }
                            });
                        case "IMPORT_STATE":
                            {
                                const { nextLiftedState } = message.payload;
                                const lastComputedState = (_a = nextLiftedState.computedStates.slice(-1)[0]) == null ? void 0 : _a.state;
                                if (!lastComputedState) return;
                                if (store === void 0) {
                                    setStateFromDevtools(lastComputedState);
                                } else {
                                    setStateFromDevtools(lastComputedState[store]);
                                }
                                connection == null ? void 0 : connection.send(null, // FIXME no-any
                                nextLiftedState);
                                return;
                            }
                        case "PAUSE_RECORDING":
                            return isRecording = !isRecording;
                    }
                    return;
            }
        });
        return initialState;
    };
const devtools = devtoolsImpl;
const parseJsonThen = (stringified, fn)=>{
    let parsed;
    try {
        parsed = JSON.parse(stringified);
    } catch (e) {
        console.error("[zustand devtools middleware] Could not parse the received json", e);
    }
    if (parsed !== void 0) fn(parsed);
};
const subscribeWithSelectorImpl = (fn)=>(set, get, api)=>{
        const origSubscribe = api.subscribe;
        api.subscribe = (selector, optListener, options)=>{
            let listener = selector;
            if (optListener) {
                const equalityFn = (options == null ? void 0 : options.equalityFn) || Object.is;
                let currentSlice = selector(api.getState());
                listener = (state)=>{
                    const nextSlice = selector(state);
                    if (!equalityFn(currentSlice, nextSlice)) {
                        const previousSlice = currentSlice;
                        optListener(currentSlice = nextSlice, previousSlice);
                    }
                };
                if (options == null ? void 0 : options.fireImmediately) {
                    optListener(currentSlice, currentSlice);
                }
            }
            return origSubscribe(listener);
        };
        const initialState = fn(set, get, api);
        return initialState;
    };
const subscribeWithSelector = subscribeWithSelectorImpl;
function combine(initialState, create) {
    return (...args)=>Object.assign({}, initialState, create(...args));
}
function createJSONStorage(getStorage, options) {
    let storage;
    try {
        storage = getStorage();
    } catch (e) {
        return;
    }
    const persistStorage = {
        getItem: (name)=>{
            var _a;
            const parse = (str2)=>{
                if (str2 === null) {
                    return null;
                }
                return JSON.parse(str2, options == null ? void 0 : options.reviver);
            };
            const str = (_a = storage.getItem(name)) != null ? _a : null;
            if (str instanceof Promise) {
                return str.then(parse);
            }
            return parse(str);
        },
        setItem: (name, newValue)=>storage.setItem(name, JSON.stringify(newValue, options == null ? void 0 : options.replacer)),
        removeItem: (name)=>storage.removeItem(name)
    };
    return persistStorage;
}
const toThenable = (fn)=>(input)=>{
        try {
            const result = fn(input);
            if (result instanceof Promise) {
                return result;
            }
            return {
                then (onFulfilled) {
                    return toThenable(onFulfilled)(result);
                },
                catch (_onRejected) {
                    return this;
                }
            };
        } catch (e) {
            return {
                then (_onFulfilled) {
                    return this;
                },
                catch (onRejected) {
                    return toThenable(onRejected)(e);
                }
            };
        }
    };
const persistImpl = (config, baseOptions)=>(set, get, api)=>{
        let options = {
            storage: createJSONStorage(()=>localStorage),
            partialize: (state)=>state,
            version: 0,
            merge: (persistedState, currentState)=>({
                    ...currentState,
                    ...persistedState
                }),
            ...baseOptions
        };
        let hasHydrated = false;
        const hydrationListeners = /* @__PURE__ */ new Set();
        const finishHydrationListeners = /* @__PURE__ */ new Set();
        let storage = options.storage;
        if (!storage) {
            return config((...args)=>{
                console.warn(`[zustand persist middleware] Unable to update item '${options.name}', the given storage is currently unavailable.`);
                set(...args);
            }, get, api);
        }
        const setItem = ()=>{
            const state = options.partialize({
                ...get()
            });
            return storage.setItem(options.name, {
                state,
                version: options.version
            });
        };
        const savedSetState = api.setState;
        api.setState = (state, replace)=>{
            savedSetState(state, replace);
            void setItem();
        };
        const configResult = config((...args)=>{
            set(...args);
            void setItem();
        }, get, api);
        api.getInitialState = ()=>configResult;
        let stateFromStorage;
        const hydrate = ()=>{
            var _a, _b;
            if (!storage) return;
            hasHydrated = false;
            hydrationListeners.forEach((cb)=>{
                var _a2;
                return cb((_a2 = get()) != null ? _a2 : configResult);
            });
            const postRehydrationCallback = ((_b = options.onRehydrateStorage) == null ? void 0 : _b.call(options, (_a = get()) != null ? _a : configResult)) || void 0;
            return toThenable(storage.getItem.bind(storage))(options.name).then((deserializedStorageValue)=>{
                if (deserializedStorageValue) {
                    if (typeof deserializedStorageValue.version === "number" && deserializedStorageValue.version !== options.version) {
                        if (options.migrate) {
                            const migration = options.migrate(deserializedStorageValue.state, deserializedStorageValue.version);
                            if (migration instanceof Promise) {
                                return migration.then((result)=>[
                                        true,
                                        result
                                    ]);
                            }
                            return [
                                true,
                                migration
                            ];
                        }
                        console.error(`State loaded from storage couldn't be migrated since no migrate function was provided`);
                    } else {
                        return [
                            false,
                            deserializedStorageValue.state
                        ];
                    }
                }
                return [
                    false,
                    void 0
                ];
            }).then((migrationResult)=>{
                var _a2;
                const [migrated, migratedState] = migrationResult;
                stateFromStorage = options.merge(migratedState, (_a2 = get()) != null ? _a2 : configResult);
                set(stateFromStorage, true);
                if (migrated) {
                    return setItem();
                }
            }).then(()=>{
                postRehydrationCallback == null ? void 0 : postRehydrationCallback(stateFromStorage, void 0);
                stateFromStorage = get();
                hasHydrated = true;
                finishHydrationListeners.forEach((cb)=>cb(stateFromStorage));
            }).catch((e)=>{
                postRehydrationCallback == null ? void 0 : postRehydrationCallback(void 0, e);
            });
        };
        api.persist = {
            setOptions: (newOptions)=>{
                options = {
                    ...options,
                    ...newOptions
                };
                if (newOptions.storage) {
                    storage = newOptions.storage;
                }
            },
            clearStorage: ()=>{
                storage == null ? void 0 : storage.removeItem(options.name);
            },
            getOptions: ()=>options,
            rehydrate: ()=>hydrate(),
            hasHydrated: ()=>hasHydrated,
            onHydrate: (cb)=>{
                hydrationListeners.add(cb);
                return ()=>{
                    hydrationListeners.delete(cb);
                };
            },
            onFinishHydration: (cb)=>{
                finishHydrationListeners.add(cb);
                return ()=>{
                    finishHydrationListeners.delete(cb);
                };
            }
        };
        if (!options.skipHydration) {
            hydrate();
        }
        return stateFromStorage || configResult;
    };
const persist = persistImpl;
;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__578bd45a._.js.map