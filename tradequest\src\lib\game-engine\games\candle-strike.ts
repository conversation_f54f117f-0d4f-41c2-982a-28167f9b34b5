import { BaseGame } from '../base-game'
import { GameType, CandlestickData } from '@/types'
import { TRADING_PAIRS } from '@/lib/constants'

interface CandlePattern {
  id: string
  name: string
  description: string
  bullish: boolean
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  minCandles: number
  maxCandles: number
}

interface CandleStrikeData {
  patterns_identified: number
  correct_identifications: number
  wrong_identifications: number
  current_pattern: CandlePattern | null
  patterns_completed: CandlePattern[]
  accuracy_percentage: number
  speed_bonus: number
  streak_count: number
  max_streak: number
}

interface PatternChallenge {
  pattern: CandlePattern
  candleData: CandlestickData[]
  patternStartIndex: number
  patternEndIndex: number
  options: string[]
  correctAnswer: number
}

export class CandleStrikeGame extends BaseGame {
  private gameData: CandleStrikeData
  private currentChallenge: PatternChallenge | null = null
  private challengeHistory: Array<{
    challenge: PatternChallenge
    userAnswer: number
    correct: boolean
    timeToAnswer: number
    timestamp: number
  }> = []
  private challengeStartTime: number = 0
  private availablePatterns: CandlePattern[]

  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {
    super('candle_strike', difficulty)
    
    this.gameData = {
      patterns_identified: 0,
      correct_identifications: 0,
      wrong_identifications: 0,
      current_pattern: null,
      patterns_completed: [],
      accuracy_percentage: 0,
      speed_bonus: 0,
      streak_count: 0,
      max_streak: 0,
    }

    this.availablePatterns = this.getPatternsByDifficulty(difficulty)
    this.config.available_pairs = [TRADING_PAIRS[0]] // Use BTC for pattern recognition
  }

  async initialize(): Promise<void> {
    // Generate first challenge
    await this.generateNewChallenge()
  }

  update(): void {
    // Update game metrics
    this.updateGameMetrics()
  }

  calculateScore(): number {
    const baseScore = this.gameData.correct_identifications * 100
    const accuracyBonus = this.gameData.accuracy_percentage * 2
    const speedBonus = this.gameData.speed_bonus
    const streakBonus = this.gameData.max_streak * 50
    
    let totalScore = baseScore + accuracyBonus + speedBonus + streakBonus
    
    // Difficulty multiplier
    totalScore *= this.state.multiplier
    
    return Math.round(Math.max(0, totalScore))
  }

  getGameSpecificData(): CandleStrikeData {
    return { ...this.gameData }
  }

  getCurrentChallenge(): PatternChallenge | null {
    return this.currentChallenge
  }

  async submitAnswer(answerIndex: number): Promise<boolean> {
    if (!this.currentChallenge || !this.isActive) return false

    const timeToAnswer = Date.now() - this.challengeStartTime
    const correct = answerIndex === this.currentChallenge.correctAnswer

    // Record the attempt
    this.challengeHistory.push({
      challenge: this.currentChallenge,
      userAnswer: answerIndex,
      correct,
      timeToAnswer,
      timestamp: Date.now(),
    })

    // Update game data
    this.gameData.patterns_identified++
    
    if (correct) {
      this.gameData.correct_identifications++
      this.gameData.streak_count++
      this.gameData.max_streak = Math.max(this.gameData.max_streak, this.gameData.streak_count)
      
      // Speed bonus for quick correct answers (under 10 seconds)
      if (timeToAnswer < 10000) {
        const speedBonus = Math.max(0, 50 - Math.floor(timeToAnswer / 200))
        this.gameData.speed_bonus += speedBonus
      }
      
      // Add pattern to completed list if not already there
      if (!this.gameData.patterns_completed.find(p => p.id === this.currentChallenge!.pattern.id)) {
        this.gameData.patterns_completed.push(this.currentChallenge.pattern)
      }
    } else {
      this.gameData.wrong_identifications++
      this.gameData.streak_count = 0
    }

    // Update accuracy
    this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100

    // Generate next challenge if game is still active
    if (this.isActive && this.state.time_remaining > 0) {
      await this.generateNewChallenge()
    }

    return correct
  }

  private async generateNewChallenge(): Promise<void> {
    // Select a random pattern based on difficulty and progress
    const pattern = this.selectNextPattern()
    
    // Generate candlestick data with the pattern
    const candleData = this.generateCandlestickDataWithPattern(pattern)
    
    // Find where the pattern occurs in the data
    const patternLocation = this.findPatternInData(candleData, pattern)
    
    // Generate multiple choice options
    const options = this.generatePatternOptions(pattern)
    
    this.currentChallenge = {
      pattern,
      candleData,
      patternStartIndex: patternLocation.start,
      patternEndIndex: patternLocation.end,
      options,
      correctAnswer: 0, // Correct answer is always first, then shuffled
    }

    // Shuffle options and update correct answer index
    this.shuffleOptions()
    
    this.gameData.current_pattern = pattern
    this.challengeStartTime = Date.now()
  }

  private selectNextPattern(): CandlePattern {
    // Prioritize patterns not yet completed
    const uncompletedPatterns = this.availablePatterns.filter(
      p => !this.gameData.patterns_completed.find(completed => completed.id === p.id)
    )
    
    const patternsToChooseFrom = uncompletedPatterns.length > 0 ? uncompletedPatterns : this.availablePatterns
    
    return patternsToChooseFrom[Math.floor(Math.random() * patternsToChooseFrom.length)]
  }

  private generateCandlestickDataWithPattern(pattern: CandlePattern): CandlestickData[] {
    const totalCandles = 50
    const patternPosition = Math.floor(Math.random() * (totalCandles - pattern.maxCandles - 10)) + 10
    
    const data: CandlestickData[] = []
    let currentPrice = 100 + Math.random() * 50
    
    // Generate candles before pattern
    for (let i = 0; i < patternPosition; i++) {
      const candle = this.generateRandomCandle(currentPrice, i)
      data.push(candle)
      currentPrice = candle.close
    }
    
    // Generate pattern candles
    const patternCandles = this.generatePatternCandles(pattern, currentPrice, patternPosition)
    data.push(...patternCandles)
    currentPrice = patternCandles[patternCandles.length - 1].close
    
    // Generate candles after pattern
    for (let i = patternPosition + patternCandles.length; i < totalCandles; i++) {
      const candle = this.generateRandomCandle(currentPrice, i)
      data.push(candle)
      currentPrice = candle.close
    }
    
    return data
  }

  private generateRandomCandle(basePrice: number, index: number): CandlestickData {
    const volatility = 0.02
    const change = (Math.random() - 0.5) * volatility * basePrice
    const open = basePrice
    const close = basePrice + change
    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice
    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice
    
    return {
      timestamp: Date.now() - (50 - index) * 3600000, // Hourly intervals
      open,
      high,
      low,
      close,
      volume: Math.random() * 1000000,
    }
  }

  private generatePatternCandles(pattern: CandlePattern, startPrice: number, startIndex: number): CandlestickData[] {
    // This is a simplified pattern generation - in a real implementation,
    // you'd have specific algorithms for each pattern type
    const candles: CandlestickData[] = []
    let currentPrice = startPrice
    
    switch (pattern.id) {
      case 'hammer':
        return this.generateHammerPattern(startPrice, startIndex)
      case 'doji':
        return this.generateDojiPattern(startPrice, startIndex)
      case 'engulfing_bullish':
        return this.generateEngulfingPattern(startPrice, startIndex, true)
      case 'engulfing_bearish':
        return this.generateEngulfingPattern(startPrice, startIndex, false)
      case 'morning_star':
        return this.generateMorningStarPattern(startPrice, startIndex)
      case 'evening_star':
        return this.generateEveningStarPattern(startPrice, startIndex)
      default:
        return this.generateHammerPattern(startPrice, startIndex)
    }
  }

  private generateHammerPattern(startPrice: number, startIndex: number): CandlestickData[] {
    const open = startPrice
    const close = startPrice + (Math.random() * 0.01 * startPrice) // Small body
    const high = Math.max(open, close) + (Math.random() * 0.005 * startPrice) // Small upper shadow
    const low = Math.min(open, close) - (0.02 + Math.random() * 0.01) * startPrice // Long lower shadow
    
    return [{
      timestamp: Date.now() - (50 - startIndex) * 3600000,
      open,
      high,
      low,
      close,
      volume: Math.random() * 1000000,
    }]
  }

  private generateDojiPattern(startPrice: number, startIndex: number): CandlestickData[] {
    const open = startPrice
    const close = startPrice + (Math.random() - 0.5) * 0.002 * startPrice // Very small body
    const high = Math.max(open, close) + (0.01 + Math.random() * 0.01) * startPrice
    const low = Math.min(open, close) - (0.01 + Math.random() * 0.01) * startPrice
    
    return [{
      timestamp: Date.now() - (50 - startIndex) * 3600000,
      open,
      high,
      low,
      close,
      volume: Math.random() * 1000000,
    }]
  }

  private generateEngulfingPattern(startPrice: number, startIndex: number, bullish: boolean): CandlestickData[] {
    const candles: CandlestickData[] = []
    
    // First candle (small)
    const firstOpen = startPrice
    const firstClose = bullish 
      ? startPrice - 0.01 * startPrice 
      : startPrice + 0.01 * startPrice
    
    candles.push({
      timestamp: Date.now() - (50 - startIndex) * 3600000,
      open: firstOpen,
      high: Math.max(firstOpen, firstClose) + 0.002 * startPrice,
      low: Math.min(firstOpen, firstClose) - 0.002 * startPrice,
      close: firstClose,
      volume: Math.random() * 1000000,
    })
    
    // Second candle (engulfing)
    const secondOpen = bullish 
      ? firstClose - 0.005 * startPrice 
      : firstClose + 0.005 * startPrice
    const secondClose = bullish 
      ? firstOpen + 0.015 * startPrice 
      : firstOpen - 0.015 * startPrice
    
    candles.push({
      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
      open: secondOpen,
      high: Math.max(secondOpen, secondClose) + 0.002 * startPrice,
      low: Math.min(secondOpen, secondClose) - 0.002 * startPrice,
      close: secondClose,
      volume: Math.random() * 1000000,
    })
    
    return candles
  }

  private generateMorningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {
    const candles: CandlestickData[] = []
    
    // First candle (bearish)
    candles.push({
      timestamp: Date.now() - (50 - startIndex) * 3600000,
      open: startPrice,
      high: startPrice + 0.002 * startPrice,
      low: startPrice - 0.015 * startPrice,
      close: startPrice - 0.012 * startPrice,
      volume: Math.random() * 1000000,
    })
    
    // Second candle (small body/doji)
    const secondPrice = startPrice - 0.015 * startPrice
    candles.push({
      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
      open: secondPrice,
      high: secondPrice + 0.005 * startPrice,
      low: secondPrice - 0.005 * startPrice,
      close: secondPrice + 0.001 * startPrice,
      volume: Math.random() * 1000000,
    })
    
    // Third candle (bullish)
    candles.push({
      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,
      open: secondPrice + 0.002 * startPrice,
      high: startPrice - 0.002 * startPrice,
      low: secondPrice,
      close: startPrice - 0.003 * startPrice,
      volume: Math.random() * 1000000,
    })
    
    return candles
  }

  private generateEveningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {
    // Similar to morning star but inverted
    const candles: CandlestickData[] = []
    
    // First candle (bullish)
    candles.push({
      timestamp: Date.now() - (50 - startIndex) * 3600000,
      open: startPrice,
      high: startPrice + 0.015 * startPrice,
      low: startPrice - 0.002 * startPrice,
      close: startPrice + 0.012 * startPrice,
      volume: Math.random() * 1000000,
    })
    
    // Second candle (small body/doji)
    const secondPrice = startPrice + 0.015 * startPrice
    candles.push({
      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
      open: secondPrice,
      high: secondPrice + 0.005 * startPrice,
      low: secondPrice - 0.005 * startPrice,
      close: secondPrice - 0.001 * startPrice,
      volume: Math.random() * 1000000,
    })
    
    // Third candle (bearish)
    candles.push({
      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,
      open: secondPrice - 0.002 * startPrice,
      high: secondPrice,
      low: startPrice + 0.002 * startPrice,
      close: startPrice + 0.003 * startPrice,
      volume: Math.random() * 1000000,
    })
    
    return candles
  }

  private findPatternInData(data: CandlestickData[], pattern: CandlePattern): { start: number; end: number } {
    // For simplicity, we know where we placed the pattern
    // In a real implementation, you'd search for the pattern in the data
    const totalCandles = data.length
    const patternPosition = Math.floor(totalCandles * 0.4) // Roughly where we placed it
    
    return {
      start: patternPosition,
      end: patternPosition + pattern.maxCandles - 1,
    }
  }

  private generatePatternOptions(correctPattern: CandlePattern): string[] {
    const allPatterns = this.getAllPatterns()
    const wrongPatterns = allPatterns
      .filter(p => p.id !== correctPattern.id)
      .sort(() => Math.random() - 0.5)
      .slice(0, 3)
    
    return [correctPattern.name, ...wrongPatterns.map(p => p.name)]
  }

  private shuffleOptions(): void {
    if (!this.currentChallenge) return
    
    const options = [...this.currentChallenge.options]
    const correctAnswer = options[0]
    
    // Fisher-Yates shuffle
    for (let i = options.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[options[i], options[j]] = [options[j], options[i]]
    }
    
    this.currentChallenge.options = options
    this.currentChallenge.correctAnswer = options.indexOf(correctAnswer)
  }

  private updateGameMetrics(): void {
    // Update accuracy percentage
    if (this.gameData.patterns_identified > 0) {
      this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100
    }
  }

  private getPatternsByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): CandlePattern[] {
    const allPatterns = this.getAllPatterns()
    return allPatterns.filter(p => p.difficulty === difficulty || (difficulty === 'advanced' && p.difficulty !== 'advanced'))
  }

  private getAllPatterns(): CandlePattern[] {
    return [
      {
        id: 'hammer',
        name: 'Hammer',
        description: 'Bullish reversal pattern with long lower shadow',
        bullish: true,
        difficulty: 'beginner',
        minCandles: 1,
        maxCandles: 1,
      },
      {
        id: 'doji',
        name: 'Doji',
        description: 'Indecision pattern with very small body',
        bullish: false,
        difficulty: 'beginner',
        minCandles: 1,
        maxCandles: 1,
      },
      {
        id: 'engulfing_bullish',
        name: 'Bullish Engulfing',
        description: 'Two-candle bullish reversal pattern',
        bullish: true,
        difficulty: 'intermediate',
        minCandles: 2,
        maxCandles: 2,
      },
      {
        id: 'engulfing_bearish',
        name: 'Bearish Engulfing',
        description: 'Two-candle bearish reversal pattern',
        bullish: false,
        difficulty: 'intermediate',
        minCandles: 2,
        maxCandles: 2,
      },
      {
        id: 'morning_star',
        name: 'Morning Star',
        description: 'Three-candle bullish reversal pattern',
        bullish: true,
        difficulty: 'advanced',
        minCandles: 3,
        maxCandles: 3,
      },
      {
        id: 'evening_star',
        name: 'Evening Star',
        description: 'Three-candle bearish reversal pattern',
        bullish: false,
        difficulty: 'advanced',
        minCandles: 3,
        maxCandles: 3,
      },
    ]
  }
}
