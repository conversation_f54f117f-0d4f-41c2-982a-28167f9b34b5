{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\n// Mock client for development when Supabase is not configured\nconst createMockClient = () => ({\n  auth: {\n    signInWithPassword: async () => ({ data: null, error: { message: 'Demo mode - authentication disabled' } }),\n    signUp: async () => ({ data: null, error: { message: 'Demo mode - registration disabled' } }),\n    signOut: async () => ({ error: null }),\n    getSession: async () => ({ data: { session: null }, error: null }),\n    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),\n    signInWithOAuth: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),\n    exchangeCodeForSession: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),\n  },\n  from: () => ({\n    select: () => ({\n      eq: () => ({\n        single: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n      })\n    }),\n    insert: () => ({\n      select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n    }),\n    update: () => ({\n      eq: () => ({\n        select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n      })\n    })\n  })\n})\n\nexport function createClient() {\n  // Check if Supabase environment variables are properly configured\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!supabaseUrl || !supabaseKey || supabaseUrl.includes('demo') || supabaseKey.includes('demo')) {\n    console.warn('⚠️  Supabase not configured - running in demo mode. Authentication and database features will be disabled.')\n    return createMockClient() as any\n  }\n\n  try {\n    return createBrowserClient(supabaseUrl, supabaseKey)\n  } catch (error) {\n    console.error('Failed to create Supabase client:', error)\n    console.warn('Falling back to demo mode')\n    return createMockClient() as any\n  }\n}\n"], "names": [], "mappings": ";;;AAgCsB;AAhCtB;AAAA;;AAEA,8DAA8D;AAC9D,MAAM,mBAAmB,IAAM,CAAC;QAC9B,MAAM;YACJ,oBAAoB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAAsC;gBAAE,CAAC;YAC1G,QAAQ,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAAoC;gBAAE,CAAC;YAC5F,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;YACrC,YAAY,UAAY,CAAC;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YACjE,mBAAmB,IAAM,CAAC;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE,CAAC;YAC/E,iBAAiB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAA6B;gBAAE,CAAC;YAC9F,wBAAwB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAA6B;gBAAE,CAAC;QACvG;QACA,MAAM,IAAM,CAAC;gBACX,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,UAAY,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAAgC;oCAAE,CAAC;4BAC1F,CAAC;oBACH,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,QAAQ,UAAY,CAAC;gCAAE,MAAM;gCAAM,OAAO;oCAAE,SAAS;gCAAgC;4BAAE,CAAC;oBAC1F,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,UAAY,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAAgC;oCAAE,CAAC;4BAC1F,CAAC;oBACH,CAAC;YACH,CAAC;IACH,CAAC;AAEM,SAAS;IACd,kEAAkE;IAClE,MAAM;IACN,MAAM;IAEN,IAAI,CAAC,eAAe,CAAC,eAAe,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,SAAS;QAChG,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/stores/user-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { UserProfile, Achievement, GameSession, ThemeConfig } from '@/types'\nimport { createClient } from '@/lib/supabase/client'\n\ninterface UserState {\n  // User data\n  user: UserProfile | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  \n  // Theme and UI\n  interfaceMode: 'adolescent' | 'adult'\n  themeConfig: ThemeConfig\n  \n  // Game state\n  currentGameSession: GameSession | null\n  recentSessions: GameSession[]\n  \n  // Actions\n  setUser: (user: UserProfile | null) => void\n  setAuthenticated: (authenticated: boolean) => void\n  setLoading: (loading: boolean) => void\n  switchInterfaceMode: (mode: 'adolescent' | 'adult') => void\n  syncWithThemeStore: () => void\n  updateThemeConfig: (config: Partial<ThemeConfig>) => void\n  addQuestCoins: (amount: number, source: string) => void\n  spendQuestCoins: (amount: number, purpose: string) => boolean\n  addExperience: (points: number) => void\n  unlockAchievement: (achievement: Achievement) => void\n  startGameSession: (session: GameSession) => void\n  endGameSession: (session: GameSession) => void\n  updateUserProfile: (updates: Partial<UserProfile>) => void\n  clearUserData: () => void\n  signOut: () => Promise<void>\n  initializeAuth: () => Promise<void>\n}\n\nconst defaultThemeConfig: ThemeConfig = {\n  mode: 'adolescent',\n  primary_color: '#8B5CF6',\n  secondary_color: '#EC4899',\n  background_style: 'fantasy',\n  font_family: 'fantasy',\n}\n\nexport const useUserStore = create<UserState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      interfaceMode: 'adolescent',\n      themeConfig: defaultThemeConfig,\n      currentGameSession: null,\n      recentSessions: [],\n\n      // User management actions\n      setUser: (user) => {\n        set({ user, isAuthenticated: !!user })\n        \n        // Update interface mode based on user preference or age\n        if (user) {\n          const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult')\n          get().switchInterfaceMode(mode)\n        }\n      },\n\n      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),\n\n      setLoading: (loading) => set({ isLoading: loading }),\n\n      // Theme and UI actions\n      switchInterfaceMode: (mode) => {\n        const newThemeConfig: ThemeConfig = mode === 'adolescent' \n          ? {\n              mode: 'adolescent',\n              primary_color: '#8B5CF6',\n              secondary_color: '#EC4899',\n              background_style: 'fantasy',\n              font_family: 'fantasy',\n            }\n          : {\n              mode: 'adult',\n              primary_color: '#1F2937',\n              secondary_color: '#3B82F6',\n              background_style: 'professional',\n              font_family: 'monospace',\n            }\n\n        set({\n          interfaceMode: mode,\n          themeConfig: newThemeConfig\n        })\n\n        // Sync with theme store\n        get().syncWithThemeStore()\n\n        // Update user preference in database\n        const { user } = get()\n        if (user) {\n          get().updateUserProfile({ interface_mode: mode })\n        }\n      },\n\n      syncWithThemeStore: () => {\n        // Import theme store dynamically to avoid circular dependency\n        import('@/lib/stores/theme-store').then(({ useThemeStore }) => {\n          const { setInterfaceMode } = useThemeStore.getState()\n          setInterfaceMode(get().interfaceMode)\n        }).catch(() => {\n          // Theme store not available, ignore\n        })\n      },\n\n      updateThemeConfig: (config) => {\n        set((state) => ({\n          themeConfig: { ...state.themeConfig, ...config }\n        }))\n      },\n\n      // Quest coins management\n      addQuestCoins: (amount, source) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins + amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Added ${amount} QuestCoins from ${source}`)\n      },\n\n      spendQuestCoins: (amount, purpose) => {\n        const { user } = get()\n        if (!user || user.total_quest_coins < amount) {\n          return false\n        }\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins - amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Spent ${amount} QuestCoins on ${purpose}`)\n        return true\n      },\n\n      // Experience and leveling\n      addExperience: (points) => {\n        const { user } = get()\n        if (!user) return\n\n        const newExperience = user.experience_points + points\n        const newLevel = calculateLevel(newExperience)\n        const leveledUp = newLevel > user.level\n\n        const updatedUser = {\n          ...user,\n          experience_points: newExperience,\n          level: newLevel\n        }\n\n        set({ user: updatedUser })\n\n        if (leveledUp) {\n          console.log(`Level up! Now level ${newLevel}`)\n          // In a real app, you'd trigger level up effects, notifications, etc.\n        }\n      },\n\n      // Achievement system\n      unlockAchievement: (achievement) => {\n        const { user } = get()\n        if (!user) return\n\n        // Check if achievement is already unlocked\n        const alreadyUnlocked = user.achievements.some(a => a.id === achievement.id)\n        if (alreadyUnlocked) return\n\n        const updatedUser = {\n          ...user,\n          achievements: [...user.achievements, { ...achievement, unlocked_at: new Date().toISOString() }]\n        }\n\n        set({ user: updatedUser })\n\n        // Award quest coins for achievement\n        get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`)\n\n        console.log(`Achievement unlocked: ${achievement.name}`)\n        // In a real app, you'd show a notification, play sound effects, etc.\n      },\n\n      // Game session management\n      startGameSession: (session) => {\n        set({ currentGameSession: session })\n      },\n\n      endGameSession: (session) => {\n        const { recentSessions } = get()\n        \n        // Add to recent sessions (keep last 10)\n        const updatedSessions = [session, ...recentSessions].slice(0, 10)\n        \n        set({ \n          currentGameSession: null,\n          recentSessions: updatedSessions\n        })\n\n        // Award quest coins and experience\n        get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`)\n        get().addExperience(Math.floor(session.score / 10))\n\n        // Check for achievements\n        checkGameAchievements(session, get())\n      },\n\n      // Profile updates\n      updateUserProfile: (updates) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = { ...user, ...updates }\n        set({ user: updatedUser })\n\n        // In a real app, you'd sync with the database\n        console.log('User profile updated:', updates)\n      },\n\n      // Cleanup\n      clearUserData: () => {\n        set({\n          user: null,\n          isAuthenticated: false,\n          currentGameSession: null,\n          recentSessions: [],\n          interfaceMode: 'adolescent',\n          themeConfig: defaultThemeConfig,\n        })\n      },\n\n      // Authentication methods\n      signOut: async () => {\n        const supabase = createClient()\n        await supabase.auth.signOut()\n        get().clearUserData()\n      },\n\n      initializeAuth: async () => {\n        try {\n          const supabase = createClient()\n\n          // Get initial session\n          const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n\n          if (sessionError) {\n            console.warn('Auth session error (demo mode):', sessionError.message)\n            // Set demo user for development\n            get().setUser({\n              id: 'demo-user-id',\n              email: '<EMAIL>',\n              username: 'DemoTrader',\n              age: 25,\n              is_minor: false,\n              interface_mode: 'adolescent',\n              avatar_url: null,\n              total_quest_coins: 1000,\n              level: 1,\n              experience_points: 0,\n              achievements: [],\n              guild_id: null,\n              preferred_language: 'en',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString(),\n            })\n            return\n          }\n\n          if (session?.user) {\n            // Fetch user profile\n            const { data: profile, error: profileError } = await supabase\n              .from('user_profiles')\n              .select('*')\n              .eq('id', session.user.id)\n              .single()\n\n            if (profileError) {\n              console.warn('Profile fetch error (demo mode):', profileError.message)\n              return\n            }\n\n            if (profile) {\n              // Fetch achievements\n              const { data: achievements } = await supabase\n                .from('user_achievements')\n                .select(`\n                  achievement_id,\n                  unlocked_at,\n                  achievements (\n                    id,\n                    name,\n                    description,\n                    icon,\n                    category,\n                    points\n                  )\n                `)\n                .eq('user_id', session.user.id)\n\n              const userAchievements = achievements?.map(ua => ({\n                ...ua.achievements,\n                unlocked_at: ua.unlocked_at\n              })) || []\n\n              get().setUser({\n                id: profile.id,\n                email: session.user.email!,\n                username: profile.username,\n                age: profile.age,\n                is_minor: profile.is_minor,\n                interface_mode: profile.interface_mode,\n                avatar_url: profile.avatar_url,\n                total_quest_coins: profile.total_quest_coins,\n                level: profile.level,\n                experience_points: profile.experience_points,\n                achievements: userAchievements,\n                guild_id: profile.guild_id,\n                preferred_language: profile.preferred_language,\n                created_at: profile.created_at,\n                updated_at: profile.updated_at,\n              })\n            }\n          }\n\n          // Listen for auth changes\n          supabase.auth.onAuthStateChange(async (event, session) => {\n            if (event === 'SIGNED_OUT' || !session) {\n              get().clearUserData()\n            } else if (event === 'SIGNED_IN' && session?.user) {\n              // Refresh user data when signed in\n              get().initializeAuth()\n            }\n          })\n        } catch (error) {\n          console.error('Auth initialization error:', error)\n          console.warn('Running in demo mode without authentication')\n\n          // Set demo user for development\n          get().setUser({\n            id: 'demo-user-id',\n            email: '<EMAIL>',\n            username: 'DemoTrader',\n            age: 25,\n            is_minor: false,\n            interface_mode: 'adolescent',\n            avatar_url: null,\n            total_quest_coins: 1000,\n            level: 1,\n            experience_points: 0,\n            achievements: [],\n            guild_id: null,\n            preferred_language: 'en',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n        }\n      },\n    }),\n    {\n      name: 'tradequest-user-storage',\n      partialize: (state) => ({\n        user: state.user,\n        interfaceMode: state.interfaceMode,\n        themeConfig: state.themeConfig,\n        recentSessions: state.recentSessions,\n      }),\n    }\n  )\n)\n\n// Helper functions\nfunction calculateLevel(experience: number): number {\n  const LEVEL_THRESHOLDS = [\n    0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n    13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n  ]\n\n  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {\n    if (experience >= LEVEL_THRESHOLDS[i]) {\n      return i + 1\n    }\n  }\n  return 1\n}\n\nfunction checkGameAchievements(session: GameSession, store: any) {\n  const achievements: Achievement[] = []\n\n  // First game achievement\n  if (store.recentSessions.length === 0) {\n    achievements.push({\n      id: 'first_game',\n      name: 'First Steps',\n      description: 'Complete your first trading game',\n      icon: '🎮',\n      category: 'trading',\n      points: 50,\n    })\n  }\n\n  // High score achievements\n  if (session.score >= 1000) {\n    achievements.push({\n      id: 'high_score_1000',\n      name: 'Rising Trader',\n      description: 'Score 1000+ points in a single game',\n      icon: '📈',\n      category: 'trading',\n      points: 100,\n    })\n  }\n\n  if (session.score >= 5000) {\n    achievements.push({\n      id: 'high_score_5000',\n      name: 'Expert Trader',\n      description: 'Score 5000+ points in a single game',\n      icon: '🏆',\n      category: 'trading',\n      points: 250,\n    })\n  }\n\n  // Game-specific achievements\n  if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {\n    achievements.push({\n      id: 'speed_scalper',\n      name: 'Lightning Fast',\n      description: 'Complete Scalper Sprint in under 30 seconds',\n      icon: '⚡',\n      category: 'trading',\n      points: 150,\n    })\n  }\n\n  // Unlock achievements\n  achievements.forEach(achievement => {\n    store.unlockAchievement(achievement)\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAmCA,MAAM,qBAAkC;IACtC,MAAM;IACN,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;AACf;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,gBAAgB,EAAE;QAElB,0BAA0B;QAC1B,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;YAEpC,wDAAwD;YACxD,IAAI,MAAM;gBACR,MAAM,OAAO,KAAK,cAAc,IAAI,CAAC,KAAK,QAAQ,GAAG,eAAe,OAAO;gBAC3E,MAAM,mBAAmB,CAAC;YAC5B;QACF;QAEA,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE,iBAAiB;YAAc;QAE1E,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAElD,uBAAuB;QACvB,qBAAqB,CAAC;YACpB,MAAM,iBAA8B,SAAS,eACzC;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf,IACA;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf;YAEJ,IAAI;gBACF,eAAe;gBACf,aAAa;YACf;YAEA,wBAAwB;YACxB,MAAM,kBAAkB;YAExB,qCAAqC;YACrC,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM;gBACR,MAAM,iBAAiB,CAAC;oBAAE,gBAAgB;gBAAK;YACjD;QACF;QAEA,oBAAoB;YAClB,8DAA8D;YAC9D,oIAAmC,IAAI,CAAC,CAAC,EAAE,aAAa,EAAE;gBACxD,MAAM,EAAE,gBAAgB,EAAE,GAAG,cAAc,QAAQ;gBACnD,iBAAiB,MAAM,aAAa;YACtC,GAAG,KAAK,CAAC;YACP,oCAAoC;YACtC;QACF;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,MAAM;oBAAC;gBACjD,CAAC;QACH;QAEA,yBAAyB;QACzB,eAAe,CAAC,QAAQ;YACtB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,iBAAiB,EAAE,QAAQ;QACzD;QAEA,iBAAiB,CAAC,QAAQ;YACxB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,QAAQ,KAAK,iBAAiB,GAAG,QAAQ;gBAC5C,OAAO;YACT;YAEA,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,eAAe,EAAE,SAAS;YACtD,OAAO;QACT;QAEA,0BAA0B;QAC1B,eAAe,CAAC;YACd,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,gBAAgB,KAAK,iBAAiB,GAAG;YAC/C,MAAM,WAAW,eAAe;YAChC,MAAM,YAAY,WAAW,KAAK,KAAK;YAEvC,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB;gBACnB,OAAO;YACT;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;YAC7C,qEAAqE;YACvE;QACF;QAEA,qBAAqB;QACrB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,2CAA2C;YAC3C,MAAM,kBAAkB,KAAK,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;YAC3E,IAAI,iBAAiB;YAErB,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,cAAc;uBAAI,KAAK,YAAY;oBAAE;wBAAE,GAAG,WAAW;wBAAE,aAAa,IAAI,OAAO,WAAW;oBAAG;iBAAE;YACjG;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,oCAAoC;YACpC,MAAM,aAAa,CAAC,YAAY,MAAM,EAAE,CAAC,aAAa,EAAE,YAAY,IAAI,EAAE;YAE1E,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,IAAI,EAAE;QACvD,qEAAqE;QACvE;QAEA,0BAA0B;QAC1B,kBAAkB,CAAC;YACjB,IAAI;gBAAE,oBAAoB;YAAQ;QACpC;QAEA,gBAAgB,CAAC;YACf,MAAM,EAAE,cAAc,EAAE,GAAG;YAE3B,wCAAwC;YACxC,MAAM,kBAAkB;gBAAC;mBAAY;aAAe,CAAC,KAAK,CAAC,GAAG;YAE9D,IAAI;gBACF,oBAAoB;gBACpB,gBAAgB;YAClB;YAEA,mCAAmC;YACnC,MAAM,aAAa,CAAC,QAAQ,kBAAkB,EAAE,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE;YAC5E,MAAM,aAAa,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;YAE/C,yBAAyB;YACzB,sBAAsB,SAAS;QACjC;QAEA,kBAAkB;QAClB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC1C,IAAI;gBAAE,MAAM;YAAY;YAExB,8CAA8C;YAC9C,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,UAAU;QACV,eAAe;YACb,IAAI;gBACF,MAAM;gBACN,iBAAiB;gBACjB,oBAAoB;gBACpB,gBAAgB,EAAE;gBAClB,eAAe;gBACf,aAAa;YACf;QACF;QAEA,yBAAyB;QACzB,SAAS;YACP,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,MAAM,aAAa;QACrB;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;gBAE5B,sBAAsB;gBACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAEjF,IAAI,cAAc;oBAChB,QAAQ,IAAI,CAAC,mCAAmC,aAAa,OAAO;oBACpE,gCAAgC;oBAChC,MAAM,OAAO,CAAC;wBACZ,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,KAAK;wBACL,UAAU;wBACV,gBAAgB;wBAChB,YAAY;wBACZ,mBAAmB;wBACnB,OAAO;wBACP,mBAAmB;wBACnB,cAAc,EAAE;wBAChB,UAAU;wBACV,oBAAoB;wBACpB,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBACA;gBACF;gBAEA,IAAI,SAAS,MAAM;oBACjB,qBAAqB;oBACrB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;oBAET,IAAI,cAAc;wBAChB,QAAQ,IAAI,CAAC,oCAAoC,aAAa,OAAO;wBACrE;oBACF;oBAEA,IAAI,SAAS;wBACX,qBAAqB;wBACrB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;;gBAWT,CAAC,EACA,EAAE,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE;wBAEhC,MAAM,mBAAmB,cAAc,IAAI,CAAA,KAAM,CAAC;gCAChD,GAAG,GAAG,YAAY;gCAClB,aAAa,GAAG,WAAW;4BAC7B,CAAC,MAAM,EAAE;wBAET,MAAM,OAAO,CAAC;4BACZ,IAAI,QAAQ,EAAE;4BACd,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,UAAU,QAAQ,QAAQ;4BAC1B,KAAK,QAAQ,GAAG;4BAChB,UAAU,QAAQ,QAAQ;4BAC1B,gBAAgB,QAAQ,cAAc;4BACtC,YAAY,QAAQ,UAAU;4BAC9B,mBAAmB,QAAQ,iBAAiB;4BAC5C,OAAO,QAAQ,KAAK;4BACpB,mBAAmB,QAAQ,iBAAiB;4BAC5C,cAAc;4BACd,UAAU,QAAQ,QAAQ;4BAC1B,oBAAoB,QAAQ,kBAAkB;4BAC9C,YAAY,QAAQ,UAAU;4BAC9B,YAAY,QAAQ,UAAU;wBAChC;oBACF;gBACF;gBAEA,0BAA0B;gBAC1B,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;oBAC5C,IAAI,UAAU,gBAAgB,CAAC,SAAS;wBACtC,MAAM,aAAa;oBACrB,OAAO,IAAI,UAAU,eAAe,SAAS,MAAM;wBACjD,mCAAmC;wBACnC,MAAM,cAAc;oBACtB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,QAAQ,IAAI,CAAC;gBAEb,gCAAgC;gBAChC,MAAM,OAAO,CAAC;oBACZ,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,KAAK;oBACL,UAAU;oBACV,gBAAgB;oBAChB,YAAY;oBACZ,mBAAmB;oBACnB,OAAO;oBACP,mBAAmB;oBACnB,cAAc,EAAE;oBAChB,UAAU;oBACV,oBAAoB;oBACpB,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;YAC9B,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH;AAIJ,mBAAmB;AACnB,SAAS,eAAe,UAAkB;IACxC,MAAM,mBAAmB;QACvB;QAAG;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAChE;IAED,IAAK,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACrD,IAAI,cAAc,gBAAgB,CAAC,EAAE,EAAE;YACrC,OAAO,IAAI;QACb;IACF;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAoB,EAAE,KAAU;IAC7D,MAAM,eAA8B,EAAE;IAEtC,yBAAyB;IACzB,IAAI,MAAM,cAAc,CAAC,MAAM,KAAK,GAAG;QACrC,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,0BAA0B;IAC1B,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,SAAS,KAAK,oBAAoB,QAAQ,gBAAgB,IAAI,IAAI;QAC5E,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,iBAAiB,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/auth/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport default function AuthProvider({ children }: AuthProviderProps) {\n  const { initializeAuth } = useUserStore()\n\n  useEffect(() => {\n    initializeAuth()\n  }, [initializeAuth])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAe;IAEnB,qBAAO;kBAAG;;AACZ;GARwB;;QACK,wIAAA,CAAA,eAAY;;;KADjB", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/themes/enhanced-color-psychology.ts"], "sourcesContent": ["/**\n * Enhanced 5-Theme Color Psychology System Based on Color Theory Principles\n * \n * Each theme follows established color theory principles:\n * 1. Monochromatic Blue - Single hue variations for focus\n * 2. Complementary Orange-Blue - Opposite colors for energy\n * 3. Triadic Green-Purple-Orange - Three equidistant colors for balance\n * 4. Analogous Warm Sunset - Adjacent warm colors for comfort\n * 5. High Contrast Accessible - Maximum accessibility compliance\n */\n\nexport interface ColorPalette {\n  // Primary colors\n  primary: string\n  primaryHover: string\n  primaryActive: string\n  \n  // Secondary colors\n  secondary: string\n  secondaryHover: string\n  secondaryActive: string\n  \n  // Background colors\n  background: string\n  backgroundSecondary: string\n  backgroundTertiary: string\n  \n  // Text colors\n  textPrimary: string\n  textSecondary: string\n  textMuted: string\n  \n  // Market condition colors\n  bullish: string\n  bullishHover: string\n  bullishBackground: string\n  \n  bearish: string\n  bearishHover: string\n  bearishBackground: string\n  \n  neutral: string\n  neutralHover: string\n  neutralBackground: string\n  \n  // Status colors\n  success: string\n  warning: string\n  error: string\n  info: string\n  \n  // Interactive elements\n  border: string\n  borderHover: string\n  borderActive: string\n  \n  // Chart colors\n  chartGrid: string\n  chartAxis: string\n  chartVolume: string\n  \n  // Accessibility\n  focus: string\n  disabled: string\n}\n\nexport interface ThemeConfig {\n  id: string\n  name: string\n  description: string\n  colorTheory: string\n  psychologyProfile: {\n    stressReduction: number // 1-10 scale\n    focusEnhancement: number // 1-10 scale\n    cognitiveLoad: number // 1-10 scale (lower is better)\n    accessibility: number // 1-10 scale\n  }\n  adolescent: ColorPalette\n  adult: ColorPalette\n}\n\n// Theme 1: Monochromatic Blue (Professional Focus)\n// Color Theory: Monochromatic scheme using various shades and tints of blue\n// Psychology: Enhances concentration, reduces stress, promotes trust and stability\nconst monochromaticBlue: ThemeConfig = {\n  id: 'monochromatic-blue',\n  name: 'Monochromatic Blue',\n  description: 'Professional blue monochromatic scheme for enhanced focus and concentration',\n  colorTheory: 'Monochromatic - Single hue with varying saturation and lightness',\n  psychologyProfile: {\n    stressReduction: 9,\n    focusEnhancement: 10,\n    cognitiveLoad: 2,\n    accessibility: 8,\n  },\n  adolescent: {\n    primary: '#3B82F6', // Bright blue\n    primaryHover: '#2563EB',\n    primaryActive: '#1D4ED8',\n    \n    secondary: '#60A5FA', // Light blue\n    secondaryHover: '#3B82F6',\n    secondaryActive: '#2563EB',\n    \n    background: '#0F172A', // Very dark blue\n    backgroundSecondary: '#1E293B',\n    backgroundTertiary: '#334155',\n    \n    textPrimary: '#F8FAFC',\n    textSecondary: '#CBD5E1',\n    textMuted: '#94A3B8',\n    \n    bullish: '#10B981',\n    bullishHover: '#059669',\n    bullishBackground: 'rgba(16, 185, 129, 0.1)',\n    \n    bearish: '#EF4444',\n    bearishHover: '#DC2626',\n    bearishBackground: 'rgba(239, 68, 68, 0.1)',\n    \n    neutral: '#8B5CF6',\n    neutralHover: '#7C3AED',\n    neutralBackground: 'rgba(139, 92, 246, 0.1)',\n    \n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    info: '#3B82F6',\n    \n    border: '#475569',\n    borderHover: '#64748B',\n    borderActive: '#94A3B8',\n    \n    chartGrid: 'rgba(59, 130, 246, 0.1)',\n    chartAxis: 'rgba(59, 130, 246, 0.3)',\n    chartVolume: 'rgba(96, 165, 250, 0.3)',\n    \n    focus: '#3B82F6',\n    disabled: 'rgba(248, 250, 252, 0.3)',\n  },\n  adult: {\n    primary: '#1E40AF', // Deep blue\n    primaryHover: '#1D4ED8',\n    primaryActive: '#2563EB',\n    \n    secondary: '#3730A3', // Darker blue\n    secondaryHover: '#4338CA',\n    secondaryActive: '#5B21B6',\n    \n    background: '#020617', // Very dark blue-black\n    backgroundSecondary: '#0F172A',\n    backgroundTertiary: '#1E293B',\n    \n    textPrimary: '#E2E8F0',\n    textSecondary: '#94A3B8',\n    textMuted: '#64748B',\n    \n    bullish: '#059669',\n    bullishHover: '#047857',\n    bullishBackground: 'rgba(5, 150, 105, 0.1)',\n    \n    bearish: '#DC2626',\n    bearishHover: '#B91C1C',\n    bearishBackground: 'rgba(220, 38, 38, 0.1)',\n    \n    neutral: '#6366F1',\n    neutralHover: '#5B21B6',\n    neutralBackground: 'rgba(99, 102, 241, 0.1)',\n    \n    success: '#059669',\n    warning: '#D97706',\n    error: '#DC2626',\n    info: '#1E40AF',\n    \n    border: '#334155',\n    borderHover: '#475569',\n    borderActive: '#64748B',\n    \n    chartGrid: 'rgba(30, 64, 175, 0.1)',\n    chartAxis: 'rgba(30, 64, 175, 0.3)',\n    chartVolume: 'rgba(99, 102, 241, 0.3)',\n    \n    focus: '#1E40AF',\n    disabled: 'rgba(226, 232, 240, 0.3)',\n  },\n}\n\n// Theme 2: Complementary Orange-Blue (High Energy & Focus)\n// Color Theory: Complementary scheme using orange and blue opposites on color wheel\n// Psychology: Creates visual excitement while maintaining focus, energizing yet balanced\nconst complementaryOrangeBlue: ThemeConfig = {\n  id: 'complementary-orange-blue',\n  name: 'Complementary Orange-Blue',\n  description: 'High-energy complementary scheme balancing excitement with focus',\n  colorTheory: 'Complementary - Opposite colors on the color wheel for maximum contrast',\n  psychologyProfile: {\n    stressReduction: 6,\n    focusEnhancement: 9,\n    cognitiveLoad: 4,\n    accessibility: 7,\n  },\n  adolescent: {\n    primary: '#F97316', // Vibrant orange\n    primaryHover: '#EA580C',\n    primaryActive: '#C2410C',\n    \n    secondary: '#2563EB', // Complementary blue\n    secondaryHover: '#1D4ED8',\n    secondaryActive: '#1E40AF',\n    \n    background: '#0C0A09', // Very dark brown\n    backgroundSecondary: '#1C1917',\n    backgroundTertiary: '#292524',\n    \n    textPrimary: '#FAFAF9',\n    textSecondary: '#E7E5E4',\n    textMuted: '#A8A29E',\n    \n    bullish: '#22C55E',\n    bullishHover: '#16A34A',\n    bullishBackground: 'rgba(34, 197, 94, 0.1)',\n    \n    bearish: '#EF4444',\n    bearishHover: '#DC2626',\n    bearishBackground: 'rgba(239, 68, 68, 0.1)',\n    \n    neutral: '#8B5CF6',\n    neutralHover: '#7C3AED',\n    neutralBackground: 'rgba(139, 92, 246, 0.1)',\n    \n    success: '#22C55E',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    info: '#2563EB',\n    \n    border: '#44403C',\n    borderHover: '#57534E',\n    borderActive: '#78716C',\n    \n    chartGrid: 'rgba(249, 115, 22, 0.1)',\n    chartAxis: 'rgba(249, 115, 22, 0.3)',\n    chartVolume: 'rgba(37, 99, 235, 0.3)',\n    \n    focus: '#F97316',\n    disabled: 'rgba(250, 250, 249, 0.3)',\n  },\n  adult: {\n    primary: '#EA580C', // Deep orange\n    primaryHover: '#C2410C',\n    primaryActive: '#9A3412',\n    \n    secondary: '#1E40AF', // Deep blue\n    secondaryHover: '#1E3A8A',\n    secondaryActive: '#1E3A8A',\n    \n    background: '#0A0A0A', // Near black\n    backgroundSecondary: '#171717',\n    backgroundTertiary: '#262626',\n    \n    textPrimary: '#F5F5F5',\n    textSecondary: '#D4D4D4',\n    textMuted: '#A3A3A3',\n    \n    bullish: '#16A34A',\n    bullishHover: '#15803D',\n    bullishBackground: 'rgba(22, 163, 74, 0.1)',\n    \n    bearish: '#DC2626',\n    bearishHover: '#B91C1C',\n    bearishBackground: 'rgba(220, 38, 38, 0.1)',\n    \n    neutral: '#7C3AED',\n    neutralHover: '#6D28D9',\n    neutralBackground: 'rgba(124, 58, 237, 0.1)',\n    \n    success: '#16A34A',\n    warning: '#D97706',\n    error: '#DC2626',\n    info: '#1E40AF',\n    \n    border: '#404040',\n    borderHover: '#525252',\n    borderActive: '#737373',\n    \n    chartGrid: 'rgba(234, 88, 12, 0.1)',\n    chartAxis: 'rgba(234, 88, 12, 0.3)',\n    chartVolume: 'rgba(30, 64, 175, 0.3)',\n    \n    focus: '#EA580C',\n    disabled: 'rgba(245, 245, 245, 0.3)',\n  },\n}\n\n// Theme 3: Triadic Green-Purple-Orange (Balanced Harmony)\n// Color Theory: Triadic scheme using three colors equally spaced on color wheel\n// Psychology: Creates vibrant harmony while maintaining balance, reduces eye strain\nconst triadicGreenPurpleOrange: ThemeConfig = {\n  id: 'triadic-green-purple-orange',\n  name: 'Triadic Harmony',\n  description: 'Balanced triadic scheme with green, purple, and orange for visual harmony',\n  colorTheory: 'Triadic - Three colors equally spaced on the color wheel for vibrant balance',\n  psychologyProfile: {\n    stressReduction: 8,\n    focusEnhancement: 7,\n    cognitiveLoad: 3,\n    accessibility: 8,\n  },\n  adolescent: {\n    primary: '#10B981', // Emerald green\n    primaryHover: '#059669',\n    primaryActive: '#047857',\n\n    secondary: '#8B5CF6', // Purple\n    secondaryHover: '#7C3AED',\n    secondaryActive: '#6D28D9',\n\n    background: '#0F0F0F', // Very dark\n    backgroundSecondary: '#1A1A1A',\n    backgroundTertiary: '#2D2D2D',\n\n    textPrimary: '#F0F0F0',\n    textSecondary: '#D0D0D0',\n    textMuted: '#A0A0A0',\n\n    bullish: '#10B981',\n    bullishHover: '#059669',\n    bullishBackground: 'rgba(16, 185, 129, 0.1)',\n\n    bearish: '#F97316', // Orange for bearish\n    bearishHover: '#EA580C',\n    bearishBackground: 'rgba(249, 115, 22, 0.1)',\n\n    neutral: '#8B5CF6',\n    neutralHover: '#7C3AED',\n    neutralBackground: 'rgba(139, 92, 246, 0.1)',\n\n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#F97316',\n    info: '#8B5CF6',\n\n    border: '#404040',\n    borderHover: '#525252',\n    borderActive: '#737373',\n\n    chartGrid: 'rgba(16, 185, 129, 0.1)',\n    chartAxis: 'rgba(16, 185, 129, 0.3)',\n    chartVolume: 'rgba(139, 92, 246, 0.3)',\n\n    focus: '#10B981',\n    disabled: 'rgba(240, 240, 240, 0.3)',\n  },\n  adult: {\n    primary: '#059669', // Deep green\n    primaryHover: '#047857',\n    primaryActive: '#065F46',\n\n    secondary: '#7C3AED', // Deep purple\n    secondaryHover: '#6D28D9',\n    secondaryActive: '#5B21B6',\n\n    background: '#000000', // Pure black\n    backgroundSecondary: '#111111',\n    backgroundTertiary: '#1F1F1F',\n\n    textPrimary: '#E5E5E5',\n    textSecondary: '#B5B5B5',\n    textMuted: '#858585',\n\n    bullish: '#059669',\n    bullishHover: '#047857',\n    bullishBackground: 'rgba(5, 150, 105, 0.1)',\n\n    bearish: '#EA580C', // Deep orange\n    bearishHover: '#C2410C',\n    bearishBackground: 'rgba(234, 88, 12, 0.1)',\n\n    neutral: '#7C3AED',\n    neutralHover: '#6D28D9',\n    neutralBackground: 'rgba(124, 58, 237, 0.1)',\n\n    success: '#059669',\n    warning: '#D97706',\n    error: '#EA580C',\n    info: '#7C3AED',\n\n    border: '#333333',\n    borderHover: '#444444',\n    borderActive: '#666666',\n\n    chartGrid: 'rgba(5, 150, 105, 0.1)',\n    chartAxis: 'rgba(5, 150, 105, 0.3)',\n    chartVolume: 'rgba(124, 58, 237, 0.3)',\n\n    focus: '#059669',\n    disabled: 'rgba(229, 229, 229, 0.3)',\n  },\n}\n\n// Theme 4: Analogous Warm Sunset (Comfort & Warmth)\n// Color Theory: Analogous scheme using adjacent warm colors (red, orange, yellow)\n// Psychology: Creates warmth and comfort, reduces anxiety, promotes creativity\nconst analogousWarmSunset: ThemeConfig = {\n  id: 'analogous-warm-sunset',\n  name: 'Analogous Warm Sunset',\n  description: 'Warm analogous scheme with sunset colors for comfort and creativity',\n  colorTheory: 'Analogous - Adjacent colors on the color wheel for harmonious warmth',\n  psychologyProfile: {\n    stressReduction: 7,\n    focusEnhancement: 6,\n    cognitiveLoad: 3,\n    accessibility: 7,\n  },\n  adolescent: {\n    primary: '#F59E0B', // Amber\n    primaryHover: '#D97706',\n    primaryActive: '#B45309',\n\n    secondary: '#EF4444', // Red\n    secondaryHover: '#DC2626',\n    secondaryActive: '#B91C1C',\n\n    background: '#1A0F0A', // Very dark warm\n    backgroundSecondary: '#2D1B14',\n    backgroundTertiary: '#44281A',\n\n    textPrimary: '#FEF3E2',\n    textSecondary: '#FDE68A',\n    textMuted: '#D69E2E',\n\n    bullish: '#10B981',\n    bullishHover: '#059669',\n    bullishBackground: 'rgba(16, 185, 129, 0.1)',\n\n    bearish: '#EF4444',\n    bearishHover: '#DC2626',\n    bearishBackground: 'rgba(239, 68, 68, 0.1)',\n\n    neutral: '#F59E0B',\n    neutralHover: '#D97706',\n    neutralBackground: 'rgba(245, 158, 11, 0.1)',\n\n    success: '#10B981',\n    warning: '#F59E0B',\n    error: '#EF4444',\n    info: '#3B82F6',\n\n    border: '#92400E',\n    borderHover: '#B45309',\n    borderActive: '#D97706',\n\n    chartGrid: 'rgba(245, 158, 11, 0.1)',\n    chartAxis: 'rgba(245, 158, 11, 0.3)',\n    chartVolume: 'rgba(239, 68, 68, 0.3)',\n\n    focus: '#F59E0B',\n    disabled: 'rgba(254, 243, 226, 0.3)',\n  },\n  adult: {\n    primary: '#D97706', // Deep amber\n    primaryHover: '#B45309',\n    primaryActive: '#92400E',\n\n    secondary: '#DC2626', // Deep red\n    secondaryHover: '#B91C1C',\n    secondaryActive: '#991B1B',\n\n    background: '#0F0A08', // Very dark warm\n    backgroundSecondary: '#1C1410',\n    backgroundTertiary: '#2C1F17',\n\n    textPrimary: '#F7E6D3',\n    textSecondary: '#E4C29F',\n    textMuted: '#B8956B',\n\n    bullish: '#059669',\n    bullishHover: '#047857',\n    bullishBackground: 'rgba(5, 150, 105, 0.1)',\n\n    bearish: '#DC2626',\n    bearishHover: '#B91C1C',\n    bearishBackground: 'rgba(220, 38, 38, 0.1)',\n\n    neutral: '#D97706',\n    neutralHover: '#B45309',\n    neutralBackground: 'rgba(217, 119, 6, 0.1)',\n\n    success: '#059669',\n    warning: '#D97706',\n    error: '#DC2626',\n    info: '#2563EB',\n\n    border: '#78350F',\n    borderHover: '#92400E',\n    borderActive: '#B45309',\n\n    chartGrid: 'rgba(217, 119, 6, 0.1)',\n    chartAxis: 'rgba(217, 119, 6, 0.3)',\n    chartVolume: 'rgba(220, 38, 38, 0.3)',\n\n    focus: '#D97706',\n    disabled: 'rgba(247, 230, 211, 0.3)',\n  },\n}\n\n// Theme 5: High Contrast Accessible (Maximum Accessibility)\n// Color Theory: High contrast monochromatic with accessibility focus\n// Psychology: Reduces cognitive load, maximizes readability, supports visual impairments\nconst highContrastAccessible: ThemeConfig = {\n  id: 'high-contrast-accessible',\n  name: 'High Contrast Accessible',\n  description: 'Maximum accessibility with WCAG AAA compliance for all users',\n  colorTheory: 'High Contrast - Maximum contrast ratios for optimal accessibility',\n  psychologyProfile: {\n    stressReduction: 8,\n    focusEnhancement: 10,\n    cognitiveLoad: 1,\n    accessibility: 10,\n  },\n  adolescent: {\n    primary: '#0066CC', // High contrast blue\n    primaryHover: '#0052A3',\n    primaryActive: '#003D7A',\n\n    secondary: '#FF6600', // High contrast orange\n    secondaryHover: '#E55A00',\n    secondaryActive: '#CC4E00',\n\n    background: '#FFFFFF', // Pure white\n    backgroundSecondary: '#F8F9FA',\n    backgroundTertiary: '#E9ECEF',\n\n    textPrimary: '#000000', // Pure black\n    textSecondary: '#212529',\n    textMuted: '#495057',\n\n    bullish: '#008000', // Pure green\n    bullishHover: '#006600',\n    bullishBackground: 'rgba(0, 128, 0, 0.1)',\n\n    bearish: '#CC0000', // Pure red\n    bearishHover: '#990000',\n    bearishBackground: 'rgba(204, 0, 0, 0.1)',\n\n    neutral: '#000080', // Navy blue\n    neutralHover: '#000066',\n    neutralBackground: 'rgba(0, 0, 128, 0.1)',\n\n    success: '#008000',\n    warning: '#FF8C00',\n    error: '#CC0000',\n    info: '#0066CC',\n\n    border: '#000000',\n    borderHover: '#333333',\n    borderActive: '#666666',\n\n    chartGrid: 'rgba(0, 0, 0, 0.2)',\n    chartAxis: 'rgba(0, 0, 0, 0.5)',\n    chartVolume: 'rgba(0, 102, 204, 0.3)',\n\n    focus: '#FF6600',\n    disabled: 'rgba(0, 0, 0, 0.3)',\n  },\n  adult: {\n    primary: '#FFFFFF', // White on black\n    primaryHover: '#E0E0E0',\n    primaryActive: '#C0C0C0',\n\n    secondary: '#FFFF00', // High contrast yellow\n    secondaryHover: '#E6E600',\n    secondaryActive: '#CCCC00',\n\n    background: '#000000', // Pure black\n    backgroundSecondary: '#1A1A1A',\n    backgroundTertiary: '#333333',\n\n    textPrimary: '#FFFFFF',\n    textSecondary: '#E0E0E0',\n    textMuted: '#B0B0B0',\n\n    bullish: '#00FF00', // Bright green\n    bullishHover: '#00E600',\n    bullishBackground: 'rgba(0, 255, 0, 0.1)',\n\n    bearish: '#FF0000', // Bright red\n    bearishHover: '#E60000',\n    bearishBackground: 'rgba(255, 0, 0, 0.1)',\n\n    neutral: '#00FFFF', // Cyan\n    neutralHover: '#00E6E6',\n    neutralBackground: 'rgba(0, 255, 255, 0.1)',\n\n    success: '#00FF00',\n    warning: '#FFFF00',\n    error: '#FF0000',\n    info: '#00FFFF',\n\n    border: '#FFFFFF',\n    borderHover: '#E0E0E0',\n    borderActive: '#C0C0C0',\n\n    chartGrid: 'rgba(255, 255, 255, 0.2)',\n    chartAxis: 'rgba(255, 255, 255, 0.5)',\n    chartVolume: 'rgba(0, 255, 255, 0.3)',\n\n    focus: '#FFFF00',\n    disabled: 'rgba(255, 255, 255, 0.3)',\n  },\n}\n\n// Export all themes\nexport const enhancedThemes: ThemeConfig[] = [\n  monochromaticBlue,\n  complementaryOrangeBlue,\n  triadicGreenPurpleOrange,\n  analogousWarmSunset,\n  highContrastAccessible,\n]\n\n// Theme utility functions\nexport const getEnhancedThemeById = (id: string): ThemeConfig | undefined => {\n  return enhancedThemes.find(theme => theme.id === id)\n}\n\nexport const getEnhancedThemeColors = (themeId: string, mode: 'adolescent' | 'adult'): ColorPalette => {\n  const theme = getEnhancedThemeById(themeId) || enhancedThemes[0] // Default to first theme\n  return theme[mode]\n}\n\n// CSS custom properties generator\nexport const generateEnhancedCSSVariables = (colors: ColorPalette): Record<string, string> => {\n  return {\n    '--color-primary': colors.primary,\n    '--color-primary-hover': colors.primaryHover,\n    '--color-primary-active': colors.primaryActive,\n    '--color-secondary': colors.secondary,\n    '--color-secondary-hover': colors.secondaryHover,\n    '--color-secondary-active': colors.secondaryActive,\n    '--color-background': colors.background,\n    '--color-background-secondary': colors.backgroundSecondary,\n    '--color-background-tertiary': colors.backgroundTertiary,\n    '--color-text-primary': colors.textPrimary,\n    '--color-text-secondary': colors.textSecondary,\n    '--color-text-muted': colors.textMuted,\n    '--color-bullish': colors.bullish,\n    '--color-bullish-hover': colors.bullishHover,\n    '--color-bullish-background': colors.bullishBackground,\n    '--color-bearish': colors.bearish,\n    '--color-bearish-hover': colors.bearishHover,\n    '--color-bearish-background': colors.bearishBackground,\n    '--color-neutral': colors.neutral,\n    '--color-neutral-hover': colors.neutralHover,\n    '--color-neutral-background': colors.neutralBackground,\n    '--color-success': colors.success,\n    '--color-warning': colors.warning,\n    '--color-error': colors.error,\n    '--color-info': colors.info,\n    '--color-border': colors.border,\n    '--color-border-hover': colors.borderHover,\n    '--color-border-active': colors.borderActive,\n    '--color-chart-grid': colors.chartGrid,\n    '--color-chart-axis': colors.chartAxis,\n    '--color-chart-volume': colors.chartVolume,\n    '--color-focus': colors.focus,\n    '--color-disabled': colors.disabled,\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;;AAwED,mDAAmD;AACnD,4EAA4E;AAC5E,mFAAmF;AACnF,MAAM,oBAAiC;IACrC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,2DAA2D;AAC3D,oFAAoF;AACpF,yFAAyF;AACzF,MAAM,0BAAuC;IAC3C,IAAI;IACJ,MAAM;IACN,aAAa;IACb,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,0DAA0D;AAC1D,gFAAgF;AAChF,oFAAoF;AACpF,MAAM,2BAAwC;IAC5C,IAAI;IACJ,MAAM;IACN,aAAa;IACb,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,oDAAoD;AACpD,kFAAkF;AAClF,+EAA+E;AAC/E,MAAM,sBAAmC;IACvC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,4DAA4D;AAC5D,qEAAqE;AACrE,yFAAyF;AACzF,MAAM,yBAAsC;IAC1C,IAAI;IACJ,MAAM;IACN,aAAa;IACb,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAGO,MAAM,iBAAgC;IAC3C;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,uBAAuB,CAAC;IACnC,OAAO,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AACnD;AAEO,MAAM,yBAAyB,CAAC,SAAiB;IACtD,MAAM,QAAQ,qBAAqB,YAAY,cAAc,CAAC,EAAE,CAAC,yBAAyB;;IAC1F,OAAO,KAAK,CAAC,KAAK;AACpB;AAGO,MAAM,+BAA+B,CAAC;IAC3C,OAAO;QACL,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,0BAA0B,OAAO,aAAa;QAC9C,qBAAqB,OAAO,SAAS;QACrC,2BAA2B,OAAO,cAAc;QAChD,4BAA4B,OAAO,eAAe;QAClD,sBAAsB,OAAO,UAAU;QACvC,gCAAgC,OAAO,mBAAmB;QAC1D,+BAA+B,OAAO,kBAAkB;QACxD,wBAAwB,OAAO,WAAW;QAC1C,0BAA0B,OAAO,aAAa;QAC9C,sBAAsB,OAAO,SAAS;QACtC,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,8BAA8B,OAAO,iBAAiB;QACtD,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,8BAA8B,OAAO,iBAAiB;QACtD,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,8BAA8B,OAAO,iBAAiB;QACtD,mBAAmB,OAAO,OAAO;QACjC,mBAAmB,OAAO,OAAO;QACjC,iBAAiB,OAAO,KAAK;QAC7B,gBAAgB,OAAO,IAAI;QAC3B,kBAAkB,OAAO,MAAM;QAC/B,wBAAwB,OAAO,WAAW;QAC1C,yBAAyB,OAAO,YAAY;QAC5C,sBAAsB,OAAO,SAAS;QACtC,sBAAsB,OAAO,SAAS;QACtC,wBAAwB,OAAO,WAAW;QAC1C,iBAAiB,OAAO,KAAK;QAC7B,oBAAoB,OAAO,QAAQ;IACrC;AACF", "debugId": null}}, {"offset": {"line": 1078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/stores/theme-store.ts"], "sourcesContent": ["import React from 'react'\nimport { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { enhancedThemes as themes, getEnhancedThemeById as getThemeById, getEnhancedThemeColors as getThemeColors, generateEnhancedCSSVariables as generateCSSVariables, type ThemeConfig, type ColorPalette } from '@/lib/themes/enhanced-color-psychology'\n\ninterface ThemeState {\n  // Current theme\n  currentThemeId: string\n  currentTheme: ThemeConfig\n  \n  // Interface mode affects which color palette is used\n  interfaceMode: 'adolescent' | 'adult'\n  \n  // Current active colors\n  colors: ColorPalette\n  \n  // Theme preferences\n  autoThemeSwitch: boolean // Switch theme based on time of day\n  reduceMotion: boolean\n  highContrast: boolean\n  \n  // Actions\n  setTheme: (themeId: string) => void\n  setInterfaceMode: (mode: 'adolescent' | 'adult') => void\n  toggleAutoThemeSwitch: () => void\n  toggleReduceMotion: () => void\n  toggleHighContrast: () => void\n  applyThemeToDOM: () => void\n  getRecommendedTheme: () => string\n}\n\nexport const useThemeStore = create<ThemeState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      currentThemeId: 'monochromatic-blue',\n      currentTheme: themes[0],\n      interfaceMode: 'adolescent',\n      colors: themes[0].adolescent,\n      autoThemeSwitch: false,\n      reduceMotion: false,\n      highContrast: false,\n\n      // Set theme by ID\n      setTheme: (themeId: string) => {\n        const theme = getThemeById(themeId)\n        if (!theme) return\n\n        const { interfaceMode } = get()\n        const colors = getThemeColors(themeId, interfaceMode)\n\n        set({\n          currentThemeId: themeId,\n          currentTheme: theme,\n          colors,\n        })\n\n        // Apply to DOM immediately\n        get().applyThemeToDOM()\n      },\n\n      // Set interface mode (adolescent/adult)\n      setInterfaceMode: (mode: 'adolescent' | 'adult') => {\n        const { currentThemeId } = get()\n        const colors = getThemeColors(currentThemeId, mode)\n\n        set({\n          interfaceMode: mode,\n          colors,\n        })\n\n        // Apply to DOM immediately\n        get().applyThemeToDOM()\n      },\n\n      // Toggle auto theme switching\n      toggleAutoThemeSwitch: () => {\n        const { autoThemeSwitch } = get()\n        set({ autoThemeSwitch: !autoThemeSwitch })\n\n        // If enabling auto switch, apply recommended theme\n        if (!autoThemeSwitch) {\n          const recommendedTheme = get().getRecommendedTheme()\n          get().setTheme(recommendedTheme)\n        }\n      },\n\n      // Toggle reduced motion\n      toggleReduceMotion: () => {\n        const { reduceMotion } = get()\n        set({ reduceMotion: !reduceMotion })\n        \n        // Apply to DOM\n        document.documentElement.style.setProperty(\n          '--animation-duration',\n          !reduceMotion ? '0s' : '0.3s'\n        )\n      },\n\n      // Toggle high contrast mode\n      toggleHighContrast: () => {\n        const { highContrast } = get()\n        const newHighContrast = !highContrast\n        \n        set({ highContrast: newHighContrast })\n\n        // If enabling high contrast, switch to high contrast theme\n        if (newHighContrast) {\n          get().setTheme('high-contrast')\n        }\n      },\n\n      // Apply current theme colors to DOM as CSS custom properties\n      applyThemeToDOM: () => {\n        const { colors, reduceMotion } = get()\n        const cssVariables = generateCSSVariables(colors)\n\n        // Apply CSS custom properties to document root\n        Object.entries(cssVariables).forEach(([property, value]) => {\n          document.documentElement.style.setProperty(property, value)\n        })\n\n        // Apply motion preferences\n        document.documentElement.style.setProperty(\n          '--animation-duration',\n          reduceMotion ? '0s' : '0.3s'\n        )\n\n        // Apply theme class to body for additional styling\n        document.body.className = document.body.className\n          .replace(/theme-\\w+/g, '')\n          .concat(` theme-${get().currentThemeId}`)\n      },\n\n      // Get recommended theme based on various factors\n      getRecommendedTheme: (): string => {\n        const hour = new Date().getHours()\n        const { highContrast } = get()\n\n        // High contrast override\n        if (highContrast) {\n          return 'high-contrast-accessible'\n        }\n\n        // Time-based recommendations\n        if (hour >= 6 && hour < 12) {\n          // Morning: Energizing warm theme\n          return 'analogous-warm-sunset'\n        } else if (hour >= 12 && hour < 18) {\n          // Afternoon: Focus-enhancing blue theme\n          return 'monochromatic-blue'\n        } else if (hour >= 18 && hour < 22) {\n          // Evening: Balanced triadic theme\n          return 'triadic-green-purple-orange'\n        } else {\n          // Night: High energy complementary theme\n          return 'complementary-orange-blue'\n        }\n      },\n    }),\n    {\n      name: 'tradequest-theme-storage',\n      partialize: (state) => ({\n        currentThemeId: state.currentThemeId,\n        interfaceMode: state.interfaceMode,\n        autoThemeSwitch: state.autoThemeSwitch,\n        reduceMotion: state.reduceMotion,\n        highContrast: state.highContrast,\n      }),\n    }\n  )\n)\n\n// Theme initialization hook\nexport const useThemeInitialization = () => {\n  const { \n    setTheme, \n    setInterfaceMode, \n    applyThemeToDOM, \n    getRecommendedTheme,\n    autoThemeSwitch,\n    currentThemeId,\n    interfaceMode \n  } = useThemeStore()\n\n  // Initialize theme on mount\n  React.useEffect(() => {\n    // Apply current theme to DOM\n    applyThemeToDOM()\n\n    // Auto theme switching\n    if (autoThemeSwitch) {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }\n\n    // Listen for system preference changes\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')\n    const handleMotionChange = (e: MediaQueryListEvent) => {\n      useThemeStore.getState().toggleReduceMotion()\n    }\n    \n    mediaQuery.addEventListener('change', handleMotionChange)\n    \n    return () => {\n      mediaQuery.removeEventListener('change', handleMotionChange)\n    }\n  }, [])\n\n  // Auto theme switching interval\n  React.useEffect(() => {\n    if (!autoThemeSwitch) return\n\n    const interval = setInterval(() => {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }, 60000) // Check every minute\n\n    return () => clearInterval(interval)\n  }, [autoThemeSwitch, currentThemeId, setTheme, getRecommendedTheme])\n}\n\n// Accessibility helpers\nexport const getAccessibilityScore = (themeId: string): number => {\n  const theme = getThemeById(themeId)\n  return theme?.psychologyProfile.accessibility || 0\n}\n\nexport const getStressReductionScore = (themeId: string): number => {\n  const theme = getThemeById(themeId)\n  return theme?.psychologyProfile.stressReduction || 0\n}\n\nexport const getFocusEnhancementScore = (themeId: string): number => {\n  const theme = getThemeById(themeId)\n  return theme?.psychologyProfile.focusEnhancement || 0\n}\n\n// Theme recommendation engine\nexport const getPersonalizedThemeRecommendation = (userProfile: {\n  age?: number\n  tradingExperience?: 'beginner' | 'intermediate' | 'advanced'\n  visualImpairment?: boolean\n  stressLevel?: 'low' | 'medium' | 'high'\n  sessionDuration?: 'short' | 'medium' | 'long'\n}): string => {\n  const { age, tradingExperience, visualImpairment, stressLevel, sessionDuration } = userProfile\n\n  // High contrast for visual impairments\n  if (visualImpairment) {\n    return 'high-contrast-accessible'\n  }\n\n  // Accessibility optimization for older users\n  if (age && age > 40) {\n    return 'high-contrast-accessible'\n  }\n\n  // Stress-based recommendations\n  if (stressLevel === 'high' || sessionDuration === 'long') {\n    return 'monochromatic-blue'\n  }\n\n  // Experience-based recommendations\n  if (tradingExperience === 'beginner') {\n    return 'analogous-warm-sunset' // Engaging for learning\n  }\n\n  if (tradingExperience === 'advanced') {\n    return 'triadic-green-purple-orange' // Balanced for experts\n  }\n\n  // Default to monochromatic blue for most users\n  return 'monochromatic-blue'\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AA4BO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,gBAAgB;QAChB,cAAc,0JAAA,CAAA,iBAAM,CAAC,EAAE;QACvB,eAAe;QACf,QAAQ,0JAAA,CAAA,iBAAM,CAAC,EAAE,CAAC,UAAU;QAC5B,iBAAiB;QACjB,cAAc;QACd,cAAc;QAEd,kBAAkB;QAClB,UAAU,CAAC;YACT,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,uBAAY,AAAD,EAAE;YAC3B,IAAI,CAAC,OAAO;YAEZ,MAAM,EAAE,aAAa,EAAE,GAAG;YAC1B,MAAM,SAAS,CAAA,GAAA,0JAAA,CAAA,yBAAc,AAAD,EAAE,SAAS;YAEvC,IAAI;gBACF,gBAAgB;gBAChB,cAAc;gBACd;YACF;YAEA,2BAA2B;YAC3B,MAAM,eAAe;QACvB;QAEA,wCAAwC;QACxC,kBAAkB,CAAC;YACjB,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,SAAS,CAAA,GAAA,0JAAA,CAAA,yBAAc,AAAD,EAAE,gBAAgB;YAE9C,IAAI;gBACF,eAAe;gBACf;YACF;YAEA,2BAA2B;YAC3B,MAAM,eAAe;QACvB;QAEA,8BAA8B;QAC9B,uBAAuB;YACrB,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,IAAI;gBAAE,iBAAiB,CAAC;YAAgB;YAExC,mDAAmD;YACnD,IAAI,CAAC,iBAAiB;gBACpB,MAAM,mBAAmB,MAAM,mBAAmB;gBAClD,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,wBAAwB;QACxB,oBAAoB;YAClB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,IAAI;gBAAE,cAAc,CAAC;YAAa;YAElC,eAAe;YACf,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CACxC,wBACA,CAAC,eAAe,OAAO;QAE3B;QAEA,4BAA4B;QAC5B,oBAAoB;YAClB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,MAAM,kBAAkB,CAAC;YAEzB,IAAI;gBAAE,cAAc;YAAgB;YAEpC,2DAA2D;YAC3D,IAAI,iBAAiB;gBACnB,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,6DAA6D;QAC7D,iBAAiB;YACf,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;YACjC,MAAM,eAAe,CAAA,GAAA,0JAAA,CAAA,+BAAoB,AAAD,EAAE;YAE1C,+CAA+C;YAC/C,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;gBACrD,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU;YACvD;YAEA,2BAA2B;YAC3B,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CACxC,wBACA,eAAe,OAAO;YAGxB,mDAAmD;YACnD,SAAS,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAC9C,OAAO,CAAC,cAAc,IACtB,MAAM,CAAC,CAAC,OAAO,EAAE,MAAM,cAAc,EAAE;QAC5C;QAEA,iDAAiD;QACjD,qBAAqB;YACnB,MAAM,OAAO,IAAI,OAAO,QAAQ;YAChC,MAAM,EAAE,YAAY,EAAE,GAAG;YAEzB,yBAAyB;YACzB,IAAI,cAAc;gBAChB,OAAO;YACT;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,KAAK,OAAO,IAAI;gBAC1B,iCAAiC;gBACjC,OAAO;YACT,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;gBAClC,wCAAwC;gBACxC,OAAO;YACT,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;gBAClC,kCAAkC;gBAClC,OAAO;YACT,OAAO;gBACL,yCAAyC;gBACzC,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,gBAAgB,MAAM,cAAc;YACpC,eAAe,MAAM,aAAa;YAClC,iBAAiB,MAAM,eAAe;YACtC,cAAc,MAAM,YAAY;YAChC,cAAc,MAAM,YAAY;QAClC,CAAC;AACH;AAKG,MAAM,yBAAyB;;IACpC,MAAM,EACJ,QAAQ,EACR,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,aAAa,EACd,GAAG;IAEJ,4BAA4B;IAC5B,6JAAA,CAAA,UAAK,CAAC,SAAS;4CAAC;YACd,6BAA6B;YAC7B;YAEA,uBAAuB;YACvB,IAAI,iBAAiB;gBACnB,MAAM,mBAAmB;gBACzB,IAAI,qBAAqB,gBAAgB;oBACvC,SAAS;gBACX;YACF;YAEA,uCAAuC;YACvC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;uEAAqB,CAAC;oBAC1B,cAAc,QAAQ,GAAG,kBAAkB;gBAC7C;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YAEtC;oDAAO;oBACL,WAAW,mBAAmB,CAAC,UAAU;gBAC3C;;QACF;2CAAG,EAAE;IAEL,gCAAgC;IAChC,6JAAA,CAAA,UAAK,CAAC,SAAS;4CAAC;YACd,IAAI,CAAC,iBAAiB;YAEtB,MAAM,WAAW;6DAAY;oBAC3B,MAAM,mBAAmB;oBACzB,IAAI,qBAAqB,gBAAgB;wBACvC,SAAS;oBACX;gBACF;4DAAG,OAAO,qBAAqB;;YAE/B;oDAAO,IAAM,cAAc;;QAC7B;2CAAG;QAAC;QAAiB;QAAgB;QAAU;KAAoB;AACrE;GAlDa;;QASP;;;AA4CC,MAAM,wBAAwB,CAAC;IACpC,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,uBAAY,AAAD,EAAE;IAC3B,OAAO,OAAO,kBAAkB,iBAAiB;AACnD;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,uBAAY,AAAD,EAAE;IAC3B,OAAO,OAAO,kBAAkB,mBAAmB;AACrD;AAEO,MAAM,2BAA2B,CAAC;IACvC,MAAM,QAAQ,CAAA,GAAA,0JAAA,CAAA,uBAAY,AAAD,EAAE;IAC3B,OAAO,OAAO,kBAAkB,oBAAoB;AACtD;AAGO,MAAM,qCAAqC,CAAC;IAOjD,MAAM,EAAE,GAAG,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG;IAEnF,uCAAuC;IACvC,IAAI,kBAAkB;QACpB,OAAO;IACT;IAEA,6CAA6C;IAC7C,IAAI,OAAO,MAAM,IAAI;QACnB,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,gBAAgB,UAAU,oBAAoB,QAAQ;QACxD,OAAO;IACT;IAEA,mCAAmC;IACnC,IAAI,sBAAsB,YAAY;QACpC,OAAO,wBAAwB,wBAAwB;;IACzD;IAEA,IAAI,sBAAsB,YAAY;QACpC,OAAO,8BAA8B,uBAAuB;;IAC9D;IAEA,+CAA+C;IAC/C,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/theme/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useThemeStore } from '@/lib/stores/theme-store'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface ThemeProviderProps {\n  children: React.ReactNode\n}\n\nexport default function ThemeProvider({ children }: ThemeProviderProps) {\n  const { \n    applyThemeToDOM, \n    setInterfaceMode,\n    autoThemeSwitch,\n    getRecommendedTheme,\n    setTheme,\n    currentThemeId\n  } = useThemeStore()\n  \n  const { interfaceMode, user } = useUserStore()\n\n  // Initialize theme system\n  useEffect(() => {\n    // Apply current theme to DOM\n    applyThemeToDOM()\n\n    // Sync interface mode with user store\n    setInterfaceMode(interfaceMode)\n\n    // Auto theme switching based on time\n    if (autoThemeSwitch) {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }\n\n    // Listen for system preference changes\n    const mediaQueries = [\n      window.matchMedia('(prefers-reduced-motion: reduce)'),\n      window.matchMedia('(prefers-contrast: high)'),\n      window.matchMedia('(prefers-color-scheme: dark)'),\n    ]\n\n    const handleSystemPreferenceChange = () => {\n      // Re-apply theme to respect system preferences\n      applyThemeToDOM()\n    }\n\n    mediaQueries.forEach(mq => {\n      mq.addEventListener('change', handleSystemPreferenceChange)\n    })\n\n    return () => {\n      mediaQueries.forEach(mq => {\n        mq.removeEventListener('change', handleSystemPreferenceChange)\n      })\n    }\n  }, [])\n\n  // Auto theme switching interval\n  useEffect(() => {\n    if (!autoThemeSwitch) return\n\n    const interval = setInterval(() => {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }, 60000) // Check every minute\n\n    return () => clearInterval(interval)\n  }, [autoThemeSwitch, currentThemeId, setTheme, getRecommendedTheme])\n\n  // Sync interface mode changes\n  useEffect(() => {\n    setInterfaceMode(interfaceMode)\n  }, [interfaceMode, setInterfaceMode])\n\n  // Apply theme changes to DOM\n  useEffect(() => {\n    applyThemeToDOM()\n  }, [currentThemeId, interfaceMode, applyThemeToDOM])\n\n  return <>{children}</>\n}\n\n// Hook for accessing theme colors in components\nexport function useThemeColors() {\n  const { colors } = useThemeStore()\n  return colors\n}\n\n// Hook for theme-aware styling\nexport function useThemeClasses() {\n  const { currentThemeId, interfaceMode } = useThemeStore()\n  \n  const getButtonClass = (variant: 'primary' | 'secondary' | 'success' | 'warning' | 'error' = 'primary') => {\n    const baseClass = 'px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2'\n    \n    const variantClasses = {\n      primary: 'bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] active:bg-[var(--color-primary-active)] text-white focus:ring-[var(--color-focus)]',\n      secondary: 'bg-[var(--color-secondary)] hover:bg-[var(--color-secondary-hover)] active:bg-[var(--color-secondary-active)] text-white focus:ring-[var(--color-focus)]',\n      success: 'bg-[var(--color-success)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',\n      warning: 'bg-[var(--color-warning)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',\n      error: 'bg-[var(--color-error)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',\n    }\n    \n    return `${baseClass} ${variantClasses[variant]}`\n  }\n  \n  const getCardClass = () => {\n    return 'bg-[var(--color-background-secondary)] border border-[var(--color-border)] rounded-lg p-4'\n  }\n  \n  const getTextClass = (variant: 'primary' | 'secondary' | 'muted' = 'primary') => {\n    const variantClasses = {\n      primary: 'text-[var(--color-text-primary)]',\n      secondary: 'text-[var(--color-text-secondary)]',\n      muted: 'text-[var(--color-text-muted)]',\n    }\n    \n    return variantClasses[variant]\n  }\n  \n  const getBullishClass = () => {\n    return 'text-[var(--color-bullish)] bg-[var(--color-bullish-background)]'\n  }\n  \n  const getBearishClass = () => {\n    return 'text-[var(--color-bearish)] bg-[var(--color-bearish-background)]'\n  }\n  \n  const getNeutralClass = () => {\n    return 'text-[var(--color-neutral)] bg-[var(--color-neutral-background)]'\n  }\n  \n  return {\n    getButtonClass,\n    getCardClass,\n    getTextClass,\n    getBullishClass,\n    getBearishClass,\n    getNeutralClass,\n    currentThemeId,\n    interfaceMode,\n  }\n}\n\n// Component for theme-aware market condition indicators\nexport function MarketConditionBadge({ \n  condition, \n  children, \n  className = '' \n}: { \n  condition: 'bullish' | 'bearish' | 'neutral'\n  children: React.ReactNode\n  className?: string \n}) {\n  const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses()\n  \n  const conditionClasses = {\n    bullish: getBullishClass(),\n    bearish: getBearishClass(),\n    neutral: getNeutralClass(),\n  }\n  \n  return (\n    <span className={`px-2 py-1 rounded text-sm font-medium ${conditionClasses[condition]} ${className}`}>\n      {children}\n    </span>\n  )\n}\n\n// Component for theme-aware trading buttons\nexport function TradingButton({ \n  action, \n  onClick, \n  children, \n  disabled = false,\n  className = '' \n}: { \n  action: 'buy' | 'sell' | 'neutral'\n  onClick: () => void\n  children: React.ReactNode\n  disabled?: boolean\n  className?: string \n}) {\n  const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses()\n  \n  const actionClasses = {\n    buy: getBullishClass(),\n    sell: getBearishClass(),\n    neutral: getNeutralClass(),\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      className={`px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[var(--color-focus)] disabled:opacity-50 disabled:cursor-not-allowed ${actionClasses[action]} ${className}`}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IACpE,MAAM,EACJ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,cAAc,EACf,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE3C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,6BAA6B;YAC7B;YAEA,sCAAsC;YACtC,iBAAiB;YAEjB,qCAAqC;YACrC,IAAI,iBAAiB;gBACnB,MAAM,mBAAmB;gBACzB,IAAI,qBAAqB,gBAAgB;oBACvC,SAAS;gBACX;YACF;YAEA,uCAAuC;YACvC,MAAM,eAAe;gBACnB,OAAO,UAAU,CAAC;gBAClB,OAAO,UAAU,CAAC;gBAClB,OAAO,UAAU,CAAC;aACnB;YAED,MAAM;wEAA+B;oBACnC,+CAA+C;oBAC/C;gBACF;;YAEA,aAAa,OAAO;2CAAC,CAAA;oBACnB,GAAG,gBAAgB,CAAC,UAAU;gBAChC;;YAEA;2CAAO;oBACL,aAAa,OAAO;mDAAC,CAAA;4BACnB,GAAG,mBAAmB,CAAC,UAAU;wBACnC;;gBACF;;QACF;kCAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,iBAAiB;YAEtB,MAAM,WAAW;oDAAY;oBAC3B,MAAM,mBAAmB;oBACzB,IAAI,qBAAqB,gBAAgB;wBACvC,SAAS;oBACX;gBACF;mDAAG,OAAO,qBAAqB;;YAE/B;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;QAAiB;QAAgB;QAAU;KAAoB;IAEnE,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,iBAAiB;QACnB;kCAAG;QAAC;QAAe;KAAiB;IAEpC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAgB;QAAe;KAAgB;IAEnD,qBAAO;kBAAG;;AACZ;GA5EwB;;QAQlB,yIAAA,CAAA,gBAAa;QAEe,wIAAA,CAAA,eAAY;;;KAVtB;AA+EjB,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAC/B,OAAO;AACT;IAHgB;;QACK,yIAAA,CAAA,gBAAa;;;AAK3B,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEtD,MAAM,iBAAiB,CAAC,UAAqE,SAAS;QACpG,MAAM,YAAY;QAElB,MAAM,iBAAiB;YACrB,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;QACT;QAEA,OAAO,GAAG,UAAU,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE;IAClD;IAEA,MAAM,eAAe;QACnB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC,UAA6C,SAAS;QAC1E,MAAM,iBAAiB;YACrB,SAAS;YACT,WAAW;YACX,OAAO;QACT;QAEA,OAAO,cAAc,CAAC,QAAQ;IAChC;IAEA,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IArDgB;;QAC4B,yIAAA,CAAA,gBAAa;;;AAuDlD,SAAS,qBAAqB,EACnC,SAAS,EACT,QAAQ,EACR,YAAY,EAAE,EAKf;;IACC,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG;IAE9D,MAAM,mBAAmB;QACvB,SAAS;QACT,SAAS;QACT,SAAS;IACX;IAEA,qBACE,6LAAC;QAAK,WAAW,CAAC,sCAAsC,EAAE,gBAAgB,CAAC,UAAU,CAAC,CAAC,EAAE,WAAW;kBACjG;;;;;;AAGP;IAtBgB;;QASgD;;;MAThD;AAyBT,SAAS,cAAc,EAC5B,MAAM,EACN,OAAO,EACP,QAAQ,EACR,WAAW,KAAK,EAChB,YAAY,EAAE,EAOf;;IACC,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG;IAE9D,MAAM,gBAAgB;QACpB,KAAK;QACL,MAAM;QACN,SAAS;IACX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,CAAC,2KAA2K,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW;kBAE5N;;;;;;AAGP;IA9BgB;;QAagD;;;MAbhD", "debugId": null}}]}