import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { UserProfile, Achievement, GameSession, ThemeConfig } from '@/types'
import { createClient } from '@/lib/supabase/client'

interface UserState {
  // User data
  user: UserProfile | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Theme and UI
  interfaceMode: 'adolescent' | 'adult'
  themeConfig: ThemeConfig
  
  // Game state
  currentGameSession: GameSession | null
  recentSessions: GameSession[]
  
  // Actions
  setUser: (user: UserProfile | null) => void
  setAuthenticated: (authenticated: boolean) => void
  setLoading: (loading: boolean) => void
  switchInterfaceMode: (mode: 'adolescent' | 'adult') => void
  syncWithThemeStore: () => void
  updateThemeConfig: (config: Partial<ThemeConfig>) => void
  addQuestCoins: (amount: number, source: string) => void
  spendQuestCoins: (amount: number, purpose: string) => boolean
  addExperience: (points: number) => void
  unlockAchievement: (achievement: Achievement) => void
  startGameSession: (session: GameSession) => void
  endGameSession: (session: GameSession) => void
  updateUserProfile: (updates: Partial<UserProfile>) => void
  clearUserData: () => void
  signOut: () => Promise<void>
  initializeAuth: () => Promise<void>
}

const defaultThemeConfig: ThemeConfig = {
  mode: 'adolescent',
  primary_color: '#8B5CF6',
  secondary_color: '#EC4899',
  background_style: 'fantasy',
  font_family: 'fantasy',
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      interfaceMode: 'adolescent',
      themeConfig: defaultThemeConfig,
      currentGameSession: null,
      recentSessions: [],

      // User management actions
      setUser: (user) => {
        set({ user, isAuthenticated: !!user })
        
        // Update interface mode based on user preference or age
        if (user) {
          const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult')
          get().switchInterfaceMode(mode)
        }
      },

      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),

      setLoading: (loading) => set({ isLoading: loading }),

      // Theme and UI actions
      switchInterfaceMode: (mode) => {
        const newThemeConfig: ThemeConfig = mode === 'adolescent' 
          ? {
              mode: 'adolescent',
              primary_color: '#8B5CF6',
              secondary_color: '#EC4899',
              background_style: 'fantasy',
              font_family: 'fantasy',
            }
          : {
              mode: 'adult',
              primary_color: '#1F2937',
              secondary_color: '#3B82F6',
              background_style: 'professional',
              font_family: 'monospace',
            }

        set({
          interfaceMode: mode,
          themeConfig: newThemeConfig
        })

        // Sync with theme store
        get().syncWithThemeStore()

        // Update user preference in database
        const { user } = get()
        if (user) {
          get().updateUserProfile({ interface_mode: mode })
        }
      },

      syncWithThemeStore: () => {
        // Import theme store dynamically to avoid circular dependency
        import('@/lib/stores/theme-store').then(({ useThemeStore }) => {
          const { setInterfaceMode } = useThemeStore.getState()
          setInterfaceMode(get().interfaceMode)
        }).catch(() => {
          // Theme store not available, ignore
        })
      },

      updateThemeConfig: (config) => {
        set((state) => ({
          themeConfig: { ...state.themeConfig, ...config }
        }))
      },

      // Quest coins management
      addQuestCoins: (amount, source) => {
        const { user } = get()
        if (!user) return

        const updatedUser = {
          ...user,
          total_quest_coins: user.total_quest_coins + amount
        }

        set({ user: updatedUser })

        // In a real app, you'd also update the database and create a transaction record
        console.log(`Added ${amount} QuestCoins from ${source}`)
      },

      spendQuestCoins: (amount, purpose) => {
        const { user } = get()
        if (!user || user.total_quest_coins < amount) {
          return false
        }

        const updatedUser = {
          ...user,
          total_quest_coins: user.total_quest_coins - amount
        }

        set({ user: updatedUser })

        // In a real app, you'd also update the database and create a transaction record
        console.log(`Spent ${amount} QuestCoins on ${purpose}`)
        return true
      },

      // Experience and leveling
      addExperience: (points) => {
        const { user } = get()
        if (!user) return

        const newExperience = user.experience_points + points
        const newLevel = calculateLevel(newExperience)
        const leveledUp = newLevel > user.level

        const updatedUser = {
          ...user,
          experience_points: newExperience,
          level: newLevel
        }

        set({ user: updatedUser })

        if (leveledUp) {
          console.log(`Level up! Now level ${newLevel}`)
          // In a real app, you'd trigger level up effects, notifications, etc.
        }
      },

      // Achievement system
      unlockAchievement: (achievement) => {
        const { user } = get()
        if (!user) return

        // Check if achievement is already unlocked
        const alreadyUnlocked = user.achievements.some(a => a.id === achievement.id)
        if (alreadyUnlocked) return

        const updatedUser = {
          ...user,
          achievements: [...user.achievements, { ...achievement, unlocked_at: new Date().toISOString() }]
        }

        set({ user: updatedUser })

        // Award quest coins for achievement
        get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`)

        console.log(`Achievement unlocked: ${achievement.name}`)
        // In a real app, you'd show a notification, play sound effects, etc.
      },

      // Game session management
      startGameSession: (session) => {
        set({ currentGameSession: session })
      },

      endGameSession: (session) => {
        const { recentSessions } = get()
        
        // Add to recent sessions (keep last 10)
        const updatedSessions = [session, ...recentSessions].slice(0, 10)
        
        set({ 
          currentGameSession: null,
          recentSessions: updatedSessions
        })

        // Award quest coins and experience
        get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`)
        get().addExperience(Math.floor(session.score / 10))

        // Check for achievements
        checkGameAchievements(session, get())
      },

      // Profile updates
      updateUserProfile: (updates) => {
        const { user } = get()
        if (!user) return

        const updatedUser = { ...user, ...updates }
        set({ user: updatedUser })

        // In a real app, you'd sync with the database
        console.log('User profile updated:', updates)
      },

      // Cleanup
      clearUserData: () => {
        set({
          user: null,
          isAuthenticated: false,
          currentGameSession: null,
          recentSessions: [],
          interfaceMode: 'adolescent',
          themeConfig: defaultThemeConfig,
        })
      },

      // Authentication methods
      signOut: async () => {
        const supabase = createClient()
        await supabase.auth.signOut()
        get().clearUserData()
      },

      initializeAuth: async () => {
        try {
          const supabase = createClient()

          // Get initial session
          const { data: { session }, error: sessionError } = await supabase.auth.getSession()

          if (sessionError) {
            console.warn('Auth session error (demo mode):', sessionError.message)
            // Set demo user for development
            get().setUser({
              id: 'demo-user-id',
              email: '<EMAIL>',
              username: 'DemoTrader',
              age: 25,
              is_minor: false,
              interface_mode: 'adolescent',
              avatar_url: null,
              total_quest_coins: 1000,
              level: 1,
              experience_points: 0,
              achievements: [],
              guild_id: null,
              preferred_language: 'en',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            })
            return
          }

          if (session?.user) {
            // Fetch user profile
            const { data: profile, error: profileError } = await supabase
              .from('user_profiles')
              .select('*')
              .eq('id', session.user.id)
              .single()

            if (profileError) {
              console.warn('Profile fetch error (demo mode):', profileError.message)
              return
            }

            if (profile) {
              // Fetch achievements
              const { data: achievements } = await supabase
                .from('user_achievements')
                .select(`
                  achievement_id,
                  unlocked_at,
                  achievements (
                    id,
                    name,
                    description,
                    icon,
                    category,
                    points
                  )
                `)
                .eq('user_id', session.user.id)

              const userAchievements = achievements?.map(ua => ({
                ...ua.achievements,
                unlocked_at: ua.unlocked_at
              })) || []

              get().setUser({
                id: profile.id,
                email: session.user.email!,
                username: profile.username,
                age: profile.age,
                is_minor: profile.is_minor,
                interface_mode: profile.interface_mode,
                avatar_url: profile.avatar_url,
                total_quest_coins: profile.total_quest_coins,
                level: profile.level,
                experience_points: profile.experience_points,
                achievements: userAchievements,
                guild_id: profile.guild_id,
                preferred_language: profile.preferred_language,
                created_at: profile.created_at,
                updated_at: profile.updated_at,
              })
            }
          }

          // Listen for auth changes
          supabase.auth.onAuthStateChange(async (event, session) => {
            if (event === 'SIGNED_OUT' || !session) {
              get().clearUserData()
            } else if (event === 'SIGNED_IN' && session?.user) {
              // Refresh user data when signed in
              get().initializeAuth()
            }
          })
        } catch (error) {
          console.error('Auth initialization error:', error)
          console.warn('Running in demo mode without authentication')

          // Set demo user for development
          get().setUser({
            id: 'demo-user-id',
            email: '<EMAIL>',
            username: 'DemoTrader',
            age: 25,
            is_minor: false,
            interface_mode: 'adolescent',
            avatar_url: null,
            total_quest_coins: 1000,
            level: 1,
            experience_points: 0,
            achievements: [],
            guild_id: null,
            preferred_language: 'en',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
        }
      },
    }),
    {
      name: 'tradequest-user-storage',
      partialize: (state) => ({
        user: state.user,
        interfaceMode: state.interfaceMode,
        themeConfig: state.themeConfig,
        recentSessions: state.recentSessions,
      }),
    }
  )
)

// Helper functions
function calculateLevel(experience: number): number {
  const LEVEL_THRESHOLDS = [
    0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,
    13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,
  ]

  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {
    if (experience >= LEVEL_THRESHOLDS[i]) {
      return i + 1
    }
  }
  return 1
}

function checkGameAchievements(session: GameSession, store: any) {
  const achievements: Achievement[] = []

  // First game achievement
  if (store.recentSessions.length === 0) {
    achievements.push({
      id: 'first_game',
      name: 'First Steps',
      description: 'Complete your first trading game',
      icon: '🎮',
      category: 'trading',
      points: 50,
    })
  }

  // High score achievements
  if (session.score >= 1000) {
    achievements.push({
      id: 'high_score_1000',
      name: 'Rising Trader',
      description: 'Score 1000+ points in a single game',
      icon: '📈',
      category: 'trading',
      points: 100,
    })
  }

  if (session.score >= 5000) {
    achievements.push({
      id: 'high_score_5000',
      name: 'Expert Trader',
      description: 'Score 5000+ points in a single game',
      icon: '🏆',
      category: 'trading',
      points: 250,
    })
  }

  // Game-specific achievements
  if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {
    achievements.push({
      id: 'speed_scalper',
      name: 'Lightning Fast',
      description: 'Complete Scalper Sprint in under 30 seconds',
      icon: '⚡',
      category: 'trading',
      points: 150,
    })
  }

  // Unlock achievements
  achievements.forEach(achievement => {
    store.unlockAchievement(achievement)
  })
}
