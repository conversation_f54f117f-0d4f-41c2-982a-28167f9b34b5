/**
 * Enhanced 5-Theme Color Psychology System Based on Color Theory Principles
 * 
 * Each theme follows established color theory principles:
 * 1. Monochromatic Blue - Single hue variations for focus
 * 2. Complementary Orange-Blue - Opposite colors for energy
 * 3. Triadic Green-Purple-Orange - Three equidistant colors for balance
 * 4. Analogous Warm Sunset - Adjacent warm colors for comfort
 * 5. High Contrast Accessible - Maximum accessibility compliance
 */

export interface ColorPalette {
  // Primary colors
  primary: string
  primaryHover: string
  primaryActive: string
  
  // Secondary colors
  secondary: string
  secondaryHover: string
  secondaryActive: string
  
  // Background colors
  background: string
  backgroundSecondary: string
  backgroundTertiary: string
  
  // Text colors
  textPrimary: string
  textSecondary: string
  textMuted: string
  
  // Market condition colors
  bullish: string
  bullishHover: string
  bullishBackground: string
  
  bearish: string
  bearishHover: string
  bearishBackground: string
  
  neutral: string
  neutralHover: string
  neutralBackground: string
  
  // Status colors
  success: string
  warning: string
  error: string
  info: string
  
  // Interactive elements
  border: string
  borderHover: string
  borderActive: string
  
  // Chart colors
  chartGrid: string
  chartAxis: string
  chartVolume: string
  
  // Accessibility
  focus: string
  disabled: string
}

export interface ThemeConfig {
  id: string
  name: string
  description: string
  colorTheory: string
  psychologyProfile: {
    stressReduction: number // 1-10 scale
    focusEnhancement: number // 1-10 scale
    cognitiveLoad: number // 1-10 scale (lower is better)
    accessibility: number // 1-10 scale
  }
  adolescent: ColorPalette
  adult: ColorPalette
}

// Theme 1: Monochromatic Blue (Professional Focus)
// Color Theory: Monochromatic scheme using various shades and tints of blue
// Psychology: Enhances concentration, reduces stress, promotes trust and stability
const monochromaticBlue: ThemeConfig = {
  id: 'monochromatic-blue',
  name: 'Monochromatic Blue',
  description: 'Professional blue monochromatic scheme for enhanced focus and concentration',
  colorTheory: 'Monochromatic - Single hue with varying saturation and lightness',
  psychologyProfile: {
    stressReduction: 9,
    focusEnhancement: 10,
    cognitiveLoad: 2,
    accessibility: 8,
  },
  adolescent: {
    primary: '#3B82F6', // Bright blue
    primaryHover: '#2563EB',
    primaryActive: '#1D4ED8',
    
    secondary: '#60A5FA', // Light blue
    secondaryHover: '#3B82F6',
    secondaryActive: '#2563EB',
    
    background: '#0F172A', // Very dark blue
    backgroundSecondary: '#1E293B',
    backgroundTertiary: '#334155',
    
    textPrimary: '#F8FAFC',
    textSecondary: '#CBD5E1',
    textMuted: '#94A3B8',
    
    bullish: '#10B981',
    bullishHover: '#059669',
    bullishBackground: 'rgba(16, 185, 129, 0.1)',
    
    bearish: '#EF4444',
    bearishHover: '#DC2626',
    bearishBackground: 'rgba(239, 68, 68, 0.1)',
    
    neutral: '#8B5CF6',
    neutralHover: '#7C3AED',
    neutralBackground: 'rgba(139, 92, 246, 0.1)',
    
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
    
    border: '#475569',
    borderHover: '#64748B',
    borderActive: '#94A3B8',
    
    chartGrid: 'rgba(59, 130, 246, 0.1)',
    chartAxis: 'rgba(59, 130, 246, 0.3)',
    chartVolume: 'rgba(96, 165, 250, 0.3)',
    
    focus: '#3B82F6',
    disabled: 'rgba(248, 250, 252, 0.3)',
  },
  adult: {
    primary: '#1E40AF', // Deep blue
    primaryHover: '#1D4ED8',
    primaryActive: '#2563EB',
    
    secondary: '#3730A3', // Darker blue
    secondaryHover: '#4338CA',
    secondaryActive: '#5B21B6',
    
    background: '#020617', // Very dark blue-black
    backgroundSecondary: '#0F172A',
    backgroundTertiary: '#1E293B',
    
    textPrimary: '#E2E8F0',
    textSecondary: '#94A3B8',
    textMuted: '#64748B',
    
    bullish: '#059669',
    bullishHover: '#047857',
    bullishBackground: 'rgba(5, 150, 105, 0.1)',
    
    bearish: '#DC2626',
    bearishHover: '#B91C1C',
    bearishBackground: 'rgba(220, 38, 38, 0.1)',
    
    neutral: '#6366F1',
    neutralHover: '#5B21B6',
    neutralBackground: 'rgba(99, 102, 241, 0.1)',
    
    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    info: '#1E40AF',
    
    border: '#334155',
    borderHover: '#475569',
    borderActive: '#64748B',
    
    chartGrid: 'rgba(30, 64, 175, 0.1)',
    chartAxis: 'rgba(30, 64, 175, 0.3)',
    chartVolume: 'rgba(99, 102, 241, 0.3)',
    
    focus: '#1E40AF',
    disabled: 'rgba(226, 232, 240, 0.3)',
  },
}

// Theme 2: Complementary Orange-Blue (High Energy & Focus)
// Color Theory: Complementary scheme using orange and blue opposites on color wheel
// Psychology: Creates visual excitement while maintaining focus, energizing yet balanced
const complementaryOrangeBlue: ThemeConfig = {
  id: 'complementary-orange-blue',
  name: 'Complementary Orange-Blue',
  description: 'High-energy complementary scheme balancing excitement with focus',
  colorTheory: 'Complementary - Opposite colors on the color wheel for maximum contrast',
  psychologyProfile: {
    stressReduction: 6,
    focusEnhancement: 9,
    cognitiveLoad: 4,
    accessibility: 7,
  },
  adolescent: {
    primary: '#F97316', // Vibrant orange
    primaryHover: '#EA580C',
    primaryActive: '#C2410C',
    
    secondary: '#2563EB', // Complementary blue
    secondaryHover: '#1D4ED8',
    secondaryActive: '#1E40AF',
    
    background: '#0C0A09', // Very dark brown
    backgroundSecondary: '#1C1917',
    backgroundTertiary: '#292524',
    
    textPrimary: '#FAFAF9',
    textSecondary: '#E7E5E4',
    textMuted: '#A8A29E',
    
    bullish: '#22C55E',
    bullishHover: '#16A34A',
    bullishBackground: 'rgba(34, 197, 94, 0.1)',
    
    bearish: '#EF4444',
    bearishHover: '#DC2626',
    bearishBackground: 'rgba(239, 68, 68, 0.1)',
    
    neutral: '#8B5CF6',
    neutralHover: '#7C3AED',
    neutralBackground: 'rgba(139, 92, 246, 0.1)',
    
    success: '#22C55E',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#2563EB',
    
    border: '#44403C',
    borderHover: '#57534E',
    borderActive: '#78716C',
    
    chartGrid: 'rgba(249, 115, 22, 0.1)',
    chartAxis: 'rgba(249, 115, 22, 0.3)',
    chartVolume: 'rgba(37, 99, 235, 0.3)',
    
    focus: '#F97316',
    disabled: 'rgba(250, 250, 249, 0.3)',
  },
  adult: {
    primary: '#EA580C', // Deep orange
    primaryHover: '#C2410C',
    primaryActive: '#9A3412',
    
    secondary: '#1E40AF', // Deep blue
    secondaryHover: '#1E3A8A',
    secondaryActive: '#1E3A8A',
    
    background: '#0A0A0A', // Near black
    backgroundSecondary: '#171717',
    backgroundTertiary: '#262626',
    
    textPrimary: '#F5F5F5',
    textSecondary: '#D4D4D4',
    textMuted: '#A3A3A3',
    
    bullish: '#16A34A',
    bullishHover: '#15803D',
    bullishBackground: 'rgba(22, 163, 74, 0.1)',
    
    bearish: '#DC2626',
    bearishHover: '#B91C1C',
    bearishBackground: 'rgba(220, 38, 38, 0.1)',
    
    neutral: '#7C3AED',
    neutralHover: '#6D28D9',
    neutralBackground: 'rgba(124, 58, 237, 0.1)',
    
    success: '#16A34A',
    warning: '#D97706',
    error: '#DC2626',
    info: '#1E40AF',
    
    border: '#404040',
    borderHover: '#525252',
    borderActive: '#737373',
    
    chartGrid: 'rgba(234, 88, 12, 0.1)',
    chartAxis: 'rgba(234, 88, 12, 0.3)',
    chartVolume: 'rgba(30, 64, 175, 0.3)',
    
    focus: '#EA580C',
    disabled: 'rgba(245, 245, 245, 0.3)',
  },
}

// Theme 3: Triadic Green-Purple-Orange (Balanced Harmony)
// Color Theory: Triadic scheme using three colors equally spaced on color wheel
// Psychology: Creates vibrant harmony while maintaining balance, reduces eye strain
const triadicGreenPurpleOrange: ThemeConfig = {
  id: 'triadic-green-purple-orange',
  name: 'Triadic Harmony',
  description: 'Balanced triadic scheme with green, purple, and orange for visual harmony',
  colorTheory: 'Triadic - Three colors equally spaced on the color wheel for vibrant balance',
  psychologyProfile: {
    stressReduction: 8,
    focusEnhancement: 7,
    cognitiveLoad: 3,
    accessibility: 8,
  },
  adolescent: {
    primary: '#10B981', // Emerald green
    primaryHover: '#059669',
    primaryActive: '#047857',

    secondary: '#8B5CF6', // Purple
    secondaryHover: '#7C3AED',
    secondaryActive: '#6D28D9',

    background: '#0F0F0F', // Very dark
    backgroundSecondary: '#1A1A1A',
    backgroundTertiary: '#2D2D2D',

    textPrimary: '#F0F0F0',
    textSecondary: '#D0D0D0',
    textMuted: '#A0A0A0',

    bullish: '#10B981',
    bullishHover: '#059669',
    bullishBackground: 'rgba(16, 185, 129, 0.1)',

    bearish: '#F97316', // Orange for bearish
    bearishHover: '#EA580C',
    bearishBackground: 'rgba(249, 115, 22, 0.1)',

    neutral: '#8B5CF6',
    neutralHover: '#7C3AED',
    neutralBackground: 'rgba(139, 92, 246, 0.1)',

    success: '#10B981',
    warning: '#F59E0B',
    error: '#F97316',
    info: '#8B5CF6',

    border: '#404040',
    borderHover: '#525252',
    borderActive: '#737373',

    chartGrid: 'rgba(16, 185, 129, 0.1)',
    chartAxis: 'rgba(16, 185, 129, 0.3)',
    chartVolume: 'rgba(139, 92, 246, 0.3)',

    focus: '#10B981',
    disabled: 'rgba(240, 240, 240, 0.3)',
  },
  adult: {
    primary: '#059669', // Deep green
    primaryHover: '#047857',
    primaryActive: '#065F46',

    secondary: '#7C3AED', // Deep purple
    secondaryHover: '#6D28D9',
    secondaryActive: '#5B21B6',

    background: '#000000', // Pure black
    backgroundSecondary: '#111111',
    backgroundTertiary: '#1F1F1F',

    textPrimary: '#E5E5E5',
    textSecondary: '#B5B5B5',
    textMuted: '#858585',

    bullish: '#059669',
    bullishHover: '#047857',
    bullishBackground: 'rgba(5, 150, 105, 0.1)',

    bearish: '#EA580C', // Deep orange
    bearishHover: '#C2410C',
    bearishBackground: 'rgba(234, 88, 12, 0.1)',

    neutral: '#7C3AED',
    neutralHover: '#6D28D9',
    neutralBackground: 'rgba(124, 58, 237, 0.1)',

    success: '#059669',
    warning: '#D97706',
    error: '#EA580C',
    info: '#7C3AED',

    border: '#333333',
    borderHover: '#444444',
    borderActive: '#666666',

    chartGrid: 'rgba(5, 150, 105, 0.1)',
    chartAxis: 'rgba(5, 150, 105, 0.3)',
    chartVolume: 'rgba(124, 58, 237, 0.3)',

    focus: '#059669',
    disabled: 'rgba(229, 229, 229, 0.3)',
  },
}

// Theme 4: Analogous Warm Sunset (Comfort & Warmth)
// Color Theory: Analogous scheme using adjacent warm colors (red, orange, yellow)
// Psychology: Creates warmth and comfort, reduces anxiety, promotes creativity
const analogousWarmSunset: ThemeConfig = {
  id: 'analogous-warm-sunset',
  name: 'Analogous Warm Sunset',
  description: 'Warm analogous scheme with sunset colors for comfort and creativity',
  colorTheory: 'Analogous - Adjacent colors on the color wheel for harmonious warmth',
  psychologyProfile: {
    stressReduction: 7,
    focusEnhancement: 6,
    cognitiveLoad: 3,
    accessibility: 7,
  },
  adolescent: {
    primary: '#F59E0B', // Amber
    primaryHover: '#D97706',
    primaryActive: '#B45309',

    secondary: '#EF4444', // Red
    secondaryHover: '#DC2626',
    secondaryActive: '#B91C1C',

    background: '#1A0F0A', // Very dark warm
    backgroundSecondary: '#2D1B14',
    backgroundTertiary: '#44281A',

    textPrimary: '#FEF3E2',
    textSecondary: '#FDE68A',
    textMuted: '#D69E2E',

    bullish: '#10B981',
    bullishHover: '#059669',
    bullishBackground: 'rgba(16, 185, 129, 0.1)',

    bearish: '#EF4444',
    bearishHover: '#DC2626',
    bearishBackground: 'rgba(239, 68, 68, 0.1)',

    neutral: '#F59E0B',
    neutralHover: '#D97706',
    neutralBackground: 'rgba(245, 158, 11, 0.1)',

    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',

    border: '#92400E',
    borderHover: '#B45309',
    borderActive: '#D97706',

    chartGrid: 'rgba(245, 158, 11, 0.1)',
    chartAxis: 'rgba(245, 158, 11, 0.3)',
    chartVolume: 'rgba(239, 68, 68, 0.3)',

    focus: '#F59E0B',
    disabled: 'rgba(254, 243, 226, 0.3)',
  },
  adult: {
    primary: '#D97706', // Deep amber
    primaryHover: '#B45309',
    primaryActive: '#92400E',

    secondary: '#DC2626', // Deep red
    secondaryHover: '#B91C1C',
    secondaryActive: '#991B1B',

    background: '#0F0A08', // Very dark warm
    backgroundSecondary: '#1C1410',
    backgroundTertiary: '#2C1F17',

    textPrimary: '#F7E6D3',
    textSecondary: '#E4C29F',
    textMuted: '#B8956B',

    bullish: '#059669',
    bullishHover: '#047857',
    bullishBackground: 'rgba(5, 150, 105, 0.1)',

    bearish: '#DC2626',
    bearishHover: '#B91C1C',
    bearishBackground: 'rgba(220, 38, 38, 0.1)',

    neutral: '#D97706',
    neutralHover: '#B45309',
    neutralBackground: 'rgba(217, 119, 6, 0.1)',

    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
    info: '#2563EB',

    border: '#78350F',
    borderHover: '#92400E',
    borderActive: '#B45309',

    chartGrid: 'rgba(217, 119, 6, 0.1)',
    chartAxis: 'rgba(217, 119, 6, 0.3)',
    chartVolume: 'rgba(220, 38, 38, 0.3)',

    focus: '#D97706',
    disabled: 'rgba(247, 230, 211, 0.3)',
  },
}

// Theme 5: High Contrast Accessible (Maximum Accessibility)
// Color Theory: High contrast monochromatic with accessibility focus
// Psychology: Reduces cognitive load, maximizes readability, supports visual impairments
const highContrastAccessible: ThemeConfig = {
  id: 'high-contrast-accessible',
  name: 'High Contrast Accessible',
  description: 'Maximum accessibility with WCAG AAA compliance for all users',
  colorTheory: 'High Contrast - Maximum contrast ratios for optimal accessibility',
  psychologyProfile: {
    stressReduction: 8,
    focusEnhancement: 10,
    cognitiveLoad: 1,
    accessibility: 10,
  },
  adolescent: {
    primary: '#0066CC', // High contrast blue
    primaryHover: '#0052A3',
    primaryActive: '#003D7A',

    secondary: '#FF6600', // High contrast orange
    secondaryHover: '#E55A00',
    secondaryActive: '#CC4E00',

    background: '#FFFFFF', // Pure white
    backgroundSecondary: '#F8F9FA',
    backgroundTertiary: '#E9ECEF',

    textPrimary: '#000000', // Pure black
    textSecondary: '#212529',
    textMuted: '#495057',

    bullish: '#008000', // Pure green
    bullishHover: '#006600',
    bullishBackground: 'rgba(0, 128, 0, 0.1)',

    bearish: '#CC0000', // Pure red
    bearishHover: '#990000',
    bearishBackground: 'rgba(204, 0, 0, 0.1)',

    neutral: '#000080', // Navy blue
    neutralHover: '#000066',
    neutralBackground: 'rgba(0, 0, 128, 0.1)',

    success: '#008000',
    warning: '#FF8C00',
    error: '#CC0000',
    info: '#0066CC',

    border: '#000000',
    borderHover: '#333333',
    borderActive: '#666666',

    chartGrid: 'rgba(0, 0, 0, 0.2)',
    chartAxis: 'rgba(0, 0, 0, 0.5)',
    chartVolume: 'rgba(0, 102, 204, 0.3)',

    focus: '#FF6600',
    disabled: 'rgba(0, 0, 0, 0.3)',
  },
  adult: {
    primary: '#FFFFFF', // White on black
    primaryHover: '#E0E0E0',
    primaryActive: '#C0C0C0',

    secondary: '#FFFF00', // High contrast yellow
    secondaryHover: '#E6E600',
    secondaryActive: '#CCCC00',

    background: '#000000', // Pure black
    backgroundSecondary: '#1A1A1A',
    backgroundTertiary: '#333333',

    textPrimary: '#FFFFFF',
    textSecondary: '#E0E0E0',
    textMuted: '#B0B0B0',

    bullish: '#00FF00', // Bright green
    bullishHover: '#00E600',
    bullishBackground: 'rgba(0, 255, 0, 0.1)',

    bearish: '#FF0000', // Bright red
    bearishHover: '#E60000',
    bearishBackground: 'rgba(255, 0, 0, 0.1)',

    neutral: '#00FFFF', // Cyan
    neutralHover: '#00E6E6',
    neutralBackground: 'rgba(0, 255, 255, 0.1)',

    success: '#00FF00',
    warning: '#FFFF00',
    error: '#FF0000',
    info: '#00FFFF',

    border: '#FFFFFF',
    borderHover: '#E0E0E0',
    borderActive: '#C0C0C0',

    chartGrid: 'rgba(255, 255, 255, 0.2)',
    chartAxis: 'rgba(255, 255, 255, 0.5)',
    chartVolume: 'rgba(0, 255, 255, 0.3)',

    focus: '#FFFF00',
    disabled: 'rgba(255, 255, 255, 0.3)',
  },
}

// Export all themes
export const enhancedThemes: ThemeConfig[] = [
  monochromaticBlue,
  complementaryOrangeBlue,
  triadicGreenPurpleOrange,
  analogousWarmSunset,
  highContrastAccessible,
]

// Theme utility functions
export const getEnhancedThemeById = (id: string): ThemeConfig | undefined => {
  return enhancedThemes.find(theme => theme.id === id)
}

export const getEnhancedThemeColors = (themeId: string, mode: 'adolescent' | 'adult'): ColorPalette => {
  const theme = getEnhancedThemeById(themeId) || enhancedThemes[0] // Default to first theme
  return theme[mode]
}

// CSS custom properties generator
export const generateEnhancedCSSVariables = (colors: ColorPalette): Record<string, string> => {
  return {
    '--color-primary': colors.primary,
    '--color-primary-hover': colors.primaryHover,
    '--color-primary-active': colors.primaryActive,
    '--color-secondary': colors.secondary,
    '--color-secondary-hover': colors.secondaryHover,
    '--color-secondary-active': colors.secondaryActive,
    '--color-background': colors.background,
    '--color-background-secondary': colors.backgroundSecondary,
    '--color-background-tertiary': colors.backgroundTertiary,
    '--color-text-primary': colors.textPrimary,
    '--color-text-secondary': colors.textSecondary,
    '--color-text-muted': colors.textMuted,
    '--color-bullish': colors.bullish,
    '--color-bullish-hover': colors.bullishHover,
    '--color-bullish-background': colors.bullishBackground,
    '--color-bearish': colors.bearish,
    '--color-bearish-hover': colors.bearishHover,
    '--color-bearish-background': colors.bearishBackground,
    '--color-neutral': colors.neutral,
    '--color-neutral-hover': colors.neutralHover,
    '--color-neutral-background': colors.neutralBackground,
    '--color-success': colors.success,
    '--color-warning': colors.warning,
    '--color-error': colors.error,
    '--color-info': colors.info,
    '--color-border': colors.border,
    '--color-border-hover': colors.borderHover,
    '--color-border-active': colors.borderActive,
    '--color-chart-grid': colors.chartGrid,
    '--color-chart-axis': colors.chartAxis,
    '--color-chart-volume': colors.chartVolume,
    '--color-focus': colors.focus,
    '--color-disabled': colors.disabled,
  }
}
