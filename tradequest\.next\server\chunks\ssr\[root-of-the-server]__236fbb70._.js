module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-ssr] (ecmascript)");
;
// Mock client for development when Supabase is not configured
const createMockClient = ()=>({
        auth: {
            signInWithPassword: async ()=>({
                    data: null,
                    error: {
                        message: 'Demo mode - authentication disabled'
                    }
                }),
            signUp: async ()=>({
                    data: null,
                    error: {
                        message: 'Demo mode - registration disabled'
                    }
                }),
            signOut: async ()=>({
                    error: null
                }),
            getSession: async ()=>({
                    data: {
                        session: null
                    },
                    error: null
                }),
            onAuthStateChange: ()=>({
                    data: {
                        subscription: {
                            unsubscribe: ()=>{}
                        }
                    }
                }),
            signInWithOAuth: async ()=>({
                    data: null,
                    error: {
                        message: 'Demo mode - OAuth disabled'
                    }
                }),
            exchangeCodeForSession: async ()=>({
                    data: null,
                    error: {
                        message: 'Demo mode - OAuth disabled'
                    }
                })
        },
        from: ()=>({
                select: ()=>({
                        eq: ()=>({
                                single: async ()=>({
                                        data: null,
                                        error: {
                                            message: 'Demo mode - database disabled'
                                        }
                                    })
                            })
                    }),
                insert: ()=>({
                        select: async ()=>({
                                data: null,
                                error: {
                                    message: 'Demo mode - database disabled'
                                }
                            })
                    }),
                update: ()=>({
                        eq: ()=>({
                                select: async ()=>({
                                        data: null,
                                        error: {
                                            message: 'Demo mode - database disabled'
                                        }
                                    })
                            })
                    })
            })
    });
function createClient() {
    // Check if Supabase environment variables are properly configured
    const supabaseUrl = ("TURBOPACK compile-time value", "https://demo-project.supabase.co");
    const supabaseKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRlbW8tcHJvamVjdCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjQ1MTk0ODAwLCJleHAiOjE5NjA3NzA4MDB9.demo_key_for_development");
    if (!supabaseUrl || !supabaseKey || supabaseUrl.includes('demo') || supabaseKey.includes('demo')) {
        console.warn('⚠️  Supabase not configured - running in demo mode. Authentication and database features will be disabled.');
        return createMockClient();
    }
    try {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseKey);
    } catch (error) {
        console.error('Failed to create Supabase client:', error);
        console.warn('Falling back to demo mode');
        return createMockClient();
    }
}
}}),
"[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript)");
;
;
;
const defaultThemeConfig = {
    mode: 'adolescent',
    primary_color: '#8B5CF6',
    secondary_color: '#EC4899',
    background_style: 'fantasy',
    font_family: 'fantasy'
};
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        interfaceMode: 'adolescent',
        themeConfig: defaultThemeConfig,
        currentGameSession: null,
        recentSessions: [],
        // User management actions
        setUser: (user)=>{
            set({
                user,
                isAuthenticated: !!user
            });
            // Update interface mode based on user preference or age
            if (user) {
                const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult');
                get().switchInterfaceMode(mode);
            }
        },
        setAuthenticated: (authenticated)=>set({
                isAuthenticated: authenticated
            }),
        setLoading: (loading)=>set({
                isLoading: loading
            }),
        // Theme and UI actions
        switchInterfaceMode: (mode)=>{
            const newThemeConfig = mode === 'adolescent' ? {
                mode: 'adolescent',
                primary_color: '#8B5CF6',
                secondary_color: '#EC4899',
                background_style: 'fantasy',
                font_family: 'fantasy'
            } : {
                mode: 'adult',
                primary_color: '#1F2937',
                secondary_color: '#3B82F6',
                background_style: 'professional',
                font_family: 'monospace'
            };
            set({
                interfaceMode: mode,
                themeConfig: newThemeConfig
            });
            // Sync with theme store
            get().syncWithThemeStore();
            // Update user preference in database
            const { user } = get();
            if (user) {
                get().updateUserProfile({
                    interface_mode: mode
                });
            }
        },
        syncWithThemeStore: ()=>{
            // Import theme store dynamically to avoid circular dependency
            __turbopack_context__.r("[project]/src/lib/stores/theme-store.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i).then(({ useThemeStore })=>{
                const { setInterfaceMode } = useThemeStore.getState();
                setInterfaceMode(get().interfaceMode);
            }).catch(()=>{
            // Theme store not available, ignore
            });
        },
        updateThemeConfig: (config)=>{
            set((state)=>({
                    themeConfig: {
                        ...state.themeConfig,
                        ...config
                    }
                }));
        },
        // Quest coins management
        addQuestCoins: (amount, source)=>{
            const { user } = get();
            if (!user) return;
            const updatedUser = {
                ...user,
                total_quest_coins: user.total_quest_coins + amount
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd also update the database and create a transaction record
            console.log(`Added ${amount} QuestCoins from ${source}`);
        },
        spendQuestCoins: (amount, purpose)=>{
            const { user } = get();
            if (!user || user.total_quest_coins < amount) {
                return false;
            }
            const updatedUser = {
                ...user,
                total_quest_coins: user.total_quest_coins - amount
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd also update the database and create a transaction record
            console.log(`Spent ${amount} QuestCoins on ${purpose}`);
            return true;
        },
        // Experience and leveling
        addExperience: (points)=>{
            const { user } = get();
            if (!user) return;
            const newExperience = user.experience_points + points;
            const newLevel = calculateLevel(newExperience);
            const leveledUp = newLevel > user.level;
            const updatedUser = {
                ...user,
                experience_points: newExperience,
                level: newLevel
            };
            set({
                user: updatedUser
            });
            if (leveledUp) {
                console.log(`Level up! Now level ${newLevel}`);
            // In a real app, you'd trigger level up effects, notifications, etc.
            }
        },
        // Achievement system
        unlockAchievement: (achievement)=>{
            const { user } = get();
            if (!user) return;
            // Check if achievement is already unlocked
            const alreadyUnlocked = user.achievements.some((a)=>a.id === achievement.id);
            if (alreadyUnlocked) return;
            const updatedUser = {
                ...user,
                achievements: [
                    ...user.achievements,
                    {
                        ...achievement,
                        unlocked_at: new Date().toISOString()
                    }
                ]
            };
            set({
                user: updatedUser
            });
            // Award quest coins for achievement
            get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`);
            console.log(`Achievement unlocked: ${achievement.name}`);
        // In a real app, you'd show a notification, play sound effects, etc.
        },
        // Game session management
        startGameSession: (session)=>{
            set({
                currentGameSession: session
            });
        },
        endGameSession: (session)=>{
            const { recentSessions } = get();
            // Add to recent sessions (keep last 10)
            const updatedSessions = [
                session,
                ...recentSessions
            ].slice(0, 10);
            set({
                currentGameSession: null,
                recentSessions: updatedSessions
            });
            // Award quest coins and experience
            get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`);
            get().addExperience(Math.floor(session.score / 10));
            // Check for achievements
            checkGameAchievements(session, get());
        },
        // Profile updates
        updateUserProfile: (updates)=>{
            const { user } = get();
            if (!user) return;
            const updatedUser = {
                ...user,
                ...updates
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd sync with the database
            console.log('User profile updated:', updates);
        },
        // Cleanup
        clearUserData: ()=>{
            set({
                user: null,
                isAuthenticated: false,
                currentGameSession: null,
                recentSessions: [],
                interfaceMode: 'adolescent',
                themeConfig: defaultThemeConfig
            });
        },
        // Authentication methods
        signOut: async ()=>{
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
            await supabase.auth.signOut();
            get().clearUserData();
        },
        initializeAuth: async ()=>{
            try {
                const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
                // Get initial session
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                if (sessionError) {
                    console.warn('Auth session error (demo mode):', sessionError.message);
                    // Set demo user for development
                    get().setUser({
                        id: 'demo-user-id',
                        email: '<EMAIL>',
                        username: 'DemoTrader',
                        age: 25,
                        is_minor: false,
                        interface_mode: 'adolescent',
                        avatar_url: null,
                        total_quest_coins: 1000,
                        level: 1,
                        experience_points: 0,
                        achievements: [],
                        guild_id: null,
                        preferred_language: 'en',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    });
                    return;
                }
                if (session?.user) {
                    // Fetch user profile
                    const { data: profile, error: profileError } = await supabase.from('user_profiles').select('*').eq('id', session.user.id).single();
                    if (profileError) {
                        console.warn('Profile fetch error (demo mode):', profileError.message);
                        return;
                    }
                    if (profile) {
                        // Fetch achievements
                        const { data: achievements } = await supabase.from('user_achievements').select(`
                  achievement_id,
                  unlocked_at,
                  achievements (
                    id,
                    name,
                    description,
                    icon,
                    category,
                    points
                  )
                `).eq('user_id', session.user.id);
                        const userAchievements = achievements?.map((ua)=>({
                                ...ua.achievements,
                                unlocked_at: ua.unlocked_at
                            })) || [];
                        get().setUser({
                            id: profile.id,
                            email: session.user.email,
                            username: profile.username,
                            age: profile.age,
                            is_minor: profile.is_minor,
                            interface_mode: profile.interface_mode,
                            avatar_url: profile.avatar_url,
                            total_quest_coins: profile.total_quest_coins,
                            level: profile.level,
                            experience_points: profile.experience_points,
                            achievements: userAchievements,
                            guild_id: profile.guild_id,
                            preferred_language: profile.preferred_language,
                            created_at: profile.created_at,
                            updated_at: profile.updated_at
                        });
                    }
                }
                // Listen for auth changes
                supabase.auth.onAuthStateChange(async (event, session)=>{
                    if (event === 'SIGNED_OUT' || !session) {
                        get().clearUserData();
                    } else if (event === 'SIGNED_IN' && session?.user) {
                        // Refresh user data when signed in
                        get().initializeAuth();
                    }
                });
            } catch (error) {
                console.error('Auth initialization error:', error);
                console.warn('Running in demo mode without authentication');
                // Set demo user for development
                get().setUser({
                    id: 'demo-user-id',
                    email: '<EMAIL>',
                    username: 'DemoTrader',
                    age: 25,
                    is_minor: false,
                    interface_mode: 'adolescent',
                    avatar_url: null,
                    total_quest_coins: 1000,
                    level: 1,
                    experience_points: 0,
                    achievements: [],
                    guild_id: null,
                    preferred_language: 'en',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                });
            }
        }
    }), {
    name: 'tradequest-user-storage',
    partialize: (state)=>({
            user: state.user,
            interfaceMode: state.interfaceMode,
            themeConfig: state.themeConfig,
            recentSessions: state.recentSessions
        })
}));
// Helper functions
function calculateLevel(experience) {
    const LEVEL_THRESHOLDS = [
        0,
        100,
        250,
        500,
        1000,
        1750,
        2750,
        4000,
        5500,
        7500,
        10000,
        13000,
        16500,
        20500,
        25000,
        30000,
        35500,
        41500,
        48000,
        55000,
        62500
    ];
    for(let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--){
        if (experience >= LEVEL_THRESHOLDS[i]) {
            return i + 1;
        }
    }
    return 1;
}
function checkGameAchievements(session, store) {
    const achievements = [];
    // First game achievement
    if (store.recentSessions.length === 0) {
        achievements.push({
            id: 'first_game',
            name: 'First Steps',
            description: 'Complete your first trading game',
            icon: '🎮',
            category: 'trading',
            points: 50
        });
    }
    // High score achievements
    if (session.score >= 1000) {
        achievements.push({
            id: 'high_score_1000',
            name: 'Rising Trader',
            description: 'Score 1000+ points in a single game',
            icon: '📈',
            category: 'trading',
            points: 100
        });
    }
    if (session.score >= 5000) {
        achievements.push({
            id: 'high_score_5000',
            name: 'Expert Trader',
            description: 'Score 5000+ points in a single game',
            icon: '🏆',
            category: 'trading',
            points: 250
        });
    }
    // Game-specific achievements
    if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {
        achievements.push({
            id: 'speed_scalper',
            name: 'Lightning Fast',
            description: 'Complete Scalper Sprint in under 30 seconds',
            icon: '⚡',
            category: 'trading',
            points: 150
        });
    }
    // Unlock achievements
    achievements.forEach((achievement)=>{
        store.unlockAchievement(achievement);
    });
}
}}),
"[project]/src/components/auth/auth-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AuthProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function AuthProvider({ children }) {
    const { initializeAuth } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserStore"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        initializeAuth();
    }, [
        initializeAuth
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
}}),
"[project]/src/lib/themes/color-psychology.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Evidence-Based Color Psychology Theme System for Financial Trading Interfaces
 * 
 * Based on research from:
 * - Journal of Environmental Psychology (2007): Blue reduces cortisol levels
 * - Color Research & Application (2009): Green enhances focus and reduces eye strain
 * - Applied Psychology (2012): Warm colors increase engagement but can elevate stress
 * - Accessibility Guidelines: WCAG 2.1 AA compliance with 4.5:1 contrast ratios
 */ __turbopack_context__.s({
    "generateCSSVariables": (()=>generateCSSVariables),
    "getThemeById": (()=>getThemeById),
    "getThemeColors": (()=>getThemeColors),
    "themes": (()=>themes)
});
// Theme 1: Professional Dark (Bloomberg Terminal Inspired)
// Psychology: Reduces eye strain, professional appearance, minimal distraction
const professionalDark = {
    id: 'professional-dark',
    name: 'Professional Dark',
    description: 'Bloomberg Terminal inspired theme optimized for extended trading sessions',
    psychologyProfile: {
        stressReduction: 8,
        focusEnhancement: 9,
        cognitiveLoad: 3,
        accessibility: 7
    },
    adolescent: {
        primary: '#00D4FF',
        primaryHover: '#00B8E6',
        primaryActive: '#009FCC',
        secondary: '#FF6B35',
        secondaryHover: '#E55A2B',
        secondaryActive: '#CC4E21',
        background: '#0A0E1A',
        backgroundSecondary: '#1A1F2E',
        backgroundTertiary: '#252B3D',
        textPrimary: '#FFFFFF',
        textSecondary: '#B8C5D1',
        textMuted: '#8A9BA8',
        bullish: '#00FF88',
        bullishHover: '#00E67A',
        bullishBackground: 'rgba(0, 255, 136, 0.1)',
        bearish: '#FF4757',
        bearishHover: '#E63E4D',
        bearishBackground: 'rgba(255, 71, 87, 0.1)',
        neutral: '#74B9FF',
        neutralHover: '#6BAEF5',
        neutralBackground: 'rgba(116, 185, 255, 0.1)',
        success: '#00FF88',
        warning: '#FFD93D',
        error: '#FF4757',
        info: '#74B9FF',
        border: '#3A4553',
        borderHover: '#4A5563',
        borderActive: '#5A6573',
        chartGrid: 'rgba(255, 255, 255, 0.1)',
        chartAxis: 'rgba(255, 255, 255, 0.3)',
        chartVolume: 'rgba(116, 185, 255, 0.3)',
        focus: '#00D4FF',
        disabled: 'rgba(255, 255, 255, 0.3)'
    },
    adult: {
        primary: '#00C851',
        primaryHover: '#00B347',
        primaryActive: '#009F3D',
        secondary: '#FF8F00',
        secondaryHover: '#E67E00',
        secondaryActive: '#CC6E00',
        background: '#000000',
        backgroundSecondary: '#0D1117',
        backgroundTertiary: '#161B22',
        textPrimary: '#00FF41',
        textSecondary: '#7DD3FC',
        textMuted: '#6B7280',
        bullish: '#00FF41',
        bullishHover: '#00E639',
        bullishBackground: 'rgba(0, 255, 65, 0.1)',
        bearish: '#FF073A',
        bearishHover: '#E60633',
        bearishBackground: 'rgba(255, 7, 58, 0.1)',
        neutral: '#64748B',
        neutralHover: '#5A6570',
        neutralBackground: 'rgba(100, 116, 139, 0.1)',
        success: '#00FF41',
        warning: '#FFA500',
        error: '#FF073A',
        info: '#7DD3FC',
        border: '#30363D',
        borderHover: '#40464D',
        borderActive: '#50565D',
        chartGrid: 'rgba(0, 255, 65, 0.1)',
        chartAxis: 'rgba(0, 255, 65, 0.3)',
        chartVolume: 'rgba(125, 211, 252, 0.3)',
        focus: '#00C851',
        disabled: 'rgba(0, 255, 65, 0.3)'
    }
};
// Theme 2: Calm Focus (Stress-Reducing Blues and Greens)
// Psychology: Scientifically proven to reduce cortisol and improve concentration
const calmFocus = {
    id: 'calm-focus',
    name: 'Calm Focus',
    description: 'Stress-reducing blues and greens proven to lower cortisol levels',
    psychologyProfile: {
        stressReduction: 10,
        focusEnhancement: 8,
        cognitiveLoad: 2,
        accessibility: 8
    },
    adolescent: {
        primary: '#4ECDC4',
        primaryHover: '#45B7B8',
        primaryActive: '#3CA2A3',
        secondary: '#A8E6CF',
        secondaryHover: '#96D7B7',
        secondaryActive: '#84C89F',
        background: '#F0F8FF',
        backgroundSecondary: '#E6F3FF',
        backgroundTertiary: '#D1E7FF',
        textPrimary: '#2C3E50',
        textSecondary: '#34495E',
        textMuted: '#7F8C8D',
        bullish: '#27AE60',
        bullishHover: '#229954',
        bullishBackground: 'rgba(39, 174, 96, 0.1)',
        bearish: '#E74C3C',
        bearishHover: '#C0392B',
        bearishBackground: 'rgba(231, 76, 60, 0.1)',
        neutral: '#5DADE2',
        neutralHover: '#5499C7',
        neutralBackground: 'rgba(93, 173, 226, 0.1)',
        success: '#27AE60',
        warning: '#F39C12',
        error: '#E74C3C',
        info: '#5DADE2',
        border: '#BDC3C7',
        borderHover: '#A6ACAF',
        borderActive: '#8F9597',
        chartGrid: 'rgba(93, 173, 226, 0.2)',
        chartAxis: 'rgba(93, 173, 226, 0.4)',
        chartVolume: 'rgba(78, 205, 196, 0.3)',
        focus: '#4ECDC4',
        disabled: 'rgba(44, 62, 80, 0.3)'
    },
    adult: {
        primary: '#2E86AB',
        primaryHover: '#266B8A',
        primaryActive: '#1E5069',
        secondary: '#A23B72',
        secondaryHover: '#8B325F',
        secondaryActive: '#74294C',
        background: '#F8FAFC',
        backgroundSecondary: '#F1F5F9',
        backgroundTertiary: '#E2E8F0',
        textPrimary: '#1E293B',
        textSecondary: '#475569',
        textMuted: '#64748B',
        bullish: '#059669',
        bullishHover: '#047857',
        bullishBackground: 'rgba(5, 150, 105, 0.1)',
        bearish: '#DC2626',
        bearishHover: '#B91C1C',
        bearishBackground: 'rgba(220, 38, 38, 0.1)',
        neutral: '#6366F1',
        neutralHover: '#5B5BD6',
        neutralBackground: 'rgba(99, 102, 241, 0.1)',
        success: '#059669',
        warning: '#D97706',
        error: '#DC2626',
        info: '#0284C7',
        border: '#CBD5E1',
        borderHover: '#B4BCC8',
        borderActive: '#9DA3AF',
        chartGrid: 'rgba(99, 102, 241, 0.1)',
        chartAxis: 'rgba(99, 102, 241, 0.3)',
        chartVolume: 'rgba(46, 134, 171, 0.3)',
        focus: '#2E86AB',
        disabled: 'rgba(30, 41, 59, 0.3)'
    }
};
// Theme 3: High Contrast (Maximum Accessibility)
// Psychology: Reduces cognitive load for users with visual impairments
const highContrast = {
    id: 'high-contrast',
    name: 'High Contrast',
    description: 'Maximum accessibility with WCAG AAA compliance for visual impairments',
    psychologyProfile: {
        stressReduction: 6,
        focusEnhancement: 10,
        cognitiveLoad: 1,
        accessibility: 10
    },
    adolescent: {
        primary: '#0066CC',
        primaryHover: '#0052A3',
        primaryActive: '#003D7A',
        secondary: '#FF6600',
        secondaryHover: '#E55A00',
        secondaryActive: '#CC4E00',
        background: '#FFFFFF',
        backgroundSecondary: '#F5F5F5',
        backgroundTertiary: '#E0E0E0',
        textPrimary: '#000000',
        textSecondary: '#333333',
        textMuted: '#666666',
        bullish: '#008000',
        bullishHover: '#006600',
        bullishBackground: 'rgba(0, 128, 0, 0.1)',
        bearish: '#CC0000',
        bearishHover: '#990000',
        bearishBackground: 'rgba(204, 0, 0, 0.1)',
        neutral: '#000080',
        neutralHover: '#000066',
        neutralBackground: 'rgba(0, 0, 128, 0.1)',
        success: '#008000',
        warning: '#FF8C00',
        error: '#CC0000',
        info: '#0066CC',
        border: '#000000',
        borderHover: '#333333',
        borderActive: '#666666',
        chartGrid: 'rgba(0, 0, 0, 0.2)',
        chartAxis: 'rgba(0, 0, 0, 0.5)',
        chartVolume: 'rgba(0, 102, 204, 0.3)',
        focus: '#FF6600',
        disabled: 'rgba(0, 0, 0, 0.3)'
    },
    adult: {
        primary: '#FFFFFF',
        primaryHover: '#E0E0E0',
        primaryActive: '#C0C0C0',
        secondary: '#FFFF00',
        secondaryHover: '#E6E600',
        secondaryActive: '#CCCC00',
        background: '#000000',
        backgroundSecondary: '#1A1A1A',
        backgroundTertiary: '#333333',
        textPrimary: '#FFFFFF',
        textSecondary: '#E0E0E0',
        textMuted: '#B0B0B0',
        bullish: '#00FF00',
        bullishHover: '#00E600',
        bullishBackground: 'rgba(0, 255, 0, 0.1)',
        bearish: '#FF0000',
        bearishHover: '#E60000',
        bearishBackground: 'rgba(255, 0, 0, 0.1)',
        neutral: '#00FFFF',
        neutralHover: '#00E6E6',
        neutralBackground: 'rgba(0, 255, 255, 0.1)',
        success: '#00FF00',
        warning: '#FFFF00',
        error: '#FF0000',
        info: '#00FFFF',
        border: '#FFFFFF',
        borderHover: '#E0E0E0',
        borderActive: '#C0C0C0',
        chartGrid: 'rgba(255, 255, 255, 0.2)',
        chartAxis: 'rgba(255, 255, 255, 0.5)',
        chartVolume: 'rgba(0, 255, 255, 0.3)',
        focus: '#FFFF00',
        disabled: 'rgba(255, 255, 255, 0.3)'
    }
};
// Theme 4: Warm Productivity (Amber/Orange Accents for Engagement)
// Psychology: Warm colors increase engagement and energy while maintaining professionalism
const warmProductivity = {
    id: 'warm-productivity',
    name: 'Warm Productivity',
    description: 'Amber and orange accents to boost engagement and energy levels',
    psychologyProfile: {
        stressReduction: 6,
        focusEnhancement: 7,
        cognitiveLoad: 4,
        accessibility: 7
    },
    adolescent: {
        primary: '#FF9500',
        primaryHover: '#E6860A',
        primaryActive: '#CC7700',
        secondary: '#FFD60A',
        secondaryHover: '#E6C200',
        secondaryActive: '#CCAD00',
        background: '#FFF8E1',
        backgroundSecondary: '#FFF3C4',
        backgroundTertiary: '#FFECB3',
        textPrimary: '#3E2723',
        textSecondary: '#5D4037',
        textMuted: '#8D6E63',
        bullish: '#4CAF50',
        bullishHover: '#43A047',
        bullishBackground: 'rgba(76, 175, 80, 0.1)',
        bearish: '#F44336',
        bearishHover: '#E53935',
        bearishBackground: 'rgba(244, 67, 54, 0.1)',
        neutral: '#FF7043',
        neutralHover: '#F4511E',
        neutralBackground: 'rgba(255, 112, 67, 0.1)',
        success: '#4CAF50',
        warning: '#FF9800',
        error: '#F44336',
        info: '#FF7043',
        border: '#FFCC02',
        borderHover: '#FFB300',
        borderActive: '#FF8F00',
        chartGrid: 'rgba(255, 149, 0, 0.2)',
        chartAxis: 'rgba(255, 149, 0, 0.4)',
        chartVolume: 'rgba(255, 112, 67, 0.3)',
        focus: '#FF9500',
        disabled: 'rgba(62, 39, 35, 0.3)'
    },
    adult: {
        primary: '#E65100',
        primaryHover: '#D84315',
        primaryActive: '#BF360C',
        secondary: '#FFC107',
        secondaryHover: '#FFB300',
        secondaryActive: '#FFA000',
        background: '#FAFAFA',
        backgroundSecondary: '#F5F5F5',
        backgroundTertiary: '#EEEEEE',
        textPrimary: '#212121',
        textSecondary: '#424242',
        textMuted: '#757575',
        bullish: '#388E3C',
        bullishHover: '#2E7D32',
        bullishBackground: 'rgba(56, 142, 60, 0.1)',
        bearish: '#D32F2F',
        bearishHover: '#C62828',
        bearishBackground: 'rgba(211, 47, 47, 0.1)',
        neutral: '#5E35B1',
        neutralHover: '#512DA8',
        neutralBackground: 'rgba(94, 53, 177, 0.1)',
        success: '#388E3C',
        warning: '#F57C00',
        error: '#D32F2F',
        info: '#1976D2',
        border: '#E0E0E0',
        borderHover: '#BDBDBD',
        borderActive: '#9E9E9E',
        chartGrid: 'rgba(230, 81, 0, 0.1)',
        chartAxis: 'rgba(230, 81, 0, 0.3)',
        chartVolume: 'rgba(94, 53, 177, 0.3)',
        focus: '#E65100',
        disabled: 'rgba(33, 33, 33, 0.3)'
    }
};
// Theme 5: Colorblind Optimized (Deuteranopia/Protanopia Friendly)
// Psychology: Reduces frustration and cognitive load for colorblind users
const colorblindOptimized = {
    id: 'colorblind-optimized',
    name: 'Colorblind Optimized',
    description: 'Deuteranopia and Protanopia friendly with shape and pattern cues',
    psychologyProfile: {
        stressReduction: 8,
        focusEnhancement: 8,
        cognitiveLoad: 2,
        accessibility: 10
    },
    adolescent: {
        primary: '#0173B2',
        primaryHover: '#01619B',
        primaryActive: '#014F84',
        secondary: '#DE8F05',
        secondaryHover: '#C57F04',
        secondaryActive: '#AC6F03',
        background: '#F7F9FC',
        backgroundSecondary: '#EDF2F7',
        backgroundTertiary: '#E2E8F0',
        textPrimary: '#2D3748',
        textSecondary: '#4A5568',
        textMuted: '#718096',
        bullish: '#029E73',
        bullishHover: '#027A5B',
        bullishBackground: 'rgba(2, 158, 115, 0.1)',
        bearish: '#D55E00',
        bearishHover: '#B84E00',
        bearishBackground: 'rgba(213, 94, 0, 0.1)',
        neutral: '#CC79A7',
        neutralHover: '#B8689A',
        neutralBackground: 'rgba(204, 121, 167, 0.1)',
        success: '#029E73',
        warning: '#DE8F05',
        error: '#D55E00',
        info: '#0173B2',
        border: '#CBD5E0',
        borderHover: '#A0AEC0',
        borderActive: '#718096',
        chartGrid: 'rgba(1, 115, 178, 0.1)',
        chartAxis: 'rgba(1, 115, 178, 0.3)',
        chartVolume: 'rgba(204, 121, 167, 0.3)',
        focus: '#DE8F05',
        disabled: 'rgba(45, 55, 72, 0.3)'
    },
    adult: {
        primary: '#004D9F',
        primaryHover: '#003D7F',
        primaryActive: '#002D5F',
        secondary: '#E69F00',
        secondaryHover: '#CC8F00',
        secondaryActive: '#B37F00',
        background: '#1A202C',
        backgroundSecondary: '#2D3748',
        backgroundTertiary: '#4A5568',
        textPrimary: '#F7FAFC',
        textSecondary: '#EDF2F7',
        textMuted: '#CBD5E0',
        bullish: '#56CC9D',
        bullishHover: '#4DB390',
        bullishBackground: 'rgba(86, 204, 157, 0.1)',
        bearish: '#F0B429',
        bearishHover: '#E6A623',
        bearishBackground: 'rgba(240, 180, 41, 0.1)',
        neutral: '#9F7AEA',
        neutralHover: '#8B5CF6',
        neutralBackground: 'rgba(159, 122, 234, 0.1)',
        success: '#56CC9D',
        warning: '#E69F00',
        error: '#F0B429',
        info: '#63B3ED',
        border: '#4A5568',
        borderHover: '#718096',
        borderActive: '#A0AEC0',
        chartGrid: 'rgba(0, 77, 159, 0.2)',
        chartAxis: 'rgba(0, 77, 159, 0.4)',
        chartVolume: 'rgba(159, 122, 234, 0.3)',
        focus: '#E69F00',
        disabled: 'rgba(247, 250, 252, 0.3)'
    }
};
const themes = [
    professionalDark,
    calmFocus,
    highContrast,
    warmProductivity,
    colorblindOptimized
];
const getThemeById = (id)=>{
    return themes.find((theme)=>theme.id === id);
};
const getThemeColors = (themeId, mode)=>{
    const theme = getThemeById(themeId) || themes[0] // Default to first theme
    ;
    return theme[mode];
};
const generateCSSVariables = (colors)=>{
    return {
        '--color-primary': colors.primary,
        '--color-primary-hover': colors.primaryHover,
        '--color-primary-active': colors.primaryActive,
        '--color-secondary': colors.secondary,
        '--color-secondary-hover': colors.secondaryHover,
        '--color-secondary-active': colors.secondaryActive,
        '--color-background': colors.background,
        '--color-background-secondary': colors.backgroundSecondary,
        '--color-background-tertiary': colors.backgroundTertiary,
        '--color-text-primary': colors.textPrimary,
        '--color-text-secondary': colors.textSecondary,
        '--color-text-muted': colors.textMuted,
        '--color-bullish': colors.bullish,
        '--color-bullish-hover': colors.bullishHover,
        '--color-bullish-background': colors.bullishBackground,
        '--color-bearish': colors.bearish,
        '--color-bearish-hover': colors.bearishHover,
        '--color-bearish-background': colors.bearishBackground,
        '--color-neutral': colors.neutral,
        '--color-neutral-hover': colors.neutralHover,
        '--color-neutral-background': colors.neutralBackground,
        '--color-success': colors.success,
        '--color-warning': colors.warning,
        '--color-error': colors.error,
        '--color-info': colors.info,
        '--color-border': colors.border,
        '--color-border-hover': colors.borderHover,
        '--color-border-active': colors.borderActive,
        '--color-chart-grid': colors.chartGrid,
        '--color-chart-axis': colors.chartAxis,
        '--color-chart-volume': colors.chartVolume,
        '--color-focus': colors.focus,
        '--color-disabled': colors.disabled
    };
};
}}),
"[project]/src/lib/stores/theme-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getAccessibilityScore": (()=>getAccessibilityScore),
    "getFocusEnhancementScore": (()=>getFocusEnhancementScore),
    "getPersonalizedThemeRecommendation": (()=>getPersonalizedThemeRecommendation),
    "getStressReductionScore": (()=>getStressReductionScore),
    "useThemeInitialization": (()=>useThemeInitialization),
    "useThemeStore": (()=>useThemeStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/themes/color-psychology.ts [app-ssr] (ecmascript)");
;
;
;
;
const useThemeStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        currentThemeId: 'professional-dark',
        currentTheme: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["themes"][0],
        interfaceMode: 'adolescent',
        colors: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["themes"][0].adolescent,
        autoThemeSwitch: false,
        reduceMotion: false,
        highContrast: false,
        // Set theme by ID
        setTheme: (themeId)=>{
            const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThemeById"])(themeId);
            if (!theme) return;
            const { interfaceMode } = get();
            const colors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThemeColors"])(themeId, interfaceMode);
            set({
                currentThemeId: themeId,
                currentTheme: theme,
                colors
            });
            // Apply to DOM immediately
            get().applyThemeToDOM();
        },
        // Set interface mode (adolescent/adult)
        setInterfaceMode: (mode)=>{
            const { currentThemeId } = get();
            const colors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThemeColors"])(currentThemeId, mode);
            set({
                interfaceMode: mode,
                colors
            });
            // Apply to DOM immediately
            get().applyThemeToDOM();
        },
        // Toggle auto theme switching
        toggleAutoThemeSwitch: ()=>{
            const { autoThemeSwitch } = get();
            set({
                autoThemeSwitch: !autoThemeSwitch
            });
            // If enabling auto switch, apply recommended theme
            if (!autoThemeSwitch) {
                const recommendedTheme = get().getRecommendedTheme();
                get().setTheme(recommendedTheme);
            }
        },
        // Toggle reduced motion
        toggleReduceMotion: ()=>{
            const { reduceMotion } = get();
            set({
                reduceMotion: !reduceMotion
            });
            // Apply to DOM
            document.documentElement.style.setProperty('--animation-duration', !reduceMotion ? '0s' : '0.3s');
        },
        // Toggle high contrast mode
        toggleHighContrast: ()=>{
            const { highContrast } = get();
            const newHighContrast = !highContrast;
            set({
                highContrast: newHighContrast
            });
            // If enabling high contrast, switch to high contrast theme
            if (newHighContrast) {
                get().setTheme('high-contrast');
            }
        },
        // Apply current theme colors to DOM as CSS custom properties
        applyThemeToDOM: ()=>{
            const { colors, reduceMotion } = get();
            const cssVariables = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateCSSVariables"])(colors);
            // Apply CSS custom properties to document root
            Object.entries(cssVariables).forEach(([property, value])=>{
                document.documentElement.style.setProperty(property, value);
            });
            // Apply motion preferences
            document.documentElement.style.setProperty('--animation-duration', reduceMotion ? '0s' : '0.3s');
            // Apply theme class to body for additional styling
            document.body.className = document.body.className.replace(/theme-\w+/g, '').concat(` theme-${get().currentThemeId}`);
        },
        // Get recommended theme based on various factors
        getRecommendedTheme: ()=>{
            const hour = new Date().getHours();
            const { highContrast } = get();
            // High contrast override
            if (highContrast) {
                return 'high-contrast';
            }
            // Time-based recommendations
            if (hour >= 6 && hour < 12) {
                // Morning: Energizing warm theme
                return 'warm-productivity';
            } else if (hour >= 12 && hour < 18) {
                // Afternoon: Focus-enhancing calm theme
                return 'calm-focus';
            } else if (hour >= 18 && hour < 22) {
                // Evening: Professional theme for serious work
                return 'professional-dark';
            } else {
                // Night: Stress-reducing calm theme
                return 'calm-focus';
            }
        }
    }), {
    name: 'tradequest-theme-storage',
    partialize: (state)=>({
            currentThemeId: state.currentThemeId,
            interfaceMode: state.interfaceMode,
            autoThemeSwitch: state.autoThemeSwitch,
            reduceMotion: state.reduceMotion,
            highContrast: state.highContrast
        })
}));
const useThemeInitialization = ()=>{
    const { setTheme, setInterfaceMode, applyThemeToDOM, getRecommendedTheme, autoThemeSwitch, currentThemeId, interfaceMode } = useThemeStore();
    // Initialize theme on mount
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        // Apply current theme to DOM
        applyThemeToDOM();
        // Auto theme switching
        if (autoThemeSwitch) {
            const recommendedTheme = getRecommendedTheme();
            if (recommendedTheme !== currentThemeId) {
                setTheme(recommendedTheme);
            }
        }
        // Listen for system preference changes
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        const handleMotionChange = (e)=>{
            useThemeStore.getState().toggleReduceMotion();
        };
        mediaQuery.addEventListener('change', handleMotionChange);
        return ()=>{
            mediaQuery.removeEventListener('change', handleMotionChange);
        };
    }, []);
    // Auto theme switching interval
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useEffect(()=>{
        if (!autoThemeSwitch) return;
        const interval = setInterval(()=>{
            const recommendedTheme = getRecommendedTheme();
            if (recommendedTheme !== currentThemeId) {
                setTheme(recommendedTheme);
            }
        }, 60000) // Check every minute
        ;
        return ()=>clearInterval(interval);
    }, [
        autoThemeSwitch,
        currentThemeId,
        setTheme,
        getRecommendedTheme
    ]);
};
const getAccessibilityScore = (themeId)=>{
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThemeById"])(themeId);
    return theme?.psychologyProfile.accessibility || 0;
};
const getStressReductionScore = (themeId)=>{
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThemeById"])(themeId);
    return theme?.psychologyProfile.stressReduction || 0;
};
const getFocusEnhancementScore = (themeId)=>{
    const theme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$themes$2f$color$2d$psychology$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getThemeById"])(themeId);
    return theme?.psychologyProfile.focusEnhancement || 0;
};
const getPersonalizedThemeRecommendation = (userProfile)=>{
    const { age, tradingExperience, visualImpairment, stressLevel, sessionDuration } = userProfile;
    // High contrast for visual impairments
    if (visualImpairment) {
        return 'high-contrast';
    }
    // Colorblind optimization for accessibility
    if (age && age > 40) {
        return 'colorblind-optimized';
    }
    // Stress-based recommendations
    if (stressLevel === 'high' || sessionDuration === 'long') {
        return 'calm-focus';
    }
    // Experience-based recommendations
    if (tradingExperience === 'beginner') {
        return 'warm-productivity' // Engaging for learning
        ;
    }
    if (tradingExperience === 'advanced') {
        return 'professional-dark' // Professional for experts
        ;
    }
    // Default to calm focus for most users
    return 'calm-focus';
};
}}),
"[project]/src/components/theme/theme-provider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MarketConditionBadge": (()=>MarketConditionBadge),
    "TradingButton": (()=>TradingButton),
    "default": (()=>ThemeProvider),
    "useThemeClasses": (()=>useThemeClasses),
    "useThemeColors": (()=>useThemeColors)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$theme$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/theme-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function ThemeProvider({ children }) {
    const { applyThemeToDOM, setInterfaceMode, autoThemeSwitch, getRecommendedTheme, setTheme, currentThemeId } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$theme$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useThemeStore"])();
    const { interfaceMode, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserStore"])();
    // Initialize theme system
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Apply current theme to DOM
        applyThemeToDOM();
        // Sync interface mode with user store
        setInterfaceMode(interfaceMode);
        // Auto theme switching based on time
        if (autoThemeSwitch) {
            const recommendedTheme = getRecommendedTheme();
            if (recommendedTheme !== currentThemeId) {
                setTheme(recommendedTheme);
            }
        }
        // Listen for system preference changes
        const mediaQueries = [
            window.matchMedia('(prefers-reduced-motion: reduce)'),
            window.matchMedia('(prefers-contrast: high)'),
            window.matchMedia('(prefers-color-scheme: dark)')
        ];
        const handleSystemPreferenceChange = ()=>{
            // Re-apply theme to respect system preferences
            applyThemeToDOM();
        };
        mediaQueries.forEach((mq)=>{
            mq.addEventListener('change', handleSystemPreferenceChange);
        });
        return ()=>{
            mediaQueries.forEach((mq)=>{
                mq.removeEventListener('change', handleSystemPreferenceChange);
            });
        };
    }, []);
    // Auto theme switching interval
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!autoThemeSwitch) return;
        const interval = setInterval(()=>{
            const recommendedTheme = getRecommendedTheme();
            if (recommendedTheme !== currentThemeId) {
                setTheme(recommendedTheme);
            }
        }, 60000) // Check every minute
        ;
        return ()=>clearInterval(interval);
    }, [
        autoThemeSwitch,
        currentThemeId,
        setTheme,
        getRecommendedTheme
    ]);
    // Sync interface mode changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setInterfaceMode(interfaceMode);
    }, [
        interfaceMode,
        setInterfaceMode
    ]);
    // Apply theme changes to DOM
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        applyThemeToDOM();
    }, [
        currentThemeId,
        interfaceMode,
        applyThemeToDOM
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
}
function useThemeColors() {
    const { colors } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$theme$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useThemeStore"])();
    return colors;
}
function useThemeClasses() {
    const { currentThemeId, interfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$theme$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useThemeStore"])();
    const getButtonClass = (variant = 'primary')=>{
        const baseClass = 'px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2';
        const variantClasses = {
            primary: 'bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] active:bg-[var(--color-primary-active)] text-white focus:ring-[var(--color-focus)]',
            secondary: 'bg-[var(--color-secondary)] hover:bg-[var(--color-secondary-hover)] active:bg-[var(--color-secondary-active)] text-white focus:ring-[var(--color-focus)]',
            success: 'bg-[var(--color-success)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',
            warning: 'bg-[var(--color-warning)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',
            error: 'bg-[var(--color-error)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]'
        };
        return `${baseClass} ${variantClasses[variant]}`;
    };
    const getCardClass = ()=>{
        return 'bg-[var(--color-background-secondary)] border border-[var(--color-border)] rounded-lg p-4';
    };
    const getTextClass = (variant = 'primary')=>{
        const variantClasses = {
            primary: 'text-[var(--color-text-primary)]',
            secondary: 'text-[var(--color-text-secondary)]',
            muted: 'text-[var(--color-text-muted)]'
        };
        return variantClasses[variant];
    };
    const getBullishClass = ()=>{
        return 'text-[var(--color-bullish)] bg-[var(--color-bullish-background)]';
    };
    const getBearishClass = ()=>{
        return 'text-[var(--color-bearish)] bg-[var(--color-bearish-background)]';
    };
    const getNeutralClass = ()=>{
        return 'text-[var(--color-neutral)] bg-[var(--color-neutral-background)]';
    };
    return {
        getButtonClass,
        getCardClass,
        getTextClass,
        getBullishClass,
        getBearishClass,
        getNeutralClass,
        currentThemeId,
        interfaceMode
    };
}
function MarketConditionBadge({ condition, children, className = '' }) {
    const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses();
    const conditionClasses = {
        bullish: getBullishClass(),
        bearish: getBearishClass(),
        neutral: getNeutralClass()
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        className: `px-2 py-1 rounded text-sm font-medium ${conditionClasses[condition]} ${className}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/theme/theme-provider.tsx",
        lineNumber: 170,
        columnNumber: 5
    }, this);
}
function TradingButton({ action, onClick, children, disabled = false, className = '' }) {
    const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses();
    const actionClasses = {
        buy: getBullishClass(),
        sell: getBearishClass(),
        neutral: getNeutralClass()
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: onClick,
        disabled: disabled,
        className: `px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[var(--color-focus)] disabled:opacity-50 disabled:cursor-not-allowed ${actionClasses[action]} ${className}`,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/theme/theme-provider.tsx",
        lineNumber: 199,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__236fbb70._.js.map