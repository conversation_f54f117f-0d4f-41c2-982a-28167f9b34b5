'use client'

import { useEffect, useRef, useState } from 'react'
import { create<PERSON>hart, IChartApi, ISeriesApi, ColorType } from 'lightweight-charts'
import { ChartPlaybackEngine, type PlaybackState, type PredictionChallenge } from '@/lib/chart-playback/playback-engine'
import { CandlestickData } from '@/types'
import { useThemeColors } from '@/components/theme/theme-provider'
import { useUserStore } from '@/lib/stores/user-store'

interface PlaybackChartProps {
  symbol: string
  timeframe: string
  startDate: Date
  endDate: Date
  onPredictionChallenge?: (challenge: PredictionChallenge) => void
  onPatternDetected?: (pattern: any) => void
  onMarketEvent?: (event: any) => void
  className?: string
}

export default function PlaybackChart({
  symbol,
  timeframe,
  startDate,
  endDate,
  onPredictionChallenge,
  onPatternDetected,
  onMarketEvent,
  className = '',
}: PlaybackChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)
  const playbackEngineRef = useRef<ChartPlaybackEngine | null>(null)
  
  const [playbackState, setPlaybackState] = useState<PlaybackState | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentPrediction, setCurrentPrediction] = useState<PredictionChallenge | null>(null)
  const [detectedPatterns, setDetectedPatterns] = useState<any[]>([])
  const [marketEvents, setMarketEvents] = useState<any[]>([])
  
  const colors = useThemeColors()
  const { interfaceMode } = useUserStore()
  const isAdolescentMode = interfaceMode === 'adolescent'

  // Initialize playback engine and chart
  useEffect(() => {
    if (!chartContainerRef.current) return

    const initializeChart = async () => {
      setIsLoading(true)
      
      try {
        // Create chart
        const chart = createChart(chartContainerRef.current!, {
          width: 800,
          height: 500,
          layout: {
            background: { type: ColorType.Solid, color: colors.background },
            textColor: colors.textPrimary,
          },
          grid: {
            vertLines: { color: colors.chartGrid },
            horzLines: { color: colors.chartGrid },
          },
          crosshair: { mode: 1 },
          rightPriceScale: { borderColor: colors.border },
          timeScale: {
            borderColor: colors.border,
            timeVisible: true,
            secondsVisible: false,
          },
        })

        chartRef.current = chart

        // Add candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
          upColor: colors.bullish,
          downColor: colors.bearish,
          borderDownColor: colors.bearish,
          borderUpColor: colors.bullish,
          wickDownColor: colors.bearish,
          wickUpColor: colors.bullish,
        })

        candlestickSeriesRef.current = candlestickSeries

        // Add volume series
        const volumeSeries = chart.addHistogramSeries({
          color: colors.chartVolume,
          priceFormat: { type: 'volume' },
          priceScaleId: '',
          scaleMargins: { top: 0.7, bottom: 0 },
        })

        volumeSeriesRef.current = volumeSeries

        // Initialize playback engine
        const engine = new ChartPlaybackEngine()
        playbackEngineRef.current = engine

        // Set up event listeners
        engine.on('state_change', (state: PlaybackState) => {
          setPlaybackState(state)
        })

        engine.on('candle_update', ({ candle, visibleData }: { candle: CandlestickData, visibleData: CandlestickData[] }) => {
          updateChart(visibleData)
        })

        engine.on('pattern_detected', (pattern: any) => {
          setDetectedPatterns(prev => [...prev, pattern])
          onPatternDetected?.(pattern)
          addPatternMarker(pattern)
        })

        engine.on('market_event', (event: any) => {
          setMarketEvents(prev => [...prev, event])
          onMarketEvent?.(event)
          addEventMarker(event)
        })

        engine.on('prediction_point', (prediction: PredictionChallenge) => {
          setCurrentPrediction(prediction)
          onPredictionChallenge?.(prediction)
        })

        // Initialize with data
        await engine.initialize(symbol, timeframe, startDate, endDate)
        
        setIsLoading(false)
      } catch (error) {
        console.error('Failed to initialize playback chart:', error)
        setIsLoading(false)
      }
    }

    initializeChart()

    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.remove()
      }
      if (playbackEngineRef.current) {
        playbackEngineRef.current.stop()
      }
    }
  }, [symbol, timeframe, startDate, endDate])

  // Update chart with new data
  const updateChart = (data: CandlestickData[]) => {
    if (!candlestickSeriesRef.current || !volumeSeriesRef.current) return

    const chartData = data.map(candle => ({
      time: Math.floor(candle.timestamp / 1000) as any,
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close,
    }))

    const volumeData = data.map(candle => ({
      time: Math.floor(candle.timestamp / 1000) as any,
      value: candle.volume,
      color: candle.close >= candle.open ? colors.bullish + '50' : colors.bearish + '50',
    }))

    candlestickSeriesRef.current.setData(chartData)
    volumeSeriesRef.current.setData(volumeData)

    // Auto-scroll to latest data
    if (chartRef.current && data.length > 0) {
      chartRef.current.timeScale().scrollToRealTime()
    }
  }

  // Add pattern detection markers
  const addPatternMarker = (pattern: any) => {
    if (!candlestickSeriesRef.current || !pattern.timestamp) return

    const markers = candlestickSeriesRef.current.markers() || []
    markers.push({
      time: Math.floor(pattern.timestamp / 1000) as any,
      position: 'belowBar' as const,
      color: colors.warning,
      shape: 'circle' as const,
      text: pattern.pattern.name,
    })

    candlestickSeriesRef.current.setMarkers(markers)
  }

  // Add market event markers
  const addEventMarker = (event: any) => {
    if (!candlestickSeriesRef.current || !event.timestamp) return

    const markers = candlestickSeriesRef.current.markers() || []
    const impactColors = {
      low: colors.info,
      medium: colors.warning,
      high: colors.error,
    }

    markers.push({
      time: Math.floor(event.timestamp / 1000) as any,
      position: 'aboveBar' as const,
      color: impactColors[event.impact] || colors.info,
      shape: 'arrowDown' as const,
      text: event.title.substring(0, 10) + '...',
    })

    candlestickSeriesRef.current.setMarkers(markers)
  }

  // Playback controls
  const handlePlay = () => playbackEngineRef.current?.play()
  const handlePause = () => playbackEngineRef.current?.pause()
  const handleStop = () => playbackEngineRef.current?.stop()
  const handleSpeedChange = (speed: number) => playbackEngineRef.current?.setSpeed(speed)
  const handleJumpToIndex = (index: number) => playbackEngineRef.current?.jumpToIndex(index)

  // Handle prediction response
  const handlePredictionResponse = (answerIndex: number) => {
    if (!currentPrediction) return

    const isCorrect = answerIndex === currentPrediction.correctAnswer
    
    // Show result (you could emit an event or show a modal here)
    console.log(`Prediction ${isCorrect ? 'correct' : 'incorrect'}: ${currentPrediction.explanation}`)
    
    setCurrentPrediction(null)
    
    // Resume playback
    setTimeout(() => {
      playbackEngineRef.current?.play()
    }, 2000)
  }

  if (isLoading) {
    return (
      <div className={`${className} flex items-center justify-center h-96`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-current mx-auto mb-4"></div>
          <p style={{ color: colors.textSecondary }}>
            {isAdolescentMode ? '🔄 Loading historical data...' : 'LOADING_HISTORICAL_DATA...'}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={`${className} space-y-4`}>
      {/* Chart Container */}
      <div 
        ref={chartContainerRef}
        className="rounded-lg border"
        style={{ borderColor: colors.border }}
      />

      {/* Playback Controls */}
      {playbackState && (
        <div 
          className="p-4 rounded-lg"
          style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={handlePlay}
                disabled={playbackState.isPlaying}
                className="px-3 py-1 rounded text-sm font-bold transition-colors"
                style={{ 
                  backgroundColor: playbackState.isPlaying ? colors.disabled : colors.success,
                  color: colors.background 
                }}
              >
                {isAdolescentMode ? '▶️ Play' : 'PLAY'}
              </button>
              
              <button
                onClick={handlePause}
                disabled={!playbackState.isPlaying}
                className="px-3 py-1 rounded text-sm font-bold transition-colors"
                style={{ 
                  backgroundColor: !playbackState.isPlaying ? colors.disabled : colors.warning,
                  color: colors.background 
                }}
              >
                {isAdolescentMode ? '⏸️ Pause' : 'PAUSE'}
              </button>
              
              <button
                onClick={handleStop}
                className="px-3 py-1 rounded text-sm font-bold transition-colors"
                style={{ backgroundColor: colors.error, color: colors.background }}
              >
                {isAdolescentMode ? '⏹️ Stop' : 'STOP'}
              </button>
            </div>

            {/* Speed Control */}
            <div className="flex items-center space-x-2">
              <span style={{ color: colors.textSecondary }} className="text-sm">
                {isAdolescentMode ? 'Speed:' : 'SPEED:'}
              </span>
              {[0.25, 0.5, 1.0, 2.0, 4.0].map(speed => (
                <button
                  key={speed}
                  onClick={() => handleSpeedChange(speed)}
                  className={`px-2 py-1 rounded text-xs transition-colors ${
                    playbackState.speed === speed ? 'font-bold' : ''
                  }`}
                  style={{ 
                    backgroundColor: playbackState.speed === speed ? colors.primary : colors.backgroundTertiary,
                    color: playbackState.speed === speed ? colors.background : colors.textSecondary
                  }}
                >
                  {speed}x
                </button>
              ))}
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm" style={{ color: colors.textSecondary }}>
              <span>
                {isAdolescentMode ? 'Progress:' : 'PROGRESS:'} {playbackState.currentIndex + 1} / {playbackState.totalCandles}
              </span>
              <span>
                {isAdolescentMode ? 'Speed:' : 'SPEED:'} {playbackState.speed}x
              </span>
            </div>
            
            <div 
              className="w-full h-2 rounded-full"
              style={{ backgroundColor: colors.backgroundTertiary }}
            >
              <div
                className="h-2 rounded-full transition-all duration-300"
                style={{ 
                  backgroundColor: colors.primary,
                  width: `${(playbackState.currentIndex / Math.max(playbackState.totalCandles - 1, 1)) * 100}%`
                }}
              />
            </div>
            
            <input
              type="range"
              min="0"
              max={playbackState.totalCandles - 1}
              value={playbackState.currentIndex}
              onChange={(e) => handleJumpToIndex(parseInt(e.target.value))}
              className="w-full"
            />
          </div>
        </div>
      )}

      {/* Prediction Challenge Modal */}
      {currentPrediction && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div 
            className="max-w-md w-full p-6 rounded-lg"
            style={{ backgroundColor: colors.background, borderColor: colors.border }}
          >
            <h3 className="text-lg font-bold mb-4" style={{ color: colors.textPrimary }}>
              {isAdolescentMode ? '🔮 Prediction Challenge!' : '📊 MARKET_PREDICTION_CHALLENGE'}
            </h3>
            
            <p className="mb-4" style={{ color: colors.textSecondary }}>
              {currentPrediction.question}
            </p>
            
            <div className="space-y-2 mb-4">
              {currentPrediction.options.map((option, index) => (
                <button
                  key={index}
                  onClick={() => handlePredictionResponse(index)}
                  className="w-full p-3 rounded text-left transition-colors"
                  style={{ 
                    backgroundColor: colors.backgroundSecondary,
                    borderColor: colors.border,
                    color: colors.textPrimary
                  }}
                >
                  {option}
                </button>
              ))}
            </div>
            
            <div className="text-sm" style={{ color: colors.textMuted }}>
              {isAdolescentMode ? `💎 Points: ${currentPrediction.points}` : `POINTS: ${currentPrediction.points}`}
            </div>
          </div>
        </div>
      )}

      {/* Pattern Detection Panel */}
      {detectedPatterns.length > 0 && (
        <div 
          className="p-4 rounded-lg"
          style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
        >
          <h4 className="font-bold mb-2" style={{ color: colors.textPrimary }}>
            {isAdolescentMode ? '🔍 Detected Patterns' : '📊 PATTERN_DETECTION'}
          </h4>
          <div className="space-y-2">
            {detectedPatterns.slice(-3).map((pattern, index) => (
              <div key={index} className="text-sm">
                <span className="font-medium" style={{ color: colors.warning }}>
                  {pattern.pattern.name}
                </span>
                <span style={{ color: colors.textSecondary }} className="ml-2">
                  - {pattern.pattern.description}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
