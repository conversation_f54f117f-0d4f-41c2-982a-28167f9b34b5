{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\n// Mock client for development when Supabase is not configured\nconst createMockClient = () => ({\n  auth: {\n    signInWithPassword: async () => ({ data: null, error: { message: 'Demo mode - authentication disabled' } }),\n    signUp: async () => ({ data: null, error: { message: 'Demo mode - registration disabled' } }),\n    signOut: async () => ({ error: null }),\n    getSession: async () => ({ data: { session: null }, error: null }),\n    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),\n    signInWithOAuth: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),\n    exchangeCodeForSession: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),\n  },\n  from: () => ({\n    select: () => ({\n      eq: () => ({\n        single: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n      })\n    }),\n    insert: () => ({\n      select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n    }),\n    update: () => ({\n      eq: () => ({\n        select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n      })\n    })\n  })\n})\n\nexport function createClient() {\n  // Check if Supabase environment variables are properly configured\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!supabaseUrl || !supabaseKey || supabaseUrl.includes('demo') || supabaseKey.includes('demo')) {\n    console.warn('⚠️  Supabase not configured - running in demo mode. Authentication and database features will be disabled.')\n    return createMockClient() as any\n  }\n\n  try {\n    return createBrowserClient(supabaseUrl, supabaseKey)\n  } catch (error) {\n    console.error('Failed to create Supabase client:', error)\n    console.warn('Falling back to demo mode')\n    return createMockClient() as any\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEA,8DAA8D;AAC9D,MAAM,mBAAmB,IAAM,CAAC;QAC9B,MAAM;YACJ,oBAAoB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAAsC;gBAAE,CAAC;YAC1G,QAAQ,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAAoC;gBAAE,CAAC;YAC5F,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;YACrC,YAAY,UAAY,CAAC;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YACjE,mBAAmB,IAAM,CAAC;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE,CAAC;YAC/E,iBAAiB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAA6B;gBAAE,CAAC;YAC9F,wBAAwB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAA6B;gBAAE,CAAC;QACvG;QACA,MAAM,IAAM,CAAC;gBACX,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,UAAY,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAAgC;oCAAE,CAAC;4BAC1F,CAAC;oBACH,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,QAAQ,UAAY,CAAC;gCAAE,MAAM;gCAAM,OAAO;oCAAE,SAAS;gCAAgC;4BAAE,CAAC;oBAC1F,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,UAAY,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAAgC;oCAAE,CAAC;4BAC1F,CAAC;oBACH,CAAC;YACH,CAAC;IACH,CAAC;AAEM,SAAS;IACd,kEAAkE;IAClE,MAAM;IACN,MAAM;IAEN,IAAI,CAAC,eAAe,CAAC,eAAe,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,SAAS;QAChG,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/stores/user-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { UserProfile, Achievement, GameSession, ThemeConfig } from '@/types'\nimport { createClient } from '@/lib/supabase/client'\n\ninterface UserState {\n  // User data\n  user: UserProfile | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  \n  // Theme and UI\n  interfaceMode: 'adolescent' | 'adult'\n  themeConfig: ThemeConfig\n  \n  // Game state\n  currentGameSession: GameSession | null\n  recentSessions: GameSession[]\n  \n  // Actions\n  setUser: (user: UserProfile | null) => void\n  setAuthenticated: (authenticated: boolean) => void\n  setLoading: (loading: boolean) => void\n  switchInterfaceMode: (mode: 'adolescent' | 'adult') => void\n  updateThemeConfig: (config: Partial<ThemeConfig>) => void\n  addQuestCoins: (amount: number, source: string) => void\n  spendQuestCoins: (amount: number, purpose: string) => boolean\n  addExperience: (points: number) => void\n  unlockAchievement: (achievement: Achievement) => void\n  startGameSession: (session: GameSession) => void\n  endGameSession: (session: GameSession) => void\n  updateUserProfile: (updates: Partial<UserProfile>) => void\n  clearUserData: () => void\n  signOut: () => Promise<void>\n  initializeAuth: () => Promise<void>\n}\n\nconst defaultThemeConfig: ThemeConfig = {\n  mode: 'adolescent',\n  primary_color: '#8B5CF6',\n  secondary_color: '#EC4899',\n  background_style: 'fantasy',\n  font_family: 'fantasy',\n}\n\nexport const useUserStore = create<UserState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      interfaceMode: 'adolescent',\n      themeConfig: defaultThemeConfig,\n      currentGameSession: null,\n      recentSessions: [],\n\n      // User management actions\n      setUser: (user) => {\n        set({ user, isAuthenticated: !!user })\n        \n        // Update interface mode based on user preference or age\n        if (user) {\n          const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult')\n          get().switchInterfaceMode(mode)\n        }\n      },\n\n      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),\n\n      setLoading: (loading) => set({ isLoading: loading }),\n\n      // Theme and UI actions\n      switchInterfaceMode: (mode) => {\n        const newThemeConfig: ThemeConfig = mode === 'adolescent' \n          ? {\n              mode: 'adolescent',\n              primary_color: '#8B5CF6',\n              secondary_color: '#EC4899',\n              background_style: 'fantasy',\n              font_family: 'fantasy',\n            }\n          : {\n              mode: 'adult',\n              primary_color: '#1F2937',\n              secondary_color: '#3B82F6',\n              background_style: 'professional',\n              font_family: 'monospace',\n            }\n\n        set({ \n          interfaceMode: mode, \n          themeConfig: newThemeConfig \n        })\n\n        // Update user preference in database\n        const { user } = get()\n        if (user) {\n          get().updateUserProfile({ interface_mode: mode })\n        }\n      },\n\n      updateThemeConfig: (config) => {\n        set((state) => ({\n          themeConfig: { ...state.themeConfig, ...config }\n        }))\n      },\n\n      // Quest coins management\n      addQuestCoins: (amount, source) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins + amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Added ${amount} QuestCoins from ${source}`)\n      },\n\n      spendQuestCoins: (amount, purpose) => {\n        const { user } = get()\n        if (!user || user.total_quest_coins < amount) {\n          return false\n        }\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins - amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Spent ${amount} QuestCoins on ${purpose}`)\n        return true\n      },\n\n      // Experience and leveling\n      addExperience: (points) => {\n        const { user } = get()\n        if (!user) return\n\n        const newExperience = user.experience_points + points\n        const newLevel = calculateLevel(newExperience)\n        const leveledUp = newLevel > user.level\n\n        const updatedUser = {\n          ...user,\n          experience_points: newExperience,\n          level: newLevel\n        }\n\n        set({ user: updatedUser })\n\n        if (leveledUp) {\n          console.log(`Level up! Now level ${newLevel}`)\n          // In a real app, you'd trigger level up effects, notifications, etc.\n        }\n      },\n\n      // Achievement system\n      unlockAchievement: (achievement) => {\n        const { user } = get()\n        if (!user) return\n\n        // Check if achievement is already unlocked\n        const alreadyUnlocked = user.achievements.some(a => a.id === achievement.id)\n        if (alreadyUnlocked) return\n\n        const updatedUser = {\n          ...user,\n          achievements: [...user.achievements, { ...achievement, unlocked_at: new Date().toISOString() }]\n        }\n\n        set({ user: updatedUser })\n\n        // Award quest coins for achievement\n        get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`)\n\n        console.log(`Achievement unlocked: ${achievement.name}`)\n        // In a real app, you'd show a notification, play sound effects, etc.\n      },\n\n      // Game session management\n      startGameSession: (session) => {\n        set({ currentGameSession: session })\n      },\n\n      endGameSession: (session) => {\n        const { recentSessions } = get()\n        \n        // Add to recent sessions (keep last 10)\n        const updatedSessions = [session, ...recentSessions].slice(0, 10)\n        \n        set({ \n          currentGameSession: null,\n          recentSessions: updatedSessions\n        })\n\n        // Award quest coins and experience\n        get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`)\n        get().addExperience(Math.floor(session.score / 10))\n\n        // Check for achievements\n        checkGameAchievements(session, get())\n      },\n\n      // Profile updates\n      updateUserProfile: (updates) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = { ...user, ...updates }\n        set({ user: updatedUser })\n\n        // In a real app, you'd sync with the database\n        console.log('User profile updated:', updates)\n      },\n\n      // Cleanup\n      clearUserData: () => {\n        set({\n          user: null,\n          isAuthenticated: false,\n          currentGameSession: null,\n          recentSessions: [],\n          interfaceMode: 'adolescent',\n          themeConfig: defaultThemeConfig,\n        })\n      },\n\n      // Authentication methods\n      signOut: async () => {\n        const supabase = createClient()\n        await supabase.auth.signOut()\n        get().clearUserData()\n      },\n\n      initializeAuth: async () => {\n        try {\n          const supabase = createClient()\n\n          // Get initial session\n          const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n\n          if (sessionError) {\n            console.warn('Auth session error (demo mode):', sessionError.message)\n            // Set demo user for development\n            get().setUser({\n              id: 'demo-user-id',\n              email: '<EMAIL>',\n              username: 'DemoTrader',\n              age: 25,\n              is_minor: false,\n              interface_mode: 'adolescent',\n              avatar_url: null,\n              total_quest_coins: 1000,\n              level: 1,\n              experience_points: 0,\n              achievements: [],\n              guild_id: null,\n              preferred_language: 'en',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString(),\n            })\n            return\n          }\n\n          if (session?.user) {\n            // Fetch user profile\n            const { data: profile, error: profileError } = await supabase\n              .from('user_profiles')\n              .select('*')\n              .eq('id', session.user.id)\n              .single()\n\n            if (profileError) {\n              console.warn('Profile fetch error (demo mode):', profileError.message)\n              return\n            }\n\n            if (profile) {\n              // Fetch achievements\n              const { data: achievements } = await supabase\n                .from('user_achievements')\n                .select(`\n                  achievement_id,\n                  unlocked_at,\n                  achievements (\n                    id,\n                    name,\n                    description,\n                    icon,\n                    category,\n                    points\n                  )\n                `)\n                .eq('user_id', session.user.id)\n\n              const userAchievements = achievements?.map(ua => ({\n                ...ua.achievements,\n                unlocked_at: ua.unlocked_at\n              })) || []\n\n              get().setUser({\n                id: profile.id,\n                email: session.user.email!,\n                username: profile.username,\n                age: profile.age,\n                is_minor: profile.is_minor,\n                interface_mode: profile.interface_mode,\n                avatar_url: profile.avatar_url,\n                total_quest_coins: profile.total_quest_coins,\n                level: profile.level,\n                experience_points: profile.experience_points,\n                achievements: userAchievements,\n                guild_id: profile.guild_id,\n                preferred_language: profile.preferred_language,\n                created_at: profile.created_at,\n                updated_at: profile.updated_at,\n              })\n            }\n          }\n\n          // Listen for auth changes\n          supabase.auth.onAuthStateChange(async (event, session) => {\n            if (event === 'SIGNED_OUT' || !session) {\n              get().clearUserData()\n            } else if (event === 'SIGNED_IN' && session?.user) {\n              // Refresh user data when signed in\n              get().initializeAuth()\n            }\n          })\n        } catch (error) {\n          console.error('Auth initialization error:', error)\n          console.warn('Running in demo mode without authentication')\n\n          // Set demo user for development\n          get().setUser({\n            id: 'demo-user-id',\n            email: '<EMAIL>',\n            username: 'DemoTrader',\n            age: 25,\n            is_minor: false,\n            interface_mode: 'adolescent',\n            avatar_url: null,\n            total_quest_coins: 1000,\n            level: 1,\n            experience_points: 0,\n            achievements: [],\n            guild_id: null,\n            preferred_language: 'en',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n        }\n      },\n    }),\n    {\n      name: 'tradequest-user-storage',\n      partialize: (state) => ({\n        user: state.user,\n        interfaceMode: state.interfaceMode,\n        themeConfig: state.themeConfig,\n        recentSessions: state.recentSessions,\n      }),\n    }\n  )\n)\n\n// Helper functions\nfunction calculateLevel(experience: number): number {\n  const LEVEL_THRESHOLDS = [\n    0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n    13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n  ]\n\n  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {\n    if (experience >= LEVEL_THRESHOLDS[i]) {\n      return i + 1\n    }\n  }\n  return 1\n}\n\nfunction checkGameAchievements(session: GameSession, store: any) {\n  const achievements: Achievement[] = []\n\n  // First game achievement\n  if (store.recentSessions.length === 0) {\n    achievements.push({\n      id: 'first_game',\n      name: 'First Steps',\n      description: 'Complete your first trading game',\n      icon: '🎮',\n      category: 'trading',\n      points: 50,\n    })\n  }\n\n  // High score achievements\n  if (session.score >= 1000) {\n    achievements.push({\n      id: 'high_score_1000',\n      name: 'Rising Trader',\n      description: 'Score 1000+ points in a single game',\n      icon: '📈',\n      category: 'trading',\n      points: 100,\n    })\n  }\n\n  if (session.score >= 5000) {\n    achievements.push({\n      id: 'high_score_5000',\n      name: 'Expert Trader',\n      description: 'Score 5000+ points in a single game',\n      icon: '🏆',\n      category: 'trading',\n      points: 250,\n    })\n  }\n\n  // Game-specific achievements\n  if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {\n    achievements.push({\n      id: 'speed_scalper',\n      name: 'Lightning Fast',\n      description: 'Complete Scalper Sprint in under 30 seconds',\n      icon: '⚡',\n      category: 'trading',\n      points: 150,\n    })\n  }\n\n  // Unlock achievements\n  achievements.forEach(achievement => {\n    store.unlockAchievement(achievement)\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAkCA,MAAM,qBAAkC;IACtC,MAAM;IACN,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;AACf;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,gBAAgB,EAAE;QAElB,0BAA0B;QAC1B,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;YAEpC,wDAAwD;YACxD,IAAI,MAAM;gBACR,MAAM,OAAO,KAAK,cAAc,IAAI,CAAC,KAAK,QAAQ,GAAG,eAAe,OAAO;gBAC3E,MAAM,mBAAmB,CAAC;YAC5B;QACF;QAEA,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE,iBAAiB;YAAc;QAE1E,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAElD,uBAAuB;QACvB,qBAAqB,CAAC;YACpB,MAAM,iBAA8B,SAAS,eACzC;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf,IACA;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf;YAEJ,IAAI;gBACF,eAAe;gBACf,aAAa;YACf;YAEA,qCAAqC;YACrC,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM;gBACR,MAAM,iBAAiB,CAAC;oBAAE,gBAAgB;gBAAK;YACjD;QACF;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,MAAM;oBAAC;gBACjD,CAAC;QACH;QAEA,yBAAyB;QACzB,eAAe,CAAC,QAAQ;YACtB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,iBAAiB,EAAE,QAAQ;QACzD;QAEA,iBAAiB,CAAC,QAAQ;YACxB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,QAAQ,KAAK,iBAAiB,GAAG,QAAQ;gBAC5C,OAAO;YACT;YAEA,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,eAAe,EAAE,SAAS;YACtD,OAAO;QACT;QAEA,0BAA0B;QAC1B,eAAe,CAAC;YACd,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,gBAAgB,KAAK,iBAAiB,GAAG;YAC/C,MAAM,WAAW,eAAe;YAChC,MAAM,YAAY,WAAW,KAAK,KAAK;YAEvC,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB;gBACnB,OAAO;YACT;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;YAC7C,qEAAqE;YACvE;QACF;QAEA,qBAAqB;QACrB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,2CAA2C;YAC3C,MAAM,kBAAkB,KAAK,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;YAC3E,IAAI,iBAAiB;YAErB,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,cAAc;uBAAI,KAAK,YAAY;oBAAE;wBAAE,GAAG,WAAW;wBAAE,aAAa,IAAI,OAAO,WAAW;oBAAG;iBAAE;YACjG;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,oCAAoC;YACpC,MAAM,aAAa,CAAC,YAAY,MAAM,EAAE,CAAC,aAAa,EAAE,YAAY,IAAI,EAAE;YAE1E,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,IAAI,EAAE;QACvD,qEAAqE;QACvE;QAEA,0BAA0B;QAC1B,kBAAkB,CAAC;YACjB,IAAI;gBAAE,oBAAoB;YAAQ;QACpC;QAEA,gBAAgB,CAAC;YACf,MAAM,EAAE,cAAc,EAAE,GAAG;YAE3B,wCAAwC;YACxC,MAAM,kBAAkB;gBAAC;mBAAY;aAAe,CAAC,KAAK,CAAC,GAAG;YAE9D,IAAI;gBACF,oBAAoB;gBACpB,gBAAgB;YAClB;YAEA,mCAAmC;YACnC,MAAM,aAAa,CAAC,QAAQ,kBAAkB,EAAE,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE;YAC5E,MAAM,aAAa,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;YAE/C,yBAAyB;YACzB,sBAAsB,SAAS;QACjC;QAEA,kBAAkB;QAClB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC1C,IAAI;gBAAE,MAAM;YAAY;YAExB,8CAA8C;YAC9C,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,UAAU;QACV,eAAe;YACb,IAAI;gBACF,MAAM;gBACN,iBAAiB;gBACjB,oBAAoB;gBACpB,gBAAgB,EAAE;gBAClB,eAAe;gBACf,aAAa;YACf;QACF;QAEA,yBAAyB;QACzB,SAAS;YACP,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,MAAM,aAAa;QACrB;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;gBAE5B,sBAAsB;gBACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAEjF,IAAI,cAAc;oBAChB,QAAQ,IAAI,CAAC,mCAAmC,aAAa,OAAO;oBACpE,gCAAgC;oBAChC,MAAM,OAAO,CAAC;wBACZ,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,KAAK;wBACL,UAAU;wBACV,gBAAgB;wBAChB,YAAY;wBACZ,mBAAmB;wBACnB,OAAO;wBACP,mBAAmB;wBACnB,cAAc,EAAE;wBAChB,UAAU;wBACV,oBAAoB;wBACpB,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBACA;gBACF;gBAEA,IAAI,SAAS,MAAM;oBACjB,qBAAqB;oBACrB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;oBAET,IAAI,cAAc;wBAChB,QAAQ,IAAI,CAAC,oCAAoC,aAAa,OAAO;wBACrE;oBACF;oBAEA,IAAI,SAAS;wBACX,qBAAqB;wBACrB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;;gBAWT,CAAC,EACA,EAAE,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE;wBAEhC,MAAM,mBAAmB,cAAc,IAAI,CAAA,KAAM,CAAC;gCAChD,GAAG,GAAG,YAAY;gCAClB,aAAa,GAAG,WAAW;4BAC7B,CAAC,MAAM,EAAE;wBAET,MAAM,OAAO,CAAC;4BACZ,IAAI,QAAQ,EAAE;4BACd,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,UAAU,QAAQ,QAAQ;4BAC1B,KAAK,QAAQ,GAAG;4BAChB,UAAU,QAAQ,QAAQ;4BAC1B,gBAAgB,QAAQ,cAAc;4BACtC,YAAY,QAAQ,UAAU;4BAC9B,mBAAmB,QAAQ,iBAAiB;4BAC5C,OAAO,QAAQ,KAAK;4BACpB,mBAAmB,QAAQ,iBAAiB;4BAC5C,cAAc;4BACd,UAAU,QAAQ,QAAQ;4BAC1B,oBAAoB,QAAQ,kBAAkB;4BAC9C,YAAY,QAAQ,UAAU;4BAC9B,YAAY,QAAQ,UAAU;wBAChC;oBACF;gBACF;gBAEA,0BAA0B;gBAC1B,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;oBAC5C,IAAI,UAAU,gBAAgB,CAAC,SAAS;wBACtC,MAAM,aAAa;oBACrB,OAAO,IAAI,UAAU,eAAe,SAAS,MAAM;wBACjD,mCAAmC;wBACnC,MAAM,cAAc;oBACtB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,QAAQ,IAAI,CAAC;gBAEb,gCAAgC;gBAChC,MAAM,OAAO,CAAC;oBACZ,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,KAAK;oBACL,UAAU;oBACV,gBAAgB;oBAChB,YAAY;oBACZ,mBAAmB;oBACnB,OAAO;oBACP,mBAAmB;oBACnB,cAAc,EAAE;oBAChB,UAAU;oBACV,oBAAoB;oBACpB,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;YAC9B,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH;AAIJ,mBAAmB;AACnB,SAAS,eAAe,UAAkB;IACxC,MAAM,mBAAmB;QACvB;QAAG;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAChE;IAED,IAAK,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACrD,IAAI,cAAc,gBAAgB,CAAC,EAAE,EAAE;YACrC,OAAO,IAAI;QACb;IACF;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAoB,EAAE,KAAU;IAC7D,MAAM,eAA8B,EAAE;IAEtC,yBAAyB;IACzB,IAAI,MAAM,cAAc,CAAC,MAAM,KAAK,GAAG;QACrC,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,0BAA0B;IAC1B,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,SAAS,KAAK,oBAAoB,QAAQ,gBAAgB,IAAI,IAAI;QAC5E,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,iBAAiB,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/auth/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport default function AuthProvider({ children }: AuthProviderProps) {\n  const { initializeAuth } = useUserStore()\n\n  useEffect(() => {\n    initializeAuth()\n  }, [initializeAuth])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASe,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAClE,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,qBAAO;kBAAG;;AACZ", "debugId": null}}]}