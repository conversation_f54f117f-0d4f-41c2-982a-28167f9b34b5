import { GameType, TradingPair } from '@/types'

// Game Configuration
export const GAME_CONFIGS = {
  scalper_sprint: {
    name: 'Scalper Sprint',
    description: '60-second trading challenges with rapid-fire decisions',
    difficulty: 'beginner',
    duration_seconds: 60,
    starting_balance: 10000,
    min_trade_size: 100,
    max_positions: 3,
    quest_coins_base: 50,
  },
  candle_strike: {
    name: 'CandleStrike',
    description: 'Pattern recognition game with candlestick charts',
    difficulty: 'beginner',
    duration_seconds: 120,
    starting_balance: 0, // Pattern recognition, no trading
    patterns_to_identify: 5,
    quest_coins_base: 75,
  },
  chain_maze: {
    name: 'ChainMaze',
    description: 'Navigate blockchain puzzles and learn consensus mechanisms',
    difficulty: 'intermediate',
    duration_seconds: 300,
    starting_balance: 1000, // Gas fees simulation
    puzzles_to_solve: 3,
    quest_coins_base: 100,
  },
  swing_trader_odyssey: {
    name: "Swing Trader's Odyssey",
    description: 'Multi-day position management with risk/reward balancing',
    difficulty: 'intermediate',
    duration_seconds: 600, // 10 minutes simulating days
    starting_balance: 50000,
    max_positions: 5,
    quest_coins_base: 150,
  },
  day_trader_arena: {
    name: 'Day Trader Arena',
    description: 'Real-time multiplayer trading competitions',
    difficulty: 'advanced',
    duration_seconds: 900, // 15 minutes
    starting_balance: 100000,
    max_positions: 10,
    quest_coins_base: 200,
  },
  portfolio_survivor: {
    name: 'Portfolio Survivor',
    description: 'Crisis management with diversification challenges',
    difficulty: 'advanced',
    duration_seconds: 1200, // 20 minutes
    starting_balance: 500000,
    max_positions: 20,
    quest_coins_base: 300,
  },
} as const

// Trading Pairs
export const TRADING_PAIRS: TradingPair[] = [
  { base: 'BTC', quote: 'USD', symbol: 'BTCUSD', exchange: 'virtual' },
  { base: 'ETH', quote: 'USD', symbol: 'ETHUSD', exchange: 'virtual' },
  { base: 'ADA', quote: 'USD', symbol: 'ADAUSD', exchange: 'virtual' },
  { base: 'SOL', quote: 'USD', symbol: 'SOLUSD', exchange: 'virtual' },
  { base: 'AAPL', quote: 'USD', symbol: 'AAPL', exchange: 'virtual' },
  { base: 'GOOGL', quote: 'USD', symbol: 'GOOGL', exchange: 'virtual' },
  { base: 'TSLA', quote: 'USD', symbol: 'TSLA', exchange: 'virtual' },
  { base: 'EUR', quote: 'USD', symbol: 'EURUSD', exchange: 'virtual' },
  { base: 'GBP', quote: 'USD', symbol: 'GBPUSD', exchange: 'virtual' },
  { base: 'JPY', quote: 'USD', symbol: 'JPYUSD', exchange: 'virtual' },
]

// Achievement Categories and Points
export const ACHIEVEMENT_CATEGORIES = {
  trading: {
    name: 'Trading Mastery',
    color: '#10B981',
    icon: '📈',
  },
  learning: {
    name: 'Knowledge Seeker',
    color: '#3B82F6',
    icon: '🎓',
  },
  social: {
    name: 'Community Builder',
    color: '#8B5CF6',
    icon: '👥',
  },
  special: {
    name: 'Special Events',
    color: '#F59E0B',
    icon: '⭐',
  },
} as const

// Level System
export const LEVEL_THRESHOLDS = [
  0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,
  13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,
]

export const QUEST_COIN_MULTIPLIERS = {
  beginner: 1.0,
  intermediate: 1.5,
  advanced: 2.0,
} as const

// UI Constants
export const INTERFACE_MODES = {
  adolescent: {
    name: 'Adventure Mode',
    description: 'Fantasy-themed interface with quests and adventures',
    primaryColor: '#8B5CF6',
    secondaryColor: '#EC4899',
    fontFamily: 'fantasy',
  },
  adult: {
    name: 'Professional Mode',
    description: 'Bloomberg Terminal-style professional interface',
    primaryColor: '#1F2937',
    secondaryColor: '#3B82F6',
    fontFamily: 'monospace',
  },
} as const

// Market Data Update Intervals
export const UPDATE_INTERVALS = {
  real_time: 1000, // 1 second
  fast: 5000, // 5 seconds
  normal: 15000, // 15 seconds
  slow: 60000, // 1 minute
} as const

// API Endpoints
export const API_ENDPOINTS = {
  coingecko: {
    base: 'https://api.coingecko.com/api/v3',
    prices: '/simple/price',
    history: '/coins/{id}/market_chart',
  },
  alpha_vantage: {
    base: 'https://www.alphavantage.co/query',
    intraday: '?function=TIME_SERIES_INTRADAY',
    forex: '?function=FX_INTRADAY',
  },
} as const

// Validation Rules
export const VALIDATION_RULES = {
  username: {
    minLength: 3,
    maxLength: 20,
    pattern: /^[a-zA-Z0-9_-]+$/,
  },
  age: {
    min: 13,
    max: 120,
  },
  trade: {
    minAmount: 1,
    maxAmount: 1000000,
  },
} as const

// Error Messages
export const ERROR_MESSAGES = {
  auth: {
    invalid_credentials: 'Invalid email or password',
    user_not_found: 'User not found',
    email_already_exists: 'Email already registered',
    weak_password: 'Password must be at least 8 characters',
    age_verification_failed: 'Age verification required',
  },
  game: {
    session_expired: 'Game session has expired',
    invalid_trade: 'Invalid trade parameters',
    insufficient_balance: 'Insufficient balance for this trade',
    max_positions_reached: 'Maximum number of positions reached',
  },
  general: {
    network_error: 'Network error, please try again',
    server_error: 'Server error, please try again later',
    validation_error: 'Please check your input and try again',
  },
} as const

// Success Messages
export const SUCCESS_MESSAGES = {
  auth: {
    registration_complete: 'Account created successfully!',
    login_success: 'Welcome back!',
    logout_success: 'Logged out successfully',
  },
  game: {
    session_complete: 'Game session completed!',
    achievement_unlocked: 'Achievement unlocked!',
    level_up: 'Level up! Congratulations!',
  },
  general: {
    save_success: 'Changes saved successfully',
    update_success: 'Updated successfully',
  },
} as const
