{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number, decimals: number = 2): string {\n  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`\n}\n\nexport function formatNumber(value: number, decimals: number = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(value)\n}\n\nexport function formatLargeNumber(value: number): string {\n  if (value >= 1e9) {\n    return `${(value / 1e9).toFixed(1)}B`\n  }\n  if (value >= 1e6) {\n    return `${(value / 1e6).toFixed(1)}M`\n  }\n  if (value >= 1e3) {\n    return `${(value / 1e3).toFixed(1)}K`\n  }\n  return value.toString()\n}\n\nexport function calculatePnL(entryPrice: number, currentPrice: number, quantity: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity\n}\n\nexport function calculatePnLPercentage(entryPrice: number, currentPrice: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  const percentage = (priceDiff / entryPrice) * 100\n  return side === 'buy' ? percentage : -percentage\n}\n\nexport function generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n}\n\nexport function isMinor(age: number): boolean {\n  return age < 18\n}\n\nexport function validateAge(age: number): boolean {\n  return age >= 13 && age <= 120\n}\n\nexport function sanitizeUsername(username: string): string {\n  return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase()\n}\n\nexport function getTimeRemaining(endTime: Date): {\n  total: number\n  days: number\n  hours: number\n  minutes: number\n  seconds: number\n} {\n  const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString())\n  const seconds = Math.floor((total / 1000) % 60)\n  const minutes = Math.floor((total / 1000 / 60) % 60)\n  const hours = Math.floor((total / (1000 * 60 * 60)) % 24)\n  const days = Math.floor(total / (1000 * 60 * 60 * 24))\n\n  return {\n    total,\n    days,\n    hours,\n    minutes,\n    seconds,\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n\nexport function getRandomElement<T>(array: T[]): T {\n  return array[Math.floor(Math.random() * array.length)]\n}\n\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array]\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1))\n    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]\n  }\n  return shuffled\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\nexport function generateColor(seed: string): string {\n  let hash = 0\n  for (let i = 0; i < seed.length; i++) {\n    hash = seed.charCodeAt(i) + ((hash << 5) - hash)\n  }\n  const hue = hash % 360\n  return `hsl(${hue}, 70%, 50%)`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa,EAAE,WAAmB,CAAC;IAClE,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC;AAC9D;AAEO,SAAS,aAAa,KAAa,EAAE,WAAmB,CAAC;IAC9D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,KAAa;IAC7C,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,aAAa,UAAkB,EAAE,YAAoB,EAAE,QAAgB,EAAE,IAAoB;IAC3G,MAAM,YAAY,eAAe;IACjC,OAAO,SAAS,QAAQ,YAAY,WAAW,CAAC,YAAY;AAC9D;AAEO,SAAS,uBAAuB,UAAkB,EAAE,YAAoB,EAAE,IAAoB;IACnG,MAAM,YAAY,eAAe;IACjC,MAAM,aAAa,AAAC,YAAY,aAAc;IAC9C,OAAO,SAAS,QAAQ,aAAa,CAAC;AACxC;AAEO,SAAS;IACd,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,MAAM;AACf;AAEO,SAAS,YAAY,GAAW;IACrC,OAAO,OAAO,MAAM,OAAO;AAC7B;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,OAAO,CAAC,mBAAmB,IAAI,WAAW;AAC5D;AAEO,SAAS,iBAAiB,OAAa;IAO5C,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,QAAQ,MAAM,KAAK,KAAK,CAAC,IAAI,OAAO,QAAQ;IAC7E,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAQ;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAO,KAAM;IACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,KAAK,EAAE,IAAK;IACtD,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;IAEpD,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,iBAAoB,KAAU;IAC5C,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAEO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC1C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IAC1D;IACA,OAAO;AACT;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,OAAO,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI;IACjD;IACA,MAAM,MAAM,OAAO;IACnB,OAAO,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/constants.ts"], "sourcesContent": ["import { GameType, TradingPair } from '@/types'\n\n// Game Configuration\nexport const GAME_CONFIGS = {\n  scalper_sprint: {\n    name: 'Scalper Sprint',\n    description: '60-second trading challenges with rapid-fire decisions',\n    difficulty: 'beginner',\n    duration_seconds: 60,\n    starting_balance: 10000,\n    min_trade_size: 100,\n    max_positions: 3,\n    quest_coins_base: 50,\n  },\n  candle_strike: {\n    name: 'CandleStrike',\n    description: 'Pattern recognition game with candlestick charts',\n    difficulty: 'beginner',\n    duration_seconds: 120,\n    starting_balance: 0, // Pattern recognition, no trading\n    patterns_to_identify: 5,\n    quest_coins_base: 75,\n  },\n  chain_maze: {\n    name: 'ChainMaze',\n    description: 'Navigate blockchain puzzles and learn consensus mechanisms',\n    difficulty: 'intermediate',\n    duration_seconds: 300,\n    starting_balance: 1000, // Gas fees simulation\n    puzzles_to_solve: 3,\n    quest_coins_base: 100,\n  },\n  swing_trader_odyssey: {\n    name: \"Swing Trader's Odyssey\",\n    description: 'Multi-day position management with risk/reward balancing',\n    difficulty: 'intermediate',\n    duration_seconds: 600, // 10 minutes simulating days\n    starting_balance: 50000,\n    max_positions: 5,\n    quest_coins_base: 150,\n  },\n  day_trader_arena: {\n    name: 'Day Trader Arena',\n    description: 'Real-time multiplayer trading competitions',\n    difficulty: 'advanced',\n    duration_seconds: 900, // 15 minutes\n    starting_balance: 100000,\n    max_positions: 10,\n    quest_coins_base: 200,\n  },\n  portfolio_survivor: {\n    name: 'Portfolio Survivor',\n    description: 'Crisis management with diversification challenges',\n    difficulty: 'advanced',\n    duration_seconds: 1200, // 20 minutes\n    starting_balance: 500000,\n    max_positions: 20,\n    quest_coins_base: 300,\n  },\n} as const\n\n// Trading Pairs\nexport const TRADING_PAIRS: TradingPair[] = [\n  { base: 'BTC', quote: 'USD', symbol: 'BTCUSD', exchange: 'virtual' },\n  { base: 'ETH', quote: 'USD', symbol: 'ETHUSD', exchange: 'virtual' },\n  { base: 'ADA', quote: 'USD', symbol: 'ADAUSD', exchange: 'virtual' },\n  { base: 'SOL', quote: 'USD', symbol: 'SOLUSD', exchange: 'virtual' },\n  { base: 'AAPL', quote: 'USD', symbol: 'AAPL', exchange: 'virtual' },\n  { base: 'GOOGL', quote: 'USD', symbol: 'GOOGL', exchange: 'virtual' },\n  { base: 'TSLA', quote: 'USD', symbol: 'TSLA', exchange: 'virtual' },\n  { base: 'EUR', quote: 'USD', symbol: 'EURUSD', exchange: 'virtual' },\n  { base: 'GBP', quote: 'USD', symbol: 'GBPUSD', exchange: 'virtual' },\n  { base: 'JPY', quote: 'USD', symbol: 'JPYUSD', exchange: 'virtual' },\n]\n\n// Achievement Categories and Points\nexport const ACHIEVEMENT_CATEGORIES = {\n  trading: {\n    name: 'Trading Mastery',\n    color: '#10B981',\n    icon: '📈',\n  },\n  learning: {\n    name: 'Knowledge Seeker',\n    color: '#3B82F6',\n    icon: '🎓',\n  },\n  social: {\n    name: 'Community Builder',\n    color: '#8B5CF6',\n    icon: '👥',\n  },\n  special: {\n    name: 'Special Events',\n    color: '#F59E0B',\n    icon: '⭐',\n  },\n} as const\n\n// Level System\nexport const LEVEL_THRESHOLDS = [\n  0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n  13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n]\n\nexport const QUEST_COIN_MULTIPLIERS = {\n  beginner: 1.0,\n  intermediate: 1.5,\n  advanced: 2.0,\n} as const\n\n// UI Constants\nexport const INTERFACE_MODES = {\n  adolescent: {\n    name: 'Adventure Mode',\n    description: 'Fantasy-themed interface with quests and adventures',\n    primaryColor: '#8B5CF6',\n    secondaryColor: '#EC4899',\n    fontFamily: 'fantasy',\n  },\n  adult: {\n    name: 'Professional Mode',\n    description: 'Bloomberg Terminal-style professional interface',\n    primaryColor: '#1F2937',\n    secondaryColor: '#3B82F6',\n    fontFamily: 'monospace',\n  },\n} as const\n\n// Market Data Update Intervals\nexport const UPDATE_INTERVALS = {\n  real_time: 1000, // 1 second\n  fast: 5000, // 5 seconds\n  normal: 15000, // 15 seconds\n  slow: 60000, // 1 minute\n} as const\n\n// API Endpoints\nexport const API_ENDPOINTS = {\n  coingecko: {\n    base: 'https://api.coingecko.com/api/v3',\n    prices: '/simple/price',\n    history: '/coins/{id}/market_chart',\n  },\n  alpha_vantage: {\n    base: 'https://www.alphavantage.co/query',\n    intraday: '?function=TIME_SERIES_INTRADAY',\n    forex: '?function=FX_INTRADAY',\n  },\n} as const\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  username: {\n    minLength: 3,\n    maxLength: 20,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n  },\n  age: {\n    min: 13,\n    max: 120,\n  },\n  trade: {\n    minAmount: 1,\n    maxAmount: 1000000,\n  },\n} as const\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  auth: {\n    invalid_credentials: 'Invalid email or password',\n    user_not_found: 'User not found',\n    email_already_exists: 'Email already registered',\n    weak_password: 'Password must be at least 8 characters',\n    age_verification_failed: 'Age verification required',\n  },\n  game: {\n    session_expired: 'Game session has expired',\n    invalid_trade: 'Invalid trade parameters',\n    insufficient_balance: 'Insufficient balance for this trade',\n    max_positions_reached: 'Maximum number of positions reached',\n  },\n  general: {\n    network_error: 'Network error, please try again',\n    server_error: 'Server error, please try again later',\n    validation_error: 'Please check your input and try again',\n  },\n} as const\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  auth: {\n    registration_complete: 'Account created successfully!',\n    login_success: 'Welcome back!',\n    logout_success: 'Logged out successfully',\n  },\n  game: {\n    session_complete: 'Game session completed!',\n    achievement_unlocked: 'Achievement unlocked!',\n    level_up: 'Level up! Congratulations!',\n  },\n  general: {\n    save_success: 'Changes saved successfully',\n    update_success: 'Updated successfully',\n  },\n} as const\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,MAAM,eAAe;IAC1B,gBAAgB;QACd,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,kBAAkB;IACpB;IACA,eAAe;QACb,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,sBAAsB;QACtB,kBAAkB;IACpB;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;IACpB;IACA,sBAAsB;QACpB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,kBAAkB;QAChB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;AACF;AAGO,MAAM,gBAA+B;IAC1C;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAS,OAAO;QAAO,QAAQ;QAAS,UAAU;IAAU;IACpE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;CACpE;AAGM,MAAM,yBAAyB;IACpC,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;AACF;AAGO,MAAM,mBAAmB;IAC9B;IAAG;IAAK;IAAK;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACtD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;CAChE;AAEM,MAAM,yBAAyB;IACpC,UAAU;IACV,cAAc;IACd,UAAU;AACZ;AAGO,MAAM,kBAAkB;IAC7B,YAAY;QACV,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;IACA,OAAO;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAGO,MAAM,gBAAgB;IAC3B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,KAAK;QACH,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,WAAW;QACX,WAAW;IACb;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,qBAAqB;QACrB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,yBAAyB;IAC3B;IACA,MAAM;QACJ,iBAAiB;QACjB,eAAe;QACf,sBAAsB;QACtB,uBAAuB;IACzB;IACA,SAAS;QACP,eAAe;QACf,cAAc;QACd,kBAAkB;IACpB;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,uBAAuB;QACvB,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM;QACJ,kBAAkB;QAClB,sBAAsB;QACtB,UAAU;IACZ;IACA,SAAS;QACP,cAAc;QACd,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/services/market-data.ts"], "sourcesContent": ["import axios from 'axios'\nimport { MarketData, CandlestickData } from '@/types'\nimport { API_ENDPOINTS } from '@/lib/constants'\n\nclass MarketDataService {\n  private coingeckoClient: any\n  private alphaVantageClient: any\n\n  constructor() {\n    this.coingeckoClient = axios.create({\n      baseURL: API_ENDPOINTS.coingecko.base,\n      timeout: 10000,\n    })\n\n    this.alphaVantageClient = axios.create({\n      baseURL: API_ENDPOINTS.alpha_vantage.base,\n      timeout: 10000,\n    })\n  }\n\n  // Cryptocurrency data from CoinGecko\n  async getCryptoPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const ids = symbols.map(symbol => this.symbolToCoinGeckoId(symbol)).join(',')\n      const response = await this.coingeckoClient.get(API_ENDPOINTS.coingecko.prices, {\n        params: {\n          ids,\n          vs_currencies: 'usd',\n          include_24hr_change: true,\n          include_24hr_vol: true,\n          include_market_cap: true,\n        },\n      })\n\n      return this.formatCoinGeckoResponse(response.data, symbols)\n    } catch (error) {\n      console.error('Error fetching crypto prices:', error)\n      return this.generateMockCryptoData(symbols)\n    }\n  }\n\n  // Stock data from Alpha Vantage\n  async getStockPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const promises = symbols.map(symbol => this.fetchStockPrice(symbol))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching stock prices:', error)\n      return this.generateMockStockData(symbols)\n    }\n  }\n\n  // Forex data from Alpha Vantage\n  async getForexPrices(pairs: string[]): Promise<MarketData[]> {\n    try {\n      const promises = pairs.map(pair => this.fetchForexPrice(pair))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching forex prices:', error)\n      return this.generateMockForexData(pairs)\n    }\n  }\n\n  // Historical candlestick data with enhanced pattern detection\n  async getCandlestickData(symbol: string, interval: string = '1h', days: number = 7): Promise<CandlestickData[]> {\n    try {\n      if (this.isCryptoSymbol(symbol)) {\n        return await this.getCryptoCandlestickData(symbol, days)\n      } else {\n        return await this.getStockCandlestickData(symbol, interval)\n      }\n    } catch (error) {\n      console.error('Error fetching candlestick data:', error)\n      return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data\n    }\n  }\n\n  // Get historical data with specific patterns for educational purposes\n  async getHistoricalDataWithPatterns(symbol: string, patternType: string, count: number = 10): Promise<CandlestickData[][]> {\n    try {\n      // For demo purposes, we'll use a combination of real data and pattern-enhanced data\n      const baseData = await this.getCandlestickData(symbol, '1h', 30) // 30 days of data\n\n      // Find or create segments with the requested pattern\n      return this.extractPatternSegments(baseData, patternType, count)\n    } catch (error) {\n      console.error('Error fetching pattern data:', error)\n      return this.generatePatternDatasets(symbol, patternType, count)\n    }\n  }\n\n  // TradingView-style data format\n  async getTradingViewData(symbol: string, resolution: string = '60', from: number, to: number): Promise<{\n    s: string\n    t: number[]\n    o: number[]\n    h: number[]\n    l: number[]\n    c: number[]\n    v: number[]\n  }> {\n    try {\n      const data = await this.getCandlestickData(symbol, '1h', 7)\n\n      return {\n        s: 'ok',\n        t: data.map(d => Math.floor(d.timestamp / 1000)),\n        o: data.map(d => d.open),\n        h: data.map(d => d.high),\n        l: data.map(d => d.low),\n        c: data.map(d => d.close),\n        v: data.map(d => d.volume),\n      }\n    } catch (error) {\n      return {\n        s: 'error',\n        t: [],\n        o: [],\n        h: [],\n        l: [],\n        c: [],\n        v: [],\n      }\n    }\n  }\n\n  // Private helper methods\n  private async fetchStockPrice(symbol: string): Promise<MarketData | null> {\n    try {\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'GLOBAL_QUOTE',\n          symbol,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const quote = response.data['Global Quote']\n      if (!quote) return null\n\n      return {\n        symbol,\n        price: parseFloat(quote['05. price']),\n        change_24h: parseFloat(quote['09. change']),\n        change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),\n        volume_24h: parseFloat(quote['06. volume']),\n        timestamp: new Date().toISOString(),\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async fetchForexPrice(pair: string): Promise<MarketData | null> {\n    try {\n      const [from, to] = pair.split('/')\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'CURRENCY_EXCHANGE_RATE',\n          from_currency: from,\n          to_currency: to,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const rate = response.data['Realtime Currency Exchange Rate']\n      if (!rate) return null\n\n      return {\n        symbol: pair,\n        price: parseFloat(rate['5. Exchange Rate']),\n        change_24h: 0, // Alpha Vantage doesn't provide 24h change for forex\n        change_percentage_24h: 0,\n        volume_24h: 0,\n        timestamp: rate['6. Last Refreshed'],\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async getCryptoCandlestickData(symbol: string, days: number): Promise<CandlestickData[]> {\n    const id = this.symbolToCoinGeckoId(symbol)\n    const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {\n      params: {\n        vs_currency: 'usd',\n        days,\n        interval: 'hourly',\n      },\n    })\n\n    const prices = response.data.prices\n    const volumes = response.data.total_volumes\n\n    return prices.map((price: [number, number], index: number) => ({\n      timestamp: price[0],\n      open: index > 0 ? prices[index - 1][1] : price[1],\n      high: price[1] * (1 + Math.random() * 0.02), // Simulate high\n      low: price[1] * (1 - Math.random() * 0.02), // Simulate low\n      close: price[1],\n      volume: volumes[index] ? volumes[index][1] : 0,\n    }))\n  }\n\n  private async getStockCandlestickData(symbol: string, interval: string): Promise<CandlestickData[]> {\n    const response = await this.alphaVantageClient.get('', {\n      params: {\n        function: 'TIME_SERIES_INTRADAY',\n        symbol,\n        interval,\n        apikey: process.env.ALPHA_VANTAGE_API_KEY,\n      },\n    })\n\n    const timeSeries = response.data[`Time Series (${interval})`]\n    if (!timeSeries) return []\n\n    return Object.entries(timeSeries).map(([timestamp, data]: [string, any]) => ({\n      timestamp: new Date(timestamp).getTime(),\n      open: parseFloat(data['1. open']),\n      high: parseFloat(data['2. high']),\n      low: parseFloat(data['3. low']),\n      close: parseFloat(data['4. close']),\n      volume: parseFloat(data['5. volume']),\n    }))\n  }\n\n  private symbolToCoinGeckoId(symbol: string): string {\n    const mapping: Record<string, string> = {\n      BTC: 'bitcoin',\n      ETH: 'ethereum',\n      ADA: 'cardano',\n      SOL: 'solana',\n      DOT: 'polkadot',\n      LINK: 'chainlink',\n      UNI: 'uniswap',\n      MATIC: 'polygon',\n    }\n    return mapping[symbol.toUpperCase()] || symbol.toLowerCase()\n  }\n\n  private isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.includes(symbol.toUpperCase())\n  }\n\n  private formatCoinGeckoResponse(data: any, symbols: string[]): MarketData[] {\n    return symbols.map(symbol => {\n      const id = this.symbolToCoinGeckoId(symbol)\n      const coinData = data[id]\n      \n      if (!coinData) return this.generateMockCryptoData([symbol])[0]\n\n      return {\n        symbol,\n        price: coinData.usd,\n        change_24h: coinData.usd_24h_change || 0,\n        change_percentage_24h: coinData.usd_24h_change || 0,\n        volume_24h: coinData.usd_24h_vol || 0,\n        market_cap: coinData.usd_market_cap,\n        timestamp: new Date().toISOString(),\n      }\n    })\n  }\n\n  // Mock data generators for development and fallback\n  private generateMockCryptoData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      BTC: 45000,\n      ETH: 3000,\n      ADA: 0.5,\n      SOL: 100,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 1000,\n      change_percentage_24h: (Math.random() - 0.5) * 10,\n      volume_24h: Math.random() * 1000000000,\n      market_cap: Math.random() * 100000000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockStockData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      AAPL: 150,\n      GOOGL: 2500,\n      TSLA: 800,\n      MSFT: 300,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 20,\n      change_percentage_24h: (Math.random() - 0.5) * 5,\n      volume_24h: Math.random() * 100000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockForexData(pairs: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      'EUR/USD': 1.1,\n      'GBP/USD': 1.3,\n      'USD/JPY': 110,\n      'USD/CHF': 0.9,\n    }\n\n    return pairs.map(pair => ({\n      symbol: pair,\n      price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),\n      change_24h: (Math.random() - 0.5) * 0.01,\n      change_percentage_24h: (Math.random() - 0.5) * 1,\n      volume_24h: 0,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockCandlestickData(symbol: string, count: number): CandlestickData[] {\n    const data: CandlestickData[] = []\n    let price = 100 + Math.random() * 900\n    const now = Date.now()\n\n    for (let i = 0; i < count; i++) {\n      const timestamp = now - (count - i) * 3600000 // Hourly intervals\n      const change = (Math.random() - 0.5) * 10\n      const open = price\n      const close = price + change\n      const high = Math.max(open, close) + Math.random() * 5\n      const low = Math.min(open, close) - Math.random() * 5\n      const volume = Math.random() * 1000000\n\n      data.push({\n        timestamp,\n        open,\n        high,\n        low,\n        close,\n        volume,\n      })\n\n      price = close\n    }\n\n    return data\n  }\n\n  // Extract segments containing specific patterns from real data\n  extractPatternSegments(data: CandlestickData[], patternType: string, count: number): CandlestickData[][] {\n    const segments: CandlestickData[][] = []\n    const segmentLength = 20 // 20 candles per segment\n\n    // Scan through data looking for patterns\n    for (let i = 0; i <= data.length - segmentLength && segments.length < count; i++) {\n      const segment = data.slice(i, i + segmentLength)\n\n      if (this.containsPattern(segment, patternType)) {\n        segments.push(segment)\n        i += segmentLength - 1 // Skip ahead to avoid overlapping segments\n      }\n    }\n\n    // If we don't have enough real patterns, generate some\n    while (segments.length < count) {\n      segments.push(this.generateSegmentWithPattern(patternType, segmentLength))\n    }\n\n    return segments\n  }\n\n  // Check if a segment contains a specific pattern\n  private containsPattern(segment: CandlestickData[], patternType: string): boolean {\n    switch (patternType) {\n      case 'hammer':\n        return this.detectHammer(segment)\n      case 'doji':\n        return this.detectDoji(segment)\n      case 'engulfing_bullish':\n        return this.detectBullishEngulfing(segment)\n      case 'engulfing_bearish':\n        return this.detectBearishEngulfing(segment)\n      case 'morning_star':\n        return this.detectMorningStar(segment)\n      case 'evening_star':\n        return this.detectEveningStar(segment)\n      default:\n        return false\n    }\n  }\n\n  // Pattern detection algorithms\n  private detectHammer(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length - 1; i++) {\n      const candle = segment[i]\n      const bodySize = Math.abs(candle.close - candle.open)\n      const lowerShadow = Math.min(candle.open, candle.close) - candle.low\n      const upperShadow = candle.high - Math.max(candle.open, candle.close)\n      const totalRange = candle.high - candle.low\n\n      // Hammer criteria: small body, long lower shadow, small upper shadow\n      if (bodySize < totalRange * 0.3 &&\n          lowerShadow > bodySize * 2 &&\n          upperShadow < bodySize * 0.5) {\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectDoji(segment: CandlestickData[]): boolean {\n    for (let i = 0; i < segment.length; i++) {\n      const candle = segment[i]\n      const bodySize = Math.abs(candle.close - candle.open)\n      const totalRange = candle.high - candle.low\n\n      // Doji criteria: very small body relative to total range\n      if (bodySize < totalRange * 0.1 && totalRange > 0) {\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectBullishEngulfing(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length; i++) {\n      const prev = segment[i - 1]\n      const curr = segment[i]\n\n      // Previous candle is bearish, current is bullish and engulfs previous\n      if (prev.close < prev.open && // Previous bearish\n          curr.close > curr.open && // Current bullish\n          curr.open < prev.close && // Current opens below previous close\n          curr.close > prev.open) { // Current closes above previous open\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectBearishEngulfing(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length; i++) {\n      const prev = segment[i - 1]\n      const curr = segment[i]\n\n      // Previous candle is bearish, current is bullish and engulfs previous\n      if (prev.close > prev.open && // Previous bullish\n          curr.close < curr.open && // Current bearish\n          curr.open > prev.close && // Current opens above previous close\n          curr.close < prev.open) { // Current closes below previous open\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectMorningStar(segment: CandlestickData[]): boolean {\n    for (let i = 2; i < segment.length; i++) {\n      const first = segment[i - 2]\n      const second = segment[i - 1]\n      const third = segment[i]\n\n      // Three candle pattern: bearish, small body, bullish\n      if (first.close < first.open && // First bearish\n          Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small\n          third.close > third.open && // Third bullish\n          third.close > (first.open + first.close) / 2) { // Third closes above midpoint of first\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectEveningStar(segment: CandlestickData[]): boolean {\n    for (let i = 2; i < segment.length; i++) {\n      const first = segment[i - 2]\n      const second = segment[i - 1]\n      const third = segment[i]\n\n      // Three candle pattern: bullish, small body, bearish\n      if (first.close > first.open && // First bullish\n          Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small\n          third.close < third.open && // Third bearish\n          third.close < (first.open + first.close) / 2) { // Third closes below midpoint of first\n        return true\n      }\n    }\n    return false\n  }\n\n  // Generate a segment with a specific pattern\n  private generateSegmentWithPattern(patternType: string, length: number): CandlestickData[] {\n    const segment: CandlestickData[] = []\n    let currentPrice = 100 + Math.random() * 50\n    const now = Date.now()\n\n    // Generate leading candles\n    const patternPosition = Math.floor(length * 0.4) + Math.floor(Math.random() * Math.floor(length * 0.3))\n\n    for (let i = 0; i < patternPosition; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i, now)\n      segment.push(candle)\n      currentPrice = candle.close\n    }\n\n    // Generate pattern candles\n    const patternCandles = this.generateSpecificPattern(patternType, currentPrice, patternPosition, now)\n    segment.push(...patternCandles)\n    currentPrice = patternCandles[patternCandles.length - 1].close\n\n    // Generate trailing candles\n    for (let i = patternPosition + patternCandles.length; i < length; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i, now)\n      segment.push(candle)\n      currentPrice = candle.close\n    }\n\n    return segment\n  }\n\n  private generateSpecificPattern(patternType: string, startPrice: number, startIndex: number, baseTime: number): CandlestickData[] {\n    switch (patternType) {\n      case 'hammer':\n        return this.generateHammerCandle(startPrice, startIndex, baseTime)\n      case 'doji':\n        return this.generateDojiCandle(startPrice, startIndex, baseTime)\n      case 'engulfing_bullish':\n        return this.generateBullishEngulfingPattern(startPrice, startIndex, baseTime)\n      case 'engulfing_bearish':\n        return this.generateBearishEngulfingPattern(startPrice, startIndex, baseTime)\n      case 'morning_star':\n        return this.generateMorningStarPattern(startPrice, startIndex, baseTime)\n      case 'evening_star':\n        return this.generateEveningStarPattern(startPrice, startIndex, baseTime)\n      default:\n        return this.generateHammerCandle(startPrice, startIndex, baseTime)\n    }\n  }\n}\n\nexport const marketDataService = new MarketDataService()\n"], "names": [], "mappings": ";;;AAuIkB;AAvIlB;AAEA;;;AAEA,MAAM;IACI,gBAAoB;IACpB,mBAAuB;IAE/B,aAAc;QACZ,IAAI,CAAC,eAAe,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAClC,SAAS,0HAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI;YACrC,SAAS;QACX;QAEA,IAAI,CAAC,kBAAkB,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACrC,SAAS,0HAAA,CAAA,gBAAa,CAAC,aAAa,CAAC,IAAI;YACzC,SAAS;QACX;IACF;IAEA,qCAAqC;IACrC,MAAM,gBAAgB,OAAiB,EAAyB;QAC9D,IAAI;YACF,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC;YACzE,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,0HAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC9E,QAAQ;oBACN;oBACA,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;oBAClB,oBAAoB;gBACtB;YACF;YAEA,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,IAAI,EAAE;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACrC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,OAAiB,EAAyB;QAC7D,IAAI;YACF,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,eAAe,CAAC;YAC5D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,KAAe,EAAyB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,eAAe,CAAC;YACxD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,8DAA8D;IAC9D,MAAM,mBAAmB,MAAc,EAAE,WAAmB,IAAI,EAAE,OAAe,CAAC,EAA8B;QAC9G,IAAI;YACF,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;gBAC/B,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ;YACrD,OAAO;gBACL,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,KAAK,wBAAwB;;QAC/E;IACF;IAEA,sEAAsE;IACtE,MAAM,8BAA8B,MAAc,EAAE,WAAmB,EAAE,QAAgB,EAAE,EAAgC;QACzH,IAAI;YACF,oFAAoF;YACpF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,MAAM,IAAI,kBAAkB;;YAEnF,qDAAqD;YACrD,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU,aAAa;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,aAAa;QAC3D;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAmB,MAAc,EAAE,aAAqB,IAAI,EAAE,IAAY,EAAE,EAAU,EAQzF;QACD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,MAAM;YAEzD,OAAO;gBACL,GAAG;gBACH,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,KAAK,KAAK,CAAC,EAAE,SAAS,GAAG;gBAC1C,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACvB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACvB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gBACtB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACxB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,GAAG;gBACH,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;YACP;QACF;IACF;IAEA,yBAAyB;IACzB,MAAc,gBAAgB,MAAc,EAA8B;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV;oBACA,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,QAAQ,SAAS,IAAI,CAAC,eAAe;YAC3C,IAAI,CAAC,OAAO,OAAO;YAEnB,OAAO;gBACL;gBACA,OAAO,WAAW,KAAK,CAAC,YAAY;gBACpC,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,uBAAuB,WAAW,KAAK,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK;gBAC3E,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,gBAAgB,IAAY,EAA8B;QACtE,IAAI;YACF,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC;YAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV,eAAe;oBACf,aAAa;oBACb,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,kCAAkC;YAC7D,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO;gBACL,QAAQ;gBACR,OAAO,WAAW,IAAI,CAAC,mBAAmB;gBAC1C,YAAY;gBACZ,uBAAuB;gBACvB,YAAY;gBACZ,WAAW,IAAI,CAAC,oBAAoB;YACtC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,yBAAyB,MAAc,EAAE,IAAY,EAA8B;QAC/F,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;QACpC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,EAAE;YAC3E,QAAQ;gBACN,aAAa;gBACb;gBACA,UAAU;YACZ;QACF;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,MAAM;QACnC,MAAM,UAAU,SAAS,IAAI,CAAC,aAAa;QAE3C,OAAO,OAAO,GAAG,CAAC,CAAC,OAAyB,QAAkB,CAAC;gBAC7D,WAAW,KAAK,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gBACjD,MAAM,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBAC1C,KAAK,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBACzC,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG;YAC/C,CAAC;IACH;IAEA,MAAc,wBAAwB,MAAc,EAAE,QAAgB,EAA8B;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;YACrD,QAAQ;gBACN,UAAU;gBACV;gBACA;gBACA,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;YAC3C;QACF;QAEA,MAAM,aAAa,SAAS,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,WAAW,KAAoB,GAAK,CAAC;gBAC3E,WAAW,IAAI,KAAK,WAAW,OAAO;gBACtC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,KAAK,WAAW,IAAI,CAAC,SAAS;gBAC9B,OAAO,WAAW,IAAI,CAAC,WAAW;gBAClC,QAAQ,WAAW,IAAI,CAAC,YAAY;YACtC,CAAC;IACH;IAEQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,UAAkC;YACtC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;IAC5D;IAEQ,eAAe,MAAc,EAAW;QAC9C,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,QAAQ,CAAC,OAAO,WAAW;IAClD;IAEQ,wBAAwB,IAAS,EAAE,OAAiB,EAAgB;QAC1E,OAAO,QAAQ,GAAG,CAAC,CAAA;YACjB,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;YACpC,MAAM,WAAW,IAAI,CAAC,GAAG;YAEzB,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBAAC;aAAO,CAAC,CAAC,EAAE;YAE9D,OAAO;gBACL;gBACA,OAAO,SAAS,GAAG;gBACnB,YAAY,SAAS,cAAc,IAAI;gBACvC,uBAAuB,SAAS,cAAc,IAAI;gBAClD,YAAY,SAAS,WAAW,IAAI;gBACpC,YAAY,SAAS,cAAc;gBACnC,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEA,oDAAoD;IAC5C,uBAAuB,OAAiB,EAAgB;QAC9D,MAAM,aAAqC;YACzC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,OAAiB,EAAgB;QAC7D,MAAM,aAAqC;YACzC,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,KAAe,EAAgB;QAC3D,MAAM,aAAqC;YACzC,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,QAAQ;gBACR,OAAO,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI;gBAC7D,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY;gBACZ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,4BAA4B,MAAc,EAAE,KAAa,EAAqB;QACpF,MAAM,OAA0B,EAAE;QAClC,IAAI,QAAQ,MAAM,KAAK,MAAM,KAAK;QAClC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,mBAAmB;;YACjE,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACvC,MAAM,OAAO;YACb,MAAM,QAAQ,QAAQ;YACtB,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACrD,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACpD,MAAM,SAAS,KAAK,MAAM,KAAK;YAE/B,KAAK,IAAI,CAAC;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,+DAA+D;IAC/D,uBAAuB,IAAuB,EAAE,WAAmB,EAAE,KAAa,EAAuB;QACvG,MAAM,WAAgC,EAAE;QACxC,MAAM,gBAAgB,GAAG,yBAAyB;;QAElD,yCAAyC;QACzC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,GAAG,iBAAiB,SAAS,MAAM,GAAG,OAAO,IAAK;YAChF,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI;YAElC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,cAAc;gBAC9C,SAAS,IAAI,CAAC;gBACd,KAAK,gBAAgB,EAAE,2CAA2C;;YACpE;QACF;QAEA,uDAAuD;QACvD,MAAO,SAAS,MAAM,GAAG,MAAO;YAC9B,SAAS,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,aAAa;QAC7D;QAEA,OAAO;IACT;IAEA,iDAAiD;IACzC,gBAAgB,OAA0B,EAAE,WAAmB,EAAW;QAChF,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,KAAK;gBACH,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IACvB,aAAa,OAA0B,EAAW;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAK;YAC3C,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;YACpD,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,GAAG;YACpE,MAAM,cAAc,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK;YACpE,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;YAE3C,qEAAqE;YACrE,IAAI,WAAW,aAAa,OACxB,cAAc,WAAW,KACzB,cAAc,WAAW,KAAK;gBAChC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,WAAW,OAA0B,EAAW;QACtD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;YACpD,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;YAE3C,yDAAyD;YACzD,IAAI,WAAW,aAAa,OAAO,aAAa,GAAG;gBACjD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,uBAAuB,OAA0B,EAAW;QAClE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE;YAC3B,MAAM,OAAO,OAAO,CAAC,EAAE;YAEvB,sEAAsE;YACtE,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,mBAAmB;YAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,kBAAkB;YAC5C,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;YAC/D,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,uBAAuB,OAA0B,EAAW;QAClE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE;YAC3B,MAAM,OAAO,OAAO,CAAC,EAAE;YAEvB,sEAAsE;YACtE,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,mBAAmB;YAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,kBAAkB;YAC5C,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;YAC/D,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,OAA0B,EAAW;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,SAAS,OAAO,CAAC,IAAI,EAAE;YAC7B,MAAM,QAAQ,OAAO,CAAC,EAAE;YAExB,qDAAqD;YACrD,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,OAAO,eAAe;YAClG,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG;gBAChD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,OAA0B,EAAW;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,SAAS,OAAO,CAAC,IAAI,EAAE;YAC7B,MAAM,QAAQ,OAAO,CAAC,EAAE;YAExB,qDAAqD;YACrD,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,OAAO,eAAe;YAClG,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG;gBAChD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,6CAA6C;IACrC,2BAA2B,WAAmB,EAAE,MAAc,EAAqB;QACzF,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe,MAAM,KAAK,MAAM,KAAK;QACzC,MAAM,MAAM,KAAK,GAAG;QAEpB,2BAA2B;QAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,SAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,CAAC,SAAS;QAElG,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG;YAC1D,QAAQ,IAAI,CAAC;YACb,eAAe,OAAO,KAAK;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,aAAa,cAAc,iBAAiB;QAChG,QAAQ,IAAI,IAAI;QAChB,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9D,4BAA4B;QAC5B,IAAK,IAAI,IAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,QAAQ,IAAK;YACrE,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG;YAC1D,QAAQ,IAAI,CAAC;YACb,eAAe,OAAO,KAAK;QAC7B;QAEA,OAAO;IACT;IAEQ,wBAAwB,WAAmB,EAAE,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAqB;QAChI,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,YAAY;YAC3D,KAAK;gBACH,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,YAAY;YACzD,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,YAAY;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,YAAY;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;YACjE,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;YACjE;gBACE,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,YAAY;QAC7D;IACF;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/base-game.ts"], "sourcesContent": ["import { GameConfig, GameState, Position, GameType } from '@/types'\nimport { generateSessionId, calculatePnL } from '@/lib/utils'\nimport { GAME_CONFIGS, QUEST_COIN_MULTIPLIERS } from '@/lib/constants'\nimport { marketDataService } from '@/lib/services/market-data'\n\nexport class BaseGame {\n  protected config: GameConfig\n  protected state: GameState\n  protected startTime: number\n  protected endTime: number\n  protected isActive: boolean = false\n  protected marketData: Map<string, number> = new Map()\n\n  constructor(gameType: GameType, difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    const gameConfig = GAME_CONFIGS[gameType]\n\n    this.config = {\n      type: gameType,\n      difficulty,\n      duration_seconds: gameConfig.duration_seconds,\n      starting_balance: gameConfig.starting_balance,\n      available_pairs: [], // Will be set by specific game implementations\n      special_rules: {},\n    }\n\n    this.state = {\n      session_id: generateSessionId(),\n      current_balance: this.config.starting_balance,\n      positions: [],\n      time_remaining: this.config.duration_seconds,\n      score: 0,\n      multiplier: QUEST_COIN_MULTIPLIERS[difficulty],\n    }\n\n    this.startTime = Date.now()\n    this.endTime = this.startTime + (this.config.duration_seconds * 1000)\n  }\n\n  // Methods that can be overridden by specific games\n  async initialize(): Promise<void> {\n    // Default implementation - can be overridden\n  }\n\n  update(): void {\n    // Default implementation - can be overridden\n  }\n\n  calculateScore(): number {\n    // Default implementation - can be overridden\n    return 0\n  }\n\n  getGameSpecificData(): any {\n    // Default implementation - can be overridden\n    return {}\n  }\n\n  // Common game lifecycle methods\n  async start(): Promise<void> {\n    await this.initialize()\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  pause(): void {\n    this.isActive = false\n  }\n\n  resume(): void {\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  end(): GameState {\n    this.isActive = false\n    this.state.score = this.calculateScore()\n    this.state.time_remaining = 0\n    return this.state\n  }\n\n  // Trading operations\n  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const currentPrice = this.marketData.get(symbol)\n    if (!currentPrice) return false\n\n    const tradeValue = currentPrice * quantity\n    const requiredBalance = side === 'buy' ? tradeValue : 0\n\n    if (this.state.current_balance < requiredBalance) {\n      return false // Insufficient balance\n    }\n\n    // Check position limits\n    const maxPositions = this.getMaxPositions()\n    if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {\n      return false // Too many positions\n    }\n\n    // Execute the trade\n    const position: Position = {\n      id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      symbol,\n      side,\n      quantity,\n      entry_price: currentPrice,\n      current_price: currentPrice,\n      pnl: 0,\n      timestamp: new Date().toISOString(),\n    }\n\n    // Update balance\n    if (side === 'buy') {\n      this.state.current_balance -= tradeValue\n    } else {\n      this.state.current_balance += tradeValue\n    }\n\n    // Add or update position\n    const existingPositionIndex = this.state.positions.findIndex(p => p.symbol === symbol)\n    if (existingPositionIndex >= 0) {\n      // Update existing position (average price calculation would go here)\n      this.state.positions[existingPositionIndex] = position\n    } else {\n      this.state.positions.push(position)\n    }\n\n    return true\n  }\n\n  async closePosition(positionId: string): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)\n    if (positionIndex === -1) return false\n\n    const position = this.state.positions[positionIndex]\n    const currentPrice = this.marketData.get(position.symbol)\n    if (!currentPrice) return false\n\n    // Calculate final P&L\n    const pnl = calculatePnL(position.entry_price, currentPrice, position.quantity, position.side)\n    \n    // Update balance with P&L\n    this.state.current_balance += pnl\n    if (position.side === 'sell') {\n      // Return the initial trade value for short positions\n      this.state.current_balance += position.entry_price * position.quantity\n    }\n\n    // Remove position\n    this.state.positions.splice(positionIndex, 1)\n\n    return true\n  }\n\n  // Market data updates\n  async updateMarketData(): Promise<void> {\n    try {\n      const symbols = this.config.available_pairs.map(pair => pair.symbol)\n      \n      // In a real implementation, you'd fetch from different services based on asset type\n      const cryptoSymbols = symbols.filter(s => this.isCryptoSymbol(s))\n      const stockSymbols = symbols.filter(s => this.isStockSymbol(s))\n      const forexSymbols = symbols.filter(s => this.isForexSymbol(s))\n\n      const [cryptoData, stockData, forexData] = await Promise.all([\n        cryptoSymbols.length > 0 ? marketDataService.getCryptoPrices(cryptoSymbols) : [],\n        stockSymbols.length > 0 ? marketDataService.getStockPrices(stockSymbols) : [],\n        forexSymbols.length > 0 ? marketDataService.getForexPrices(forexSymbols) : [],\n      ])\n\n      // Update market data map\n      const allData = cryptoData.concat(stockData).concat(forexData)\n      allData.forEach(data => {\n        this.marketData.set(data.symbol, data.price)\n      })\n\n      // Update position P&L\n      this.updatePositionPnL()\n    } catch (error) {\n      console.error('Error updating market data:', error)\n    }\n  }\n\n  // Game state getters\n  getState(): GameState {\n    return { ...this.state }\n  }\n\n  getConfig(): GameConfig {\n    return { ...this.config }\n  }\n\n  isGameActive(): boolean {\n    return this.isActive && this.state.time_remaining > 0\n  }\n\n  getTimeRemaining(): number {\n    if (!this.isActive) return 0\n    const remaining = Math.max(0, this.endTime - Date.now())\n    this.state.time_remaining = Math.floor(remaining / 1000)\n    return this.state.time_remaining\n  }\n\n  // Protected helper methods\n  protected startGameLoop(): void {\n    if (!this.isActive) return\n\n    const gameLoop = () => {\n      if (!this.isActive) return\n\n      this.update()\n      this.getTimeRemaining()\n\n      if (this.state.time_remaining <= 0) {\n        this.end()\n        return\n      }\n\n      setTimeout(gameLoop, 1000) // Update every second\n    }\n\n    gameLoop()\n  }\n\n  protected updatePositionPnL(): void {\n    this.state.positions.forEach(position => {\n      const currentPrice = this.marketData.get(position.symbol)\n      if (currentPrice) {\n        position.current_price = currentPrice\n        position.pnl = calculatePnL(\n          position.entry_price,\n          currentPrice,\n          position.quantity,\n          position.side\n        )\n      }\n    })\n  }\n\n  protected getTotalPnL(): number {\n    return this.state.positions.reduce((total, position) => total + position.pnl, 0)\n  }\n\n  protected getMaxPositions(): number {\n    const gameConfig = GAME_CONFIGS[this.config.type]\n    return (gameConfig as any).max_positions || 5\n  }\n\n  protected hasExistingPosition(symbol: string): boolean {\n    return this.state.positions.some(p => p.symbol === symbol)\n  }\n\n  protected isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.some(crypto => symbol.includes(crypto))\n  }\n\n  protected isStockSymbol(symbol: string): boolean {\n    const stockSymbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN', 'META', 'NVDA']\n    return stockSymbols.includes(symbol)\n  }\n\n  protected isForexSymbol(symbol: string): boolean {\n    return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY')\n  }\n\n  protected generateRandomPrice(basePrice: number, volatility: number = 0.02): number {\n    const change = (Math.random() - 0.5) * 2 * volatility\n    return basePrice * (1 + change)\n  }\n\n  protected simulateMarketMovement(): void {\n    // Simulate realistic market movements for game purposes\n    this.marketData.forEach((price, symbol) => {\n      const volatility = this.getSymbolVolatility(symbol)\n      const newPrice = this.generateRandomPrice(price, volatility)\n      this.marketData.set(symbol, newPrice)\n    })\n  }\n\n  protected getSymbolVolatility(symbol: string): number {\n    if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto\n    if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks\n    if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex\n    return 0.02 // Default 2%\n  }\n}\n\nexport { BaseGame }\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM;IACD,OAAkB;IAClB,MAAgB;IAChB,UAAiB;IACjB,QAAe;IACf,WAAoB,MAAK;IACzB,aAAkC,IAAI,MAAK;IAErD,YAAY,QAAkB,EAAE,UAAoD,CAAE;QACpF,MAAM,aAAa,0HAAA,CAAA,eAAY,CAAC,SAAS;QAEzC,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM;YACN;YACA,kBAAkB,WAAW,gBAAgB;YAC7C,kBAAkB,WAAW,gBAAgB;YAC7C,iBAAiB,EAAE;YACnB,eAAe,CAAC;QAClB;QAEA,IAAI,CAAC,KAAK,GAAG;YACX,YAAY,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;YAC5B,iBAAiB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC7C,WAAW,EAAE;YACb,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC5C,OAAO;YACP,YAAY,0HAAA,CAAA,yBAAsB,CAAC,WAAW;QAChD;QAEA,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG;IAClE;IAEA,mDAAmD;IACnD,MAAM,aAA4B;IAChC,6CAA6C;IAC/C;IAEA,SAAe;IACb,6CAA6C;IAC/C;IAEA,iBAAyB;QACvB,6CAA6C;QAC7C,OAAO;IACT;IAEA,sBAA2B;QACzB,6CAA6C;QAC7C,OAAO,CAAC;IACV;IAEA,gCAAgC;IAChC,MAAM,QAAuB;QAC3B,MAAM,IAAI,CAAC,UAAU;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,QAAc;QACZ,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,SAAe;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,MAAiB;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;QACtC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;QAC5B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAc,EAAE,IAAoB,EAAE,QAAgB,EAAoB;QAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,aAAa,eAAe;QAClC,MAAM,kBAAkB,SAAS,QAAQ,aAAa;QAEtD,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,iBAAiB;YAChD,OAAO,MAAM,uBAAuB;;QACtC;QAEA,wBAAwB;QACxB,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS;YACpF,OAAO,MAAM,qBAAqB;;QACpC;QAEA,oBAAoB;QACpB,MAAM,WAAqB;YACzB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE;YACA;YACA;YACA,aAAa;YACb,eAAe;YACf,KAAK;YACL,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC;QAEA,yBAAyB;QACzB,MAAM,wBAAwB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAC/E,IAAI,yBAAyB,GAAG;YAC9B,qEAAqE;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG;QAChD,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QAC5B;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnE,IAAI,kBAAkB,CAAC,GAAG,OAAO;QAEjC,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;QACpD,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;QACxD,IAAI,CAAC,cAAc,OAAO;QAE1B,sBAAsB;QACtB,MAAM,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,WAAW,EAAE,cAAc,SAAS,QAAQ,EAAE,SAAS,IAAI;QAE7F,0BAA0B;QAC1B,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAC9B,IAAI,SAAS,IAAI,KAAK,QAAQ;YAC5B,qDAAqD;YACrD,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,SAAS,WAAW,GAAG,SAAS,QAAQ;QACxE;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe;QAE3C,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YAEnE,oFAAoF;YACpF,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,cAAc,CAAC;YAC9D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAC5D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAE5D,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3D,cAAc,MAAM,GAAG,IAAI,2IAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,iBAAiB,EAAE;gBAChF,aAAa,MAAM,GAAG,IAAI,2IAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;gBAC7E,aAAa,MAAM,GAAG,IAAI,2IAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;aAC9E;YAED,yBAAyB;YACzB,MAAM,UAAU,WAAW,MAAM,CAAC,WAAW,MAAM,CAAC;YACpD,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,KAAK;YAC7C;YAEA,sBAAsB;YACtB,IAAI,CAAC,iBAAiB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,qBAAqB;IACrB,WAAsB;QACpB,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK;QAAC;IACzB;IAEA,YAAwB;QACtB,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,eAAwB;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;IACtD;IAEA,mBAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC3B,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG;QACrD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC,YAAY;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc;IAClC;IAEA,2BAA2B;IACjB,gBAAsB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,MAAM,WAAW;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAEpB,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,gBAAgB;YAErB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,GAAG;gBAClC,IAAI,CAAC,GAAG;gBACR;YACF;YAEA,WAAW,UAAU,MAAM,sBAAsB;;QACnD;QAEA;IACF;IAEU,oBAA0B;QAClC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAC3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;YACxD,IAAI,cAAc;gBAChB,SAAS,aAAa,GAAG;gBACzB,SAAS,GAAG,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EACxB,SAAS,WAAW,EACpB,cACA,SAAS,QAAQ,EACjB,SAAS,IAAI;YAEjB;QACF;IACF;IAEU,cAAsB;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,GAAG,EAAE;IAChF;IAEU,kBAA0B;QAClC,MAAM,aAAa,0HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjD,OAAO,AAAC,WAAmB,aAAa,IAAI;IAC9C;IAEU,oBAAoB,MAAc,EAAW;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IACrD;IAEU,eAAe,MAAc,EAAW;QAChD,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC;IACtD;IAEU,cAAc,MAAc,EAAW;QAC/C,MAAM,eAAe;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;QAC9E,OAAO,aAAa,QAAQ,CAAC;IAC/B;IAEU,cAAc,MAAc,EAAW;QAC/C,OAAO,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC;IACvG;IAEU,oBAAoB,SAAiB,EAAE,aAAqB,IAAI,EAAU;QAClF,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI;QAC3C,OAAO,YAAY,CAAC,IAAI,MAAM;IAChC;IAEU,yBAA+B;QACvC,wDAAwD;QACxD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAC5C,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC,OAAO;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC9B;IACF;IAEU,oBAAoB,MAAc,EAAU;QACpD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACxE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACvE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,0BAA0B;;QACtE,OAAO,KAAK,aAAa;;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/games/candle-strike.ts"], "sourcesContent": ["import { BaseGame } from '../base-game'\nimport { GameType, CandlestickData } from '@/types'\nimport { TRADING_PAIRS } from '@/lib/constants'\n\ninterface CandlePattern {\n  id: string\n  name: string\n  description: string\n  bullish: boolean\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  minCandles: number\n  maxCandles: number\n}\n\ninterface CandleStrikeData {\n  patterns_identified: number\n  correct_identifications: number\n  wrong_identifications: number\n  current_pattern: CandlePattern | null\n  patterns_completed: CandlePattern[]\n  accuracy_percentage: number\n  speed_bonus: number\n  streak_count: number\n  max_streak: number\n}\n\ninterface PatternChallenge {\n  pattern: CandlePattern\n  candleData: CandlestickData[]\n  patternStartIndex: number\n  patternEndIndex: number\n  options: string[]\n  correctAnswer: number\n}\n\nexport class CandleStrikeGame extends BaseGame {\n  private gameData: CandleStrikeData\n  private currentChallenge: PatternChallenge | null = null\n  private challengeHistory: Array<{\n    challenge: PatternChallenge\n    userAnswer: number\n    correct: boolean\n    timeToAnswer: number\n    timestamp: number\n  }> = []\n  private challengeStartTime: number = 0\n  private availablePatterns: CandlePattern[]\n\n  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    super('candle_strike', difficulty)\n    \n    this.gameData = {\n      patterns_identified: 0,\n      correct_identifications: 0,\n      wrong_identifications: 0,\n      current_pattern: null,\n      patterns_completed: [],\n      accuracy_percentage: 0,\n      speed_bonus: 0,\n      streak_count: 0,\n      max_streak: 0,\n    }\n\n    this.availablePatterns = this.getPatternsByDifficulty(difficulty)\n    this.config.available_pairs = [TRADING_PAIRS[0]] // Use BTC for pattern recognition\n  }\n\n  async initialize(): Promise<void> {\n    // Generate first challenge\n    await this.generateNewChallenge()\n  }\n\n  update(): void {\n    // Update game metrics\n    this.updateGameMetrics()\n  }\n\n  calculateScore(): number {\n    const baseScore = this.gameData.correct_identifications * 100\n    const accuracyBonus = this.gameData.accuracy_percentage * 2\n    const speedBonus = this.gameData.speed_bonus\n    const streakBonus = this.gameData.max_streak * 50\n    \n    let totalScore = baseScore + accuracyBonus + speedBonus + streakBonus\n    \n    // Difficulty multiplier\n    totalScore *= this.state.multiplier\n    \n    return Math.round(Math.max(0, totalScore))\n  }\n\n  getGameSpecificData(): CandleStrikeData {\n    return { ...this.gameData }\n  }\n\n  getCurrentChallenge(): PatternChallenge | null {\n    return this.currentChallenge\n  }\n\n  async submitAnswer(answerIndex: number): Promise<boolean> {\n    if (!this.currentChallenge || !this.isActive) return false\n\n    const timeToAnswer = Date.now() - this.challengeStartTime\n    const correct = answerIndex === this.currentChallenge.correctAnswer\n\n    // Record the attempt\n    this.challengeHistory.push({\n      challenge: this.currentChallenge,\n      userAnswer: answerIndex,\n      correct,\n      timeToAnswer,\n      timestamp: Date.now(),\n    })\n\n    // Update game data\n    this.gameData.patterns_identified++\n    \n    if (correct) {\n      this.gameData.correct_identifications++\n      this.gameData.streak_count++\n      this.gameData.max_streak = Math.max(this.gameData.max_streak, this.gameData.streak_count)\n      \n      // Speed bonus for quick correct answers (under 10 seconds)\n      if (timeToAnswer < 10000) {\n        const speedBonus = Math.max(0, 50 - Math.floor(timeToAnswer / 200))\n        this.gameData.speed_bonus += speedBonus\n      }\n      \n      // Add pattern to completed list if not already there\n      if (!this.gameData.patterns_completed.find(p => p.id === this.currentChallenge!.pattern.id)) {\n        this.gameData.patterns_completed.push(this.currentChallenge.pattern)\n      }\n    } else {\n      this.gameData.wrong_identifications++\n      this.gameData.streak_count = 0\n    }\n\n    // Update accuracy\n    this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100\n\n    // Generate next challenge if game is still active\n    if (this.isActive && this.state.time_remaining > 0) {\n      await this.generateNewChallenge()\n    }\n\n    return correct\n  }\n\n  private async generateNewChallenge(): Promise<void> {\n    // Select a random pattern based on difficulty and progress\n    const pattern = this.selectNextPattern()\n    \n    // Generate candlestick data with the pattern\n    const candleData = this.generateCandlestickDataWithPattern(pattern)\n    \n    // Find where the pattern occurs in the data\n    const patternLocation = this.findPatternInData(candleData, pattern)\n    \n    // Generate multiple choice options\n    const options = this.generatePatternOptions(pattern)\n    \n    this.currentChallenge = {\n      pattern,\n      candleData,\n      patternStartIndex: patternLocation.start,\n      patternEndIndex: patternLocation.end,\n      options,\n      correctAnswer: 0, // Correct answer is always first, then shuffled\n    }\n\n    // Shuffle options and update correct answer index\n    this.shuffleOptions()\n    \n    this.gameData.current_pattern = pattern\n    this.challengeStartTime = Date.now()\n  }\n\n  private selectNextPattern(): CandlePattern {\n    // Prioritize patterns not yet completed\n    const uncompletedPatterns = this.availablePatterns.filter(\n      p => !this.gameData.patterns_completed.find(completed => completed.id === p.id)\n    )\n    \n    const patternsToChooseFrom = uncompletedPatterns.length > 0 ? uncompletedPatterns : this.availablePatterns\n    \n    return patternsToChooseFrom[Math.floor(Math.random() * patternsToChooseFrom.length)]\n  }\n\n  private generateCandlestickDataWithPattern(pattern: CandlePattern): CandlestickData[] {\n    const totalCandles = 50\n    const patternPosition = Math.floor(Math.random() * (totalCandles - pattern.maxCandles - 10)) + 10\n    \n    const data: CandlestickData[] = []\n    let currentPrice = 100 + Math.random() * 50\n    \n    // Generate candles before pattern\n    for (let i = 0; i < patternPosition; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i)\n      data.push(candle)\n      currentPrice = candle.close\n    }\n    \n    // Generate pattern candles\n    const patternCandles = this.generatePatternCandles(pattern, currentPrice, patternPosition)\n    data.push(...patternCandles)\n    currentPrice = patternCandles[patternCandles.length - 1].close\n    \n    // Generate candles after pattern\n    for (let i = patternPosition + patternCandles.length; i < totalCandles; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i)\n      data.push(candle)\n      currentPrice = candle.close\n    }\n    \n    return data\n  }\n\n  private generateRandomCandle(basePrice: number, index: number): CandlestickData {\n    const volatility = 0.02\n    const change = (Math.random() - 0.5) * volatility * basePrice\n    const open = basePrice\n    const close = basePrice + change\n    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice\n    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice\n    \n    return {\n      timestamp: Date.now() - (50 - index) * 3600000, // Hourly intervals\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }\n  }\n\n  private generatePatternCandles(pattern: CandlePattern, startPrice: number, startIndex: number): CandlestickData[] {\n    // This is a simplified pattern generation - in a real implementation,\n    // you'd have specific algorithms for each pattern type\n    const candles: CandlestickData[] = []\n    let currentPrice = startPrice\n    \n    switch (pattern.id) {\n      case 'hammer':\n        return this.generateHammerPattern(startPrice, startIndex)\n      case 'doji':\n        return this.generateDojiPattern(startPrice, startIndex)\n      case 'engulfing_bullish':\n        return this.generateEngulfingPattern(startPrice, startIndex, true)\n      case 'engulfing_bearish':\n        return this.generateEngulfingPattern(startPrice, startIndex, false)\n      case 'morning_star':\n        return this.generateMorningStarPattern(startPrice, startIndex)\n      case 'evening_star':\n        return this.generateEveningStarPattern(startPrice, startIndex)\n      default:\n        return this.generateHammerPattern(startPrice, startIndex)\n    }\n  }\n\n  private generateHammerPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const open = startPrice\n    const close = startPrice + (Math.random() * 0.01 * startPrice) // Small body\n    const high = Math.max(open, close) + (Math.random() * 0.005 * startPrice) // Small upper shadow\n    const low = Math.min(open, close) - (0.02 + Math.random() * 0.01) * startPrice // Long lower shadow\n    \n    return [{\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }]\n  }\n\n  private generateDojiPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const open = startPrice\n    const close = startPrice + (Math.random() - 0.5) * 0.002 * startPrice // Very small body\n    const high = Math.max(open, close) + (0.01 + Math.random() * 0.01) * startPrice\n    const low = Math.min(open, close) - (0.01 + Math.random() * 0.01) * startPrice\n    \n    return [{\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }]\n  }\n\n  private generateEngulfingPattern(startPrice: number, startIndex: number, bullish: boolean): CandlestickData[] {\n    const candles: CandlestickData[] = []\n    \n    // First candle (small)\n    const firstOpen = startPrice\n    const firstClose = bullish \n      ? startPrice - 0.01 * startPrice \n      : startPrice + 0.01 * startPrice\n    \n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: firstOpen,\n      high: Math.max(firstOpen, firstClose) + 0.002 * startPrice,\n      low: Math.min(firstOpen, firstClose) - 0.002 * startPrice,\n      close: firstClose,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (engulfing)\n    const secondOpen = bullish \n      ? firstClose - 0.005 * startPrice \n      : firstClose + 0.005 * startPrice\n    const secondClose = bullish \n      ? firstOpen + 0.015 * startPrice \n      : firstOpen - 0.015 * startPrice\n    \n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondOpen,\n      high: Math.max(secondOpen, secondClose) + 0.002 * startPrice,\n      low: Math.min(secondOpen, secondClose) - 0.002 * startPrice,\n      close: secondClose,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private generateMorningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const candles: CandlestickData[] = []\n    \n    // First candle (bearish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: startPrice,\n      high: startPrice + 0.002 * startPrice,\n      low: startPrice - 0.015 * startPrice,\n      close: startPrice - 0.012 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (small body/doji)\n    const secondPrice = startPrice - 0.015 * startPrice\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondPrice,\n      high: secondPrice + 0.005 * startPrice,\n      low: secondPrice - 0.005 * startPrice,\n      close: secondPrice + 0.001 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Third candle (bullish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,\n      open: secondPrice + 0.002 * startPrice,\n      high: startPrice - 0.002 * startPrice,\n      low: secondPrice,\n      close: startPrice - 0.003 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private generateEveningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    // Similar to morning star but inverted\n    const candles: CandlestickData[] = []\n    \n    // First candle (bullish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: startPrice,\n      high: startPrice + 0.015 * startPrice,\n      low: startPrice - 0.002 * startPrice,\n      close: startPrice + 0.012 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (small body/doji)\n    const secondPrice = startPrice + 0.015 * startPrice\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondPrice,\n      high: secondPrice + 0.005 * startPrice,\n      low: secondPrice - 0.005 * startPrice,\n      close: secondPrice - 0.001 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Third candle (bearish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,\n      open: secondPrice - 0.002 * startPrice,\n      high: secondPrice,\n      low: startPrice + 0.002 * startPrice,\n      close: startPrice + 0.003 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private findPatternInData(data: CandlestickData[], pattern: CandlePattern): { start: number; end: number } {\n    // For simplicity, we know where we placed the pattern\n    // In a real implementation, you'd search for the pattern in the data\n    const totalCandles = data.length\n    const patternPosition = Math.floor(totalCandles * 0.4) // Roughly where we placed it\n    \n    return {\n      start: patternPosition,\n      end: patternPosition + pattern.maxCandles - 1,\n    }\n  }\n\n  private generatePatternOptions(correctPattern: CandlePattern): string[] {\n    const allPatterns = this.getAllPatterns()\n    const wrongPatterns = allPatterns\n      .filter(p => p.id !== correctPattern.id)\n      .sort(() => Math.random() - 0.5)\n      .slice(0, 3)\n    \n    return [correctPattern.name, ...wrongPatterns.map(p => p.name)]\n  }\n\n  private shuffleOptions(): void {\n    if (!this.currentChallenge) return\n    \n    const options = [...this.currentChallenge.options]\n    const correctAnswer = options[0]\n    \n    // Fisher-Yates shuffle\n    for (let i = options.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1))\n      ;[options[i], options[j]] = [options[j], options[i]]\n    }\n    \n    this.currentChallenge.options = options\n    this.currentChallenge.correctAnswer = options.indexOf(correctAnswer)\n  }\n\n  private updateGameMetrics(): void {\n    // Update accuracy percentage\n    if (this.gameData.patterns_identified > 0) {\n      this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100\n    }\n  }\n\n  private getPatternsByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): CandlePattern[] {\n    const allPatterns = this.getAllPatterns()\n    return allPatterns.filter(p => p.difficulty === difficulty || (difficulty === 'advanced' && p.difficulty !== 'advanced'))\n  }\n\n  private getAllPatterns(): CandlePattern[] {\n    return [\n      {\n        id: 'hammer',\n        name: 'Hammer',\n        description: 'Bullish reversal pattern with long lower shadow',\n        bullish: true,\n        difficulty: 'beginner',\n        minCandles: 1,\n        maxCandles: 1,\n      },\n      {\n        id: 'doji',\n        name: 'Doji',\n        description: 'Indecision pattern with very small body',\n        bullish: false,\n        difficulty: 'beginner',\n        minCandles: 1,\n        maxCandles: 1,\n      },\n      {\n        id: 'engulfing_bullish',\n        name: 'Bullish Engulfing',\n        description: 'Two-candle bullish reversal pattern',\n        bullish: true,\n        difficulty: 'intermediate',\n        minCandles: 2,\n        maxCandles: 2,\n      },\n      {\n        id: 'engulfing_bearish',\n        name: 'Bearish Engulfing',\n        description: 'Two-candle bearish reversal pattern',\n        bullish: false,\n        difficulty: 'intermediate',\n        minCandles: 2,\n        maxCandles: 2,\n      },\n      {\n        id: 'morning_star',\n        name: 'Morning Star',\n        description: 'Three-candle bullish reversal pattern',\n        bullish: true,\n        difficulty: 'advanced',\n        minCandles: 3,\n        maxCandles: 3,\n      },\n      {\n        id: 'evening_star',\n        name: 'Evening Star',\n        description: 'Three-candle bearish reversal pattern',\n        bullish: false,\n        difficulty: 'advanced',\n        minCandles: 3,\n        maxCandles: 3,\n      },\n    ]\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAiCO,MAAM,yBAAyB,+IAAA,CAAA,WAAQ;IACpC,SAA0B;IAC1B,mBAA4C,KAAI;IAChD,mBAMH,EAAE,CAAA;IACC,qBAA6B,EAAC;IAC9B,kBAAkC;IAE1C,YAAY,UAAoD,CAAE;QAChE,KAAK,CAAC,iBAAiB;QAEvB,IAAI,CAAC,QAAQ,GAAG;YACd,qBAAqB;YACrB,yBAAyB;YACzB,uBAAuB;YACvB,iBAAiB;YACjB,oBAAoB,EAAE;YACtB,qBAAqB;YACrB,aAAa;YACb,cAAc;YACd,YAAY;QACd;QAEA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG;YAAC,0HAAA,CAAA,gBAAa,CAAC,EAAE;SAAC,CAAC,kCAAkC;;IACrF;IAEA,MAAM,aAA4B;QAChC,2BAA2B;QAC3B,MAAM,IAAI,CAAC,oBAAoB;IACjC;IAEA,SAAe;QACb,sBAAsB;QACtB,IAAI,CAAC,iBAAiB;IACxB;IAEA,iBAAyB;QACvB,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG;QAC1D,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG;QAC1D,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,WAAW;QAC5C,MAAM,cAAc,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE/C,IAAI,aAAa,YAAY,gBAAgB,aAAa;QAE1D,wBAAwB;QACxB,cAAc,IAAI,CAAC,KAAK,CAAC,UAAU;QAEnC,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG;IAChC;IAEA,sBAAwC;QACtC,OAAO;YAAE,GAAG,IAAI,CAAC,QAAQ;QAAC;IAC5B;IAEA,sBAA+C;QAC7C,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA,MAAM,aAAa,WAAmB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAErD,MAAM,eAAe,KAAK,GAAG,KAAK,IAAI,CAAC,kBAAkB;QACzD,MAAM,UAAU,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAEnE,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACzB,WAAW,IAAI,CAAC,gBAAgB;YAChC,YAAY;YACZ;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QAEjC,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,uBAAuB;YACrC,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC1B,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;YAExF,2DAA2D;YAC3D,IAAI,eAAe,OAAO;gBACxB,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,eAAe;gBAC9D,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;YAC/B;YAEA,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAE,OAAO,CAAC,EAAE,GAAG;gBAC3F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO;YACrE;QACF,OAAO;YACL,IAAI,CAAC,QAAQ,CAAC,qBAAqB;YACnC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG;QAC/B;QAEA,kBAAkB;QAClB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,AAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAI;QAElH,kDAAkD;QAClD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG;YAClD,MAAM,IAAI,CAAC,oBAAoB;QACjC;QAEA,OAAO;IACT;IAEA,MAAc,uBAAsC;QAClD,2DAA2D;QAC3D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QAEtC,6CAA6C;QAC7C,MAAM,aAAa,IAAI,CAAC,kCAAkC,CAAC;QAE3D,4CAA4C;QAC5C,MAAM,kBAAkB,IAAI,CAAC,iBAAiB,CAAC,YAAY;QAE3D,mCAAmC;QACnC,MAAM,UAAU,IAAI,CAAC,sBAAsB,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG;YACtB;YACA;YACA,mBAAmB,gBAAgB,KAAK;YACxC,iBAAiB,gBAAgB,GAAG;YACpC;YACA,eAAe;QACjB;QAEA,kDAAkD;QAClD,IAAI,CAAC,cAAc;QAEnB,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG;QAChC,IAAI,CAAC,kBAAkB,GAAG,KAAK,GAAG;IACpC;IAEQ,oBAAmC;QACzC,wCAAwC;QACxC,MAAM,sBAAsB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CACvD,CAAA,IAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK,EAAE,EAAE;QAGhF,MAAM,uBAAuB,oBAAoB,MAAM,GAAG,IAAI,sBAAsB,IAAI,CAAC,iBAAiB;QAE1G,OAAO,oBAAoB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,qBAAqB,MAAM,EAAE;IACtF;IAEQ,mCAAmC,OAAsB,EAAqB;QACpF,MAAM,eAAe;QACrB,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,eAAe,QAAQ,UAAU,GAAG,EAAE,KAAK;QAE/F,MAAM,OAA0B,EAAE;QAClC,IAAI,eAAe,MAAM,KAAK,MAAM,KAAK;QAEzC,kCAAkC;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACvD,KAAK,IAAI,CAAC;YACV,eAAe,OAAO,KAAK;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,SAAS,cAAc;QAC1E,KAAK,IAAI,IAAI;QACb,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9D,iCAAiC;QACjC,IAAK,IAAI,IAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,cAAc,IAAK;YAC3E,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACvD,KAAK,IAAI,CAAC;YACV,eAAe,OAAO,KAAK;QAC7B;QAEA,OAAO;IACT;IAEQ,qBAAqB,SAAiB,EAAE,KAAa,EAAmB;QAC9E,MAAM,aAAa;QACnB,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;QACpD,MAAM,OAAO;QACb,MAAM,QAAQ,YAAY;QAC1B,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAC5D,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAE3D,OAAO;YACL,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI;YACvC;YACA;YACA;YACA;YACA,QAAQ,KAAK,MAAM,KAAK;QAC1B;IACF;IAEQ,uBAAuB,OAAsB,EAAE,UAAkB,EAAE,UAAkB,EAAqB;QAChH,sEAAsE;QACtE,uDAAuD;QACvD,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe;QAEnB,OAAQ,QAAQ,EAAE;YAChB,KAAK;gBACH,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY;YAChD,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY;YAC9C,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,YAAY;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,YAAY;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY;YACrD,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY;YACrD;gBACE,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY;QAClD;IACF;IAEQ,sBAAsB,UAAkB,EAAE,UAAkB,EAAqB;QACvF,MAAM,OAAO;QACb,MAAM,QAAQ,aAAc,KAAK,MAAM,KAAK,OAAO,WAAY,aAAa;;QAC5E,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAU,KAAK,MAAM,KAAK,QAAQ,WAAY,qBAAqB;;QAC/F,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,WAAW,oBAAoB;;QAEnG,OAAO;YAAC;gBACN,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;gBAC5C;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,MAAM,KAAK;YAC1B;SAAE;IACJ;IAEQ,oBAAoB,UAAkB,EAAE,UAAkB,EAAqB;QACrF,MAAM,OAAO;QACb,MAAM,QAAQ,aAAa,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,WAAW,kBAAkB;;QACxF,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;QACrE,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;QAEpE,OAAO;YAAC;gBACN,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;gBAC5C;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,MAAM,KAAK;YAC1B;SAAE;IACJ;IAEQ,yBAAyB,UAAkB,EAAE,UAAkB,EAAE,OAAgB,EAAqB;QAC5G,MAAM,UAA6B,EAAE;QAErC,uBAAuB;QACvB,MAAM,YAAY;QAClB,MAAM,aAAa,UACf,aAAa,OAAO,aACpB,aAAa,OAAO;QAExB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,KAAK,GAAG,CAAC,WAAW,cAAc,QAAQ;YAChD,KAAK,KAAK,GAAG,CAAC,WAAW,cAAc,QAAQ;YAC/C,OAAO;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,4BAA4B;QAC5B,MAAM,aAAa,UACf,aAAa,QAAQ,aACrB,aAAa,QAAQ;QACzB,MAAM,cAAc,UAChB,YAAY,QAAQ,aACpB,YAAY,QAAQ;QAExB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,KAAK,GAAG,CAAC,YAAY,eAAe,QAAQ;YAClD,KAAK,KAAK,GAAG,CAAC,YAAY,eAAe,QAAQ;YACjD,OAAO;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,2BAA2B,UAAkB,EAAE,UAAkB,EAAqB;QAC5F,MAAM,UAA6B,EAAE;QAErC,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,aAAa,QAAQ;YAC3B,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,kCAAkC;QAClC,MAAM,cAAc,aAAa,QAAQ;QACzC,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,cAAc,QAAQ;YAC5B,KAAK,cAAc,QAAQ;YAC3B,OAAO,cAAc,QAAQ;YAC7B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM,cAAc,QAAQ;YAC5B,MAAM,aAAa,QAAQ;YAC3B,KAAK;YACL,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,2BAA2B,UAAkB,EAAE,UAAkB,EAAqB;QAC5F,uCAAuC;QACvC,MAAM,UAA6B,EAAE;QAErC,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,aAAa,QAAQ;YAC3B,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,kCAAkC;QAClC,MAAM,cAAc,aAAa,QAAQ;QACzC,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,cAAc,QAAQ;YAC5B,KAAK,cAAc,QAAQ;YAC3B,OAAO,cAAc,QAAQ;YAC7B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM,cAAc,QAAQ;YAC5B,MAAM;YACN,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,kBAAkB,IAAuB,EAAE,OAAsB,EAAkC;QACzG,sDAAsD;QACtD,qEAAqE;QACrE,MAAM,eAAe,KAAK,MAAM;QAChC,MAAM,kBAAkB,KAAK,KAAK,CAAC,eAAe,KAAK,6BAA6B;;QAEpF,OAAO;YACL,OAAO;YACP,KAAK,kBAAkB,QAAQ,UAAU,GAAG;QAC9C;IACF;IAEQ,uBAAuB,cAA6B,EAAY;QACtE,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,gBAAgB,YACnB,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE,EACtC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK,KAC3B,KAAK,CAAC,GAAG;QAEZ,OAAO;YAAC,eAAe,IAAI;eAAK,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;SAAE;IACjE;IAEQ,iBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,MAAM,UAAU;eAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO;SAAC;QAClD,MAAM,gBAAgB,OAAO,CAAC,EAAE;QAEhC,uBAAuB;QACvB,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;YAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;YAC1C,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG;gBAAC,OAAO,CAAC,EAAE;gBAAE,OAAO,CAAC,EAAE;aAAC;QACtD;QAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;QAChC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,QAAQ,OAAO,CAAC;IACxD;IAEQ,oBAA0B;QAChC,6BAA6B;QAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,GAAG;YACzC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,AAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAI;QACpH;IACF;IAEQ,wBAAwB,UAAoD,EAAmB;QACrG,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,cAAe,eAAe,cAAc,EAAE,UAAU,KAAK;IAC/G;IAEQ,iBAAkC;QACxC,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/games/candle-strike-game.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { CandleStrikeGame } from '@/lib/game-engine/games/candle-strike'\n// import CandlestickChart, { PatternAnnotation, ChartSkeleton } from '@/components/charts/candlestick-chart'\n// import PlaybackChart from '@/components/charts/playback-chart'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport { marketDataService } from '@/lib/services/market-data'\n\ninterface CandleStrikeGameProps {\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  onGameEnd: (score: number) => void\n  usePlayback?: boolean\n  className?: string\n}\n\nexport default function CandleStrikeGameComponent({\n  difficulty,\n  onGameEnd,\n  usePlayback = false,\n  className = '',\n}: CandleStrikeGameProps) {\n  const { interfaceMode } = useUserStore()\n  const [game, setGame] = useState<CandleStrikeGame | null>(null)\n  const [gameState, setGameState] = useState<any>(null)\n  const [currentChallenge, setCurrentChallenge] = useState<any>(null)\n  const [gameData, setGameData] = useState<any>(null)\n  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [lastAnswerCorrect, setLastAnswerCorrect] = useState<boolean | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  useEffect(() => {\n    initializeGame()\n  }, [difficulty])\n\n  const initializeGame = async () => {\n    setIsLoading(true)\n    const newGame = new CandleStrikeGame(difficulty)\n    setGame(newGame)\n    \n    await newGame.start()\n    \n    // Update game state every second\n    const interval = setInterval(() => {\n      if (newGame.isGameActive()) {\n        setGameState(newGame.getState())\n        setCurrentChallenge(newGame.getCurrentChallenge())\n        setGameData(newGame.getGameSpecificData())\n      } else {\n        clearInterval(interval)\n        const finalScore = newGame.calculateScore()\n        onGameEnd(finalScore)\n      }\n    }, 1000)\n\n    setIsLoading(false)\n  }\n\n  const handleAnswerSubmit = async (answerIndex: number) => {\n    if (!game || selectedAnswer !== null || showResult) return\n\n    setSelectedAnswer(answerIndex)\n    const correct = await game.submitAnswer(answerIndex)\n    setLastAnswerCorrect(correct)\n    setShowResult(true)\n\n    // Update game state\n    setGameState(game.getState())\n    setGameData(game.getGameSpecificData())\n\n    // Auto-advance to next challenge after 2 seconds\n    setTimeout(() => {\n      setSelectedAnswer(null)\n      setShowResult(false)\n      setLastAnswerCorrect(null)\n      setCurrentChallenge(game.getCurrentChallenge())\n    }, 2000)\n  }\n\n  const getPatternHighlight = () => {\n    if (!currentChallenge) return undefined\n\n    return {\n      startIndex: currentChallenge.patternStartIndex,\n      endIndex: currentChallenge.patternEndIndex,\n      color: isAdolescentMode ? '#fbbf24' : '#10b981',\n    }\n  }\n\n  const getAnswerButtonStyle = (index: number) => {\n    const baseStyle = `p-3 rounded-lg font-bold transition-all duration-300 ${\n      isAdolescentMode\n        ? 'text-white border-2'\n        : 'text-gray-900 border-2'\n    }`\n\n    if (showResult && selectedAnswer !== null) {\n      if (index === currentChallenge?.correctAnswer) {\n        // Correct answer\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50'\n            : 'bg-green-400 border-green-500 shadow-lg'\n        }`\n      } else if (index === selectedAnswer) {\n        // Wrong selected answer\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50'\n            : 'bg-red-400 border-red-500 shadow-lg'\n        }`\n      } else {\n        // Other options\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-gray-600 border-gray-500 opacity-50'\n            : 'bg-gray-300 border-gray-400 opacity-50'\n        }`\n      }\n    } else {\n      // Normal state\n      return `${baseStyle} ${\n        isAdolescentMode\n          ? 'bg-purple-500 hover:bg-purple-600 border-purple-400 hover:shadow-lg hover:shadow-purple-500/30'\n          : 'bg-purple-400 hover:bg-purple-300 border-purple-500 hover:shadow-lg'\n      }`\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className={`${className} space-y-6`}>\n        <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n          <h3 className=\"text-xl font-bold mb-2\">\n            {isAdolescentMode ? '🕯️ Loading CandleStrike...' : '📊 INITIALIZING_PATTERN_RECOGNITION'}\n          </h3>\n        </div>\n        <ChartSkeleton \n          width={800} \n          height={400} \n          theme={isAdolescentMode ? 'dark' : 'dark'} \n        />\n      </div>\n    )\n  }\n\n  if (!game || !gameState || !currentChallenge) {\n    return (\n      <div className={`${className} text-center`}>\n        <p className={isAdolescentMode ? 'text-white' : 'text-green-400'}>\n          {isAdolescentMode ? '🎮 Game not ready...' : 'SYSTEM_NOT_READY'}\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`${className} space-y-6`}>\n      {/* Game Header */}\n      <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n        <h3 className=\"text-xl font-bold mb-2\">\n          {isAdolescentMode ? '🕯️ CandleStrike Challenge' : '📊 PATTERN_RECOGNITION_MODULE'}\n        </h3>\n        <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n          {isAdolescentMode \n            ? 'Identify the candlestick pattern in the highlighted area!' \n            : 'IDENTIFY_CANDLESTICK_PATTERN_IN_HIGHLIGHTED_REGION'\n          }\n        </p>\n      </div>\n\n      {/* Game Stats */}\n      <div className={`grid grid-cols-4 gap-4 p-4 rounded-lg ${\n        isAdolescentMode ? 'bg-white/10' : 'bg-gray-800 border border-green-400'\n      }`}>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Score' : 'SCORE'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n            {gameState.score}\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Accuracy' : 'ACCURACY'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {gameData?.accuracy_percentage?.toFixed(1) || 0}%\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Streak' : 'STREAK'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-orange-300' : 'text-orange-400'}`}>\n            {gameData?.streak_count || 0}\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>\n            {gameState.time_remaining}s\n          </div>\n        </div>\n      </div>\n\n      {/* Chart Display - Temporarily Disabled */}\n      <div className=\"flex justify-center\">\n        <div className=\"w-full max-w-4xl h-96 border rounded-lg flex items-center justify-center bg-gray-800\">\n          <div className=\"text-center\">\n            <div className=\"text-4xl mb-4\">📈</div>\n            <p className=\"text-white\">\n              {isAdolescentMode ? 'Chart Loading...' : 'CHART_SYSTEM_INITIALIZING...'}\n            </p>\n            <p className=\"text-gray-400 text-sm mt-2\">\n              {usePlayback ? 'Playback Mode' : 'Pattern Recognition Mode'}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Pattern Information */}\n      {currentChallenge.pattern && (\n        <div className=\"flex justify-center\">\n          <PatternAnnotation \n            pattern={{\n              name: \"Pattern to Identify\",\n              description: isAdolescentMode \n                ? \"Look at the highlighted candles and identify the pattern!\"\n                : \"ANALYZE_HIGHLIGHTED_CANDLESTICKS_AND_IDENTIFY_PATTERN\",\n              bullish: true\n            }}\n            theme=\"dark\"\n          />\n        </div>\n      )}\n\n      {/* Answer Options */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        {currentChallenge.options.map((option: string, index: number) => (\n          <button\n            key={index}\n            onClick={() => handleAnswerSubmit(index)}\n            disabled={selectedAnswer !== null || showResult}\n            className={getAnswerButtonStyle(index)}\n          >\n            {option}\n          </button>\n        ))}\n      </div>\n\n      {/* Result Feedback */}\n      {showResult && lastAnswerCorrect !== null && (\n        <div className={`text-center p-4 rounded-lg ${\n          lastAnswerCorrect\n            ? (isAdolescentMode \n                ? 'bg-green-500/20 border border-green-400 text-green-100'\n                : 'bg-green-900/50 border border-green-400 text-green-300'\n              )\n            : (isAdolescentMode \n                ? 'bg-red-500/20 border border-red-400 text-red-100'\n                : 'bg-red-900/50 border border-red-400 text-red-300'\n              )\n        }`}>\n          <div className=\"text-2xl mb-2\">\n            {lastAnswerCorrect \n              ? (isAdolescentMode ? '🎉' : '✅') \n              : (isAdolescentMode ? '😅' : '❌')\n            }\n          </div>\n          <p className=\"font-bold\">\n            {lastAnswerCorrect \n              ? (isAdolescentMode ? 'Excellent! Correct pattern identified!' : 'CORRECT_PATTERN_IDENTIFICATION')\n              : (isAdolescentMode ? 'Not quite! The correct answer was highlighted.' : 'INCORRECT_PATTERN_IDENTIFICATION')\n            }\n          </p>\n          {!lastAnswerCorrect && currentChallenge.pattern && (\n            <p className=\"text-sm mt-2\">\n              {isAdolescentMode \n                ? `The correct pattern was: ${currentChallenge.options[currentChallenge.correctAnswer]}`\n                : `CORRECT_PATTERN: ${currentChallenge.options[currentChallenge.correctAnswer]}`\n              }\n            </p>\n          )}\n        </div>\n      )}\n\n      {/* Progress Indicator */}\n      <div className={`text-center text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n        {isAdolescentMode \n          ? `🎯 Patterns Identified: ${gameData?.patterns_identified || 0} | Correct: ${gameData?.correct_identifications || 0}`\n          : `PATTERNS_IDENTIFIED: ${gameData?.patterns_identified || 0} | CORRECT: ${gameData?.correct_identifications || 0}`\n        }\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,6GAA6G;AAC7G,iEAAiE;AACjE;;;AANA;;;;AAgBe,SAAS,0BAA0B,EAChD,UAAU,EACV,SAAS,EACT,cAAc,KAAK,EACnB,YAAY,EAAE,EACQ;;IACtB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,kBAAkB;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+CAAE;YACR;QACF;8CAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB;QACrB,aAAa;QACb,MAAM,UAAU,IAAI,4JAAA,CAAA,mBAAgB,CAAC;QACrC,QAAQ;QAER,MAAM,QAAQ,KAAK;QAEnB,iCAAiC;QACjC,MAAM,WAAW,YAAY;YAC3B,IAAI,QAAQ,YAAY,IAAI;gBAC1B,aAAa,QAAQ,QAAQ;gBAC7B,oBAAoB,QAAQ,mBAAmB;gBAC/C,YAAY,QAAQ,mBAAmB;YACzC,OAAO;gBACL,cAAc;gBACd,MAAM,aAAa,QAAQ,cAAc;gBACzC,UAAU;YACZ;QACF,GAAG;QAEH,aAAa;IACf;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,mBAAmB,QAAQ,YAAY;QAEpD,kBAAkB;QAClB,MAAM,UAAU,MAAM,KAAK,YAAY,CAAC;QACxC,qBAAqB;QACrB,cAAc;QAEd,oBAAoB;QACpB,aAAa,KAAK,QAAQ;QAC1B,YAAY,KAAK,mBAAmB;QAEpC,iDAAiD;QACjD,WAAW;YACT,kBAAkB;YAClB,cAAc;YACd,qBAAqB;YACrB,oBAAoB,KAAK,mBAAmB;QAC9C,GAAG;IACL;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,kBAAkB,OAAO;QAE9B,OAAO;YACL,YAAY,iBAAiB,iBAAiB;YAC9C,UAAU,iBAAiB,eAAe;YAC1C,OAAO,mBAAmB,YAAY;QACxC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,YAAY,CAAC,qDAAqD,EACtE,mBACI,wBACA,0BACJ;QAEF,IAAI,cAAc,mBAAmB,MAAM;YACzC,IAAI,UAAU,kBAAkB,eAAe;gBAC7C,iBAAiB;gBACjB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,gEACA,2CACJ;YACJ,OAAO,IAAI,UAAU,gBAAgB;gBACnC,wBAAwB;gBACxB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,0DACA,uCACJ;YACJ,OAAO;gBACL,gBAAgB;gBAChB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,2CACA,0CACJ;YACJ;QACF,OAAO;YACL,eAAe;YACf,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,mGACA,uEACJ;QACJ;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;8BACtC,6LAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,eAAe,kBAAkB;8BACjF,cAAA,6LAAC;wBAAG,WAAU;kCACX,mBAAmB,gCAAgC;;;;;;;;;;;8BAGxD,6LAAC;oBACC,OAAO;oBACP,QAAQ;oBACR,OAAO,mBAAmB,SAAS;;;;;;;;;;;;IAI3C;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB;QAC5C,qBACE,6LAAC;YAAI,WAAW,GAAG,UAAU,YAAY,CAAC;sBACxC,cAAA,6LAAC;gBAAE,WAAW,mBAAmB,eAAe;0BAC7C,mBAAmB,yBAAyB;;;;;;;;;;;IAIrD;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;0BAEtC,6LAAC;gBAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,eAAe,kBAAkB;;kCACjF,6LAAC;wBAAG,WAAU;kCACX,mBAAmB,+BAA+B;;;;;;kCAErD,6LAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kCAC7E,mBACG,8DACA;;;;;;;;;;;;0BAMR,6LAAC;gBAAI,WAAW,CAAC,sCAAsC,EACrD,mBAAmB,gBAAgB,uCACnC;;kCACA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,UAAU;;;;;;0CAEhC,6LAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,kBAAkB;0CAC3F,UAAU,KAAK;;;;;;;;;;;;kCAGpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,aAAa;;;;;;0CAEnC,6LAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;;oCACtF,UAAU,qBAAqB,QAAQ,MAAM;oCAAE;;;;;;;;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,WAAW;;;;;;0CAEjC,6LAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,mBAAmB;0CAC5F,UAAU,gBAAgB;;;;;;;;;;;;kCAG/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,cAAc;;;;;;0CAEpC,6LAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,iBAAiB,gBAAgB;;oCACtF,UAAU,cAAc;oCAAC;;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAE,WAAU;0CACV,mBAAmB,qBAAqB;;;;;;0CAE3C,6LAAC;gCAAE,WAAU;0CACV,cAAc,kBAAkB;;;;;;;;;;;;;;;;;;;;;;YAOxC,iBAAiB,OAAO,kBACvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS;wBACP,MAAM;wBACN,aAAa,mBACT,8DACA;wBACJ,SAAS;oBACX;oBACA,OAAM;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAC7C,6LAAC;wBAEC,SAAS,IAAM,mBAAmB;wBAClC,UAAU,mBAAmB,QAAQ;wBACrC,WAAW,qBAAqB;kCAE/B;uBALI;;;;;;;;;;YAWV,cAAc,sBAAsB,sBACnC,6LAAC;gBAAI,WAAW,CAAC,2BAA2B,EAC1C,oBACK,mBACG,2DACA,2DAEH,mBACG,qDACA,oDAER;;kCACA,6LAAC;wBAAI,WAAU;kCACZ,oBACI,mBAAmB,OAAO,MAC1B,mBAAmB,OAAO;;;;;;kCAGjC,6LAAC;wBAAE,WAAU;kCACV,oBACI,mBAAmB,2CAA2C,mCAC9D,mBAAmB,mDAAmD;;;;;;oBAG5E,CAAC,qBAAqB,iBAAiB,OAAO,kBAC7C,6LAAC;wBAAE,WAAU;kCACV,mBACG,CAAC,yBAAyB,EAAE,iBAAiB,OAAO,CAAC,iBAAiB,aAAa,CAAC,EAAE,GACtF,CAAC,iBAAiB,EAAE,iBAAiB,OAAO,CAAC,iBAAiB,aAAa,CAAC,EAAE;;;;;;;;;;;;0BAQ1F,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,kBAAkB,kBAAkB;0BAC3F,mBACG,CAAC,wBAAwB,EAAE,UAAU,uBAAuB,EAAE,YAAY,EAAE,UAAU,2BAA2B,GAAG,GACpH,CAAC,qBAAqB,EAAE,UAAU,uBAAuB,EAAE,YAAY,EAAE,UAAU,2BAA2B,GAAG;;;;;;;;;;;;AAK7H;GA9RwB;;QAMI,wIAAA,CAAA,eAAY;;;KANhB", "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/theme/theme-selector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useThemeStore } from '@/lib/stores/theme-store'\nimport { enhancedThemes as themes } from '@/lib/themes/enhanced-color-psychology'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface ThemeSelectorProps {\n  className?: string\n  showPsychologyInfo?: boolean\n}\n\nexport default function ThemeSelector({ \n  className = '', \n  showPsychologyInfo = true \n}: ThemeSelectorProps) {\n  const { \n    currentThemeId, \n    setTheme, \n    interfaceMode,\n    autoThemeSwitch,\n    toggleAutoThemeSwitch,\n    highContrast,\n    toggleHighContrast,\n    reduceMotion,\n    toggleReduceMotion\n  } = useThemeStore()\n  \n  const { user } = useUserStore()\n  const [showAdvanced, setShowAdvanced] = useState(false)\n  \n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  const handleThemeChange = (themeId: string) => {\n    setTheme(themeId)\n  }\n\n  const getThemePreview = (themeId: string) => {\n    const theme = themes.find(t => t.id === themeId)\n    if (!theme) return null\n\n    const colors = theme[interfaceMode]\n    \n    return (\n      <div className=\"flex space-x-1\">\n        <div \n          className=\"w-4 h-4 rounded-full border\"\n          style={{ backgroundColor: colors.primary }}\n        />\n        <div \n          className=\"w-4 h-4 rounded-full border\"\n          style={{ backgroundColor: colors.bullish }}\n        />\n        <div \n          className=\"w-4 h-4 rounded-full border\"\n          style={{ backgroundColor: colors.bearish }}\n        />\n        <div \n          className=\"w-4 h-4 rounded-full border\"\n          style={{ backgroundColor: colors.background }}\n        />\n      </div>\n    )\n  }\n\n  const getPsychologyBars = (theme: any) => {\n    const { psychologyProfile } = theme\n    return (\n      <div className=\"space-y-1 text-xs\">\n        <div className=\"flex items-center justify-between\">\n          <span className={isAdolescentMode ? 'text-white/80' : 'text-green-300'}>\n            {isAdolescentMode ? '😌 Stress Relief' : 'STRESS_REDUCTION'}\n          </span>\n          <div className=\"flex space-x-1\">\n            {Array.from({ length: 10 }, (_, i) => (\n              <div\n                key={i}\n                className={`w-1 h-2 ${\n                  i < psychologyProfile.stressReduction\n                    ? 'bg-green-400'\n                    : isAdolescentMode ? 'bg-white/20' : 'bg-gray-600'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className={isAdolescentMode ? 'text-white/80' : 'text-green-300'}>\n            {isAdolescentMode ? '🎯 Focus' : 'FOCUS_ENHANCEMENT'}\n          </span>\n          <div className=\"flex space-x-1\">\n            {Array.from({ length: 10 }, (_, i) => (\n              <div\n                key={i}\n                className={`w-1 h-2 ${\n                  i < psychologyProfile.focusEnhancement\n                    ? 'bg-blue-400'\n                    : isAdolescentMode ? 'bg-white/20' : 'bg-gray-600'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className={isAdolescentMode ? 'text-white/80' : 'text-green-300'}>\n            {isAdolescentMode ? '♿ Accessibility' : 'ACCESSIBILITY'}\n          </span>\n          <div className=\"flex space-x-1\">\n            {Array.from({ length: 10 }, (_, i) => (\n              <div\n                key={i}\n                className={`w-1 h-2 ${\n                  i < psychologyProfile.accessibility\n                    ? 'bg-purple-400'\n                    : isAdolescentMode ? 'bg-white/20' : 'bg-gray-600'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`${className}`}>\n      {/* Header */}\n      <div className=\"mb-6\">\n        <h3 className={`text-lg font-bold mb-2 ${\n          isAdolescentMode ? 'text-white' : 'text-green-400'\n        }`}>\n          {isAdolescentMode ? '🎨 Choose Your Adventure Theme' : '🎨 INTERFACE_THEME_SELECTION'}\n        </h3>\n        <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n          {isAdolescentMode \n            ? 'Each theme is scientifically designed to enhance your trading experience!'\n            : 'Evidence-based color psychology themes optimized for trading performance.'\n          }\n        </p>\n      </div>\n\n      {/* Theme Grid */}\n      <div className=\"grid gap-4 mb-6\">\n        {themes.map((theme) => (\n          <div\n            key={theme.id}\n            className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n              currentThemeId === theme.id\n                ? (isAdolescentMode \n                    ? 'border-yellow-400 bg-yellow-400/10' \n                    : 'border-green-400 bg-green-400/10'\n                  )\n                : (isAdolescentMode \n                    ? 'border-white/20 hover:border-white/40 bg-white/5' \n                    : 'border-gray-600 hover:border-green-400/50 bg-gray-800/50'\n                  )\n            }`}\n            onClick={() => handleThemeChange(theme.id)}\n          >\n            <div className=\"flex items-start justify-between mb-3\">\n              <div className=\"flex-1\">\n                <h4 className={`font-bold mb-1 ${\n                  isAdolescentMode ? 'text-white' : 'text-green-400'\n                }`}>\n                  {isAdolescentMode ? theme.name : theme.name.toUpperCase()}\n                </h4>\n                <p className={`text-sm ${\n                  isAdolescentMode ? 'text-white/80' : 'text-green-300'\n                }`}>\n                  {theme.description}\n                </p>\n                <p className={`text-xs mt-1 ${\n                  isAdolescentMode ? 'text-white/60' : 'text-green-400'\n                }`}>\n                  🎨 {theme.colorTheory}\n                </p>\n              </div>\n              <div className=\"ml-4\">\n                {getThemePreview(theme.id)}\n              </div>\n            </div>\n\n            {showPsychologyInfo && (\n              <div className=\"mt-3 pt-3 border-t border-current/20\">\n                {getPsychologyBars(theme)}\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n\n      {/* Advanced Options */}\n      <div className={`border-t pt-4 ${\n        isAdolescentMode ? 'border-white/20' : 'border-gray-600'\n      }`}>\n        <button\n          onClick={() => setShowAdvanced(!showAdvanced)}\n          className={`text-sm font-medium mb-4 hover:underline ${\n            isAdolescentMode ? 'text-white/80' : 'text-green-300'\n          }`}\n        >\n          {isAdolescentMode ? '⚙️ Advanced Options' : '⚙️ ADVANCED_SETTINGS'} \n          {showAdvanced ? ' ▼' : ' ▶'}\n        </button>\n\n        {showAdvanced && (\n          <div className=\"space-y-4\">\n            {/* Auto Theme Switch */}\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className={`font-medium ${\n                  isAdolescentMode ? 'text-white' : 'text-green-400'\n                }`}>\n                  {isAdolescentMode ? '🕐 Auto Theme Switch' : '🕐 AUTO_THEME_SWITCHING'}\n                </label>\n                <p className={`text-xs ${\n                  isAdolescentMode ? 'text-white/70' : 'text-green-300'\n                }`}>\n                  {isAdolescentMode \n                    ? 'Automatically change themes based on time of day'\n                    : 'Automatic theme selection based on circadian rhythms'\n                  }\n                </p>\n              </div>\n              <button\n                onClick={toggleAutoThemeSwitch}\n                className={`w-12 h-6 rounded-full transition-colors ${\n                  autoThemeSwitch\n                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')\n                    : (isAdolescentMode ? 'bg-white/20' : 'bg-gray-600')\n                }`}\n              >\n                <div className={`w-5 h-5 rounded-full bg-white transition-transform ${\n                  autoThemeSwitch ? 'translate-x-6' : 'translate-x-0.5'\n                }`} />\n              </button>\n            </div>\n\n            {/* High Contrast */}\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className={`font-medium ${\n                  isAdolescentMode ? 'text-white' : 'text-green-400'\n                }`}>\n                  {isAdolescentMode ? '👁️ High Contrast' : '👁️ HIGH_CONTRAST_MODE'}\n                </label>\n                <p className={`text-xs ${\n                  isAdolescentMode ? 'text-white/70' : 'text-green-300'\n                }`}>\n                  {isAdolescentMode \n                    ? 'Enhanced visibility for better readability'\n                    : 'WCAG AAA compliance for visual accessibility'\n                  }\n                </p>\n              </div>\n              <button\n                onClick={toggleHighContrast}\n                className={`w-12 h-6 rounded-full transition-colors ${\n                  highContrast\n                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')\n                    : (isAdolescentMode ? 'bg-white/20' : 'bg-gray-600')\n                }`}\n              >\n                <div className={`w-5 h-5 rounded-full bg-white transition-transform ${\n                  highContrast ? 'translate-x-6' : 'translate-x-0.5'\n                }`} />\n              </button>\n            </div>\n\n            {/* Reduce Motion */}\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <label className={`font-medium ${\n                  isAdolescentMode ? 'text-white' : 'text-green-400'\n                }`}>\n                  {isAdolescentMode ? '🎭 Reduce Motion' : '🎭 REDUCE_MOTION'}\n                </label>\n                <p className={`text-xs ${\n                  isAdolescentMode ? 'text-white/70' : 'text-green-300'\n                }`}>\n                  {isAdolescentMode \n                    ? 'Minimize animations for comfort'\n                    : 'Disable animations for vestibular disorders'\n                  }\n                </p>\n              </div>\n              <button\n                onClick={toggleReduceMotion}\n                className={`w-12 h-6 rounded-full transition-colors ${\n                  reduceMotion\n                    ? (isAdolescentMode ? 'bg-yellow-400' : 'bg-green-400')\n                    : (isAdolescentMode ? 'bg-white/20' : 'bg-gray-600')\n                }`}\n              >\n                <div className={`w-5 h-5 rounded-full bg-white transition-transform ${\n                  reduceMotion ? 'translate-x-6' : 'translate-x-0.5'\n                }`} />\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Current Theme Info */}\n      {currentThemeId && (\n        <div className={`mt-6 p-4 rounded-lg ${\n          isAdolescentMode ? 'bg-white/10' : 'bg-gray-800'\n        }`}>\n          <h4 className={`font-bold mb-2 ${\n            isAdolescentMode ? 'text-white' : 'text-green-400'\n          }`}>\n            {isAdolescentMode ? '✨ Current Theme Benefits' : '📊 CURRENT_THEME_ANALYSIS'}\n          </h4>\n          {(() => {\n            const currentTheme = themes.find(t => t.id === currentThemeId)\n            if (!currentTheme) return null\n            \n            return (\n              <div className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                <p className=\"mb-2\">{currentTheme.description}</p>\n                {showPsychologyInfo && (\n                  <div className=\"text-xs\">\n                    <p>\n                      {isAdolescentMode ? '😌 Stress Relief: ' : 'STRESS_REDUCTION: '}\n                      <span className=\"font-bold\">{currentTheme.psychologyProfile.stressReduction}/10</span>\n                    </p>\n                    <p>\n                      {isAdolescentMode ? '🎯 Focus Enhancement: ' : 'FOCUS_ENHANCEMENT: '}\n                      <span className=\"font-bold\">{currentTheme.psychologyProfile.focusEnhancement}/10</span>\n                    </p>\n                    <p>\n                      {isAdolescentMode ? '♿ Accessibility: ' : 'ACCESSIBILITY: '}\n                      <span className=\"font-bold\">{currentTheme.psychologyProfile.accessibility}/10</span>\n                    </p>\n                  </div>\n                )}\n              </div>\n            )\n          })()}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYe,SAAS,cAAc,EACpC,YAAY,EAAE,EACd,qBAAqB,IAAI,EACN;;IACnB,MAAM,EACJ,cAAc,EACd,QAAQ,EACR,aAAa,EACb,eAAe,EACf,qBAAqB,EACrB,YAAY,EACZ,kBAAkB,EAClB,YAAY,EACZ,kBAAkB,EACnB,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,mBAAmB,kBAAkB;IAE3C,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,0JAAA,CAAA,iBAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,SAAS,KAAK,CAAC,cAAc;QAEnC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,OAAO,OAAO;oBAAC;;;;;;8BAE3C,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,OAAO,OAAO;oBAAC;;;;;;8BAE3C,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,OAAO,OAAO;oBAAC;;;;;;8BAE3C,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,OAAO,UAAU;oBAAC;;;;;;;;;;;;IAIpD;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,iBAAiB,EAAE,GAAG;QAC9B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAW,mBAAmB,kBAAkB;sCACnD,mBAAmB,qBAAqB;;;;;;sCAE3C,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC;oCAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,kBAAkB,eAAe,GACjC,iBACA,mBAAmB,gBAAgB,eACvC;mCALG;;;;;;;;;;;;;;;;8BAUb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAW,mBAAmB,kBAAkB;sCACnD,mBAAmB,aAAa;;;;;;sCAEnC,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC;oCAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,kBAAkB,gBAAgB,GAClC,gBACA,mBAAmB,gBAAgB,eACvC;mCALG;;;;;;;;;;;;;;;;8BAUb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAW,mBAAmB,kBAAkB;sCACnD,mBAAmB,oBAAoB;;;;;;sCAE1C,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,CAAC,GAAG,kBAC9B,6LAAC;oCAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,kBAAkB,aAAa,GAC/B,kBACA,mBAAmB,gBAAgB,eACvC;mCALG;;;;;;;;;;;;;;;;;;;;;;IAYnB;IAEA,qBACE,6LAAC;QAAI,WAAW,GAAG,WAAW;;0BAE5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAW,CAAC,uBAAuB,EACrC,mBAAmB,eAAe,kBAClC;kCACC,mBAAmB,mCAAmC;;;;;;kCAEzD,6LAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kCAC7E,mBACG,8EACA;;;;;;;;;;;;0BAMR,6LAAC;gBAAI,WAAU;0BACZ,0JAAA,CAAA,iBAAM,CAAC,GAAG,CAAC,CAAC,sBACX,6LAAC;wBAEC,WAAW,CAAC,sDAAsD,EAChE,mBAAmB,MAAM,EAAE,GACtB,mBACG,uCACA,qCAEH,mBACG,qDACA,4DAER;wBACF,SAAS,IAAM,kBAAkB,MAAM,EAAE;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAW,CAAC,eAAe,EAC7B,mBAAmB,eAAe,kBAClC;0DACC,mBAAmB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW;;;;;;0DAEzD,6LAAC;gDAAE,WAAW,CAAC,QAAQ,EACrB,mBAAmB,kBAAkB,kBACrC;0DACC,MAAM,WAAW;;;;;;0DAEpB,6LAAC;gDAAE,WAAW,CAAC,aAAa,EAC1B,mBAAmB,kBAAkB,kBACrC;;oDAAE;oDACE,MAAM,WAAW;;;;;;;;;;;;;kDAGzB,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,EAAE;;;;;;;;;;;;4BAI5B,oCACC,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB;;;;;;;uBAvClB,MAAM,EAAE;;;;;;;;;;0BA+CnB,6LAAC;gBAAI,WAAW,CAAC,cAAc,EAC7B,mBAAmB,oBAAoB,mBACvC;;kCACA,6LAAC;wBACC,SAAS,IAAM,gBAAgB,CAAC;wBAChC,WAAW,CAAC,yCAAyC,EACnD,mBAAmB,kBAAkB,kBACrC;;4BAED,mBAAmB,wBAAwB;4BAC3C,eAAe,OAAO;;;;;;;oBAGxB,8BACC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAC,YAAY,EAC7B,mBAAmB,eAAe,kBAClC;0DACC,mBAAmB,yBAAyB;;;;;;0DAE/C,6LAAC;gDAAE,WAAW,CAAC,QAAQ,EACrB,mBAAmB,kBAAkB,kBACrC;0DACC,mBACG,qDACA;;;;;;;;;;;;kDAIR,6LAAC;wCACC,SAAS;wCACT,WAAW,CAAC,wCAAwC,EAClD,kBACK,mBAAmB,kBAAkB,iBACrC,mBAAmB,gBAAgB,eACxC;kDAEF,cAAA,6LAAC;4CAAI,WAAW,CAAC,mDAAmD,EAClE,kBAAkB,kBAAkB,mBACpC;;;;;;;;;;;;;;;;;0CAKN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAC,YAAY,EAC7B,mBAAmB,eAAe,kBAClC;0DACC,mBAAmB,sBAAsB;;;;;;0DAE5C,6LAAC;gDAAE,WAAW,CAAC,QAAQ,EACrB,mBAAmB,kBAAkB,kBACrC;0DACC,mBACG,+CACA;;;;;;;;;;;;kDAIR,6LAAC;wCACC,SAAS;wCACT,WAAW,CAAC,wCAAwC,EAClD,eACK,mBAAmB,kBAAkB,iBACrC,mBAAmB,gBAAgB,eACxC;kDAEF,cAAA,6LAAC;4CAAI,WAAW,CAAC,mDAAmD,EAClE,eAAe,kBAAkB,mBACjC;;;;;;;;;;;;;;;;;0CAKN,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAW,CAAC,YAAY,EAC7B,mBAAmB,eAAe,kBAClC;0DACC,mBAAmB,qBAAqB;;;;;;0DAE3C,6LAAC;gDAAE,WAAW,CAAC,QAAQ,EACrB,mBAAmB,kBAAkB,kBACrC;0DACC,mBACG,oCACA;;;;;;;;;;;;kDAIR,6LAAC;wCACC,SAAS;wCACT,WAAW,CAAC,wCAAwC,EAClD,eACK,mBAAmB,kBAAkB,iBACrC,mBAAmB,gBAAgB,eACxC;kDAEF,cAAA,6LAAC;4CAAI,WAAW,CAAC,mDAAmD,EAClE,eAAe,kBAAkB,mBACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQX,gCACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EACnC,mBAAmB,gBAAgB,eACnC;;kCACA,6LAAC;wBAAG,WAAW,CAAC,eAAe,EAC7B,mBAAmB,eAAe,kBAClC;kCACC,mBAAmB,6BAA6B;;;;;;oBAElD,CAAC;wBACA,MAAM,eAAe,0JAAA,CAAA,iBAAM,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBAC/C,IAAI,CAAC,cAAc,OAAO;wBAE1B,qBACE,6LAAC;4BAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;;8CAChF,6LAAC;oCAAE,WAAU;8CAAQ,aAAa,WAAW;;;;;;gCAC5C,oCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDACE,mBAAmB,uBAAuB;8DAC3C,6LAAC;oDAAK,WAAU;;wDAAa,aAAa,iBAAiB,CAAC,eAAe;wDAAC;;;;;;;;;;;;;sDAE9E,6LAAC;;gDACE,mBAAmB,2BAA2B;8DAC/C,6LAAC;oDAAK,WAAU;;wDAAa,aAAa,iBAAiB,CAAC,gBAAgB;wDAAC;;;;;;;;;;;;;sDAE/E,6LAAC;;gDACE,mBAAmB,sBAAsB;8DAC1C,6LAAC;oDAAK,WAAU;;wDAAa,aAAa,iBAAiB,CAAC,aAAa;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;;oBAMtF,CAAC;;;;;;;;;;;;;AAKX;GA3UwB;;QAclB,yIAAA,CAAA,gBAAa;QAEA,wIAAA,CAAA,eAAY;;;KAhBP", "debugId": null}}, {"offset": {"line": 2667, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/app/demo/playback/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\n// import PlaybackChart from '@/components/charts/playback-chart'\nimport CandleStrikeGameComponent from '@/components/games/candle-strike-game'\nimport ThemeSelector from '@/components/theme/theme-selector'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport { useThemeColors } from '@/components/theme/theme-provider'\nimport Link from 'next/link'\n\nexport default function PlaybackDemoPage() {\n  const { interfaceMode, switchInterfaceMode } = useUserStore()\n  const [activeDemo, setActiveDemo] = useState<'playback' | 'candle-strike' | 'themes'>('playback')\n  const [symbol, setSymbol] = useState('BTCUSD')\n  const [timeframe, setTimeframe] = useState('1h')\n  const colors = useThemeColors()\n  \n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  const symbols = ['BTCUSD', 'ETHUSD', 'AAPL', 'GOOGL', 'TSLA']\n  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']\n\n  const handlePatternDetected = (pattern: any) => {\n    console.log('Pattern detected:', pattern)\n    // You could show a notification here\n  }\n\n  const handlePredictionChallenge = (challenge: any) => {\n    console.log('Prediction challenge:', challenge)\n    // Handle prediction challenge\n  }\n\n  const handleMarketEvent = (event: any) => {\n    console.log('Market event:', event)\n    // Handle market event\n  }\n\n  const handleCandleStrikeEnd = (score: number) => {\n    console.log('CandleStrike ended with score:', score)\n  }\n\n  return (\n    <div \n      className=\"min-h-screen\"\n      style={{ backgroundColor: colors.background }}\n    >\n      {/* Header */}\n      <header className=\"p-6 border-b\" style={{ borderColor: colors.border }}>\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div>\n            <Link \n              href=\"/\"\n              className=\"text-sm hover:underline mb-2 block\"\n              style={{ color: colors.textSecondary }}\n            >\n              ← {isAdolescentMode ? 'Back to Quest Hub' : 'RETURN_TO_MAIN'}\n            </Link>\n            <h1 className=\"text-3xl font-bold\" style={{ color: colors.textPrimary }}>\n              {isAdolescentMode ? '🚀 Advanced Trading Features Demo' : '📊 ADVANCED_TRADING_SYSTEMS_DEMO'}\n            </h1>\n            <p className=\"mt-2\" style={{ color: colors.textSecondary }}>\n              {isAdolescentMode \n                ? 'Experience the future of trading education with real-time chart playback, pattern recognition, and adaptive themes!'\n                : 'Comprehensive demonstration of chart playback systems, pattern recognition algorithms, and evidence-based interface themes.'\n              }\n            </p>\n          </div>\n          \n          <div className=\"flex items-center gap-4\">\n            <button\n              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}\n              className=\"px-4 py-2 rounded-lg transition-colors\"\n              style={{ \n                backgroundColor: colors.backgroundSecondary,\n                borderColor: colors.border,\n                color: colors.textPrimary\n              }}\n            >\n              {isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'}\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"p-6 border-b\" style={{ borderColor: colors.border }}>\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"flex space-x-4\">\n            {[\n              { id: 'playback', label: isAdolescentMode ? '📈 Chart Playback' : '📈 CHART_PLAYBACK', icon: '📈' },\n              { id: 'candle-strike', label: isAdolescentMode ? '🕯️ Pattern Recognition' : '🕯️ PATTERN_RECOGNITION', icon: '🕯️' },\n              { id: 'themes', label: isAdolescentMode ? '🎨 Theme System' : '🎨 THEME_SYSTEM', icon: '🎨' },\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveDemo(tab.id as any)}\n                className={`px-6 py-3 rounded-lg font-bold transition-all ${\n                  activeDemo === tab.id ? 'shadow-lg' : ''\n                }`}\n                style={{\n                  backgroundColor: activeDemo === tab.id ? colors.primary : colors.backgroundSecondary,\n                  color: activeDemo === tab.id ? colors.background : colors.textPrimary,\n                  borderColor: colors.border,\n                }}\n              >\n                {tab.label}\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto p-6\">\n        {activeDemo === 'playback' && (\n          <div className=\"space-y-6\">\n            {/* Controls */}\n            <div \n              className=\"p-6 rounded-lg\"\n              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n            >\n              <h2 className=\"text-xl font-bold mb-4\" style={{ color: colors.textPrimary }}>\n                {isAdolescentMode ? '⚙️ Chart Configuration' : '⚙️ CHART_CONFIGURATION'}\n              </h2>\n              \n              <div className=\"grid md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium mb-2\" style={{ color: colors.textSecondary }}>\n                    {isAdolescentMode ? 'Trading Pair' : 'TRADING_PAIR'}\n                  </label>\n                  <select\n                    value={symbol}\n                    onChange={(e) => setSymbol(e.target.value)}\n                    className=\"w-full p-2 rounded border\"\n                    style={{ \n                      backgroundColor: colors.backgroundTertiary,\n                      borderColor: colors.border,\n                      color: colors.textPrimary\n                    }}\n                  >\n                    {symbols.map(s => (\n                      <option key={s} value={s}>{s}</option>\n                    ))}\n                  </select>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium mb-2\" style={{ color: colors.textSecondary }}>\n                    {isAdolescentMode ? 'Time Frame' : 'TIMEFRAME'}\n                  </label>\n                  <select\n                    value={timeframe}\n                    onChange={(e) => setTimeframe(e.target.value)}\n                    className=\"w-full p-2 rounded border\"\n                    style={{ \n                      backgroundColor: colors.backgroundTertiary,\n                      borderColor: colors.border,\n                      color: colors.textPrimary\n                    }}\n                  >\n                    {timeframes.map(tf => (\n                      <option key={tf} value={tf}>{tf}</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n            </div>\n\n            {/* Playback Chart */}\n            <div \n              className=\"p-6 rounded-lg\"\n              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n            >\n              <h2 className=\"text-xl font-bold mb-4\" style={{ color: colors.textPrimary }}>\n                {isAdolescentMode ? '📊 Interactive Historical Chart' : '📊 INTERACTIVE_HISTORICAL_CHART'}\n              </h2>\n              \n              <div className=\"w-full h-96 border rounded-lg flex items-center justify-center\" style={{ backgroundColor: colors.backgroundTertiary, borderColor: colors.border }}>\n                <div className=\"text-center\">\n                  <div className=\"text-4xl mb-4\">📈</div>\n                  <p style={{ color: colors.textPrimary }}>\n                    {isAdolescentMode ? 'Interactive Chart Coming Soon!' : 'CHART_SYSTEM_UNDER_DEVELOPMENT'}\n                  </p>\n                  <p style={{ color: colors.textSecondary }} className=\"text-sm mt-2\">\n                    Symbol: {symbol} | Timeframe: {timeframe}\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* Features List */}\n            <div \n              className=\"p-6 rounded-lg\"\n              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n            >\n              <h3 className=\"text-lg font-bold mb-4\" style={{ color: colors.textPrimary }}>\n                {isAdolescentMode ? '✨ Playback Features' : '📋 PLAYBACK_FEATURES'}\n              </h3>\n              \n              <div className=\"grid md:grid-cols-2 gap-4\">\n                {[\n                  {\n                    title: isAdolescentMode ? '⏯️ Time Travel Controls' : '⏯️ TEMPORAL_CONTROLS',\n                    description: isAdolescentMode \n                      ? 'Play, pause, and scrub through historical data at different speeds'\n                      : 'Play/pause/scrub controls with variable speed adjustment'\n                  },\n                  {\n                    title: isAdolescentMode ? '🔍 Pattern Detection' : '🔍 PATTERN_DETECTION',\n                    description: isAdolescentMode \n                      ? 'Real-time identification of candlestick patterns as they form'\n                      : 'Real-time candlestick pattern recognition algorithms'\n                  },\n                  {\n                    title: isAdolescentMode ? '🎯 Prediction Challenges' : '🎯 PREDICTION_CHALLENGES',\n                    description: isAdolescentMode \n                      ? 'Test your market prediction skills at key moments'\n                      : 'Interactive prediction challenges at critical market points'\n                  },\n                  {\n                    title: isAdolescentMode ? '📰 Market Events' : '📰 MARKET_EVENTS',\n                    description: isAdolescentMode \n                      ? 'Learn how news and events affect price movements'\n                      : 'Historical market events with impact analysis'\n                  },\n                ].map((feature, index) => (\n                  <div key={index} className=\"p-4 rounded border\" style={{ borderColor: colors.border }}>\n                    <h4 className=\"font-bold mb-2\" style={{ color: colors.textPrimary }}>\n                      {feature.title}\n                    </h4>\n                    <p className=\"text-sm\" style={{ color: colors.textSecondary }}>\n                      {feature.description}\n                    </p>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {activeDemo === 'candle-strike' && (\n          <div className=\"space-y-6\">\n            <div \n              className=\"p-6 rounded-lg\"\n              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n            >\n              <h2 className=\"text-xl font-bold mb-4\" style={{ color: colors.textPrimary }}>\n                {isAdolescentMode ? '🕯️ Enhanced Pattern Recognition Game' : '🕯️ ENHANCED_PATTERN_RECOGNITION'}\n              </h2>\n              \n              <CandleStrikeGameComponent\n                difficulty=\"beginner\"\n                onGameEnd={handleCandleStrikeEnd}\n                usePlayback={true}\n                className=\"w-full\"\n              />\n            </div>\n          </div>\n        )}\n\n        {activeDemo === 'themes' && (\n          <div className=\"space-y-6\">\n            <div \n              className=\"p-6 rounded-lg\"\n              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n            >\n              <h2 className=\"text-xl font-bold mb-4\" style={{ color: colors.textPrimary }}>\n                {isAdolescentMode ? '🎨 Evidence-Based Color Psychology Themes' : '🎨 COLOR_PSYCHOLOGY_THEME_SYSTEM'}\n              </h2>\n              \n              <ThemeSelector showPsychologyInfo={true} />\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA,iEAAiE;AACjE;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2C;IACtF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,mJAAA,CAAA,iBAAc,AAAD;IAE5B,MAAM,mBAAmB,kBAAkB;IAE3C,MAAM,UAAU;QAAC;QAAU;QAAU;QAAQ;QAAS;KAAO;IAC7D,MAAM,aAAa;QAAC;QAAM;QAAM;QAAO;QAAM;QAAM;KAAK;IAExD,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,qBAAqB;IACjC,qCAAqC;IACvC;IAEA,MAAM,4BAA4B,CAAC;QACjC,QAAQ,GAAG,CAAC,yBAAyB;IACrC,8BAA8B;IAChC;IAEA,MAAM,oBAAoB,CAAC;QACzB,QAAQ,GAAG,CAAC,iBAAiB;IAC7B,sBAAsB;IACxB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,kCAAkC;IAChD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB,OAAO,UAAU;QAAC;;0BAG5C,6LAAC;gBAAO,WAAU;gBAAe,OAAO;oBAAE,aAAa,OAAO,MAAM;gBAAC;0BACnE,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;oCACV,OAAO;wCAAE,OAAO,OAAO,aAAa;oCAAC;;wCACtC;wCACI,mBAAmB,sBAAsB;;;;;;;8CAE9C,6LAAC;oCAAG,WAAU;oCAAqB,OAAO;wCAAE,OAAO,OAAO,WAAW;oCAAC;8CACnE,mBAAmB,sCAAsC;;;;;;8CAE5D,6LAAC;oCAAE,WAAU;oCAAO,OAAO;wCAAE,OAAO,OAAO,aAAa;oCAAC;8CACtD,mBACG,wHACA;;;;;;;;;;;;sCAKR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,oBAAoB,mBAAmB,UAAU;gCAChE,WAAU;gCACV,OAAO;oCACL,iBAAiB,OAAO,mBAAmB;oCAC3C,aAAa,OAAO,MAAM;oCAC1B,OAAO,OAAO,WAAW;gCAC3B;0CAEC,mBAAmB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;0BAO5C,6LAAC;gBAAI,WAAU;gBAAe,OAAO;oBAAE,aAAa,OAAO,MAAM;gBAAC;0BAChE,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,IAAI;gCAAY,OAAO,mBAAmB,sBAAsB;gCAAqB,MAAM;4BAAK;4BAClG;gCAAE,IAAI;gCAAiB,OAAO,mBAAmB,4BAA4B;gCAA2B,MAAM;4BAAM;4BACpH;gCAAE,IAAI;gCAAU,OAAO,mBAAmB,oBAAoB;gCAAmB,MAAM;4BAAK;yBAC7F,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;gCAEC,SAAS,IAAM,cAAc,IAAI,EAAE;gCACnC,WAAW,CAAC,8CAA8C,EACxD,eAAe,IAAI,EAAE,GAAG,cAAc,IACtC;gCACF,OAAO;oCACL,iBAAiB,eAAe,IAAI,EAAE,GAAG,OAAO,OAAO,GAAG,OAAO,mBAAmB;oCACpF,OAAO,eAAe,IAAI,EAAE,GAAG,OAAO,UAAU,GAAG,OAAO,WAAW;oCACrE,aAAa,OAAO,MAAM;gCAC5B;0CAEC,IAAI,KAAK;+BAXL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;0BAmBrB,6LAAC;gBAAK,WAAU;;oBACb,eAAe,4BACd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,OAAO,mBAAmB;oCAAE,aAAa,OAAO,MAAM;gCAAC;;kDAEjF,6LAAC;wCAAG,WAAU;wCAAyB,OAAO;4CAAE,OAAO,OAAO,WAAW;wCAAC;kDACvE,mBAAmB,2BAA2B;;;;;;kDAGjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;wDAAiC,OAAO;4DAAE,OAAO,OAAO,aAAa;wDAAC;kEACpF,mBAAmB,iBAAiB;;;;;;kEAEvC,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wDACzC,WAAU;wDACV,OAAO;4DACL,iBAAiB,OAAO,kBAAkB;4DAC1C,aAAa,OAAO,MAAM;4DAC1B,OAAO,OAAO,WAAW;wDAC3B;kEAEC,QAAQ,GAAG,CAAC,CAAA,kBACX,6LAAC;gEAAe,OAAO;0EAAI;+DAAd;;;;;;;;;;;;;;;;0DAKnB,6LAAC;;kEACC,6LAAC;wDAAM,WAAU;wDAAiC,OAAO;4DAAE,OAAO,OAAO,aAAa;wDAAC;kEACpF,mBAAmB,eAAe;;;;;;kEAErC,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wDAC5C,WAAU;wDACV,OAAO;4DACL,iBAAiB,OAAO,kBAAkB;4DAC1C,aAAa,OAAO,MAAM;4DAC1B,OAAO,OAAO,WAAW;wDAC3B;kEAEC,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gEAAgB,OAAO;0EAAK;+DAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQvB,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,OAAO,mBAAmB;oCAAE,aAAa,OAAO,MAAM;gCAAC;;kDAEjF,6LAAC;wCAAG,WAAU;wCAAyB,OAAO;4CAAE,OAAO,OAAO,WAAW;wCAAC;kDACvE,mBAAmB,oCAAoC;;;;;;kDAG1D,6LAAC;wCAAI,WAAU;wCAAiE,OAAO;4CAAE,iBAAiB,OAAO,kBAAkB;4CAAE,aAAa,OAAO,MAAM;wCAAC;kDAC9J,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAE,OAAO;wDAAE,OAAO,OAAO,WAAW;oDAAC;8DACnC,mBAAmB,mCAAmC;;;;;;8DAEzD,6LAAC;oDAAE,OAAO;wDAAE,OAAO,OAAO,aAAa;oDAAC;oDAAG,WAAU;;wDAAe;wDACzD;wDAAO;wDAAe;;;;;;;;;;;;;;;;;;;;;;;;0CAOvC,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,OAAO,mBAAmB;oCAAE,aAAa,OAAO,MAAM;gCAAC;;kDAEjF,6LAAC;wCAAG,WAAU;wCAAyB,OAAO;4CAAE,OAAO,OAAO,WAAW;wCAAC;kDACvE,mBAAmB,wBAAwB;;;;;;kDAG9C,6LAAC;wCAAI,WAAU;kDACZ;4CACC;gDACE,OAAO,mBAAmB,4BAA4B;gDACtD,aAAa,mBACT,uEACA;4CACN;4CACA;gDACE,OAAO,mBAAmB,yBAAyB;gDACnD,aAAa,mBACT,kEACA;4CACN;4CACA;gDACE,OAAO,mBAAmB,6BAA6B;gDACvD,aAAa,mBACT,sDACA;4CACN;4CACA;gDACE,OAAO,mBAAmB,qBAAqB;gDAC/C,aAAa,mBACT,qDACA;4CACN;yCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;gDAAgB,WAAU;gDAAqB,OAAO;oDAAE,aAAa,OAAO,MAAM;gDAAC;;kEAClF,6LAAC;wDAAG,WAAU;wDAAiB,OAAO;4DAAE,OAAO,OAAO,WAAW;wDAAC;kEAC/D,QAAQ,KAAK;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;wDAAU,OAAO;4DAAE,OAAO,OAAO,aAAa;wDAAC;kEACzD,QAAQ,WAAW;;;;;;;+CALd;;;;;;;;;;;;;;;;;;;;;;oBAcnB,eAAe,iCACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,OAAO,mBAAmB;gCAAE,aAAa,OAAO,MAAM;4BAAC;;8CAEjF,6LAAC;oCAAG,WAAU;oCAAyB,OAAO;wCAAE,OAAO,OAAO,WAAW;oCAAC;8CACvE,mBAAmB,0CAA0C;;;;;;8CAGhE,6LAAC,0JAAA,CAAA,UAAyB;oCACxB,YAAW;oCACX,WAAW;oCACX,aAAa;oCACb,WAAU;;;;;;;;;;;;;;;;;oBAMjB,eAAe,0BACd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,OAAO,mBAAmB;gCAAE,aAAa,OAAO,MAAM;4BAAC;;8CAEjF,6LAAC;oCAAG,WAAU;oCAAyB,OAAO;wCAAE,OAAO,OAAO,WAAW;oCAAC;8CACvE,mBAAmB,8CAA8C;;;;;;8CAGpE,6LAAC,mJAAA,CAAA,UAAa;oCAAC,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GA3QwB;;QACyB,wIAAA,CAAA,eAAY;QAI5C,mJAAA,CAAA,iBAAc;;;KALP", "debugId": null}}]}