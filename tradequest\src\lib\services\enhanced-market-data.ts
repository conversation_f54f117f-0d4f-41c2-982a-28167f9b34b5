/**
 * Enhanced Market Data Service with Real Historical Data Integration
 * Supports multiple data providers and timeframes for immersive chart playback
 */

import { CandlestickData } from '@/types'

export interface MarketDataProvider {
  name: string
  baseUrl: string
  apiKey?: string
  rateLimit: number // requests per minute
  supportedSymbols: string[]
  supportedTimeframes: string[]
}

export interface HistoricalDataRequest {
  symbol: string
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  startDate: Date
  endDate: Date
  limit?: number
}

export interface PlaybackEvent {
  timestamp: number
  type: 'price_update' | 'volume_spike' | 'news_event' | 'pattern_detected'
  data: any
  description?: string
}

export interface MarketEvent {
  timestamp: number
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  category: 'earnings' | 'news' | 'economic' | 'technical'
}

class EnhancedMarketDataService {
  private providers: Map<string, MarketDataProvider> = new Map()
  private cache: Map<string, { data: CandlestickData[], timestamp: number }> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  constructor() {
    this.initializeProviders()
  }

  private initializeProviders() {
    // Alpha Vantage Provider
    this.providers.set('alphavantage', {
      name: 'Alpha Vantage',
      baseUrl: 'https://www.alphavantage.co/query',
      apiKey: process.env.ALPHA_VANTAGE_API_KEY,
      rateLimit: 5, // 5 requests per minute for free tier
      supportedSymbols: ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY', 'QQQ'],
      supportedTimeframes: ['1m', '5m', '15m', '30m', '60m', '1d'],
    })

    // Polygon.io Provider
    this.providers.set('polygon', {
      name: 'Polygon.io',
      baseUrl: 'https://api.polygon.io/v2/aggs/ticker',
      apiKey: process.env.POLYGON_API_KEY,
      rateLimit: 5, // 5 requests per minute for free tier
      supportedSymbols: ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY', 'QQQ'],
      supportedTimeframes: ['1m', '5m', '15m', '1h', '1d'],
    })

    // CoinGecko for Crypto (Free)
    this.providers.set('coingecko', {
      name: 'CoinGecko',
      baseUrl: 'https://api.coingecko.com/api/v3',
      rateLimit: 10, // 10-30 requests per minute
      supportedSymbols: ['bitcoin', 'ethereum', 'cardano', 'solana', 'polkadot'],
      supportedTimeframes: ['1h', '4h', '1d'],
    })
  }

  // Get historical data with automatic provider selection
  async getHistoricalData(request: HistoricalDataRequest): Promise<CandlestickData[]> {
    const cacheKey = this.generateCacheKey(request)
    
    // Check cache first
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.data
    }

    try {
      let data: CandlestickData[] = []

      // Try different providers based on symbol type
      if (this.isCryptoSymbol(request.symbol)) {
        data = await this.fetchFromCoinGecko(request)
      } else if (this.isStockSymbol(request.symbol)) {
        // Try Alpha Vantage first, fallback to Polygon
        try {
          data = await this.fetchFromAlphaVantage(request)
        } catch (error) {
          console.warn('Alpha Vantage failed, trying Polygon:', error)
          data = await this.fetchFromPolygon(request)
        }
      }

      // If no real data available, generate realistic mock data
      if (data.length === 0) {
        console.warn('No real data available, generating mock data for:', request.symbol)
        data = this.generateRealisticHistoricalData(request)
      }

      // Cache the result
      this.cache.set(cacheKey, { data, timestamp: Date.now() })
      
      return data
    } catch (error) {
      console.error('Error fetching historical data:', error)
      // Fallback to mock data
      return this.generateRealisticHistoricalData(request)
    }
  }

  // Fetch from Alpha Vantage
  private async fetchFromAlphaVantage(request: HistoricalDataRequest): Promise<CandlestickData[]> {
    const provider = this.providers.get('alphavantage')!
    if (!provider.apiKey) {
      throw new Error('Alpha Vantage API key not configured')
    }

    const timeframeMap: Record<string, string> = {
      '1m': '1min',
      '5m': '5min',
      '15m': '15min',
      '1h': '60min',
      '1d': 'daily',
    }

    const interval = timeframeMap[request.timeframe] || 'daily'
    const functionType = request.timeframe === '1d' ? 'TIME_SERIES_DAILY' : 'TIME_SERIES_INTRADAY'
    
    const params = new URLSearchParams({
      function: functionType,
      symbol: request.symbol,
      apikey: provider.apiKey,
      outputsize: 'full',
      ...(functionType === 'TIME_SERIES_INTRADAY' && { interval }),
    })

    const response = await fetch(`${provider.baseUrl}?${params}`)
    const data = await response.json()

    if (data['Error Message']) {
      throw new Error(data['Error Message'])
    }

    // Parse Alpha Vantage response
    const timeSeriesKey = Object.keys(data).find(key => key.includes('Time Series'))
    if (!timeSeriesKey) {
      throw new Error('Invalid Alpha Vantage response format')
    }

    const timeSeries = data[timeSeriesKey]
    const candlesticks: CandlestickData[] = []

    for (const [timestamp, values] of Object.entries(timeSeries)) {
      const candle = values as any
      candlesticks.push({
        timestamp: new Date(timestamp).getTime(),
        open: parseFloat(candle['1. open']),
        high: parseFloat(candle['2. high']),
        low: parseFloat(candle['3. low']),
        close: parseFloat(candle['4. close']),
        volume: parseFloat(candle['5. volume']),
      })
    }

    // Filter by date range and sort
    return candlesticks
      .filter(candle => {
        const date = new Date(candle.timestamp)
        return date >= request.startDate && date <= request.endDate
      })
      .sort((a, b) => a.timestamp - b.timestamp)
      .slice(0, request.limit || 1000)
  }

  // Fetch from Polygon.io
  private async fetchFromPolygon(request: HistoricalDataRequest): Promise<CandlestickData[]> {
    const provider = this.providers.get('polygon')!
    if (!provider.apiKey) {
      throw new Error('Polygon API key not configured')
    }

    const timeframeMap: Record<string, { multiplier: number, timespan: string }> = {
      '1m': { multiplier: 1, timespan: 'minute' },
      '5m': { multiplier: 5, timespan: 'minute' },
      '15m': { multiplier: 15, timespan: 'minute' },
      '1h': { multiplier: 1, timespan: 'hour' },
      '1d': { multiplier: 1, timespan: 'day' },
    }

    const { multiplier, timespan } = timeframeMap[request.timeframe] || { multiplier: 1, timespan: 'day' }
    
    const startDate = request.startDate.toISOString().split('T')[0]
    const endDate = request.endDate.toISOString().split('T')[0]

    const url = `${provider.baseUrl}/${request.symbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?apikey=${provider.apiKey}`
    
    const response = await fetch(url)
    const data = await response.json()

    if (data.status !== 'OK') {
      throw new Error(data.error || 'Polygon API error')
    }

    // Parse Polygon response
    const candlesticks: CandlestickData[] = data.results?.map((result: any) => ({
      timestamp: result.t,
      open: result.o,
      high: result.h,
      low: result.l,
      close: result.c,
      volume: result.v,
    })) || []

    return candlesticks
      .sort((a, b) => a.timestamp - b.timestamp)
      .slice(0, request.limit || 1000)
  }

  // Fetch from CoinGecko
  private async fetchFromCoinGecko(request: HistoricalDataRequest): Promise<CandlestickData[]> {
    const provider = this.providers.get('coingecko')!
    
    // CoinGecko uses different symbol format
    const coinId = this.getCoinGeckoId(request.symbol)
    const days = Math.ceil((request.endDate.getTime() - request.startDate.getTime()) / (1000 * 60 * 60 * 24))
    
    const url = `${provider.baseUrl}/coins/${coinId}/ohlc?vs_currency=usd&days=${days}`
    
    const response = await fetch(url)
    const data = await response.json()

    if (!Array.isArray(data)) {
      throw new Error('Invalid CoinGecko response')
    }

    // Parse CoinGecko OHLC data
    const candlesticks: CandlestickData[] = data.map((ohlc: number[]) => ({
      timestamp: ohlc[0],
      open: ohlc[1],
      high: ohlc[2],
      low: ohlc[3],
      close: ohlc[4],
      volume: Math.random() * 1000000, // CoinGecko OHLC doesn't include volume
    }))

    return candlesticks
      .filter(candle => {
        const date = new Date(candle.timestamp)
        return date >= request.startDate && date <= request.endDate
      })
      .sort((a, b) => a.timestamp - b.timestamp)
      .slice(0, request.limit || 1000)
  }

  // Generate realistic historical data with patterns and events
  private generateRealisticHistoricalData(request: HistoricalDataRequest): CandlestickData[] {
    const { symbol, timeframe, startDate, endDate, limit = 1000 } = request
    
    const timeframeMs = this.getTimeframeMs(timeframe)
    const totalCandles = Math.min(
      Math.floor((endDate.getTime() - startDate.getTime()) / timeframeMs),
      limit
    )

    const candlesticks: CandlestickData[] = []
    let currentPrice = this.getBasePrice(symbol)
    let currentTime = startDate.getTime()

    // Add market events and patterns
    const events = this.generateMarketEvents(startDate, endDate, symbol)
    
    for (let i = 0; i < totalCandles; i++) {
      // Check for events at this timestamp
      const currentEvent = events.find(event => 
        Math.abs(event.timestamp - currentTime) < timeframeMs
      )

      // Generate candle with event influence
      const candle = this.generateCandleWithEvent(
        currentPrice, 
        currentTime, 
        symbol, 
        timeframe, 
        currentEvent
      )
      
      candlesticks.push(candle)
      currentPrice = candle.close
      currentTime += timeframeMs
    }

    return candlesticks
  }

  // Generate market events for educational purposes
  private generateMarketEvents(startDate: Date, endDate: Date, symbol: string): MarketEvent[] {
    const events: MarketEvent[] = []
    const duration = endDate.getTime() - startDate.getTime()
    const eventCount = Math.floor(duration / (1000 * 60 * 60 * 24 * 7)) // Weekly events
    
    for (let i = 0; i < eventCount; i++) {
      const timestamp = startDate.getTime() + (duration * Math.random())
      
      events.push({
        timestamp,
        title: this.getRandomEventTitle(symbol),
        description: this.getRandomEventDescription(symbol),
        impact: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
        category: ['earnings', 'news', 'economic', 'technical'][Math.floor(Math.random() * 4)] as any,
      })
    }
    
    return events.sort((a, b) => a.timestamp - b.timestamp)
  }

  // Helper methods
  private generateCacheKey(request: HistoricalDataRequest): string {
    return `${request.symbol}_${request.timeframe}_${request.startDate.getTime()}_${request.endDate.getTime()}`
  }

  private isCryptoSymbol(symbol: string): boolean {
    return ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'bitcoin', 'ethereum'].includes(symbol.toLowerCase())
  }

  private isStockSymbol(symbol: string): boolean {
    return ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY', 'QQQ'].includes(symbol.toUpperCase())
  }

  private getCoinGeckoId(symbol: string): string {
    const mapping: Record<string, string> = {
      'BTC': 'bitcoin',
      'ETH': 'ethereum',
      'ADA': 'cardano',
      'SOL': 'solana',
      'DOT': 'polkadot',
    }
    return mapping[symbol.toUpperCase()] || 'bitcoin'
  }

  private getTimeframeMs(timeframe: string): number {
    const mapping: Record<string, number> = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
    }
    return mapping[timeframe] || 60 * 60 * 1000
  }

  private getBasePrice(symbol: string): number {
    const prices: Record<string, number> = {
      'AAPL': 150,
      'GOOGL': 2500,
      'MSFT': 300,
      'TSLA': 800,
      'SPY': 400,
      'BTC': 45000,
      'ETH': 3000,
    }
    return prices[symbol.toUpperCase()] || 100
  }

  private generateCandleWithEvent(
    basePrice: number, 
    timestamp: number, 
    symbol: string, 
    timeframe: string, 
    event?: MarketEvent
  ): CandlestickData {
    let volatility = 0.02 // 2% base volatility
    
    // Increase volatility for events
    if (event) {
      const eventMultiplier = { low: 1.5, medium: 2.5, high: 4.0 }
      volatility *= eventMultiplier[event.impact]
    }

    const change = (Math.random() - 0.5) * volatility * basePrice
    const open = basePrice
    const close = basePrice + change
    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice
    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice

    return {
      timestamp,
      open,
      high,
      low,
      close,
      volume: (500000 + Math.random() * 1000000) * (event ? 2 : 1),
    }
  }

  private getRandomEventTitle(symbol: string): string {
    const titles = [
      `${symbol} Earnings Report Released`,
      `Major ${symbol} Partnership Announced`,
      `${symbol} Stock Split Declared`,
      `Analyst Upgrades ${symbol} Rating`,
      `${symbol} CEO Interview on Market Outlook`,
      `Regulatory News Affects ${symbol}`,
      `${symbol} Technical Breakout Detected`,
    ]
    return titles[Math.floor(Math.random() * titles.length)]
  }

  private getRandomEventDescription(symbol: string): string {
    const descriptions = [
      `Quarterly earnings exceeded expectations with strong revenue growth.`,
      `Strategic partnership announcement drives investor confidence.`,
      `Stock split announcement indicates management confidence in future growth.`,
      `Analyst upgrade based on improved fundamentals and market position.`,
      `CEO provides positive outlook on company direction and market opportunities.`,
      `Regulatory developments create uncertainty in the market.`,
      `Technical analysis indicates potential breakout from consolidation pattern.`,
    ]
    return descriptions[Math.floor(Math.random() * descriptions.length)]
  }
}

export const enhancedMarketDataService = new EnhancedMarketDataService()
