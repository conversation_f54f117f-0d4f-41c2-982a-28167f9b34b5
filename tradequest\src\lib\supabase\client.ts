import { createBrowserClient } from '@supabase/ssr'

// Mock client for development when Supabase is not configured
const createMockClient = () => ({
  auth: {
    signInWithPassword: async () => ({ data: null, error: { message: 'Demo mode - authentication disabled' } }),
    signUp: async () => ({ data: null, error: { message: 'Demo mode - registration disabled' } }),
    signOut: async () => ({ error: null }),
    getSession: async () => ({ data: { session: null }, error: null }),
    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
    signInWithOAuth: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),
    exchangeCodeForSession: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),
  },
  from: () => ({
    select: () => ({
      eq: () => ({
        single: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })
      })
    }),
    insert: () => ({
      select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })
    }),
    update: () => ({
      eq: () => ({
        select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })
      })
    })
  })
})

export function createClient() {
  // Check if Supabase environment variables are properly configured
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey || supabaseUrl.includes('demo') || supabaseKey.includes('demo')) {
    console.warn('⚠️  Supabase not configured - running in demo mode. Authentication and database features will be disabled.')
    return createMockClient() as any
  }

  try {
    return createBrowserClient(supabaseUrl, supabaseKey)
  } catch (error) {
    console.error('Failed to create Supabase client:', error)
    console.warn('Falling back to demo mode')
    return createMockClient() as any
  }
}
