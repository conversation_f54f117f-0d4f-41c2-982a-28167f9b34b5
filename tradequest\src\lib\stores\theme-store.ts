import React from 'react'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { themes, getThemeById, getThemeColors, generateCSSVariables, type ThemeConfig, type ColorPalette } from '@/lib/themes/color-psychology'

interface ThemeState {
  // Current theme
  currentThemeId: string
  currentTheme: ThemeConfig
  
  // Interface mode affects which color palette is used
  interfaceMode: 'adolescent' | 'adult'
  
  // Current active colors
  colors: ColorPalette
  
  // Theme preferences
  autoThemeSwitch: boolean // Switch theme based on time of day
  reduceMotion: boolean
  highContrast: boolean
  
  // Actions
  setTheme: (themeId: string) => void
  setInterfaceMode: (mode: 'adolescent' | 'adult') => void
  toggleAutoThemeSwitch: () => void
  toggleReduceMotion: () => void
  toggleHighContrast: () => void
  applyThemeToDOM: () => void
  getRecommendedTheme: () => string
}

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentThemeId: 'professional-dark',
      currentTheme: themes[0],
      interfaceMode: 'adolescent',
      colors: themes[0].adolescent,
      autoThemeSwitch: false,
      reduceMotion: false,
      highContrast: false,

      // Set theme by ID
      setTheme: (themeId: string) => {
        const theme = getThemeById(themeId)
        if (!theme) return

        const { interfaceMode } = get()
        const colors = getThemeColors(themeId, interfaceMode)

        set({
          currentThemeId: themeId,
          currentTheme: theme,
          colors,
        })

        // Apply to DOM immediately
        get().applyThemeToDOM()
      },

      // Set interface mode (adolescent/adult)
      setInterfaceMode: (mode: 'adolescent' | 'adult') => {
        const { currentThemeId } = get()
        const colors = getThemeColors(currentThemeId, mode)

        set({
          interfaceMode: mode,
          colors,
        })

        // Apply to DOM immediately
        get().applyThemeToDOM()
      },

      // Toggle auto theme switching
      toggleAutoThemeSwitch: () => {
        const { autoThemeSwitch } = get()
        set({ autoThemeSwitch: !autoThemeSwitch })

        // If enabling auto switch, apply recommended theme
        if (!autoThemeSwitch) {
          const recommendedTheme = get().getRecommendedTheme()
          get().setTheme(recommendedTheme)
        }
      },

      // Toggle reduced motion
      toggleReduceMotion: () => {
        const { reduceMotion } = get()
        set({ reduceMotion: !reduceMotion })
        
        // Apply to DOM
        document.documentElement.style.setProperty(
          '--animation-duration',
          !reduceMotion ? '0s' : '0.3s'
        )
      },

      // Toggle high contrast mode
      toggleHighContrast: () => {
        const { highContrast } = get()
        const newHighContrast = !highContrast
        
        set({ highContrast: newHighContrast })

        // If enabling high contrast, switch to high contrast theme
        if (newHighContrast) {
          get().setTheme('high-contrast')
        }
      },

      // Apply current theme colors to DOM as CSS custom properties
      applyThemeToDOM: () => {
        const { colors, reduceMotion } = get()
        const cssVariables = generateCSSVariables(colors)

        // Apply CSS custom properties to document root
        Object.entries(cssVariables).forEach(([property, value]) => {
          document.documentElement.style.setProperty(property, value)
        })

        // Apply motion preferences
        document.documentElement.style.setProperty(
          '--animation-duration',
          reduceMotion ? '0s' : '0.3s'
        )

        // Apply theme class to body for additional styling
        document.body.className = document.body.className
          .replace(/theme-\w+/g, '')
          .concat(` theme-${get().currentThemeId}`)
      },

      // Get recommended theme based on various factors
      getRecommendedTheme: (): string => {
        const hour = new Date().getHours()
        const { highContrast } = get()

        // High contrast override
        if (highContrast) {
          return 'high-contrast'
        }

        // Time-based recommendations
        if (hour >= 6 && hour < 12) {
          // Morning: Energizing warm theme
          return 'warm-productivity'
        } else if (hour >= 12 && hour < 18) {
          // Afternoon: Focus-enhancing calm theme
          return 'calm-focus'
        } else if (hour >= 18 && hour < 22) {
          // Evening: Professional theme for serious work
          return 'professional-dark'
        } else {
          // Night: Stress-reducing calm theme
          return 'calm-focus'
        }
      },
    }),
    {
      name: 'tradequest-theme-storage',
      partialize: (state) => ({
        currentThemeId: state.currentThemeId,
        interfaceMode: state.interfaceMode,
        autoThemeSwitch: state.autoThemeSwitch,
        reduceMotion: state.reduceMotion,
        highContrast: state.highContrast,
      }),
    }
  )
)

// Theme initialization hook
export const useThemeInitialization = () => {
  const { 
    setTheme, 
    setInterfaceMode, 
    applyThemeToDOM, 
    getRecommendedTheme,
    autoThemeSwitch,
    currentThemeId,
    interfaceMode 
  } = useThemeStore()

  // Initialize theme on mount
  React.useEffect(() => {
    // Apply current theme to DOM
    applyThemeToDOM()

    // Auto theme switching
    if (autoThemeSwitch) {
      const recommendedTheme = getRecommendedTheme()
      if (recommendedTheme !== currentThemeId) {
        setTheme(recommendedTheme)
      }
    }

    // Listen for system preference changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    const handleMotionChange = (e: MediaQueryListEvent) => {
      useThemeStore.getState().toggleReduceMotion()
    }
    
    mediaQuery.addEventListener('change', handleMotionChange)
    
    return () => {
      mediaQuery.removeEventListener('change', handleMotionChange)
    }
  }, [])

  // Auto theme switching interval
  React.useEffect(() => {
    if (!autoThemeSwitch) return

    const interval = setInterval(() => {
      const recommendedTheme = getRecommendedTheme()
      if (recommendedTheme !== currentThemeId) {
        setTheme(recommendedTheme)
      }
    }, 60000) // Check every minute

    return () => clearInterval(interval)
  }, [autoThemeSwitch, currentThemeId, setTheme, getRecommendedTheme])
}

// Accessibility helpers
export const getAccessibilityScore = (themeId: string): number => {
  const theme = getThemeById(themeId)
  return theme?.psychologyProfile.accessibility || 0
}

export const getStressReductionScore = (themeId: string): number => {
  const theme = getThemeById(themeId)
  return theme?.psychologyProfile.stressReduction || 0
}

export const getFocusEnhancementScore = (themeId: string): number => {
  const theme = getThemeById(themeId)
  return theme?.psychologyProfile.focusEnhancement || 0
}

// Theme recommendation engine
export const getPersonalizedThemeRecommendation = (userProfile: {
  age?: number
  tradingExperience?: 'beginner' | 'intermediate' | 'advanced'
  visualImpairment?: boolean
  stressLevel?: 'low' | 'medium' | 'high'
  sessionDuration?: 'short' | 'medium' | 'long'
}): string => {
  const { age, tradingExperience, visualImpairment, stressLevel, sessionDuration } = userProfile

  // High contrast for visual impairments
  if (visualImpairment) {
    return 'high-contrast'
  }

  // Colorblind optimization for accessibility
  if (age && age > 40) {
    return 'colorblind-optimized'
  }

  // Stress-based recommendations
  if (stressLevel === 'high' || sessionDuration === 'long') {
    return 'calm-focus'
  }

  // Experience-based recommendations
  if (tradingExperience === 'beginner') {
    return 'warm-productivity' // Engaging for learning
  }

  if (tradingExperience === 'advanced') {
    return 'professional-dark' // Professional for experts
  }

  // Default to calm focus for most users
  return 'calm-focus'
}
