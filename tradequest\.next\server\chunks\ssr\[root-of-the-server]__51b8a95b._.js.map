{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number, decimals: number = 2): string {\n  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`\n}\n\nexport function formatNumber(value: number, decimals: number = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(value)\n}\n\nexport function formatLargeNumber(value: number): string {\n  if (value >= 1e9) {\n    return `${(value / 1e9).toFixed(1)}B`\n  }\n  if (value >= 1e6) {\n    return `${(value / 1e6).toFixed(1)}M`\n  }\n  if (value >= 1e3) {\n    return `${(value / 1e3).toFixed(1)}K`\n  }\n  return value.toString()\n}\n\nexport function calculatePnL(entryPrice: number, currentPrice: number, quantity: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity\n}\n\nexport function calculatePnLPercentage(entryPrice: number, currentPrice: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  const percentage = (priceDiff / entryPrice) * 100\n  return side === 'buy' ? percentage : -percentage\n}\n\nexport function generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n}\n\nexport function isMinor(age: number): boolean {\n  return age < 18\n}\n\nexport function validateAge(age: number): boolean {\n  return age >= 13 && age <= 120\n}\n\nexport function sanitizeUsername(username: string): string {\n  return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase()\n}\n\nexport function getTimeRemaining(endTime: Date): {\n  total: number\n  days: number\n  hours: number\n  minutes: number\n  seconds: number\n} {\n  const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString())\n  const seconds = Math.floor((total / 1000) % 60)\n  const minutes = Math.floor((total / 1000 / 60) % 60)\n  const hours = Math.floor((total / (1000 * 60 * 60)) % 24)\n  const days = Math.floor(total / (1000 * 60 * 60 * 24))\n\n  return {\n    total,\n    days,\n    hours,\n    minutes,\n    seconds,\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n\nexport function getRandomElement<T>(array: T[]): T {\n  return array[Math.floor(Math.random() * array.length)]\n}\n\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array]\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1))\n    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]\n  }\n  return shuffled\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\nexport function generateColor(seed: string): string {\n  let hash = 0\n  for (let i = 0; i < seed.length; i++) {\n    hash = seed.charCodeAt(i) + ((hash << 5) - hash)\n  }\n  const hue = hash % 360\n  return `hsl(${hue}, 70%, 50%)`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa,EAAE,WAAmB,CAAC;IAClE,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC;AAC9D;AAEO,SAAS,aAAa,KAAa,EAAE,WAAmB,CAAC;IAC9D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,KAAa;IAC7C,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,aAAa,UAAkB,EAAE,YAAoB,EAAE,QAAgB,EAAE,IAAoB;IAC3G,MAAM,YAAY,eAAe;IACjC,OAAO,SAAS,QAAQ,YAAY,WAAW,CAAC,YAAY;AAC9D;AAEO,SAAS,uBAAuB,UAAkB,EAAE,YAAoB,EAAE,IAAoB;IACnG,MAAM,YAAY,eAAe;IACjC,MAAM,aAAa,AAAC,YAAY,aAAc;IAC9C,OAAO,SAAS,QAAQ,aAAa,CAAC;AACxC;AAEO,SAAS;IACd,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,MAAM;AACf;AAEO,SAAS,YAAY,GAAW;IACrC,OAAO,OAAO,MAAM,OAAO;AAC7B;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,OAAO,CAAC,mBAAmB,IAAI,WAAW;AAC5D;AAEO,SAAS,iBAAiB,OAAa;IAO5C,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,QAAQ,MAAM,KAAK,KAAK,CAAC,IAAI,OAAO,QAAQ;IAC7E,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAQ;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAO,KAAM;IACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,KAAK,EAAE,IAAK;IACtD,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;IAEpD,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,iBAAoB,KAAU;IAC5C,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAEO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC1C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IAC1D;IACA,OAAO;AACT;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,OAAO,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI;IACjD;IACA,MAAM,MAAM,OAAO;IACnB,OAAO,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/constants.ts"], "sourcesContent": ["import { GameType, TradingPair } from '@/types'\n\n// Game Configuration\nexport const GAME_CONFIGS = {\n  scalper_sprint: {\n    name: 'Scalper Sprint',\n    description: '60-second trading challenges with rapid-fire decisions',\n    difficulty: 'beginner',\n    duration_seconds: 60,\n    starting_balance: 10000,\n    min_trade_size: 100,\n    max_positions: 3,\n    quest_coins_base: 50,\n  },\n  candle_strike: {\n    name: 'CandleStrike',\n    description: 'Pattern recognition game with candlestick charts',\n    difficulty: 'beginner',\n    duration_seconds: 120,\n    starting_balance: 0, // Pattern recognition, no trading\n    patterns_to_identify: 5,\n    quest_coins_base: 75,\n  },\n  chain_maze: {\n    name: 'ChainMaze',\n    description: 'Navigate blockchain puzzles and learn consensus mechanisms',\n    difficulty: 'intermediate',\n    duration_seconds: 300,\n    starting_balance: 1000, // Gas fees simulation\n    puzzles_to_solve: 3,\n    quest_coins_base: 100,\n  },\n  swing_trader_odyssey: {\n    name: \"Swing Trader's Odyssey\",\n    description: 'Multi-day position management with risk/reward balancing',\n    difficulty: 'intermediate',\n    duration_seconds: 600, // 10 minutes simulating days\n    starting_balance: 50000,\n    max_positions: 5,\n    quest_coins_base: 150,\n  },\n  day_trader_arena: {\n    name: 'Day Trader Arena',\n    description: 'Real-time multiplayer trading competitions',\n    difficulty: 'advanced',\n    duration_seconds: 900, // 15 minutes\n    starting_balance: 100000,\n    max_positions: 10,\n    quest_coins_base: 200,\n  },\n  portfolio_survivor: {\n    name: 'Portfolio Survivor',\n    description: 'Crisis management with diversification challenges',\n    difficulty: 'advanced',\n    duration_seconds: 1200, // 20 minutes\n    starting_balance: 500000,\n    max_positions: 20,\n    quest_coins_base: 300,\n  },\n} as const\n\n// Trading Pairs\nexport const TRADING_PAIRS: TradingPair[] = [\n  { base: 'BTC', quote: 'USD', symbol: 'BTCUSD', exchange: 'virtual' },\n  { base: 'ETH', quote: 'USD', symbol: 'ETHUSD', exchange: 'virtual' },\n  { base: 'ADA', quote: 'USD', symbol: 'ADAUSD', exchange: 'virtual' },\n  { base: 'SOL', quote: 'USD', symbol: 'SOLUSD', exchange: 'virtual' },\n  { base: 'AAPL', quote: 'USD', symbol: 'AAPL', exchange: 'virtual' },\n  { base: 'GOOGL', quote: 'USD', symbol: 'GOOGL', exchange: 'virtual' },\n  { base: 'TSLA', quote: 'USD', symbol: 'TSLA', exchange: 'virtual' },\n  { base: 'EUR', quote: 'USD', symbol: 'EURUSD', exchange: 'virtual' },\n  { base: 'GBP', quote: 'USD', symbol: 'GBPUSD', exchange: 'virtual' },\n  { base: 'JPY', quote: 'USD', symbol: 'JPYUSD', exchange: 'virtual' },\n]\n\n// Achievement Categories and Points\nexport const ACHIEVEMENT_CATEGORIES = {\n  trading: {\n    name: 'Trading Mastery',\n    color: '#10B981',\n    icon: '📈',\n  },\n  learning: {\n    name: 'Knowledge Seeker',\n    color: '#3B82F6',\n    icon: '🎓',\n  },\n  social: {\n    name: 'Community Builder',\n    color: '#8B5CF6',\n    icon: '👥',\n  },\n  special: {\n    name: 'Special Events',\n    color: '#F59E0B',\n    icon: '⭐',\n  },\n} as const\n\n// Level System\nexport const LEVEL_THRESHOLDS = [\n  0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n  13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n]\n\nexport const QUEST_COIN_MULTIPLIERS = {\n  beginner: 1.0,\n  intermediate: 1.5,\n  advanced: 2.0,\n} as const\n\n// UI Constants\nexport const INTERFACE_MODES = {\n  adolescent: {\n    name: 'Adventure Mode',\n    description: 'Fantasy-themed interface with quests and adventures',\n    primaryColor: '#8B5CF6',\n    secondaryColor: '#EC4899',\n    fontFamily: 'fantasy',\n  },\n  adult: {\n    name: 'Professional Mode',\n    description: 'Bloomberg Terminal-style professional interface',\n    primaryColor: '#1F2937',\n    secondaryColor: '#3B82F6',\n    fontFamily: 'monospace',\n  },\n} as const\n\n// Market Data Update Intervals\nexport const UPDATE_INTERVALS = {\n  real_time: 1000, // 1 second\n  fast: 5000, // 5 seconds\n  normal: 15000, // 15 seconds\n  slow: 60000, // 1 minute\n} as const\n\n// API Endpoints\nexport const API_ENDPOINTS = {\n  coingecko: {\n    base: 'https://api.coingecko.com/api/v3',\n    prices: '/simple/price',\n    history: '/coins/{id}/market_chart',\n  },\n  alpha_vantage: {\n    base: 'https://www.alphavantage.co/query',\n    intraday: '?function=TIME_SERIES_INTRADAY',\n    forex: '?function=FX_INTRADAY',\n  },\n} as const\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  username: {\n    minLength: 3,\n    maxLength: 20,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n  },\n  age: {\n    min: 13,\n    max: 120,\n  },\n  trade: {\n    minAmount: 1,\n    maxAmount: 1000000,\n  },\n} as const\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  auth: {\n    invalid_credentials: 'Invalid email or password',\n    user_not_found: 'User not found',\n    email_already_exists: 'Email already registered',\n    weak_password: 'Password must be at least 8 characters',\n    age_verification_failed: 'Age verification required',\n  },\n  game: {\n    session_expired: 'Game session has expired',\n    invalid_trade: 'Invalid trade parameters',\n    insufficient_balance: 'Insufficient balance for this trade',\n    max_positions_reached: 'Maximum number of positions reached',\n  },\n  general: {\n    network_error: 'Network error, please try again',\n    server_error: 'Server error, please try again later',\n    validation_error: 'Please check your input and try again',\n  },\n} as const\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  auth: {\n    registration_complete: 'Account created successfully!',\n    login_success: 'Welcome back!',\n    logout_success: 'Logged out successfully',\n  },\n  game: {\n    session_complete: 'Game session completed!',\n    achievement_unlocked: 'Achievement unlocked!',\n    level_up: 'Level up! Congratulations!',\n  },\n  general: {\n    save_success: 'Changes saved successfully',\n    update_success: 'Updated successfully',\n  },\n} as const\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,MAAM,eAAe;IAC1B,gBAAgB;QACd,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,kBAAkB;IACpB;IACA,eAAe;QACb,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,sBAAsB;QACtB,kBAAkB;IACpB;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;IACpB;IACA,sBAAsB;QACpB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,kBAAkB;QAChB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;AACF;AAGO,MAAM,gBAA+B;IAC1C;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAS,OAAO;QAAO,QAAQ;QAAS,UAAU;IAAU;IACpE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;CACpE;AAGM,MAAM,yBAAyB;IACpC,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;AACF;AAGO,MAAM,mBAAmB;IAC9B;IAAG;IAAK;IAAK;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACtD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;CAChE;AAEM,MAAM,yBAAyB;IACpC,UAAU;IACV,cAAc;IACd,UAAU;AACZ;AAGO,MAAM,kBAAkB;IAC7B,YAAY;QACV,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;IACA,OAAO;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAGO,MAAM,gBAAgB;IAC3B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,KAAK;QACH,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,WAAW;QACX,WAAW;IACb;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,qBAAqB;QACrB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,yBAAyB;IAC3B;IACA,MAAM;QACJ,iBAAiB;QACjB,eAAe;QACf,sBAAsB;QACtB,uBAAuB;IACzB;IACA,SAAS;QACP,eAAe;QACf,cAAc;QACd,kBAAkB;IACpB;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,uBAAuB;QACvB,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM;QACJ,kBAAkB;QAClB,sBAAsB;QACtB,UAAU;IACZ;IACA,SAAS;QACP,cAAc;QACd,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/services/market-data.ts"], "sourcesContent": ["import axios from 'axios'\nimport { MarketData, CandlestickData } from '@/types'\nimport { API_ENDPOINTS } from '@/lib/constants'\n\nclass MarketDataService {\n  private coingeckoClient: any\n  private alphaVantageClient: any\n\n  constructor() {\n    this.coingeckoClient = axios.create({\n      baseURL: API_ENDPOINTS.coingecko.base,\n      timeout: 10000,\n    })\n\n    this.alphaVantageClient = axios.create({\n      baseURL: API_ENDPOINTS.alpha_vantage.base,\n      timeout: 10000,\n    })\n  }\n\n  // Cryptocurrency data from CoinGecko\n  async getCryptoPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const ids = symbols.map(symbol => this.symbolToCoinGeckoId(symbol)).join(',')\n      const response = await this.coingeckoClient.get(API_ENDPOINTS.coingecko.prices, {\n        params: {\n          ids,\n          vs_currencies: 'usd',\n          include_24hr_change: true,\n          include_24hr_vol: true,\n          include_market_cap: true,\n        },\n      })\n\n      return this.formatCoinGeckoResponse(response.data, symbols)\n    } catch (error) {\n      console.error('Error fetching crypto prices:', error)\n      return this.generateMockCryptoData(symbols)\n    }\n  }\n\n  // Stock data from Alpha Vantage\n  async getStockPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const promises = symbols.map(symbol => this.fetchStockPrice(symbol))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching stock prices:', error)\n      return this.generateMockStockData(symbols)\n    }\n  }\n\n  // Forex data from Alpha Vantage\n  async getForexPrices(pairs: string[]): Promise<MarketData[]> {\n    try {\n      const promises = pairs.map(pair => this.fetchForexPrice(pair))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching forex prices:', error)\n      return this.generateMockForexData(pairs)\n    }\n  }\n\n  // Historical candlestick data with enhanced pattern detection\n  async getCandlestickData(symbol: string, interval: string = '1h', days: number = 7): Promise<CandlestickData[]> {\n    try {\n      if (this.isCryptoSymbol(symbol)) {\n        return await this.getCryptoCandlestickData(symbol, days)\n      } else {\n        return await this.getStockCandlestickData(symbol, interval)\n      }\n    } catch (error) {\n      console.error('Error fetching candlestick data:', error)\n      return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data\n    }\n  }\n\n  // Get historical data with specific patterns for educational purposes\n  async getHistoricalDataWithPatterns(symbol: string, patternType: string, count: number = 10): Promise<CandlestickData[][]> {\n    try {\n      // For demo purposes, we'll use a combination of real data and pattern-enhanced data\n      const baseData = await this.getCandlestickData(symbol, '1h', 30) // 30 days of data\n\n      // Find or create segments with the requested pattern\n      return this.extractPatternSegments(baseData, patternType, count)\n    } catch (error) {\n      console.error('Error fetching pattern data:', error)\n      return this.generatePatternDatasets(symbol, patternType, count)\n    }\n  }\n\n  // TradingView-style data format\n  async getTradingViewData(symbol: string, resolution: string = '60', from: number, to: number): Promise<{\n    s: string\n    t: number[]\n    o: number[]\n    h: number[]\n    l: number[]\n    c: number[]\n    v: number[]\n  }> {\n    try {\n      const data = await this.getCandlestickData(symbol, '1h', 7)\n\n      return {\n        s: 'ok',\n        t: data.map(d => Math.floor(d.timestamp / 1000)),\n        o: data.map(d => d.open),\n        h: data.map(d => d.high),\n        l: data.map(d => d.low),\n        c: data.map(d => d.close),\n        v: data.map(d => d.volume),\n      }\n    } catch (error) {\n      return {\n        s: 'error',\n        t: [],\n        o: [],\n        h: [],\n        l: [],\n        c: [],\n        v: [],\n      }\n    }\n  }\n\n  // Private helper methods\n  private async fetchStockPrice(symbol: string): Promise<MarketData | null> {\n    try {\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'GLOBAL_QUOTE',\n          symbol,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const quote = response.data['Global Quote']\n      if (!quote) return null\n\n      return {\n        symbol,\n        price: parseFloat(quote['05. price']),\n        change_24h: parseFloat(quote['09. change']),\n        change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),\n        volume_24h: parseFloat(quote['06. volume']),\n        timestamp: new Date().toISOString(),\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async fetchForexPrice(pair: string): Promise<MarketData | null> {\n    try {\n      const [from, to] = pair.split('/')\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'CURRENCY_EXCHANGE_RATE',\n          from_currency: from,\n          to_currency: to,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const rate = response.data['Realtime Currency Exchange Rate']\n      if (!rate) return null\n\n      return {\n        symbol: pair,\n        price: parseFloat(rate['5. Exchange Rate']),\n        change_24h: 0, // Alpha Vantage doesn't provide 24h change for forex\n        change_percentage_24h: 0,\n        volume_24h: 0,\n        timestamp: rate['6. Last Refreshed'],\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async getCryptoCandlestickData(symbol: string, days: number): Promise<CandlestickData[]> {\n    const id = this.symbolToCoinGeckoId(symbol)\n    const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {\n      params: {\n        vs_currency: 'usd',\n        days,\n        interval: 'hourly',\n      },\n    })\n\n    const prices = response.data.prices\n    const volumes = response.data.total_volumes\n\n    return prices.map((price: [number, number], index: number) => ({\n      timestamp: price[0],\n      open: index > 0 ? prices[index - 1][1] : price[1],\n      high: price[1] * (1 + Math.random() * 0.02), // Simulate high\n      low: price[1] * (1 - Math.random() * 0.02), // Simulate low\n      close: price[1],\n      volume: volumes[index] ? volumes[index][1] : 0,\n    }))\n  }\n\n  private async getStockCandlestickData(symbol: string, interval: string): Promise<CandlestickData[]> {\n    const response = await this.alphaVantageClient.get('', {\n      params: {\n        function: 'TIME_SERIES_INTRADAY',\n        symbol,\n        interval,\n        apikey: process.env.ALPHA_VANTAGE_API_KEY,\n      },\n    })\n\n    const timeSeries = response.data[`Time Series (${interval})`]\n    if (!timeSeries) return []\n\n    return Object.entries(timeSeries).map(([timestamp, data]: [string, any]) => ({\n      timestamp: new Date(timestamp).getTime(),\n      open: parseFloat(data['1. open']),\n      high: parseFloat(data['2. high']),\n      low: parseFloat(data['3. low']),\n      close: parseFloat(data['4. close']),\n      volume: parseFloat(data['5. volume']),\n    }))\n  }\n\n  private symbolToCoinGeckoId(symbol: string): string {\n    const mapping: Record<string, string> = {\n      BTC: 'bitcoin',\n      ETH: 'ethereum',\n      ADA: 'cardano',\n      SOL: 'solana',\n      DOT: 'polkadot',\n      LINK: 'chainlink',\n      UNI: 'uniswap',\n      MATIC: 'polygon',\n    }\n    return mapping[symbol.toUpperCase()] || symbol.toLowerCase()\n  }\n\n  private isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.includes(symbol.toUpperCase())\n  }\n\n  private formatCoinGeckoResponse(data: any, symbols: string[]): MarketData[] {\n    return symbols.map(symbol => {\n      const id = this.symbolToCoinGeckoId(symbol)\n      const coinData = data[id]\n      \n      if (!coinData) return this.generateMockCryptoData([symbol])[0]\n\n      return {\n        symbol,\n        price: coinData.usd,\n        change_24h: coinData.usd_24h_change || 0,\n        change_percentage_24h: coinData.usd_24h_change || 0,\n        volume_24h: coinData.usd_24h_vol || 0,\n        market_cap: coinData.usd_market_cap,\n        timestamp: new Date().toISOString(),\n      }\n    })\n  }\n\n  // Mock data generators for development and fallback\n  private generateMockCryptoData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      BTC: 45000,\n      ETH: 3000,\n      ADA: 0.5,\n      SOL: 100,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 1000,\n      change_percentage_24h: (Math.random() - 0.5) * 10,\n      volume_24h: Math.random() * 1000000000,\n      market_cap: Math.random() * 100000000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockStockData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      AAPL: 150,\n      GOOGL: 2500,\n      TSLA: 800,\n      MSFT: 300,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 20,\n      change_percentage_24h: (Math.random() - 0.5) * 5,\n      volume_24h: Math.random() * 100000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockForexData(pairs: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      'EUR/USD': 1.1,\n      'GBP/USD': 1.3,\n      'USD/JPY': 110,\n      'USD/CHF': 0.9,\n    }\n\n    return pairs.map(pair => ({\n      symbol: pair,\n      price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),\n      change_24h: (Math.random() - 0.5) * 0.01,\n      change_percentage_24h: (Math.random() - 0.5) * 1,\n      volume_24h: 0,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockCandlestickData(symbol: string, count: number): CandlestickData[] {\n    const data: CandlestickData[] = []\n    let price = 100 + Math.random() * 900\n    const now = Date.now()\n\n    for (let i = 0; i < count; i++) {\n      const timestamp = now - (count - i) * 3600000 // Hourly intervals\n      const change = (Math.random() - 0.5) * 10\n      const open = price\n      const close = price + change\n      const high = Math.max(open, close) + Math.random() * 5\n      const low = Math.min(open, close) - Math.random() * 5\n      const volume = Math.random() * 1000000\n\n      data.push({\n        timestamp,\n        open,\n        high,\n        low,\n        close,\n        volume,\n      })\n\n      price = close\n    }\n\n    return data\n  }\n\n  // Extract segments containing specific patterns from real data\n  extractPatternSegments(data: CandlestickData[], patternType: string, count: number): CandlestickData[][] {\n    const segments: CandlestickData[][] = []\n    const segmentLength = 20 // 20 candles per segment\n\n    // Scan through data looking for patterns\n    for (let i = 0; i <= data.length - segmentLength && segments.length < count; i++) {\n      const segment = data.slice(i, i + segmentLength)\n\n      if (this.containsPattern(segment, patternType)) {\n        segments.push(segment)\n        i += segmentLength - 1 // Skip ahead to avoid overlapping segments\n      }\n    }\n\n    // If we don't have enough real patterns, generate some\n    while (segments.length < count) {\n      segments.push(this.generateSegmentWithPattern(patternType, segmentLength))\n    }\n\n    return segments\n  }\n\n  // Check if a segment contains a specific pattern\n  private containsPattern(segment: CandlestickData[], patternType: string): boolean {\n    switch (patternType) {\n      case 'hammer':\n        return this.detectHammer(segment)\n      case 'doji':\n        return this.detectDoji(segment)\n      case 'engulfing_bullish':\n        return this.detectBullishEngulfing(segment)\n      case 'engulfing_bearish':\n        return this.detectBearishEngulfing(segment)\n      case 'morning_star':\n        return this.detectMorningStar(segment)\n      case 'evening_star':\n        return this.detectEveningStar(segment)\n      default:\n        return false\n    }\n  }\n\n  // Pattern detection algorithms\n  private detectHammer(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length - 1; i++) {\n      const candle = segment[i]\n      const bodySize = Math.abs(candle.close - candle.open)\n      const lowerShadow = Math.min(candle.open, candle.close) - candle.low\n      const upperShadow = candle.high - Math.max(candle.open, candle.close)\n      const totalRange = candle.high - candle.low\n\n      // Hammer criteria: small body, long lower shadow, small upper shadow\n      if (bodySize < totalRange * 0.3 &&\n          lowerShadow > bodySize * 2 &&\n          upperShadow < bodySize * 0.5) {\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectDoji(segment: CandlestickData[]): boolean {\n    for (let i = 0; i < segment.length; i++) {\n      const candle = segment[i]\n      const bodySize = Math.abs(candle.close - candle.open)\n      const totalRange = candle.high - candle.low\n\n      // Doji criteria: very small body relative to total range\n      if (bodySize < totalRange * 0.1 && totalRange > 0) {\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectBullishEngulfing(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length; i++) {\n      const prev = segment[i - 1]\n      const curr = segment[i]\n\n      // Previous candle is bearish, current is bullish and engulfs previous\n      if (prev.close < prev.open && // Previous bearish\n          curr.close > curr.open && // Current bullish\n          curr.open < prev.close && // Current opens below previous close\n          curr.close > prev.open) { // Current closes above previous open\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectBearishEngulfing(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length; i++) {\n      const prev = segment[i - 1]\n      const curr = segment[i]\n\n      // Previous candle is bearish, current is bullish and engulfs previous\n      if (prev.close > prev.open && // Previous bullish\n          curr.close < curr.open && // Current bearish\n          curr.open > prev.close && // Current opens above previous close\n          curr.close < prev.open) { // Current closes below previous open\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectMorningStar(segment: CandlestickData[]): boolean {\n    for (let i = 2; i < segment.length; i++) {\n      const first = segment[i - 2]\n      const second = segment[i - 1]\n      const third = segment[i]\n\n      // Three candle pattern: bearish, small body, bullish\n      if (first.close < first.open && // First bearish\n          Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small\n          third.close > third.open && // Third bullish\n          third.close > (first.open + first.close) / 2) { // Third closes above midpoint of first\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectEveningStar(segment: CandlestickData[]): boolean {\n    for (let i = 2; i < segment.length; i++) {\n      const first = segment[i - 2]\n      const second = segment[i - 1]\n      const third = segment[i]\n\n      // Three candle pattern: bullish, small body, bearish\n      if (first.close > first.open && // First bullish\n          Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small\n          third.close < third.open && // Third bearish\n          third.close < (first.open + first.close) / 2) { // Third closes below midpoint of first\n        return true\n      }\n    }\n    return false\n  }\n\n  // Generate a segment with a specific pattern\n  private generateSegmentWithPattern(patternType: string, length: number): CandlestickData[] {\n    const segment: CandlestickData[] = []\n    let currentPrice = 100 + Math.random() * 50\n    const now = Date.now()\n\n    // Generate leading candles\n    const patternPosition = Math.floor(length * 0.4) + Math.floor(Math.random() * Math.floor(length * 0.3))\n\n    for (let i = 0; i < patternPosition; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i, now)\n      segment.push(candle)\n      currentPrice = candle.close\n    }\n\n    // Generate pattern candles\n    const patternCandles = this.generateSpecificPattern(patternType, currentPrice, patternPosition, now)\n    segment.push(...patternCandles)\n    currentPrice = patternCandles[patternCandles.length - 1].close\n\n    // Generate trailing candles\n    for (let i = patternPosition + patternCandles.length; i < length; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i, now)\n      segment.push(candle)\n      currentPrice = candle.close\n    }\n\n    return segment\n  }\n\n  private generateSpecificPattern(patternType: string, startPrice: number, startIndex: number, baseTime: number): CandlestickData[] {\n    switch (patternType) {\n      case 'hammer':\n        return this.generateHammerCandle(startPrice, startIndex, baseTime)\n      case 'doji':\n        return this.generateDojiCandle(startPrice, startIndex, baseTime)\n      case 'engulfing_bullish':\n        return this.generateBullishEngulfingPattern(startPrice, startIndex, baseTime)\n      case 'engulfing_bearish':\n        return this.generateBearishEngulfingPattern(startPrice, startIndex, baseTime)\n      case 'morning_star':\n        return this.generateMorningStarPattern(startPrice, startIndex, baseTime)\n      case 'evening_star':\n        return this.generateEveningStarPattern(startPrice, startIndex, baseTime)\n      default:\n        return this.generateHammerCandle(startPrice, startIndex, baseTime)\n    }\n  }\n}\n\nexport const marketDataService = new MarketDataService()\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM;IACI,gBAAoB;IACpB,mBAAuB;IAE/B,aAAc;QACZ,IAAI,CAAC,eAAe,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAClC,SAAS,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI;YACrC,SAAS;QACX;QAEA,IAAI,CAAC,kBAAkB,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACrC,SAAS,uHAAA,CAAA,gBAAa,CAAC,aAAa,CAAC,IAAI;YACzC,SAAS;QACX;IACF;IAEA,qCAAqC;IACrC,MAAM,gBAAgB,OAAiB,EAAyB;QAC9D,IAAI;YACF,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC;YACzE,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC9E,QAAQ;oBACN;oBACA,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;oBAClB,oBAAoB;gBACtB;YACF;YAEA,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,IAAI,EAAE;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACrC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,OAAiB,EAAyB;QAC7D,IAAI;YACF,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,eAAe,CAAC;YAC5D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,KAAe,EAAyB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,eAAe,CAAC;YACxD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,8DAA8D;IAC9D,MAAM,mBAAmB,MAAc,EAAE,WAAmB,IAAI,EAAE,OAAe,CAAC,EAA8B;QAC9G,IAAI;YACF,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;gBAC/B,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ;YACrD,OAAO;gBACL,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,KAAK,wBAAwB;;QAC/E;IACF;IAEA,sEAAsE;IACtE,MAAM,8BAA8B,MAAc,EAAE,WAAmB,EAAE,QAAgB,EAAE,EAAgC;QACzH,IAAI;YACF,oFAAoF;YACpF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,MAAM,IAAI,kBAAkB;;YAEnF,qDAAqD;YACrD,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU,aAAa;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,aAAa;QAC3D;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAmB,MAAc,EAAE,aAAqB,IAAI,EAAE,IAAY,EAAE,EAAU,EAQzF;QACD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,MAAM;YAEzD,OAAO;gBACL,GAAG;gBACH,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,KAAK,KAAK,CAAC,EAAE,SAAS,GAAG;gBAC1C,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACvB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACvB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gBACtB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACxB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,GAAG;gBACH,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;YACP;QACF;IACF;IAEA,yBAAyB;IACzB,MAAc,gBAAgB,MAAc,EAA8B;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV;oBACA,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,QAAQ,SAAS,IAAI,CAAC,eAAe;YAC3C,IAAI,CAAC,OAAO,OAAO;YAEnB,OAAO;gBACL;gBACA,OAAO,WAAW,KAAK,CAAC,YAAY;gBACpC,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,uBAAuB,WAAW,KAAK,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK;gBAC3E,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,gBAAgB,IAAY,EAA8B;QACtE,IAAI;YACF,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC;YAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV,eAAe;oBACf,aAAa;oBACb,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,kCAAkC;YAC7D,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO;gBACL,QAAQ;gBACR,OAAO,WAAW,IAAI,CAAC,mBAAmB;gBAC1C,YAAY;gBACZ,uBAAuB;gBACvB,YAAY;gBACZ,WAAW,IAAI,CAAC,oBAAoB;YACtC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,yBAAyB,MAAc,EAAE,IAAY,EAA8B;QAC/F,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;QACpC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,EAAE;YAC3E,QAAQ;gBACN,aAAa;gBACb;gBACA,UAAU;YACZ;QACF;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,MAAM;QACnC,MAAM,UAAU,SAAS,IAAI,CAAC,aAAa;QAE3C,OAAO,OAAO,GAAG,CAAC,CAAC,OAAyB,QAAkB,CAAC;gBAC7D,WAAW,KAAK,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gBACjD,MAAM,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBAC1C,KAAK,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBACzC,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG;YAC/C,CAAC;IACH;IAEA,MAAc,wBAAwB,MAAc,EAAE,QAAgB,EAA8B;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;YACrD,QAAQ;gBACN,UAAU;gBACV;gBACA;gBACA,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;YAC3C;QACF;QAEA,MAAM,aAAa,SAAS,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,WAAW,KAAoB,GAAK,CAAC;gBAC3E,WAAW,IAAI,KAAK,WAAW,OAAO;gBACtC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,KAAK,WAAW,IAAI,CAAC,SAAS;gBAC9B,OAAO,WAAW,IAAI,CAAC,WAAW;gBAClC,QAAQ,WAAW,IAAI,CAAC,YAAY;YACtC,CAAC;IACH;IAEQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,UAAkC;YACtC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;IAC5D;IAEQ,eAAe,MAAc,EAAW;QAC9C,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,QAAQ,CAAC,OAAO,WAAW;IAClD;IAEQ,wBAAwB,IAAS,EAAE,OAAiB,EAAgB;QAC1E,OAAO,QAAQ,GAAG,CAAC,CAAA;YACjB,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;YACpC,MAAM,WAAW,IAAI,CAAC,GAAG;YAEzB,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBAAC;aAAO,CAAC,CAAC,EAAE;YAE9D,OAAO;gBACL;gBACA,OAAO,SAAS,GAAG;gBACnB,YAAY,SAAS,cAAc,IAAI;gBACvC,uBAAuB,SAAS,cAAc,IAAI;gBAClD,YAAY,SAAS,WAAW,IAAI;gBACpC,YAAY,SAAS,cAAc;gBACnC,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEA,oDAAoD;IAC5C,uBAAuB,OAAiB,EAAgB;QAC9D,MAAM,aAAqC;YACzC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,OAAiB,EAAgB;QAC7D,MAAM,aAAqC;YACzC,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,KAAe,EAAgB;QAC3D,MAAM,aAAqC;YACzC,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,QAAQ;gBACR,OAAO,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI;gBAC7D,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY;gBACZ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,4BAA4B,MAAc,EAAE,KAAa,EAAqB;QACpF,MAAM,OAA0B,EAAE;QAClC,IAAI,QAAQ,MAAM,KAAK,MAAM,KAAK;QAClC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,mBAAmB;;YACjE,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACvC,MAAM,OAAO;YACb,MAAM,QAAQ,QAAQ;YACtB,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACrD,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACpD,MAAM,SAAS,KAAK,MAAM,KAAK;YAE/B,KAAK,IAAI,CAAC;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,+DAA+D;IAC/D,uBAAuB,IAAuB,EAAE,WAAmB,EAAE,KAAa,EAAuB;QACvG,MAAM,WAAgC,EAAE;QACxC,MAAM,gBAAgB,GAAG,yBAAyB;;QAElD,yCAAyC;QACzC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,GAAG,iBAAiB,SAAS,MAAM,GAAG,OAAO,IAAK;YAChF,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI;YAElC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,cAAc;gBAC9C,SAAS,IAAI,CAAC;gBACd,KAAK,gBAAgB,EAAE,2CAA2C;;YACpE;QACF;QAEA,uDAAuD;QACvD,MAAO,SAAS,MAAM,GAAG,MAAO;YAC9B,SAAS,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,aAAa;QAC7D;QAEA,OAAO;IACT;IAEA,iDAAiD;IACzC,gBAAgB,OAA0B,EAAE,WAAmB,EAAW;QAChF,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,KAAK;gBACH,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IACvB,aAAa,OAA0B,EAAW;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAK;YAC3C,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;YACpD,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,GAAG;YACpE,MAAM,cAAc,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK;YACpE,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;YAE3C,qEAAqE;YACrE,IAAI,WAAW,aAAa,OACxB,cAAc,WAAW,KACzB,cAAc,WAAW,KAAK;gBAChC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,WAAW,OAA0B,EAAW;QACtD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;YACpD,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;YAE3C,yDAAyD;YACzD,IAAI,WAAW,aAAa,OAAO,aAAa,GAAG;gBACjD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,uBAAuB,OAA0B,EAAW;QAClE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE;YAC3B,MAAM,OAAO,OAAO,CAAC,EAAE;YAEvB,sEAAsE;YACtE,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,mBAAmB;YAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,kBAAkB;YAC5C,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;YAC/D,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,uBAAuB,OAA0B,EAAW;QAClE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE;YAC3B,MAAM,OAAO,OAAO,CAAC,EAAE;YAEvB,sEAAsE;YACtE,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,mBAAmB;YAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,kBAAkB;YAC5C,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;YAC/D,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,OAA0B,EAAW;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,SAAS,OAAO,CAAC,IAAI,EAAE;YAC7B,MAAM,QAAQ,OAAO,CAAC,EAAE;YAExB,qDAAqD;YACrD,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,OAAO,eAAe;YAClG,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG;gBAChD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,OAA0B,EAAW;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,SAAS,OAAO,CAAC,IAAI,EAAE;YAC7B,MAAM,QAAQ,OAAO,CAAC,EAAE;YAExB,qDAAqD;YACrD,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,OAAO,eAAe;YAClG,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG;gBAChD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,6CAA6C;IACrC,2BAA2B,WAAmB,EAAE,MAAc,EAAqB;QACzF,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe,MAAM,KAAK,MAAM,KAAK;QACzC,MAAM,MAAM,KAAK,GAAG;QAEpB,2BAA2B;QAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,SAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,CAAC,SAAS;QAElG,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG;YAC1D,QAAQ,IAAI,CAAC;YACb,eAAe,OAAO,KAAK;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,aAAa,cAAc,iBAAiB;QAChG,QAAQ,IAAI,IAAI;QAChB,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9D,4BAA4B;QAC5B,IAAK,IAAI,IAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,QAAQ,IAAK;YACrE,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG;YAC1D,QAAQ,IAAI,CAAC;YACb,eAAe,OAAO,KAAK;QAC7B;QAEA,OAAO;IACT;IAEQ,wBAAwB,WAAmB,EAAE,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAqB;QAChI,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,YAAY;YAC3D,KAAK;gBACH,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,YAAY;YACzD,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,YAAY;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,YAAY;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;YACjE,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;YACjE;gBACE,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,YAAY;QAC7D;IACF;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/base-game.ts"], "sourcesContent": ["import { GameConfig, GameState, Position, GameType } from '@/types'\nimport { generateSessionId, calculatePnL } from '@/lib/utils'\nimport { GAME_CONFIGS, QUEST_COIN_MULTIPLIERS } from '@/lib/constants'\nimport { marketDataService } from '@/lib/services/market-data'\n\nexport class BaseGame {\n  protected config: GameConfig\n  protected state: GameState\n  protected startTime: number\n  protected endTime: number\n  protected isActive: boolean = false\n  protected marketData: Map<string, number> = new Map()\n\n  constructor(gameType: GameType, difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    const gameConfig = GAME_CONFIGS[gameType]\n\n    this.config = {\n      type: gameType,\n      difficulty,\n      duration_seconds: gameConfig.duration_seconds,\n      starting_balance: gameConfig.starting_balance,\n      available_pairs: [], // Will be set by specific game implementations\n      special_rules: {},\n    }\n\n    this.state = {\n      session_id: generateSessionId(),\n      current_balance: this.config.starting_balance,\n      positions: [],\n      time_remaining: this.config.duration_seconds,\n      score: 0,\n      multiplier: QUEST_COIN_MULTIPLIERS[difficulty],\n    }\n\n    this.startTime = Date.now()\n    this.endTime = this.startTime + (this.config.duration_seconds * 1000)\n  }\n\n  // Methods that can be overridden by specific games\n  async initialize(): Promise<void> {\n    // Default implementation - can be overridden\n  }\n\n  update(): void {\n    // Default implementation - can be overridden\n  }\n\n  calculateScore(): number {\n    // Default implementation - can be overridden\n    return 0\n  }\n\n  getGameSpecificData(): any {\n    // Default implementation - can be overridden\n    return {}\n  }\n\n  // Common game lifecycle methods\n  async start(): Promise<void> {\n    await this.initialize()\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  pause(): void {\n    this.isActive = false\n  }\n\n  resume(): void {\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  end(): GameState {\n    this.isActive = false\n    this.state.score = this.calculateScore()\n    this.state.time_remaining = 0\n    return this.state\n  }\n\n  // Trading operations\n  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const currentPrice = this.marketData.get(symbol)\n    if (!currentPrice) return false\n\n    const tradeValue = currentPrice * quantity\n    const requiredBalance = side === 'buy' ? tradeValue : 0\n\n    if (this.state.current_balance < requiredBalance) {\n      return false // Insufficient balance\n    }\n\n    // Check position limits\n    const maxPositions = this.getMaxPositions()\n    if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {\n      return false // Too many positions\n    }\n\n    // Execute the trade\n    const position: Position = {\n      id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      symbol,\n      side,\n      quantity,\n      entry_price: currentPrice,\n      current_price: currentPrice,\n      pnl: 0,\n      timestamp: new Date().toISOString(),\n    }\n\n    // Update balance\n    if (side === 'buy') {\n      this.state.current_balance -= tradeValue\n    } else {\n      this.state.current_balance += tradeValue\n    }\n\n    // Add or update position\n    const existingPositionIndex = this.state.positions.findIndex(p => p.symbol === symbol)\n    if (existingPositionIndex >= 0) {\n      // Update existing position (average price calculation would go here)\n      this.state.positions[existingPositionIndex] = position\n    } else {\n      this.state.positions.push(position)\n    }\n\n    return true\n  }\n\n  async closePosition(positionId: string): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)\n    if (positionIndex === -1) return false\n\n    const position = this.state.positions[positionIndex]\n    const currentPrice = this.marketData.get(position.symbol)\n    if (!currentPrice) return false\n\n    // Calculate final P&L\n    const pnl = calculatePnL(position.entry_price, currentPrice, position.quantity, position.side)\n    \n    // Update balance with P&L\n    this.state.current_balance += pnl\n    if (position.side === 'sell') {\n      // Return the initial trade value for short positions\n      this.state.current_balance += position.entry_price * position.quantity\n    }\n\n    // Remove position\n    this.state.positions.splice(positionIndex, 1)\n\n    return true\n  }\n\n  // Market data updates\n  async updateMarketData(): Promise<void> {\n    try {\n      const symbols = this.config.available_pairs.map(pair => pair.symbol)\n      \n      // In a real implementation, you'd fetch from different services based on asset type\n      const cryptoSymbols = symbols.filter(s => this.isCryptoSymbol(s))\n      const stockSymbols = symbols.filter(s => this.isStockSymbol(s))\n      const forexSymbols = symbols.filter(s => this.isForexSymbol(s))\n\n      const [cryptoData, stockData, forexData] = await Promise.all([\n        cryptoSymbols.length > 0 ? marketDataService.getCryptoPrices(cryptoSymbols) : [],\n        stockSymbols.length > 0 ? marketDataService.getStockPrices(stockSymbols) : [],\n        forexSymbols.length > 0 ? marketDataService.getForexPrices(forexSymbols) : [],\n      ])\n\n      // Update market data map\n      const allData = cryptoData.concat(stockData).concat(forexData)\n      allData.forEach(data => {\n        this.marketData.set(data.symbol, data.price)\n      })\n\n      // Update position P&L\n      this.updatePositionPnL()\n    } catch (error) {\n      console.error('Error updating market data:', error)\n    }\n  }\n\n  // Game state getters\n  getState(): GameState {\n    return { ...this.state }\n  }\n\n  getConfig(): GameConfig {\n    return { ...this.config }\n  }\n\n  isGameActive(): boolean {\n    return this.isActive && this.state.time_remaining > 0\n  }\n\n  getTimeRemaining(): number {\n    if (!this.isActive) return 0\n    const remaining = Math.max(0, this.endTime - Date.now())\n    this.state.time_remaining = Math.floor(remaining / 1000)\n    return this.state.time_remaining\n  }\n\n  // Protected helper methods\n  protected startGameLoop(): void {\n    if (!this.isActive) return\n\n    const gameLoop = () => {\n      if (!this.isActive) return\n\n      this.update()\n      this.getTimeRemaining()\n\n      if (this.state.time_remaining <= 0) {\n        this.end()\n        return\n      }\n\n      setTimeout(gameLoop, 1000) // Update every second\n    }\n\n    gameLoop()\n  }\n\n  protected updatePositionPnL(): void {\n    this.state.positions.forEach(position => {\n      const currentPrice = this.marketData.get(position.symbol)\n      if (currentPrice) {\n        position.current_price = currentPrice\n        position.pnl = calculatePnL(\n          position.entry_price,\n          currentPrice,\n          position.quantity,\n          position.side\n        )\n      }\n    })\n  }\n\n  protected getTotalPnL(): number {\n    return this.state.positions.reduce((total, position) => total + position.pnl, 0)\n  }\n\n  protected getMaxPositions(): number {\n    const gameConfig = GAME_CONFIGS[this.config.type]\n    return (gameConfig as any).max_positions || 5\n  }\n\n  protected hasExistingPosition(symbol: string): boolean {\n    return this.state.positions.some(p => p.symbol === symbol)\n  }\n\n  protected isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.some(crypto => symbol.includes(crypto))\n  }\n\n  protected isStockSymbol(symbol: string): boolean {\n    const stockSymbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN', 'META', 'NVDA']\n    return stockSymbols.includes(symbol)\n  }\n\n  protected isForexSymbol(symbol: string): boolean {\n    return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY')\n  }\n\n  protected generateRandomPrice(basePrice: number, volatility: number = 0.02): number {\n    const change = (Math.random() - 0.5) * 2 * volatility\n    return basePrice * (1 + change)\n  }\n\n  protected simulateMarketMovement(): void {\n    // Simulate realistic market movements for game purposes\n    this.marketData.forEach((price, symbol) => {\n      const volatility = this.getSymbolVolatility(symbol)\n      const newPrice = this.generateRandomPrice(price, volatility)\n      this.marketData.set(symbol, newPrice)\n    })\n  }\n\n  protected getSymbolVolatility(symbol: string): number {\n    if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto\n    if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks\n    if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex\n    return 0.02 // Default 2%\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM;IACD,OAAkB;IAClB,MAAgB;IAChB,UAAiB;IACjB,QAAe;IACf,WAAoB,MAAK;IACzB,aAAkC,IAAI,MAAK;IAErD,YAAY,QAAkB,EAAE,UAAoD,CAAE;QACpF,MAAM,aAAa,uHAAA,CAAA,eAAY,CAAC,SAAS;QAEzC,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM;YACN;YACA,kBAAkB,WAAW,gBAAgB;YAC7C,kBAAkB,WAAW,gBAAgB;YAC7C,iBAAiB,EAAE;YACnB,eAAe,CAAC;QAClB;QAEA,IAAI,CAAC,KAAK,GAAG;YACX,YAAY,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD;YAC5B,iBAAiB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC7C,WAAW,EAAE;YACb,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC5C,OAAO;YACP,YAAY,uHAAA,CAAA,yBAAsB,CAAC,WAAW;QAChD;QAEA,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG;IAClE;IAEA,mDAAmD;IACnD,MAAM,aAA4B;IAChC,6CAA6C;IAC/C;IAEA,SAAe;IACb,6CAA6C;IAC/C;IAEA,iBAAyB;QACvB,6CAA6C;QAC7C,OAAO;IACT;IAEA,sBAA2B;QACzB,6CAA6C;QAC7C,OAAO,CAAC;IACV;IAEA,gCAAgC;IAChC,MAAM,QAAuB;QAC3B,MAAM,IAAI,CAAC,UAAU;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,QAAc;QACZ,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,SAAe;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,MAAiB;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;QACtC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;QAC5B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAc,EAAE,IAAoB,EAAE,QAAgB,EAAoB;QAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,aAAa,eAAe;QAClC,MAAM,kBAAkB,SAAS,QAAQ,aAAa;QAEtD,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,iBAAiB;YAChD,OAAO,MAAM,uBAAuB;;QACtC;QAEA,wBAAwB;QACxB,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS;YACpF,OAAO,MAAM,qBAAqB;;QACpC;QAEA,oBAAoB;QACpB,MAAM,WAAqB;YACzB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE;YACA;YACA;YACA,aAAa;YACb,eAAe;YACf,KAAK;YACL,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC;QAEA,yBAAyB;QACzB,MAAM,wBAAwB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAC/E,IAAI,yBAAyB,GAAG;YAC9B,qEAAqE;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG;QAChD,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QAC5B;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnE,IAAI,kBAAkB,CAAC,GAAG,OAAO;QAEjC,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;QACpD,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;QACxD,IAAI,CAAC,cAAc,OAAO;QAE1B,sBAAsB;QACtB,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,WAAW,EAAE,cAAc,SAAS,QAAQ,EAAE,SAAS,IAAI;QAE7F,0BAA0B;QAC1B,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAC9B,IAAI,SAAS,IAAI,KAAK,QAAQ;YAC5B,qDAAqD;YACrD,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,SAAS,WAAW,GAAG,SAAS,QAAQ;QACxE;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe;QAE3C,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YAEnE,oFAAoF;YACpF,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,cAAc,CAAC;YAC9D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAC5D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAE5D,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3D,cAAc,MAAM,GAAG,IAAI,wIAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,iBAAiB,EAAE;gBAChF,aAAa,MAAM,GAAG,IAAI,wIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;gBAC7E,aAAa,MAAM,GAAG,IAAI,wIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;aAC9E;YAED,yBAAyB;YACzB,MAAM,UAAU,WAAW,MAAM,CAAC,WAAW,MAAM,CAAC;YACpD,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,KAAK;YAC7C;YAEA,sBAAsB;YACtB,IAAI,CAAC,iBAAiB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,qBAAqB;IACrB,WAAsB;QACpB,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK;QAAC;IACzB;IAEA,YAAwB;QACtB,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,eAAwB;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;IACtD;IAEA,mBAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC3B,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG;QACrD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC,YAAY;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc;IAClC;IAEA,2BAA2B;IACjB,gBAAsB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,MAAM,WAAW;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAEpB,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,gBAAgB;YAErB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,GAAG;gBAClC,IAAI,CAAC,GAAG;gBACR;YACF;YAEA,WAAW,UAAU,MAAM,sBAAsB;;QACnD;QAEA;IACF;IAEU,oBAA0B;QAClC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAC3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;YACxD,IAAI,cAAc;gBAChB,SAAS,aAAa,GAAG;gBACzB,SAAS,GAAG,GAAG,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EACxB,SAAS,WAAW,EACpB,cACA,SAAS,QAAQ,EACjB,SAAS,IAAI;YAEjB;QACF;IACF;IAEU,cAAsB;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,GAAG,EAAE;IAChF;IAEU,kBAA0B;QAClC,MAAM,aAAa,uHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjD,OAAO,AAAC,WAAmB,aAAa,IAAI;IAC9C;IAEU,oBAAoB,MAAc,EAAW;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IACrD;IAEU,eAAe,MAAc,EAAW;QAChD,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC;IACtD;IAEU,cAAc,MAAc,EAAW;QAC/C,MAAM,eAAe;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;QAC9E,OAAO,aAAa,QAAQ,CAAC;IAC/B;IAEU,cAAc,MAAc,EAAW;QAC/C,OAAO,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC;IACvG;IAEU,oBAAoB,SAAiB,EAAE,aAAqB,IAAI,EAAU;QAClF,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI;QAC3C,OAAO,YAAY,CAAC,IAAI,MAAM;IAChC;IAEU,yBAA+B;QACvC,wDAAwD;QACxD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAC5C,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC,OAAO;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC9B;IACF;IAEU,oBAAoB,MAAc,EAAU;QACpD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACxE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACvE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,0BAA0B;;QACtE,OAAO,KAAK,aAAa;;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 1234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/games/scalper-sprint.ts"], "sourcesContent": ["import { BaseGame } from '../base-game'\nimport { GameType, TradingPair } from '@/types'\nimport { TRADING_PAIRS } from '@/lib/constants'\n\ninterface ScalperSprintData {\n  trades_executed: number\n  successful_trades: number\n  largest_gain: number\n  largest_loss: number\n  average_hold_time: number\n  speed_bonus: number\n}\n\nexport class ScalperSprintGame extends BaseGame {\n  private gameData: ScalperSprintData\n  private tradeHistory: Array<{\n    timestamp: number\n    symbol: string\n    side: 'buy' | 'sell'\n    entry_price: number\n    exit_price?: number\n    hold_time?: number\n    pnl?: number\n  }> = []\n\n  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    super('scalper_sprint', difficulty)\n    \n    this.gameData = {\n      trades_executed: 0,\n      successful_trades: 0,\n      largest_gain: 0,\n      largest_loss: 0,\n      average_hold_time: 0,\n      speed_bonus: 0,\n    }\n\n    // Set available trading pairs for scalping (high volatility pairs)\n    this.config.available_pairs = this.getScalpingPairs(difficulty)\n  }\n\n  async initialize(): Promise<void> {\n    // Initialize market data with realistic scalping prices\n    const initialPrices = this.generateInitialPrices()\n    initialPrices.forEach((price, symbol) => {\n      this.marketData.set(symbol, price)\n    })\n\n    // Start market data updates more frequently for scalping\n    this.startMarketDataUpdates()\n  }\n\n  update(): void {\n    // Update market data with high frequency for scalping simulation\n    this.simulateScalpingMarketMovement()\n    this.updatePositionPnL()\n    \n    // Check for auto-close conditions (stop loss, take profit)\n    this.checkAutoCloseConditions()\n    \n    // Update game-specific metrics\n    this.updateGameMetrics()\n  }\n\n  calculateScore(): number {\n    const totalPnL = this.getTotalPnL()\n    const balanceChange = this.state.current_balance - this.config.starting_balance + totalPnL\n    const balanceChangePercentage = (balanceChange / this.config.starting_balance) * 100\n\n    // Base score from P&L percentage\n    let score = Math.max(0, balanceChangePercentage * 10)\n\n    // Bonus for number of successful trades\n    const successRate = this.gameData.trades_executed > 0 \n      ? this.gameData.successful_trades / this.gameData.trades_executed \n      : 0\n    score += successRate * 50\n\n    // Speed bonus for quick decision making\n    score += this.gameData.speed_bonus\n\n    // Penalty for holding positions too long (this is scalping!)\n    const avgHoldTimePenalty = Math.max(0, (this.gameData.average_hold_time - 10) * 2)\n    score -= avgHoldTimePenalty\n\n    // Difficulty multiplier\n    score *= this.state.multiplier\n\n    return Math.round(Math.max(0, score))\n  }\n\n  getGameSpecificData(): ScalperSprintData {\n    return { ...this.gameData }\n  }\n\n  // Override trade execution to add scalping-specific logic\n  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {\n    const success = await super.executeTrade(symbol, side, quantity)\n    \n    if (success) {\n      this.gameData.trades_executed++\n      \n      // Record trade for analytics\n      this.tradeHistory.push({\n        timestamp: Date.now(),\n        symbol,\n        side,\n        entry_price: this.marketData.get(symbol)!,\n      })\n\n      // Speed bonus for quick trades\n      const timeSinceStart = (Date.now() - this.startTime) / 1000\n      if (timeSinceStart < 10) {\n        this.gameData.speed_bonus += 5\n      }\n    }\n\n    return success\n  }\n\n  // Override position closing to track scalping metrics\n  async closePosition(positionId: string): Promise<boolean> {\n    const position = this.state.positions.find(p => p.id === positionId)\n    if (!position) return false\n\n    const success = await super.closePosition(positionId)\n    \n    if (success && position) {\n      const tradeRecord = this.tradeHistory.find(t => \n        t.symbol === position.symbol && \n        t.entry_price === position.entry_price &&\n        !t.exit_price\n      )\n\n      if (tradeRecord) {\n        const holdTime = (Date.now() - tradeRecord.timestamp) / 1000\n        const exitPrice = this.marketData.get(position.symbol)!\n        const pnl = position.pnl\n\n        // Update trade record\n        tradeRecord.exit_price = exitPrice\n        tradeRecord.hold_time = holdTime\n        tradeRecord.pnl = pnl\n\n        // Update game metrics\n        if (pnl > 0) {\n          this.gameData.successful_trades++\n          this.gameData.largest_gain = Math.max(this.gameData.largest_gain, pnl)\n        } else {\n          this.gameData.largest_loss = Math.min(this.gameData.largest_loss, pnl)\n        }\n\n        this.updateAverageHoldTime()\n      }\n    }\n\n    return success\n  }\n\n  private getScalpingPairs(difficulty: 'beginner' | 'intermediate' | 'advanced'): TradingPair[] {\n    const allPairs = TRADING_PAIRS\n    \n    switch (difficulty) {\n      case 'beginner':\n        // Major crypto pairs with high liquidity\n        return allPairs.filter(pair => \n          ['BTCUSD', 'ETHUSD'].includes(pair.symbol)\n        )\n      case 'intermediate':\n        // Add some altcoins and major stocks\n        return allPairs.filter(pair => \n          ['BTCUSD', 'ETHUSD', 'ADAUSD', 'AAPL', 'GOOGL'].includes(pair.symbol)\n        )\n      case 'advanced':\n        // All available pairs including forex\n        return allPairs\n      default:\n        return allPairs.slice(0, 3)\n    }\n  }\n\n  private generateInitialPrices(): Map<string, number> {\n    const prices = new Map<string, number>()\n    \n    // Realistic starting prices for scalping simulation\n    const basePrices: Record<string, number> = {\n      'BTCUSD': 45000 + (Math.random() - 0.5) * 2000,\n      'ETHUSD': 3000 + (Math.random() - 0.5) * 200,\n      'ADAUSD': 0.5 + (Math.random() - 0.5) * 0.1,\n      'SOLUSD': 100 + (Math.random() - 0.5) * 20,\n      'AAPL': 150 + (Math.random() - 0.5) * 10,\n      'GOOGL': 2500 + (Math.random() - 0.5) * 100,\n      'TSLA': 800 + (Math.random() - 0.5) * 50,\n      'EURUSD': 1.1 + (Math.random() - 0.5) * 0.02,\n      'GBPUSD': 1.3 + (Math.random() - 0.5) * 0.02,\n      'JPYUSD': 0.009 + (Math.random() - 0.5) * 0.0002,\n    }\n\n    this.config.available_pairs.forEach(pair => {\n      prices.set(pair.symbol, basePrices[pair.symbol] || 100)\n    })\n\n    return prices\n  }\n\n  private startMarketDataUpdates(): void {\n    // Update market data every 2 seconds for realistic scalping\n    const updateInterval = setInterval(() => {\n      if (!this.isActive) {\n        clearInterval(updateInterval)\n        return\n      }\n      this.simulateScalpingMarketMovement()\n    }, 2000)\n  }\n\n  private simulateScalpingMarketMovement(): void {\n    // Simulate high-frequency price movements typical in scalping\n    this.marketData.forEach((price, symbol) => {\n      // Higher volatility and more frequent small movements\n      const volatility = this.getScalpingVolatility(symbol)\n      const direction = Math.random() > 0.5 ? 1 : -1\n      const change = direction * Math.random() * volatility * price\n      \n      // Add some momentum (trending behavior)\n      const momentum = this.calculateMomentum(symbol)\n      const newPrice = price + change + momentum\n      \n      this.marketData.set(symbol, Math.max(0.01, newPrice))\n    })\n  }\n\n  private getScalpingVolatility(symbol: string): number {\n    // Higher volatility for scalping simulation\n    if (this.isCryptoSymbol(symbol)) return 0.008 // 0.8% per update\n    if (this.isStockSymbol(symbol)) return 0.003 // 0.3% per update\n    if (this.isForexSymbol(symbol)) return 0.001 // 0.1% per update\n    return 0.005\n  }\n\n  private calculateMomentum(symbol: string): number {\n    // Simple momentum calculation based on recent price history\n    // In a real implementation, this would use actual price history\n    return (Math.random() - 0.5) * 0.001 * (this.marketData.get(symbol) || 0)\n  }\n\n  private checkAutoCloseConditions(): void {\n    // Auto-close positions that hit stop loss or take profit levels\n    this.state.positions.forEach(position => {\n      const currentPrice = position.current_price\n      const entryPrice = position.entry_price\n      const pnlPercentage = (position.pnl / (entryPrice * position.quantity)) * 100\n\n      // Auto-close on 5% loss (stop loss) or 3% gain (take profit) for scalping\n      if (pnlPercentage <= -5 || pnlPercentage >= 3) {\n        this.closePosition(position.id)\n      }\n    })\n  }\n\n  private updateGameMetrics(): void {\n    // Update average hold time\n    this.updateAverageHoldTime()\n    \n    // Update speed bonus based on quick decision making\n    const recentTrades = this.tradeHistory.filter(t => \n      Date.now() - t.timestamp < 5000 // Last 5 seconds\n    )\n    \n    if (recentTrades.length >= 2) {\n      this.gameData.speed_bonus += 2 // Bonus for rapid trading\n    }\n  }\n\n  private updateAverageHoldTime(): void {\n    const completedTrades = this.tradeHistory.filter(t => t.hold_time !== undefined)\n    if (completedTrades.length > 0) {\n      const totalHoldTime = completedTrades.reduce((sum, trade) => sum + (trade.hold_time || 0), 0)\n      this.gameData.average_hold_time = totalHoldTime / completedTrades.length\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAWO,MAAM,0BAA0B,4IAAA,CAAA,WAAQ;IACrC,SAA2B;IAC3B,eAQH,EAAE,CAAA;IAEP,YAAY,UAAoD,CAAE;QAChE,KAAK,CAAC,kBAAkB;QAExB,IAAI,CAAC,QAAQ,GAAG;YACd,iBAAiB;YACjB,mBAAmB;YACnB,cAAc;YACd,cAAc;YACd,mBAAmB;YACnB,aAAa;QACf;QAEA,mEAAmE;QACnE,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACtD;IAEA,MAAM,aAA4B;QAChC,wDAAwD;QACxD,MAAM,gBAAgB,IAAI,CAAC,qBAAqB;QAChD,cAAc,OAAO,CAAC,CAAC,OAAO;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC9B;QAEA,yDAAyD;QACzD,IAAI,CAAC,sBAAsB;IAC7B;IAEA,SAAe;QACb,iEAAiE;QACjE,IAAI,CAAC,8BAA8B;QACnC,IAAI,CAAC,iBAAiB;QAEtB,2DAA2D;QAC3D,IAAI,CAAC,wBAAwB;QAE7B,+BAA+B;QAC/B,IAAI,CAAC,iBAAiB;IACxB;IAEA,iBAAyB;QACvB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG;QAClF,MAAM,0BAA0B,AAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAI;QAEjF,iCAAiC;QACjC,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,0BAA0B;QAElD,wCAAwC;QACxC,MAAM,cAAc,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAChD,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,GAC/D;QACJ,SAAS,cAAc;QAEvB,wCAAwC;QACxC,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW;QAElC,6DAA6D;QAC7D,MAAM,qBAAqB,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,EAAE,IAAI;QAChF,SAAS;QAET,wBAAwB;QACxB,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU;QAE9B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG;IAChC;IAEA,sBAAyC;QACvC,OAAO;YAAE,GAAG,IAAI,CAAC,QAAQ;QAAC;IAC5B;IAEA,0DAA0D;IAC1D,MAAM,aAAa,MAAc,EAAE,IAAoB,EAAE,QAAgB,EAAoB;QAC3F,MAAM,UAAU,MAAM,KAAK,CAAC,aAAa,QAAQ,MAAM;QAEvD,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,eAAe;YAE7B,6BAA6B;YAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,WAAW,KAAK,GAAG;gBACnB;gBACA;gBACA,aAAa,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YACnC;YAEA,+BAA+B;YAC/B,MAAM,iBAAiB,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,SAAS,IAAI;YACvD,IAAI,iBAAiB,IAAI;gBACvB,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;YAC/B;QACF;QAEA,OAAO;IACT;IAEA,sDAAsD;IACtD,MAAM,cAAc,UAAkB,EAAoB;QACxD,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,UAAU,MAAM,KAAK,CAAC,cAAc;QAE1C,IAAI,WAAW,UAAU;YACvB,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IACzC,EAAE,MAAM,KAAK,SAAS,MAAM,IAC5B,EAAE,WAAW,KAAK,SAAS,WAAW,IACtC,CAAC,EAAE,UAAU;YAGf,IAAI,aAAa;gBACf,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,YAAY,SAAS,IAAI;gBACxD,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;gBACrD,MAAM,MAAM,SAAS,GAAG;gBAExB,sBAAsB;gBACtB,YAAY,UAAU,GAAG;gBACzB,YAAY,SAAS,GAAG;gBACxB,YAAY,GAAG,GAAG;gBAElB,sBAAsB;gBACtB,IAAI,MAAM,GAAG;oBACX,IAAI,CAAC,QAAQ,CAAC,iBAAiB;oBAC/B,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBACpE,OAAO;oBACL,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBACpE;gBAEA,IAAI,CAAC,qBAAqB;YAC5B;QACF;QAEA,OAAO;IACT;IAEQ,iBAAiB,UAAoD,EAAiB;QAC5F,MAAM,WAAW,uHAAA,CAAA,gBAAa;QAE9B,OAAQ;YACN,KAAK;gBACH,yCAAyC;gBACzC,OAAO,SAAS,MAAM,CAAC,CAAA,OACrB;wBAAC;wBAAU;qBAAS,CAAC,QAAQ,CAAC,KAAK,MAAM;YAE7C,KAAK;gBACH,qCAAqC;gBACrC,OAAO,SAAS,MAAM,CAAC,CAAA,OACrB;wBAAC;wBAAU;wBAAU;wBAAU;wBAAQ;qBAAQ,CAAC,QAAQ,CAAC,KAAK,MAAM;YAExE,KAAK;gBACH,sCAAsC;gBACtC,OAAO;YACT;gBACE,OAAO,SAAS,KAAK,CAAC,GAAG;QAC7B;IACF;IAEQ,wBAA6C;QACnD,MAAM,SAAS,IAAI;QAEnB,oDAAoD;QACpD,MAAM,aAAqC;YACzC,UAAU,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC1C,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,QAAQ,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACtC,SAAS,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,QAAQ,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACtC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,UAAU,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC5C;QAEA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAClC,OAAO,GAAG,CAAC,KAAK,MAAM,EAAE,UAAU,CAAC,KAAK,MAAM,CAAC,IAAI;QACrD;QAEA,OAAO;IACT;IAEQ,yBAA+B;QACrC,4DAA4D;QAC5D,MAAM,iBAAiB,YAAY;YACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,cAAc;gBACd;YACF;YACA,IAAI,CAAC,8BAA8B;QACrC,GAAG;IACL;IAEQ,iCAAuC;QAC7C,8DAA8D;QAC9D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,sDAAsD;YACtD,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;YAC9C,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;YAC7C,MAAM,SAAS,YAAY,KAAK,MAAM,KAAK,aAAa;YAExD,wCAAwC;YACxC,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC;YACxC,MAAM,WAAW,QAAQ,SAAS;YAElC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM;QAC7C;IACF;IAEQ,sBAAsB,MAAc,EAAU;QACpD,4CAA4C;QAC5C,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,MAAM,kBAAkB;;QAChE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,MAAM,kBAAkB;;QAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,MAAM,kBAAkB;;QAC/D,OAAO;IACT;IAEQ,kBAAkB,MAAc,EAAU;QAChD,4DAA4D;QAC5D,gEAAgE;QAChE,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC;IAC1E;IAEQ,2BAAiC;QACvC,gEAAgE;QAChE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAC3B,MAAM,eAAe,SAAS,aAAa;YAC3C,MAAM,aAAa,SAAS,WAAW;YACvC,MAAM,gBAAgB,AAAC,SAAS,GAAG,GAAG,CAAC,aAAa,SAAS,QAAQ,IAAK;YAE1E,0EAA0E;YAC1E,IAAI,iBAAiB,CAAC,KAAK,iBAAiB,GAAG;gBAC7C,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YAChC;QACF;IACF;IAEQ,oBAA0B;QAChC,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB;QAE1B,oDAAoD;QACpD,MAAM,eAAe,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAC5C,KAAK,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK,iBAAiB;;QAGnD,IAAI,aAAa,MAAM,IAAI,GAAG;YAC5B,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE,0BAA0B;;QAC3D;IACF;IAEQ,wBAA8B;QACpC,MAAM,kBAAkB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACtE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,SAAS,IAAI,CAAC,GAAG;YAC3F,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,gBAAgB,gBAAgB,MAAM;QAC1E;IACF;AACF", "debugId": null}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/games/chain-maze.ts"], "sourcesContent": ["import { BaseGame } from '../base-game'\nimport { GameType } from '@/types'\n\ninterface BlockchainNode {\n  id: string\n  type: 'validator' | 'miner' | 'user' | 'contract'\n  position: { x: number; y: number }\n  connections: string[]\n  gasPrice: number\n  congestion: number\n  reward: number\n  active: boolean\n}\n\ninterface Transaction {\n  id: string\n  from: string\n  to: string\n  value: number\n  gasLimit: number\n  gasPrice: number\n  status: 'pending' | 'confirmed' | 'failed'\n  blockNumber?: number\n}\n\ninterface Block {\n  number: number\n  hash: string\n  transactions: Transaction[]\n  gasUsed: number\n  gasLimit: number\n  miner: string\n  timestamp: number\n  difficulty: number\n}\n\ninterface Puzzle {\n  id: string\n  type: 'gas_optimization' | 'consensus_mechanism' | 'network_routing'\n  title: string\n  description: string\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  objective: string\n  initialState: any\n  solution: any\n  hints: string[]\n}\n\ninterface ChainMazeData {\n  puzzles_solved: number\n  gas_saved: number\n  blocks_mined: number\n  transactions_processed: number\n  consensus_score: number\n  efficiency_rating: number\n  current_puzzle: Puzzle | null\n  network_health: number\n  total_gas_used: number\n}\n\nexport class ChainMazeGame extends BaseGame {\n  private gameData: ChainMazeData\n  private blockchain: Block[] = []\n  private network: BlockchainNode[] = []\n  private pendingTransactions: Transaction[] = []\n  private currentPuzzle: Puzzle | null = null\n  private puzzleStartTime: number = 0\n  private availablePuzzles: Puzzle[]\n\n  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    super('chain_maze', difficulty)\n    \n    this.gameData = {\n      puzzles_solved: 0,\n      gas_saved: 0,\n      blocks_mined: 0,\n      transactions_processed: 0,\n      consensus_score: 0,\n      efficiency_rating: 100,\n      current_puzzle: null,\n      network_health: 100,\n      total_gas_used: 0,\n    }\n\n    this.availablePuzzles = this.generatePuzzlesByDifficulty(difficulty)\n    this.config.starting_balance = 1000 // Gas budget\n  }\n\n  async initialize(): Promise<void> {\n    // Initialize blockchain network\n    this.initializeNetwork()\n    this.generateGenesisBlock()\n    \n    // Start first puzzle\n    await this.loadNextPuzzle()\n    \n    // Start network simulation\n    this.startNetworkSimulation()\n  }\n\n  update(): void {\n    // Update network state\n    this.updateNetworkState()\n    \n    // Process pending transactions\n    this.processPendingTransactions()\n    \n    // Update game metrics\n    this.updateGameMetrics()\n  }\n\n  calculateScore(): number {\n    let score = 0\n    \n    // Base score from puzzles solved\n    score += this.gameData.puzzles_solved * 200\n    \n    // Gas efficiency bonus\n    const gasEfficiency = Math.max(0, 1000 - this.gameData.total_gas_used)\n    score += gasEfficiency\n    \n    // Consensus participation bonus\n    score += this.gameData.consensus_score * 10\n    \n    // Network health bonus\n    score += this.gameData.network_health * 2\n    \n    // Efficiency rating bonus\n    score += this.gameData.efficiency_rating * 5\n    \n    // Difficulty multiplier\n    score *= this.state.multiplier\n    \n    return Math.round(Math.max(0, score))\n  }\n\n  getGameSpecificData(): ChainMazeData {\n    return { ...this.gameData }\n  }\n\n  getCurrentPuzzle(): Puzzle | null {\n    return this.currentPuzzle\n  }\n\n  getNetworkState(): {\n    nodes: BlockchainNode[]\n    blockchain: Block[]\n    pendingTransactions: Transaction[]\n  } {\n    return {\n      nodes: this.network,\n      blockchain: this.blockchain,\n      pendingTransactions: this.pendingTransactions,\n    }\n  }\n\n  async submitPuzzleSolution(solution: any): Promise<boolean> {\n    if (!this.currentPuzzle || !this.isActive) return false\n\n    const correct = this.validateSolution(this.currentPuzzle, solution)\n    const timeToSolve = Date.now() - this.puzzleStartTime\n\n    if (correct) {\n      this.gameData.puzzles_solved++\n      \n      // Apply puzzle effects\n      this.applyPuzzleEffects(this.currentPuzzle, solution)\n      \n      // Time bonus for quick solutions\n      if (timeToSolve < 30000) { // Under 30 seconds\n        const timeBonus = Math.max(0, 100 - Math.floor(timeToSolve / 300))\n        this.gameData.efficiency_rating += timeBonus\n      }\n      \n      // Load next puzzle\n      await this.loadNextPuzzle()\n    } else {\n      // Penalty for wrong solution\n      this.gameData.efficiency_rating = Math.max(0, this.gameData.efficiency_rating - 10)\n    }\n\n    return correct\n  }\n\n  async executeTransaction(tx: Transaction): Promise<boolean> {\n    if (this.state.current_balance < tx.gasPrice * tx.gasLimit) {\n      return false // Insufficient gas budget\n    }\n\n    // Deduct gas cost\n    this.state.current_balance -= tx.gasPrice * tx.gasLimit\n    this.gameData.total_gas_used += tx.gasPrice * tx.gasLimit\n\n    // Add to pending transactions\n    this.pendingTransactions.push({\n      ...tx,\n      status: 'pending',\n    })\n\n    return true\n  }\n\n  async mineBlock(): Promise<boolean> {\n    if (this.pendingTransactions.length === 0) return false\n\n    const block = this.createNewBlock()\n    this.blockchain.push(block)\n    this.gameData.blocks_mined++\n    \n    // Reward for mining\n    const miningReward = 50\n    this.state.current_balance += miningReward\n    \n    // Clear processed transactions\n    this.pendingTransactions = this.pendingTransactions.filter(\n      tx => !block.transactions.includes(tx)\n    )\n\n    return true\n  }\n\n  private initializeNetwork(): void {\n    // Create network nodes\n    this.network = [\n      {\n        id: 'validator_1',\n        type: 'validator',\n        position: { x: 100, y: 100 },\n        connections: ['validator_2', 'miner_1'],\n        gasPrice: 20,\n        congestion: 0.3,\n        reward: 10,\n        active: true,\n      },\n      {\n        id: 'validator_2',\n        type: 'validator',\n        position: { x: 300, y: 100 },\n        connections: ['validator_1', 'miner_2'],\n        gasPrice: 25,\n        congestion: 0.5,\n        reward: 15,\n        active: true,\n      },\n      {\n        id: 'miner_1',\n        type: 'miner',\n        position: { x: 200, y: 200 },\n        connections: ['validator_1', 'contract_1'],\n        gasPrice: 30,\n        congestion: 0.7,\n        reward: 20,\n        active: true,\n      },\n      {\n        id: 'miner_2',\n        type: 'miner',\n        position: { x: 400, y: 200 },\n        connections: ['validator_2', 'contract_1'],\n        gasPrice: 35,\n        congestion: 0.4,\n        reward: 25,\n        active: true,\n      },\n      {\n        id: 'contract_1',\n        type: 'contract',\n        position: { x: 300, y: 300 },\n        connections: ['miner_1', 'miner_2'],\n        gasPrice: 40,\n        congestion: 0.6,\n        reward: 30,\n        active: true,\n      },\n    ]\n  }\n\n  private generateGenesisBlock(): void {\n    this.blockchain = [{\n      number: 0,\n      hash: '0x0000000000000000000000000000000000000000000000000000000000000000',\n      transactions: [],\n      gasUsed: 0,\n      gasLimit: 8000000,\n      miner: 'genesis',\n      timestamp: Date.now(),\n      difficulty: 1,\n    }]\n  }\n\n  private async loadNextPuzzle(): Promise<void> {\n    if (this.availablePuzzles.length === 0) {\n      // Generate more puzzles or end game\n      this.availablePuzzles = this.generatePuzzlesByDifficulty(this.config.difficulty)\n    }\n\n    this.currentPuzzle = this.availablePuzzles.shift() || null\n    this.gameData.current_puzzle = this.currentPuzzle\n    this.puzzleStartTime = Date.now()\n  }\n\n  private generatePuzzlesByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): Puzzle[] {\n    const puzzles: Puzzle[] = []\n\n    if (difficulty === 'beginner' || difficulty === 'intermediate' || difficulty === 'advanced') {\n      puzzles.push({\n        id: 'gas_opt_1',\n        type: 'gas_optimization',\n        title: 'Optimize Transaction Gas',\n        description: 'Find the most cost-effective path to execute this transaction',\n        difficulty: 'beginner',\n        objective: 'Minimize gas cost while ensuring transaction success',\n        initialState: {\n          transaction: { value: 100, gasLimit: 21000 },\n          availableNodes: ['validator_1', 'validator_2'],\n        },\n        solution: { selectedNode: 'validator_1', gasPrice: 20 },\n        hints: ['Lower gas price nodes are more cost-effective', 'Check node congestion levels'],\n      })\n    }\n\n    if (difficulty === 'intermediate' || difficulty === 'advanced') {\n      puzzles.push({\n        id: 'consensus_1',\n        type: 'consensus_mechanism',\n        title: 'Achieve Network Consensus',\n        description: 'Coordinate validators to reach consensus on the next block',\n        difficulty: 'intermediate',\n        objective: 'Get 2/3 majority agreement from validators',\n        initialState: {\n          validators: ['validator_1', 'validator_2'],\n          proposedBlock: { number: 1, transactions: 3 },\n        },\n        solution: { votes: ['validator_1', 'validator_2'], consensus: true },\n        hints: ['Validators need to agree on block validity', 'Majority consensus is required'],\n      })\n    }\n\n    if (difficulty === 'advanced') {\n      puzzles.push({\n        id: 'routing_1',\n        type: 'network_routing',\n        title: 'Optimize Network Routing',\n        description: 'Find the optimal path through the network for maximum efficiency',\n        difficulty: 'advanced',\n        objective: 'Route transactions through the network with minimal latency and cost',\n        initialState: {\n          source: 'validator_1',\n          destination: 'contract_1',\n          constraints: { maxGas: 100, maxHops: 3 },\n        },\n        solution: { path: ['validator_1', 'miner_1', 'contract_1'], totalCost: 70 },\n        hints: ['Consider both gas cost and network congestion', 'Shorter paths are not always optimal'],\n      })\n    }\n\n    return puzzles\n  }\n\n  private validateSolution(puzzle: Puzzle, solution: any): boolean {\n    switch (puzzle.type) {\n      case 'gas_optimization':\n        return this.validateGasOptimization(puzzle, solution)\n      case 'consensus_mechanism':\n        return this.validateConsensus(puzzle, solution)\n      case 'network_routing':\n        return this.validateRouting(puzzle, solution)\n      default:\n        return false\n    }\n  }\n\n  private validateGasOptimization(puzzle: Puzzle, solution: any): boolean {\n    const expectedSolution = puzzle.solution\n    const selectedNode = this.network.find(n => n.id === solution.selectedNode)\n    \n    if (!selectedNode) return false\n    \n    // Check if solution is optimal (lowest gas price with acceptable congestion)\n    return solution.selectedNode === expectedSolution.selectedNode &&\n           solution.gasPrice <= expectedSolution.gasPrice * 1.1 // 10% tolerance\n  }\n\n  private validateConsensus(puzzle: Puzzle, solution: any): boolean {\n    const requiredVotes = Math.ceil(puzzle.initialState.validators.length * 2 / 3)\n    return solution.votes && solution.votes.length >= requiredVotes && solution.consensus === true\n  }\n\n  private validateRouting(puzzle: Puzzle, solution: any): boolean {\n    const expectedSolution = puzzle.solution\n    \n    // Validate path exists and meets constraints\n    if (!solution.path || !Array.isArray(solution.path)) return false\n    \n    // Check path validity\n    const pathValid = this.validateNetworkPath(solution.path)\n    const costAcceptable = solution.totalCost <= expectedSolution.totalCost * 1.2 // 20% tolerance\n    \n    return pathValid && costAcceptable\n  }\n\n  private validateNetworkPath(path: string[]): boolean {\n    for (let i = 0; i < path.length - 1; i++) {\n      const currentNode = this.network.find(n => n.id === path[i])\n      const nextNode = path[i + 1]\n      \n      if (!currentNode || !currentNode.connections.includes(nextNode)) {\n        return false\n      }\n    }\n    return true\n  }\n\n  private applyPuzzleEffects(puzzle: Puzzle, solution: any): void {\n    switch (puzzle.type) {\n      case 'gas_optimization':\n        const gasSaved = 50 - (solution.gasPrice || 30)\n        this.gameData.gas_saved += Math.max(0, gasSaved)\n        break\n      case 'consensus_mechanism':\n        this.gameData.consensus_score += 10\n        this.gameData.network_health += 5\n        break\n      case 'network_routing':\n        this.gameData.efficiency_rating += 10\n        break\n    }\n  }\n\n  private startNetworkSimulation(): void {\n    // Simulate network activity\n    const simulationInterval = setInterval(() => {\n      if (!this.isActive) {\n        clearInterval(simulationInterval)\n        return\n      }\n      \n      // Generate random transactions\n      if (Math.random() < 0.3) {\n        this.generateRandomTransaction()\n      }\n      \n      // Update node states\n      this.updateNodeStates()\n      \n    }, 2000)\n  }\n\n  private generateRandomTransaction(): void {\n    const nodes = this.network.filter(n => n.active)\n    const fromNode = nodes[Math.floor(Math.random() * nodes.length)]\n    const toNode = nodes[Math.floor(Math.random() * nodes.length)]\n    \n    if (fromNode.id !== toNode.id) {\n      const tx: Transaction = {\n        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        from: fromNode.id,\n        to: toNode.id,\n        value: Math.floor(Math.random() * 100) + 1,\n        gasLimit: 21000,\n        gasPrice: fromNode.gasPrice,\n        status: 'pending',\n      }\n      \n      this.pendingTransactions.push(tx)\n    }\n  }\n\n  private updateNodeStates(): void {\n    this.network.forEach(node => {\n      // Update congestion based on pending transactions\n      const nodeTxCount = this.pendingTransactions.filter(\n        tx => tx.from === node.id || tx.to === node.id\n      ).length\n      \n      node.congestion = Math.min(1, nodeTxCount / 10)\n      \n      // Adjust gas price based on congestion\n      const basePrices = { validator: 20, miner: 30, user: 15, contract: 40 }\n      node.gasPrice = basePrices[node.type] * (1 + node.congestion)\n    })\n  }\n\n  private processPendingTransactions(): void {\n    // Process a few transactions each update\n    const txToProcess = this.pendingTransactions.slice(0, 3)\n    \n    txToProcess.forEach(tx => {\n      if (Math.random() < 0.7) { // 70% success rate\n        tx.status = 'confirmed'\n        this.gameData.transactions_processed++\n      } else {\n        tx.status = 'failed'\n      }\n    })\n    \n    // Remove processed transactions\n    this.pendingTransactions = this.pendingTransactions.filter(\n      tx => !txToProcess.includes(tx)\n    )\n  }\n\n  private createNewBlock(): Block {\n    const txToInclude = this.pendingTransactions.slice(0, 5) // Max 5 tx per block\n    const gasUsed = txToInclude.reduce((sum, tx) => sum + (tx.gasLimit * tx.gasPrice), 0)\n    \n    return {\n      number: this.blockchain.length,\n      hash: `0x${Math.random().toString(16).substr(2, 64)}`,\n      transactions: txToInclude,\n      gasUsed,\n      gasLimit: 8000000,\n      miner: 'player',\n      timestamp: Date.now(),\n      difficulty: Math.floor(Math.random() * 1000) + 1000,\n    }\n  }\n\n  private updateNetworkState(): void {\n    // Update network health based on various factors\n    const avgCongestion = this.network.reduce((sum, node) => sum + node.congestion, 0) / this.network.length\n    const pendingTxRatio = Math.min(1, this.pendingTransactions.length / 20)\n    \n    this.gameData.network_health = Math.max(0, 100 - (avgCongestion * 30) - (pendingTxRatio * 40))\n  }\n\n  private updateGameMetrics(): void {\n    // Update efficiency rating based on performance\n    const gasEfficiency = Math.max(0, 100 - (this.gameData.total_gas_used / 10))\n    const consensusEfficiency = Math.min(100, this.gameData.consensus_score * 2)\n    \n    this.gameData.efficiency_rating = (gasEfficiency + consensusEfficiency + this.gameData.network_health) / 3\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AA4DO,MAAM,sBAAsB,4IAAA,CAAA,WAAQ;IACjC,SAAuB;IACvB,aAAsB,EAAE,CAAA;IACxB,UAA4B,EAAE,CAAA;IAC9B,sBAAqC,EAAE,CAAA;IACvC,gBAA+B,KAAI;IACnC,kBAA0B,EAAC;IAC3B,iBAA0B;IAElC,YAAY,UAAoD,CAAE;QAChE,KAAK,CAAC,cAAc;QAEpB,IAAI,CAAC,QAAQ,GAAG;YACd,gBAAgB;YAChB,WAAW;YACX,cAAc;YACd,wBAAwB;YACxB,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB;YAChB,gBAAgB;YAChB,gBAAgB;QAClB;QAEA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG,KAAK,aAAa;;IACnD;IAEA,MAAM,aAA4B;QAChC,gCAAgC;QAChC,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,oBAAoB;QAEzB,qBAAqB;QACrB,MAAM,IAAI,CAAC,cAAc;QAEzB,2BAA2B;QAC3B,IAAI,CAAC,sBAAsB;IAC7B;IAEA,SAAe;QACb,uBAAuB;QACvB,IAAI,CAAC,kBAAkB;QAEvB,+BAA+B;QAC/B,IAAI,CAAC,0BAA0B;QAE/B,sBAAsB;QACtB,IAAI,CAAC,iBAAiB;IACxB;IAEA,iBAAyB;QACvB,IAAI,QAAQ;QAEZ,iCAAiC;QACjC,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG;QAExC,uBAAuB;QACvB,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc;QACrE,SAAS;QAET,gCAAgC;QAChC,SAAS,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG;QAEzC,uBAAuB;QACvB,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG;QAExC,0BAA0B;QAC1B,SAAS,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG;QAE3C,wBAAwB;QACxB,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU;QAE9B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG;IAChC;IAEA,sBAAqC;QACnC,OAAO;YAAE,GAAG,IAAI,CAAC,QAAQ;QAAC;IAC5B;IAEA,mBAAkC;QAChC,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,kBAIE;QACA,OAAO;YACL,OAAO,IAAI,CAAC,OAAO;YACnB,YAAY,IAAI,CAAC,UAAU;YAC3B,qBAAqB,IAAI,CAAC,mBAAmB;QAC/C;IACF;IAEA,MAAM,qBAAqB,QAAa,EAAoB;QAC1D,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAElD,MAAM,UAAU,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE;QAC1D,MAAM,cAAc,KAAK,GAAG,KAAK,IAAI,CAAC,eAAe;QAErD,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,cAAc;YAE5B,uBAAuB;YACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,EAAE;YAE5C,iCAAiC;YACjC,IAAI,cAAc,OAAO;gBACvB,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,MAAM,KAAK,KAAK,CAAC,cAAc;gBAC7D,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI;YACrC;YAEA,mBAAmB;YACnB,MAAM,IAAI,CAAC,cAAc;QAC3B,OAAO;YACL,6BAA6B;YAC7B,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG;QAClF;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,EAAe,EAAoB;QAC1D,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,GAAG,GAAG,QAAQ,EAAE;YAC1D,OAAO,MAAM,0BAA0B;;QACzC;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,GAAG,QAAQ,GAAG,GAAG,QAAQ;QACvD,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,GAAG,QAAQ,GAAG,GAAG,QAAQ;QAEzD,8BAA8B;QAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC5B,GAAG,EAAE;YACL,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,MAAM,YAA8B;QAClC,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,GAAG,OAAO;QAElD,MAAM,QAAQ,IAAI,CAAC,cAAc;QACjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,YAAY;QAE1B,oBAAoB;QACpB,MAAM,eAAe;QACrB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAE9B,+BAA+B;QAC/B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CACxD,CAAA,KAAM,CAAC,MAAM,YAAY,CAAC,QAAQ,CAAC;QAGrC,OAAO;IACT;IAEQ,oBAA0B;QAChC,uBAAuB;QACvB,IAAI,CAAC,OAAO,GAAG;YACb;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,aAAa;oBAAC;oBAAe;iBAAU;gBACvC,UAAU;gBACV,YAAY;gBACZ,QAAQ;gBACR,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,aAAa;oBAAC;oBAAe;iBAAU;gBACvC,UAAU;gBACV,YAAY;gBACZ,QAAQ;gBACR,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,aAAa;oBAAC;oBAAe;iBAAa;gBAC1C,UAAU;gBACV,YAAY;gBACZ,QAAQ;gBACR,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,aAAa;oBAAC;oBAAe;iBAAa;gBAC1C,UAAU;gBACV,YAAY;gBACZ,QAAQ;gBACR,QAAQ;YACV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;oBAAE,GAAG;oBAAK,GAAG;gBAAI;gBAC3B,aAAa;oBAAC;oBAAW;iBAAU;gBACnC,UAAU;gBACV,YAAY;gBACZ,QAAQ;gBACR,QAAQ;YACV;SACD;IACH;IAEQ,uBAA6B;QACnC,IAAI,CAAC,UAAU,GAAG;YAAC;gBACjB,QAAQ;gBACR,MAAM;gBACN,cAAc,EAAE;gBAChB,SAAS;gBACT,UAAU;gBACV,OAAO;gBACP,WAAW,KAAK,GAAG;gBACnB,YAAY;YACd;SAAE;IACJ;IAEA,MAAc,iBAAgC;QAC5C,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,GAAG;YACtC,oCAAoC;YACpC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;QACjF;QAEA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,MAAM;QACtD,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa;QACjD,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;IACjC;IAEQ,4BAA4B,UAAoD,EAAY;QAClG,MAAM,UAAoB,EAAE;QAE5B,IAAI,eAAe,cAAc,eAAe,kBAAkB,eAAe,YAAY;YAC3F,QAAQ,IAAI,CAAC;gBACX,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,WAAW;gBACX,cAAc;oBACZ,aAAa;wBAAE,OAAO;wBAAK,UAAU;oBAAM;oBAC3C,gBAAgB;wBAAC;wBAAe;qBAAc;gBAChD;gBACA,UAAU;oBAAE,cAAc;oBAAe,UAAU;gBAAG;gBACtD,OAAO;oBAAC;oBAAiD;iBAA+B;YAC1F;QACF;QAEA,IAAI,eAAe,kBAAkB,eAAe,YAAY;YAC9D,QAAQ,IAAI,CAAC;gBACX,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,WAAW;gBACX,cAAc;oBACZ,YAAY;wBAAC;wBAAe;qBAAc;oBAC1C,eAAe;wBAAE,QAAQ;wBAAG,cAAc;oBAAE;gBAC9C;gBACA,UAAU;oBAAE,OAAO;wBAAC;wBAAe;qBAAc;oBAAE,WAAW;gBAAK;gBACnE,OAAO;oBAAC;oBAA8C;iBAAiC;YACzF;QACF;QAEA,IAAI,eAAe,YAAY;YAC7B,QAAQ,IAAI,CAAC;gBACX,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,YAAY;gBACZ,WAAW;gBACX,cAAc;oBACZ,QAAQ;oBACR,aAAa;oBACb,aAAa;wBAAE,QAAQ;wBAAK,SAAS;oBAAE;gBACzC;gBACA,UAAU;oBAAE,MAAM;wBAAC;wBAAe;wBAAW;qBAAa;oBAAE,WAAW;gBAAG;gBAC1E,OAAO;oBAAC;oBAAiD;iBAAuC;YAClG;QACF;QAEA,OAAO;IACT;IAEQ,iBAAiB,MAAc,EAAE,QAAa,EAAW;QAC/D,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ;YAC9C,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ;YACxC,KAAK;gBACH,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ;YACtC;gBACE,OAAO;QACX;IACF;IAEQ,wBAAwB,MAAc,EAAE,QAAa,EAAW;QACtE,MAAM,mBAAmB,OAAO,QAAQ;QACxC,MAAM,eAAe,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,YAAY;QAE1E,IAAI,CAAC,cAAc,OAAO;QAE1B,6EAA6E;QAC7E,OAAO,SAAS,YAAY,KAAK,iBAAiB,YAAY,IACvD,SAAS,QAAQ,IAAI,iBAAiB,QAAQ,GAAG,IAAI,gBAAgB;;IAC9E;IAEQ,kBAAkB,MAAc,EAAE,QAAa,EAAW;QAChE,MAAM,gBAAgB,KAAK,IAAI,CAAC,OAAO,YAAY,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI;QAC5E,OAAO,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,MAAM,IAAI,iBAAiB,SAAS,SAAS,KAAK;IAC5F;IAEQ,gBAAgB,MAAc,EAAE,QAAa,EAAW;QAC9D,MAAM,mBAAmB,OAAO,QAAQ;QAExC,6CAA6C;QAC7C,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG,OAAO;QAE5D,sBAAsB;QACtB,MAAM,YAAY,IAAI,CAAC,mBAAmB,CAAC,SAAS,IAAI;QACxD,MAAM,iBAAiB,SAAS,SAAS,IAAI,iBAAiB,SAAS,GAAG,IAAI,gBAAgB;;QAE9F,OAAO,aAAa;IACtB;IAEQ,oBAAoB,IAAc,EAAW;QACnD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;YACxC,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE;YAC3D,MAAM,WAAW,IAAI,CAAC,IAAI,EAAE;YAE5B,IAAI,CAAC,eAAe,CAAC,YAAY,WAAW,CAAC,QAAQ,CAAC,WAAW;gBAC/D,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,mBAAmB,MAAc,EAAE,QAAa,EAAQ;QAC9D,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,MAAM,WAAW,KAAK,CAAC,SAAS,QAAQ,IAAI,EAAE;gBAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,GAAG;gBACvC;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI;gBACjC,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI;gBAChC;YACF,KAAK;gBACH,IAAI,CAAC,QAAQ,CAAC,iBAAiB,IAAI;gBACnC;QACJ;IACF;IAEQ,yBAA+B;QACrC,4BAA4B;QAC5B,MAAM,qBAAqB,YAAY;YACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,cAAc;gBACd;YACF;YAEA,+BAA+B;YAC/B,IAAI,KAAK,MAAM,KAAK,KAAK;gBACvB,IAAI,CAAC,yBAAyB;YAChC;YAEA,qBAAqB;YACrB,IAAI,CAAC,gBAAgB;QAEvB,GAAG;IACL;IAEQ,4BAAkC;QACxC,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM;QAC/C,MAAM,WAAW,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;QAChE,MAAM,SAAS,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;QAE9D,IAAI,SAAS,EAAE,KAAK,OAAO,EAAE,EAAE;YAC7B,MAAM,KAAkB;gBACtB,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACjE,MAAM,SAAS,EAAE;gBACjB,IAAI,OAAO,EAAE;gBACb,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBACzC,UAAU;gBACV,UAAU,SAAS,QAAQ;gBAC3B,QAAQ;YACV;YAEA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;QAChC;IACF;IAEQ,mBAAyB;QAC/B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;YACnB,kDAAkD;YAClD,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,MAAM,CACjD,CAAA,KAAM,GAAG,IAAI,KAAK,KAAK,EAAE,IAAI,GAAG,EAAE,KAAK,KAAK,EAAE,EAC9C,MAAM;YAER,KAAK,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,cAAc;YAE5C,uCAAuC;YACvC,MAAM,aAAa;gBAAE,WAAW;gBAAI,OAAO;gBAAI,MAAM;gBAAI,UAAU;YAAG;YACtE,KAAK,QAAQ,GAAG,UAAU,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU;QAC9D;IACF;IAEQ,6BAAmC;QACzC,yCAAyC;QACzC,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG;QAEtD,YAAY,OAAO,CAAC,CAAA;YAClB,IAAI,KAAK,MAAM,KAAK,KAAK;gBACvB,GAAG,MAAM,GAAG;gBACZ,IAAI,CAAC,QAAQ,CAAC,sBAAsB;YACtC,OAAO;gBACL,GAAG,MAAM,GAAG;YACd;QACF;QAEA,gCAAgC;QAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CACxD,CAAA,KAAM,CAAC,YAAY,QAAQ,CAAC;IAEhC;IAEQ,iBAAwB;QAC9B,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,GAAG,qBAAqB;;QAC9E,MAAM,UAAU,YAAY,MAAM,CAAC,CAAC,KAAK,KAAO,MAAO,GAAG,QAAQ,GAAG,GAAG,QAAQ,EAAG;QAEnF,OAAO;YACL,QAAQ,IAAI,CAAC,UAAU,CAAC,MAAM;YAC9B,MAAM,CAAC,EAAE,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,KAAK;YACrD,cAAc;YACd;YACA,UAAU;YACV,OAAO;YACP,WAAW,KAAK,GAAG;YACnB,YAAY,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;QACjD;IACF;IAEQ,qBAA2B;QACjC,iDAAiD;QACjD,MAAM,gBAAgB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM;QACxG,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG;QAErE,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,GAAG,CAAC,GAAG,MAAO,gBAAgB,KAAO,iBAAiB;IAC5F;IAEQ,oBAA0B;QAChC,gDAAgD;QAChD,MAAM,gBAAgB,KAAK,GAAG,CAAC,GAAG,MAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG;QACxE,MAAM,sBAAsB,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG;QAE1E,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,CAAC,gBAAgB,sBAAsB,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI;IAC3G;AACF", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/games/candle-strike.ts"], "sourcesContent": ["import { BaseGame } from '../base-game'\nimport { GameType, CandlestickData } from '@/types'\nimport { TRADING_PAIRS } from '@/lib/constants'\n\ninterface CandlePattern {\n  id: string\n  name: string\n  description: string\n  bullish: boolean\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  minCandles: number\n  maxCandles: number\n}\n\ninterface CandleStrikeData {\n  patterns_identified: number\n  correct_identifications: number\n  wrong_identifications: number\n  current_pattern: CandlePattern | null\n  patterns_completed: CandlePattern[]\n  accuracy_percentage: number\n  speed_bonus: number\n  streak_count: number\n  max_streak: number\n}\n\ninterface PatternChallenge {\n  pattern: CandlePattern\n  candleData: CandlestickData[]\n  patternStartIndex: number\n  patternEndIndex: number\n  options: string[]\n  correctAnswer: number\n}\n\nexport class CandleStrikeGame extends BaseGame {\n  private gameData: CandleStrikeData\n  private currentChallenge: PatternChallenge | null = null\n  private challengeHistory: Array<{\n    challenge: PatternChallenge\n    userAnswer: number\n    correct: boolean\n    timeToAnswer: number\n    timestamp: number\n  }> = []\n  private challengeStartTime: number = 0\n  private availablePatterns: CandlePattern[]\n\n  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    super('candle_strike', difficulty)\n    \n    this.gameData = {\n      patterns_identified: 0,\n      correct_identifications: 0,\n      wrong_identifications: 0,\n      current_pattern: null,\n      patterns_completed: [],\n      accuracy_percentage: 0,\n      speed_bonus: 0,\n      streak_count: 0,\n      max_streak: 0,\n    }\n\n    this.availablePatterns = this.getPatternsByDifficulty(difficulty)\n    this.config.available_pairs = [TRADING_PAIRS[0]] // Use BTC for pattern recognition\n  }\n\n  async initialize(): Promise<void> {\n    // Generate first challenge\n    await this.generateNewChallenge()\n  }\n\n  update(): void {\n    // Update game metrics\n    this.updateGameMetrics()\n  }\n\n  calculateScore(): number {\n    const baseScore = this.gameData.correct_identifications * 100\n    const accuracyBonus = this.gameData.accuracy_percentage * 2\n    const speedBonus = this.gameData.speed_bonus\n    const streakBonus = this.gameData.max_streak * 50\n    \n    let totalScore = baseScore + accuracyBonus + speedBonus + streakBonus\n    \n    // Difficulty multiplier\n    totalScore *= this.state.multiplier\n    \n    return Math.round(Math.max(0, totalScore))\n  }\n\n  getGameSpecificData(): CandleStrikeData {\n    return { ...this.gameData }\n  }\n\n  getCurrentChallenge(): PatternChallenge | null {\n    return this.currentChallenge\n  }\n\n  async submitAnswer(answerIndex: number): Promise<boolean> {\n    if (!this.currentChallenge || !this.isActive) return false\n\n    const timeToAnswer = Date.now() - this.challengeStartTime\n    const correct = answerIndex === this.currentChallenge.correctAnswer\n\n    // Record the attempt\n    this.challengeHistory.push({\n      challenge: this.currentChallenge,\n      userAnswer: answerIndex,\n      correct,\n      timeToAnswer,\n      timestamp: Date.now(),\n    })\n\n    // Update game data\n    this.gameData.patterns_identified++\n    \n    if (correct) {\n      this.gameData.correct_identifications++\n      this.gameData.streak_count++\n      this.gameData.max_streak = Math.max(this.gameData.max_streak, this.gameData.streak_count)\n      \n      // Speed bonus for quick correct answers (under 10 seconds)\n      if (timeToAnswer < 10000) {\n        const speedBonus = Math.max(0, 50 - Math.floor(timeToAnswer / 200))\n        this.gameData.speed_bonus += speedBonus\n      }\n      \n      // Add pattern to completed list if not already there\n      if (!this.gameData.patterns_completed.find(p => p.id === this.currentChallenge!.pattern.id)) {\n        this.gameData.patterns_completed.push(this.currentChallenge.pattern)\n      }\n    } else {\n      this.gameData.wrong_identifications++\n      this.gameData.streak_count = 0\n    }\n\n    // Update accuracy\n    this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100\n\n    // Generate next challenge if game is still active\n    if (this.isActive && this.state.time_remaining > 0) {\n      await this.generateNewChallenge()\n    }\n\n    return correct\n  }\n\n  private async generateNewChallenge(): Promise<void> {\n    // Select a random pattern based on difficulty and progress\n    const pattern = this.selectNextPattern()\n    \n    // Generate candlestick data with the pattern\n    const candleData = this.generateCandlestickDataWithPattern(pattern)\n    \n    // Find where the pattern occurs in the data\n    const patternLocation = this.findPatternInData(candleData, pattern)\n    \n    // Generate multiple choice options\n    const options = this.generatePatternOptions(pattern)\n    \n    this.currentChallenge = {\n      pattern,\n      candleData,\n      patternStartIndex: patternLocation.start,\n      patternEndIndex: patternLocation.end,\n      options,\n      correctAnswer: 0, // Correct answer is always first, then shuffled\n    }\n\n    // Shuffle options and update correct answer index\n    this.shuffleOptions()\n    \n    this.gameData.current_pattern = pattern\n    this.challengeStartTime = Date.now()\n  }\n\n  private selectNextPattern(): CandlePattern {\n    // Prioritize patterns not yet completed\n    const uncompletedPatterns = this.availablePatterns.filter(\n      p => !this.gameData.patterns_completed.find(completed => completed.id === p.id)\n    )\n    \n    const patternsToChooseFrom = uncompletedPatterns.length > 0 ? uncompletedPatterns : this.availablePatterns\n    \n    return patternsToChooseFrom[Math.floor(Math.random() * patternsToChooseFrom.length)]\n  }\n\n  private generateCandlestickDataWithPattern(pattern: CandlePattern): CandlestickData[] {\n    const totalCandles = 50\n    const patternPosition = Math.floor(Math.random() * (totalCandles - pattern.maxCandles - 10)) + 10\n    \n    const data: CandlestickData[] = []\n    let currentPrice = 100 + Math.random() * 50\n    \n    // Generate candles before pattern\n    for (let i = 0; i < patternPosition; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i)\n      data.push(candle)\n      currentPrice = candle.close\n    }\n    \n    // Generate pattern candles\n    const patternCandles = this.generatePatternCandles(pattern, currentPrice, patternPosition)\n    data.push(...patternCandles)\n    currentPrice = patternCandles[patternCandles.length - 1].close\n    \n    // Generate candles after pattern\n    for (let i = patternPosition + patternCandles.length; i < totalCandles; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i)\n      data.push(candle)\n      currentPrice = candle.close\n    }\n    \n    return data\n  }\n\n  private generateRandomCandle(basePrice: number, index: number): CandlestickData {\n    const volatility = 0.02\n    const change = (Math.random() - 0.5) * volatility * basePrice\n    const open = basePrice\n    const close = basePrice + change\n    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice\n    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice\n    \n    return {\n      timestamp: Date.now() - (50 - index) * 3600000, // Hourly intervals\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }\n  }\n\n  private generatePatternCandles(pattern: CandlePattern, startPrice: number, startIndex: number): CandlestickData[] {\n    // This is a simplified pattern generation - in a real implementation,\n    // you'd have specific algorithms for each pattern type\n    const candles: CandlestickData[] = []\n    let currentPrice = startPrice\n    \n    switch (pattern.id) {\n      case 'hammer':\n        return this.generateHammerPattern(startPrice, startIndex)\n      case 'doji':\n        return this.generateDojiPattern(startPrice, startIndex)\n      case 'engulfing_bullish':\n        return this.generateEngulfingPattern(startPrice, startIndex, true)\n      case 'engulfing_bearish':\n        return this.generateEngulfingPattern(startPrice, startIndex, false)\n      case 'morning_star':\n        return this.generateMorningStarPattern(startPrice, startIndex)\n      case 'evening_star':\n        return this.generateEveningStarPattern(startPrice, startIndex)\n      default:\n        return this.generateHammerPattern(startPrice, startIndex)\n    }\n  }\n\n  private generateHammerPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const open = startPrice\n    const close = startPrice + (Math.random() * 0.01 * startPrice) // Small body\n    const high = Math.max(open, close) + (Math.random() * 0.005 * startPrice) // Small upper shadow\n    const low = Math.min(open, close) - (0.02 + Math.random() * 0.01) * startPrice // Long lower shadow\n    \n    return [{\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }]\n  }\n\n  private generateDojiPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const open = startPrice\n    const close = startPrice + (Math.random() - 0.5) * 0.002 * startPrice // Very small body\n    const high = Math.max(open, close) + (0.01 + Math.random() * 0.01) * startPrice\n    const low = Math.min(open, close) - (0.01 + Math.random() * 0.01) * startPrice\n    \n    return [{\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }]\n  }\n\n  private generateEngulfingPattern(startPrice: number, startIndex: number, bullish: boolean): CandlestickData[] {\n    const candles: CandlestickData[] = []\n    \n    // First candle (small)\n    const firstOpen = startPrice\n    const firstClose = bullish \n      ? startPrice - 0.01 * startPrice \n      : startPrice + 0.01 * startPrice\n    \n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: firstOpen,\n      high: Math.max(firstOpen, firstClose) + 0.002 * startPrice,\n      low: Math.min(firstOpen, firstClose) - 0.002 * startPrice,\n      close: firstClose,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (engulfing)\n    const secondOpen = bullish \n      ? firstClose - 0.005 * startPrice \n      : firstClose + 0.005 * startPrice\n    const secondClose = bullish \n      ? firstOpen + 0.015 * startPrice \n      : firstOpen - 0.015 * startPrice\n    \n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondOpen,\n      high: Math.max(secondOpen, secondClose) + 0.002 * startPrice,\n      low: Math.min(secondOpen, secondClose) - 0.002 * startPrice,\n      close: secondClose,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private generateMorningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const candles: CandlestickData[] = []\n    \n    // First candle (bearish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: startPrice,\n      high: startPrice + 0.002 * startPrice,\n      low: startPrice - 0.015 * startPrice,\n      close: startPrice - 0.012 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (small body/doji)\n    const secondPrice = startPrice - 0.015 * startPrice\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondPrice,\n      high: secondPrice + 0.005 * startPrice,\n      low: secondPrice - 0.005 * startPrice,\n      close: secondPrice + 0.001 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Third candle (bullish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,\n      open: secondPrice + 0.002 * startPrice,\n      high: startPrice - 0.002 * startPrice,\n      low: secondPrice,\n      close: startPrice - 0.003 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private generateEveningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    // Similar to morning star but inverted\n    const candles: CandlestickData[] = []\n    \n    // First candle (bullish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: startPrice,\n      high: startPrice + 0.015 * startPrice,\n      low: startPrice - 0.002 * startPrice,\n      close: startPrice + 0.012 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (small body/doji)\n    const secondPrice = startPrice + 0.015 * startPrice\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondPrice,\n      high: secondPrice + 0.005 * startPrice,\n      low: secondPrice - 0.005 * startPrice,\n      close: secondPrice - 0.001 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Third candle (bearish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,\n      open: secondPrice - 0.002 * startPrice,\n      high: secondPrice,\n      low: startPrice + 0.002 * startPrice,\n      close: startPrice + 0.003 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private findPatternInData(data: CandlestickData[], pattern: CandlePattern): { start: number; end: number } {\n    // For simplicity, we know where we placed the pattern\n    // In a real implementation, you'd search for the pattern in the data\n    const totalCandles = data.length\n    const patternPosition = Math.floor(totalCandles * 0.4) // Roughly where we placed it\n    \n    return {\n      start: patternPosition,\n      end: patternPosition + pattern.maxCandles - 1,\n    }\n  }\n\n  private generatePatternOptions(correctPattern: CandlePattern): string[] {\n    const allPatterns = this.getAllPatterns()\n    const wrongPatterns = allPatterns\n      .filter(p => p.id !== correctPattern.id)\n      .sort(() => Math.random() - 0.5)\n      .slice(0, 3)\n    \n    return [correctPattern.name, ...wrongPatterns.map(p => p.name)]\n  }\n\n  private shuffleOptions(): void {\n    if (!this.currentChallenge) return\n    \n    const options = [...this.currentChallenge.options]\n    const correctAnswer = options[0]\n    \n    // Fisher-Yates shuffle\n    for (let i = options.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1))\n      ;[options[i], options[j]] = [options[j], options[i]]\n    }\n    \n    this.currentChallenge.options = options\n    this.currentChallenge.correctAnswer = options.indexOf(correctAnswer)\n  }\n\n  private updateGameMetrics(): void {\n    // Update accuracy percentage\n    if (this.gameData.patterns_identified > 0) {\n      this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100\n    }\n  }\n\n  private getPatternsByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): CandlePattern[] {\n    const allPatterns = this.getAllPatterns()\n    return allPatterns.filter(p => p.difficulty === difficulty || (difficulty === 'advanced' && p.difficulty !== 'advanced'))\n  }\n\n  private getAllPatterns(): CandlePattern[] {\n    return [\n      {\n        id: 'hammer',\n        name: 'Hammer',\n        description: 'Bullish reversal pattern with long lower shadow',\n        bullish: true,\n        difficulty: 'beginner',\n        minCandles: 1,\n        maxCandles: 1,\n      },\n      {\n        id: 'doji',\n        name: 'Doji',\n        description: 'Indecision pattern with very small body',\n        bullish: false,\n        difficulty: 'beginner',\n        minCandles: 1,\n        maxCandles: 1,\n      },\n      {\n        id: 'engulfing_bullish',\n        name: 'Bullish Engulfing',\n        description: 'Two-candle bullish reversal pattern',\n        bullish: true,\n        difficulty: 'intermediate',\n        minCandles: 2,\n        maxCandles: 2,\n      },\n      {\n        id: 'engulfing_bearish',\n        name: 'Bearish Engulfing',\n        description: 'Two-candle bearish reversal pattern',\n        bullish: false,\n        difficulty: 'intermediate',\n        minCandles: 2,\n        maxCandles: 2,\n      },\n      {\n        id: 'morning_star',\n        name: 'Morning Star',\n        description: 'Three-candle bullish reversal pattern',\n        bullish: true,\n        difficulty: 'advanced',\n        minCandles: 3,\n        maxCandles: 3,\n      },\n      {\n        id: 'evening_star',\n        name: 'Evening Star',\n        description: 'Three-candle bearish reversal pattern',\n        bullish: false,\n        difficulty: 'advanced',\n        minCandles: 3,\n        maxCandles: 3,\n      },\n    ]\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAiCO,MAAM,yBAAyB,4IAAA,CAAA,WAAQ;IACpC,SAA0B;IAC1B,mBAA4C,KAAI;IAChD,mBAMH,EAAE,CAAA;IACC,qBAA6B,EAAC;IAC9B,kBAAkC;IAE1C,YAAY,UAAoD,CAAE;QAChE,KAAK,CAAC,iBAAiB;QAEvB,IAAI,CAAC,QAAQ,GAAG;YACd,qBAAqB;YACrB,yBAAyB;YACzB,uBAAuB;YACvB,iBAAiB;YACjB,oBAAoB,EAAE;YACtB,qBAAqB;YACrB,aAAa;YACb,cAAc;YACd,YAAY;QACd;QAEA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG;YAAC,uHAAA,CAAA,gBAAa,CAAC,EAAE;SAAC,CAAC,kCAAkC;;IACrF;IAEA,MAAM,aAA4B;QAChC,2BAA2B;QAC3B,MAAM,IAAI,CAAC,oBAAoB;IACjC;IAEA,SAAe;QACb,sBAAsB;QACtB,IAAI,CAAC,iBAAiB;IACxB;IAEA,iBAAyB;QACvB,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG;QAC1D,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG;QAC1D,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,WAAW;QAC5C,MAAM,cAAc,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE/C,IAAI,aAAa,YAAY,gBAAgB,aAAa;QAE1D,wBAAwB;QACxB,cAAc,IAAI,CAAC,KAAK,CAAC,UAAU;QAEnC,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG;IAChC;IAEA,sBAAwC;QACtC,OAAO;YAAE,GAAG,IAAI,CAAC,QAAQ;QAAC;IAC5B;IAEA,sBAA+C;QAC7C,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA,MAAM,aAAa,WAAmB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAErD,MAAM,eAAe,KAAK,GAAG,KAAK,IAAI,CAAC,kBAAkB;QACzD,MAAM,UAAU,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAEnE,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACzB,WAAW,IAAI,CAAC,gBAAgB;YAChC,YAAY;YACZ;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QAEjC,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,uBAAuB;YACrC,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC1B,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;YAExF,2DAA2D;YAC3D,IAAI,eAAe,OAAO;gBACxB,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,eAAe;gBAC9D,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;YAC/B;YAEA,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAE,OAAO,CAAC,EAAE,GAAG;gBAC3F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO;YACrE;QACF,OAAO;YACL,IAAI,CAAC,QAAQ,CAAC,qBAAqB;YACnC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG;QAC/B;QAEA,kBAAkB;QAClB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,AAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAI;QAElH,kDAAkD;QAClD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG;YAClD,MAAM,IAAI,CAAC,oBAAoB;QACjC;QAEA,OAAO;IACT;IAEA,MAAc,uBAAsC;QAClD,2DAA2D;QAC3D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QAEtC,6CAA6C;QAC7C,MAAM,aAAa,IAAI,CAAC,kCAAkC,CAAC;QAE3D,4CAA4C;QAC5C,MAAM,kBAAkB,IAAI,CAAC,iBAAiB,CAAC,YAAY;QAE3D,mCAAmC;QACnC,MAAM,UAAU,IAAI,CAAC,sBAAsB,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG;YACtB;YACA;YACA,mBAAmB,gBAAgB,KAAK;YACxC,iBAAiB,gBAAgB,GAAG;YACpC;YACA,eAAe;QACjB;QAEA,kDAAkD;QAClD,IAAI,CAAC,cAAc;QAEnB,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG;QAChC,IAAI,CAAC,kBAAkB,GAAG,KAAK,GAAG;IACpC;IAEQ,oBAAmC;QACzC,wCAAwC;QACxC,MAAM,sBAAsB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CACvD,CAAA,IAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK,EAAE,EAAE;QAGhF,MAAM,uBAAuB,oBAAoB,MAAM,GAAG,IAAI,sBAAsB,IAAI,CAAC,iBAAiB;QAE1G,OAAO,oBAAoB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,qBAAqB,MAAM,EAAE;IACtF;IAEQ,mCAAmC,OAAsB,EAAqB;QACpF,MAAM,eAAe;QACrB,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,eAAe,QAAQ,UAAU,GAAG,EAAE,KAAK;QAE/F,MAAM,OAA0B,EAAE;QAClC,IAAI,eAAe,MAAM,KAAK,MAAM,KAAK;QAEzC,kCAAkC;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACvD,KAAK,IAAI,CAAC;YACV,eAAe,OAAO,KAAK;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,SAAS,cAAc;QAC1E,KAAK,IAAI,IAAI;QACb,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9D,iCAAiC;QACjC,IAAK,IAAI,IAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,cAAc,IAAK;YAC3E,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACvD,KAAK,IAAI,CAAC;YACV,eAAe,OAAO,KAAK;QAC7B;QAEA,OAAO;IACT;IAEQ,qBAAqB,SAAiB,EAAE,KAAa,EAAmB;QAC9E,MAAM,aAAa;QACnB,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;QACpD,MAAM,OAAO;QACb,MAAM,QAAQ,YAAY;QAC1B,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAC5D,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAE3D,OAAO;YACL,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI;YACvC;YACA;YACA;YACA;YACA,QAAQ,KAAK,MAAM,KAAK;QAC1B;IACF;IAEQ,uBAAuB,OAAsB,EAAE,UAAkB,EAAE,UAAkB,EAAqB;QAChH,sEAAsE;QACtE,uDAAuD;QACvD,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe;QAEnB,OAAQ,QAAQ,EAAE;YAChB,KAAK;gBACH,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY;YAChD,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY;YAC9C,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,YAAY;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,YAAY;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY;YACrD,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY;YACrD;gBACE,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY;QAClD;IACF;IAEQ,sBAAsB,UAAkB,EAAE,UAAkB,EAAqB;QACvF,MAAM,OAAO;QACb,MAAM,QAAQ,aAAc,KAAK,MAAM,KAAK,OAAO,WAAY,aAAa;;QAC5E,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAU,KAAK,MAAM,KAAK,QAAQ,WAAY,qBAAqB;;QAC/F,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,WAAW,oBAAoB;;QAEnG,OAAO;YAAC;gBACN,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;gBAC5C;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,MAAM,KAAK;YAC1B;SAAE;IACJ;IAEQ,oBAAoB,UAAkB,EAAE,UAAkB,EAAqB;QACrF,MAAM,OAAO;QACb,MAAM,QAAQ,aAAa,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,WAAW,kBAAkB;;QACxF,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;QACrE,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;QAEpE,OAAO;YAAC;gBACN,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;gBAC5C;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,MAAM,KAAK;YAC1B;SAAE;IACJ;IAEQ,yBAAyB,UAAkB,EAAE,UAAkB,EAAE,OAAgB,EAAqB;QAC5G,MAAM,UAA6B,EAAE;QAErC,uBAAuB;QACvB,MAAM,YAAY;QAClB,MAAM,aAAa,UACf,aAAa,OAAO,aACpB,aAAa,OAAO;QAExB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,KAAK,GAAG,CAAC,WAAW,cAAc,QAAQ;YAChD,KAAK,KAAK,GAAG,CAAC,WAAW,cAAc,QAAQ;YAC/C,OAAO;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,4BAA4B;QAC5B,MAAM,aAAa,UACf,aAAa,QAAQ,aACrB,aAAa,QAAQ;QACzB,MAAM,cAAc,UAChB,YAAY,QAAQ,aACpB,YAAY,QAAQ;QAExB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,KAAK,GAAG,CAAC,YAAY,eAAe,QAAQ;YAClD,KAAK,KAAK,GAAG,CAAC,YAAY,eAAe,QAAQ;YACjD,OAAO;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,2BAA2B,UAAkB,EAAE,UAAkB,EAAqB;QAC5F,MAAM,UAA6B,EAAE;QAErC,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,aAAa,QAAQ;YAC3B,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,kCAAkC;QAClC,MAAM,cAAc,aAAa,QAAQ;QACzC,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,cAAc,QAAQ;YAC5B,KAAK,cAAc,QAAQ;YAC3B,OAAO,cAAc,QAAQ;YAC7B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM,cAAc,QAAQ;YAC5B,MAAM,aAAa,QAAQ;YAC3B,KAAK;YACL,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,2BAA2B,UAAkB,EAAE,UAAkB,EAAqB;QAC5F,uCAAuC;QACvC,MAAM,UAA6B,EAAE;QAErC,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,aAAa,QAAQ;YAC3B,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,kCAAkC;QAClC,MAAM,cAAc,aAAa,QAAQ;QACzC,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,cAAc,QAAQ;YAC5B,KAAK,cAAc,QAAQ;YAC3B,OAAO,cAAc,QAAQ;YAC7B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM,cAAc,QAAQ;YAC5B,MAAM;YACN,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,kBAAkB,IAAuB,EAAE,OAAsB,EAAkC;QACzG,sDAAsD;QACtD,qEAAqE;QACrE,MAAM,eAAe,KAAK,MAAM;QAChC,MAAM,kBAAkB,KAAK,KAAK,CAAC,eAAe,KAAK,6BAA6B;;QAEpF,OAAO;YACL,OAAO;YACP,KAAK,kBAAkB,QAAQ,UAAU,GAAG;QAC9C;IACF;IAEQ,uBAAuB,cAA6B,EAAY;QACtE,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,gBAAgB,YACnB,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE,EACtC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK,KAC3B,KAAK,CAAC,GAAG;QAEZ,OAAO;YAAC,eAAe,IAAI;eAAK,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;SAAE;IACjE;IAEQ,iBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,MAAM,UAAU;eAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO;SAAC;QAClD,MAAM,gBAAgB,OAAO,CAAC,EAAE;QAEhC,uBAAuB;QACvB,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;YAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;YAC1C,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG;gBAAC,OAAO,CAAC,EAAE;gBAAE,OAAO,CAAC,EAAE;aAAC;QACtD;QAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;QAChC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,QAAQ,OAAO,CAAC;IACxD;IAEQ,oBAA0B;QAChC,6BAA6B;QAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,GAAG;YACzC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,AAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAI;QACpH;IACF;IAEQ,wBAAwB,UAAoD,EAAmB;QACrG,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,cAAe,eAAe,cAAc,EAAE,UAAU,KAAK;IAC/G;IAEQ,iBAAkC;QACxC,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/charts/candlestick-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { create<PERSON><PERSON>, IChartApi, ISeriesApi, CandlestickData as LWCandlestickData, ColorType } from 'lightweight-charts'\nimport { CandlestickData } from '@/types'\n\ninterface CandlestickChartProps {\n  data: CandlestickData[]\n  width?: number\n  height?: number\n  theme?: 'light' | 'dark'\n  patternHighlight?: {\n    startIndex: number\n    endIndex: number\n    color: string\n  }\n  onPatternClick?: (startIndex: number, endIndex: number) => void\n  showVolume?: boolean\n  title?: string\n  className?: string\n}\n\nexport default function CandlestickChart({\n  data,\n  width = 800,\n  height = 400,\n  theme = 'dark',\n  patternHighlight,\n  onPatternClick,\n  showVolume = true,\n  title,\n  className = '',\n}: CandlestickChartProps) {\n  const chartContainerRef = useRef<HTMLDivElement>(null)\n  const chartRef = useRef<IChartApi | null>(null)\n  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)\n  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    if (!chartContainerRef.current || data.length === 0) return\n\n    // Create chart\n    const chart = createChart(chartContainerRef.current, {\n      width,\n      height,\n      layout: {\n        background: { type: ColorType.Solid, color: theme === 'dark' ? '#1a1a1a' : '#ffffff' },\n        textColor: theme === 'dark' ? '#d1d5db' : '#374151',\n      },\n      grid: {\n        vertLines: { color: theme === 'dark' ? '#374151' : '#e5e7eb' },\n        horzLines: { color: theme === 'dark' ? '#374151' : '#e5e7eb' },\n      },\n      crosshair: {\n        mode: 1,\n      },\n      rightPriceScale: {\n        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',\n      },\n      timeScale: {\n        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',\n        timeVisible: true,\n        secondsVisible: false,\n      },\n    })\n\n    chartRef.current = chart\n\n    // Add candlestick series\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#10b981',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#10b981',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#10b981',\n    })\n\n    candlestickSeriesRef.current = candlestickSeries\n\n    // Add volume series if enabled\n    if (showVolume) {\n      const volumeSeries = chart.addHistogramSeries({\n        color: theme === 'dark' ? '#6b7280' : '#9ca3af',\n        priceFormat: {\n          type: 'volume',\n        },\n        priceScaleId: '',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0,\n        },\n      })\n      volumeSeriesRef.current = volumeSeries\n    }\n\n    // Convert data format\n    const chartData: LWCandlestickData[] = data.map(candle => ({\n      time: Math.floor(candle.timestamp / 1000) as any,\n      open: candle.open,\n      high: candle.high,\n      low: candle.low,\n      close: candle.close,\n    }))\n\n    const volumeData = data.map(candle => ({\n      time: Math.floor(candle.timestamp / 1000) as any,\n      value: candle.volume,\n      color: candle.close >= candle.open ? '#10b98150' : '#ef444450',\n    }))\n\n    // Set data\n    candlestickSeries.setData(chartData)\n    if (showVolume && volumeSeriesRef.current) {\n      volumeSeriesRef.current.setData(volumeData)\n    }\n\n    // Fit content\n    chart.timeScale().fitContent()\n\n    setIsLoading(false)\n\n    // Cleanup\n    return () => {\n      chart.remove()\n    }\n  }, [data, width, height, theme, showVolume])\n\n  // Handle pattern highlighting\n  useEffect(() => {\n    if (!chartRef.current || !candlestickSeriesRef.current || !patternHighlight) return\n\n    // Add pattern highlight markers\n    const markers = []\n    \n    // Start marker\n    markers.push({\n      time: Math.floor(data[patternHighlight.startIndex]?.timestamp / 1000) as any,\n      position: 'belowBar' as const,\n      color: patternHighlight.color,\n      shape: 'arrowUp' as const,\n      text: 'Pattern Start',\n    })\n\n    // End marker\n    markers.push({\n      time: Math.floor(data[patternHighlight.endIndex]?.timestamp / 1000) as any,\n      position: 'belowBar' as const,\n      color: patternHighlight.color,\n      shape: 'arrowUp' as const,\n      text: 'Pattern End',\n    })\n\n    candlestickSeriesRef.current.setMarkers(markers)\n  }, [patternHighlight, data])\n\n  // Handle click events\n  useEffect(() => {\n    if (!chartRef.current || !onPatternClick) return\n\n    const handleClick = (param: any) => {\n      if (param.time) {\n        const clickedIndex = data.findIndex(\n          candle => Math.floor(candle.timestamp / 1000) === param.time\n        )\n        if (clickedIndex !== -1) {\n          // For simplicity, assume pattern is 3 candles around clicked point\n          const startIndex = Math.max(0, clickedIndex - 1)\n          const endIndex = Math.min(data.length - 1, clickedIndex + 1)\n          onPatternClick(startIndex, endIndex)\n        }\n      }\n    }\n\n    chartRef.current.subscribeClick(handleClick)\n\n    return () => {\n      if (chartRef.current) {\n        chartRef.current.unsubscribeClick(handleClick)\n      }\n    }\n  }, [data, onPatternClick])\n\n  return (\n    <div className={`relative ${className}`}>\n      {title && (\n        <div className={`text-center mb-2 font-bold ${\n          theme === 'dark' ? 'text-white' : 'text-gray-900'\n        }`}>\n          {title}\n        </div>\n      )}\n      \n      {isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black/20 rounded\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400\"></div>\n        </div>\n      )}\n      \n      <div \n        ref={chartContainerRef} \n        className=\"rounded border\"\n        style={{ \n          width: `${width}px`, \n          height: `${height}px`,\n          borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'\n        }}\n      />\n      \n      {/* Chart controls */}\n      <div className={`mt-2 flex justify-between text-xs ${\n        theme === 'dark' ? 'text-gray-400' : 'text-gray-600'\n      }`}>\n        <span>📊 Candlestick Chart</span>\n        <span>{data.length} candles</span>\n      </div>\n    </div>\n  )\n}\n\n// Pattern annotation component\nexport function PatternAnnotation({ \n  pattern, \n  theme = 'dark' \n}: { \n  pattern: { name: string; description: string; bullish: boolean }\n  theme?: 'light' | 'dark'\n}) {\n  return (\n    <div className={`p-3 rounded-lg border ${\n      theme === 'dark' \n        ? 'bg-gray-800 border-gray-600 text-white' \n        : 'bg-white border-gray-300 text-gray-900'\n    }`}>\n      <div className=\"flex items-center gap-2 mb-1\">\n        <span className={`text-lg ${pattern.bullish ? 'text-green-400' : 'text-red-400'}`}>\n          {pattern.bullish ? '📈' : '📉'}\n        </span>\n        <span className=\"font-bold\">{pattern.name}</span>\n      </div>\n      <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>\n        {pattern.description}\n      </p>\n    </div>\n  )\n}\n\n// Chart loading skeleton\nexport function ChartSkeleton({ \n  width = 800, \n  height = 400, \n  theme = 'dark' \n}: { \n  width?: number\n  height?: number\n  theme?: 'light' | 'dark'\n}) {\n  return (\n    <div \n      className={`animate-pulse rounded border ${\n        theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-gray-200 border-gray-300'\n      }`}\n      style={{ width: `${width}px`, height: `${height}px` }}\n    >\n      <div className=\"flex items-center justify-center h-full\">\n        <div className={`text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-current mx-auto mb-2\"></div>\n          <p>Loading chart data...</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAsBe,SAAS,iBAAiB,EACvC,IAAI,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,QAAQ,MAAM,EACd,gBAAgB,EAChB,cAAc,EACd,aAAa,IAAI,EACjB,KAAK,EACL,YAAY,EAAE,EACQ;IACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoC;IACtE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkC;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;QAErD,eAAe;QACf,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,OAAO,EAAE;YACnD;YACA;YACA,QAAQ;gBACN,YAAY;oBAAE,MAAM,sLAAA,CAAA,YAAS,CAAC,KAAK;oBAAE,OAAO,UAAU,SAAS,YAAY;gBAAU;gBACrF,WAAW,UAAU,SAAS,YAAY;YAC5C;YACA,MAAM;gBACJ,WAAW;oBAAE,OAAO,UAAU,SAAS,YAAY;gBAAU;gBAC7D,WAAW;oBAAE,OAAO,UAAU,SAAS,YAAY;gBAAU;YAC/D;YACA,WAAW;gBACT,MAAM;YACR;YACA,iBAAiB;gBACf,aAAa,UAAU,SAAS,YAAY;YAC9C;YACA,WAAW;gBACT,aAAa,UAAU,SAAS,YAAY;gBAC5C,aAAa;gBACb,gBAAgB;YAClB;QACF;QAEA,SAAS,OAAO,GAAG;QAEnB,yBAAyB;QACzB,MAAM,oBAAoB,MAAM,oBAAoB,CAAC;YACnD,SAAS;YACT,WAAW;YACX,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,aAAa;QACf;QAEA,qBAAqB,OAAO,GAAG;QAE/B,+BAA+B;QAC/B,IAAI,YAAY;YACd,MAAM,eAAe,MAAM,kBAAkB,CAAC;gBAC5C,OAAO,UAAU,SAAS,YAAY;gBACtC,aAAa;oBACX,MAAM;gBACR;gBACA,cAAc;gBACd,cAAc;oBACZ,KAAK;oBACL,QAAQ;gBACV;YACF;YACA,gBAAgB,OAAO,GAAG;QAC5B;QAEA,sBAAsB;QACtB,MAAM,YAAiC,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACzD,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG;gBACpC,MAAM,OAAO,IAAI;gBACjB,MAAM,OAAO,IAAI;gBACjB,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;YACrB,CAAC;QAED,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACrC,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG;gBACpC,OAAO,OAAO,MAAM;gBACpB,OAAO,OAAO,KAAK,IAAI,OAAO,IAAI,GAAG,cAAc;YACrD,CAAC;QAED,WAAW;QACX,kBAAkB,OAAO,CAAC;QAC1B,IAAI,cAAc,gBAAgB,OAAO,EAAE;YACzC,gBAAgB,OAAO,CAAC,OAAO,CAAC;QAClC;QAEA,cAAc;QACd,MAAM,SAAS,GAAG,UAAU;QAE5B,aAAa;QAEb,UAAU;QACV,OAAO;YACL,MAAM,MAAM;QACd;IACF,GAAG;QAAC;QAAM;QAAO;QAAQ;QAAO;KAAW;IAE3C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,kBAAkB;QAE7E,gCAAgC;QAChC,MAAM,UAAU,EAAE;QAElB,eAAe;QACf,QAAQ,IAAI,CAAC;YACX,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,iBAAiB,UAAU,CAAC,EAAE,YAAY;YAChE,UAAU;YACV,OAAO,iBAAiB,KAAK;YAC7B,OAAO;YACP,MAAM;QACR;QAEA,aAAa;QACb,QAAQ,IAAI,CAAC;YACX,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,EAAE,YAAY;YAC9D,UAAU;YACV,OAAO,iBAAiB,KAAK;YAC7B,OAAO;YACP,MAAM;QACR;QAEA,qBAAqB,OAAO,CAAC,UAAU,CAAC;IAC1C,GAAG;QAAC;QAAkB;KAAK;IAE3B,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,gBAAgB;QAE1C,MAAM,cAAc,CAAC;YACnB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,eAAe,KAAK,SAAS,CACjC,CAAA,SAAU,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG,UAAU,MAAM,IAAI;gBAE9D,IAAI,iBAAiB,CAAC,GAAG;oBACvB,mEAAmE;oBACnE,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,eAAe;oBAC9C,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,GAAG,eAAe;oBAC1D,eAAe,YAAY;gBAC7B;YACF;QACF;QAEA,SAAS,OAAO,CAAC,cAAc,CAAC;QAEhC,OAAO;YACL,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,gBAAgB,CAAC;YACpC;QACF;IACF,GAAG;QAAC;QAAM;KAAe;IAEzB,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;YACpC,uBACC,8OAAC;gBAAI,WAAW,CAAC,2BAA2B,EAC1C,UAAU,SAAS,eAAe,iBAClC;0BACC;;;;;;YAIJ,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAInB,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,OAAO,GAAG,MAAM,EAAE,CAAC;oBACnB,QAAQ,GAAG,OAAO,EAAE,CAAC;oBACrB,aAAa,UAAU,SAAS,YAAY;gBAC9C;;;;;;0BAIF,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EACjD,UAAU,SAAS,kBAAkB,iBACrC;;kCACA,8OAAC;kCAAK;;;;;;kCACN,8OAAC;;4BAAM,KAAK,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAI3B;AAGO,SAAS,kBAAkB,EAChC,OAAO,EACP,QAAQ,MAAM,EAIf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EACrC,UAAU,SACN,2CACA,0CACJ;;0BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,OAAO,GAAG,mBAAmB,gBAAgB;kCAC9E,QAAQ,OAAO,GAAG,OAAO;;;;;;kCAE5B,8OAAC;wBAAK,WAAU;kCAAa,QAAQ,IAAI;;;;;;;;;;;;0BAE3C,8OAAC;gBAAE,WAAW,CAAC,QAAQ,EAAE,UAAU,SAAS,kBAAkB,iBAAiB;0BAC5E,QAAQ,WAAW;;;;;;;;;;;;AAI5B;AAGO,SAAS,cAAc,EAC5B,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,QAAQ,MAAM,EAKf;IACC,qBACE,8OAAC;QACC,WAAW,CAAC,6BAA6B,EACvC,UAAU,SAAS,gCAAgC,+BACnD;QACF,OAAO;YAAE,OAAO,GAAG,MAAM,EAAE,CAAC;YAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;QAAC;kBAEpD,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,YAAY,EAAE,UAAU,SAAS,kBAAkB,iBAAiB;;kCACnF,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 2694, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/games/candle-strike-game.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { CandleStrikeGame } from '@/lib/game-engine/games/candle-strike'\nimport CandlestickChart, { PatternAnnotation, ChartSkeleton } from '@/components/charts/candlestick-chart'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport { marketDataService } from '@/lib/services/market-data'\n\ninterface CandleStrikeGameProps {\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  onGameEnd: (score: number) => void\n  className?: string\n}\n\nexport default function CandleStrikeGameComponent({\n  difficulty,\n  onGameEnd,\n  className = '',\n}: CandleStrikeGameProps) {\n  const { interfaceMode } = useUserStore()\n  const [game, setGame] = useState<CandleStrikeGame | null>(null)\n  const [gameState, setGameState] = useState<any>(null)\n  const [currentChallenge, setCurrentChallenge] = useState<any>(null)\n  const [gameData, setGameData] = useState<any>(null)\n  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [lastAnswerCorrect, setLastAnswerCorrect] = useState<boolean | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  useEffect(() => {\n    initializeGame()\n  }, [difficulty])\n\n  const initializeGame = async () => {\n    setIsLoading(true)\n    const newGame = new CandleStrikeGame(difficulty)\n    setGame(newGame)\n    \n    await newGame.start()\n    \n    // Update game state every second\n    const interval = setInterval(() => {\n      if (newGame.isGameActive()) {\n        setGameState(newGame.getState())\n        setCurrentChallenge(newGame.getCurrentChallenge())\n        setGameData(newGame.getGameSpecificData())\n      } else {\n        clearInterval(interval)\n        const finalScore = newGame.calculateScore()\n        onGameEnd(finalScore)\n      }\n    }, 1000)\n\n    setIsLoading(false)\n  }\n\n  const handleAnswerSubmit = async (answerIndex: number) => {\n    if (!game || selectedAnswer !== null || showResult) return\n\n    setSelectedAnswer(answerIndex)\n    const correct = await game.submitAnswer(answerIndex)\n    setLastAnswerCorrect(correct)\n    setShowResult(true)\n\n    // Update game state\n    setGameState(game.getState())\n    setGameData(game.getGameSpecificData())\n\n    // Auto-advance to next challenge after 2 seconds\n    setTimeout(() => {\n      setSelectedAnswer(null)\n      setShowResult(false)\n      setLastAnswerCorrect(null)\n      setCurrentChallenge(game.getCurrentChallenge())\n    }, 2000)\n  }\n\n  const getPatternHighlight = () => {\n    if (!currentChallenge) return undefined\n\n    return {\n      startIndex: currentChallenge.patternStartIndex,\n      endIndex: currentChallenge.patternEndIndex,\n      color: isAdolescentMode ? '#fbbf24' : '#10b981',\n    }\n  }\n\n  const getAnswerButtonStyle = (index: number) => {\n    const baseStyle = `p-3 rounded-lg font-bold transition-all duration-300 ${\n      isAdolescentMode\n        ? 'text-white border-2'\n        : 'text-gray-900 border-2'\n    }`\n\n    if (showResult && selectedAnswer !== null) {\n      if (index === currentChallenge?.correctAnswer) {\n        // Correct answer\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50'\n            : 'bg-green-400 border-green-500 shadow-lg'\n        }`\n      } else if (index === selectedAnswer) {\n        // Wrong selected answer\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50'\n            : 'bg-red-400 border-red-500 shadow-lg'\n        }`\n      } else {\n        // Other options\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-gray-600 border-gray-500 opacity-50'\n            : 'bg-gray-300 border-gray-400 opacity-50'\n        }`\n      }\n    } else {\n      // Normal state\n      return `${baseStyle} ${\n        isAdolescentMode\n          ? 'bg-purple-500 hover:bg-purple-600 border-purple-400 hover:shadow-lg hover:shadow-purple-500/30'\n          : 'bg-purple-400 hover:bg-purple-300 border-purple-500 hover:shadow-lg'\n      }`\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className={`${className} space-y-6`}>\n        <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n          <h3 className=\"text-xl font-bold mb-2\">\n            {isAdolescentMode ? '🕯️ Loading CandleStrike...' : '📊 INITIALIZING_PATTERN_RECOGNITION'}\n          </h3>\n        </div>\n        <ChartSkeleton \n          width={800} \n          height={400} \n          theme={isAdolescentMode ? 'dark' : 'dark'} \n        />\n      </div>\n    )\n  }\n\n  if (!game || !gameState || !currentChallenge) {\n    return (\n      <div className={`${className} text-center`}>\n        <p className={isAdolescentMode ? 'text-white' : 'text-green-400'}>\n          {isAdolescentMode ? '🎮 Game not ready...' : 'SYSTEM_NOT_READY'}\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`${className} space-y-6`}>\n      {/* Game Header */}\n      <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n        <h3 className=\"text-xl font-bold mb-2\">\n          {isAdolescentMode ? '🕯️ CandleStrike Challenge' : '📊 PATTERN_RECOGNITION_MODULE'}\n        </h3>\n        <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n          {isAdolescentMode \n            ? 'Identify the candlestick pattern in the highlighted area!' \n            : 'IDENTIFY_CANDLESTICK_PATTERN_IN_HIGHLIGHTED_REGION'\n          }\n        </p>\n      </div>\n\n      {/* Game Stats */}\n      <div className={`grid grid-cols-4 gap-4 p-4 rounded-lg ${\n        isAdolescentMode ? 'bg-white/10' : 'bg-gray-800 border border-green-400'\n      }`}>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Score' : 'SCORE'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n            {gameState.score}\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Accuracy' : 'ACCURACY'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {gameData?.accuracy_percentage?.toFixed(1) || 0}%\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Streak' : 'STREAK'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-orange-300' : 'text-orange-400'}`}>\n            {gameData?.streak_count || 0}\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>\n            {gameState.time_remaining}s\n          </div>\n        </div>\n      </div>\n\n      {/* Candlestick Chart */}\n      <div className=\"flex justify-center\">\n        <CandlestickChart\n          data={currentChallenge.candleData}\n          width={800}\n          height={400}\n          theme=\"dark\"\n          patternHighlight={getPatternHighlight()}\n          title={isAdolescentMode ? '📈 Trading Chart' : '📊 MARKET_DATA_VISUALIZATION'}\n          className=\"border rounded-lg\"\n        />\n      </div>\n\n      {/* Pattern Information */}\n      {currentChallenge.pattern && (\n        <div className=\"flex justify-center\">\n          <PatternAnnotation \n            pattern={{\n              name: \"Pattern to Identify\",\n              description: isAdolescentMode \n                ? \"Look at the highlighted candles and identify the pattern!\"\n                : \"ANALYZE_HIGHLIGHTED_CANDLESTICKS_AND_IDENTIFY_PATTERN\",\n              bullish: true\n            }}\n            theme=\"dark\"\n          />\n        </div>\n      )}\n\n      {/* Answer Options */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        {currentChallenge.options.map((option: string, index: number) => (\n          <button\n            key={index}\n            onClick={() => handleAnswerSubmit(index)}\n            disabled={selectedAnswer !== null || showResult}\n            className={getAnswerButtonStyle(index)}\n          >\n            {option}\n          </button>\n        ))}\n      </div>\n\n      {/* Result Feedback */}\n      {showResult && lastAnswerCorrect !== null && (\n        <div className={`text-center p-4 rounded-lg ${\n          lastAnswerCorrect\n            ? (isAdolescentMode \n                ? 'bg-green-500/20 border border-green-400 text-green-100'\n                : 'bg-green-900/50 border border-green-400 text-green-300'\n              )\n            : (isAdolescentMode \n                ? 'bg-red-500/20 border border-red-400 text-red-100'\n                : 'bg-red-900/50 border border-red-400 text-red-300'\n              )\n        }`}>\n          <div className=\"text-2xl mb-2\">\n            {lastAnswerCorrect \n              ? (isAdolescentMode ? '🎉' : '✅') \n              : (isAdolescentMode ? '😅' : '❌')\n            }\n          </div>\n          <p className=\"font-bold\">\n            {lastAnswerCorrect \n              ? (isAdolescentMode ? 'Excellent! Correct pattern identified!' : 'CORRECT_PATTERN_IDENTIFICATION')\n              : (isAdolescentMode ? 'Not quite! The correct answer was highlighted.' : 'INCORRECT_PATTERN_IDENTIFICATION')\n            }\n          </p>\n          {!lastAnswerCorrect && currentChallenge.pattern && (\n            <p className=\"text-sm mt-2\">\n              {isAdolescentMode \n                ? `The correct pattern was: ${currentChallenge.options[currentChallenge.correctAnswer]}`\n                : `CORRECT_PATTERN: ${currentChallenge.options[currentChallenge.correctAnswer]}`\n              }\n            </p>\n          )}\n        </div>\n      )}\n\n      {/* Progress Indicator */}\n      <div className={`text-center text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n        {isAdolescentMode \n          ? `🎯 Patterns Identified: ${gameData?.patterns_identified || 0} | Correct: ${gameData?.correct_identifications || 0}`\n          : `PATTERNS_IDENTIFIED: ${gameData?.patterns_identified || 0} | CORRECT: ${gameData?.correct_identifications || 0}`\n        }\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAce,SAAS,0BAA0B,EAChD,UAAU,EACV,SAAS,EACT,YAAY,EAAE,EACQ;IACtB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,kBAAkB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB;QACrB,aAAa;QACb,MAAM,UAAU,IAAI,yJAAA,CAAA,mBAAgB,CAAC;QACrC,QAAQ;QAER,MAAM,QAAQ,KAAK;QAEnB,iCAAiC;QACjC,MAAM,WAAW,YAAY;YAC3B,IAAI,QAAQ,YAAY,IAAI;gBAC1B,aAAa,QAAQ,QAAQ;gBAC7B,oBAAoB,QAAQ,mBAAmB;gBAC/C,YAAY,QAAQ,mBAAmB;YACzC,OAAO;gBACL,cAAc;gBACd,MAAM,aAAa,QAAQ,cAAc;gBACzC,UAAU;YACZ;QACF,GAAG;QAEH,aAAa;IACf;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,mBAAmB,QAAQ,YAAY;QAEpD,kBAAkB;QAClB,MAAM,UAAU,MAAM,KAAK,YAAY,CAAC;QACxC,qBAAqB;QACrB,cAAc;QAEd,oBAAoB;QACpB,aAAa,KAAK,QAAQ;QAC1B,YAAY,KAAK,mBAAmB;QAEpC,iDAAiD;QACjD,WAAW;YACT,kBAAkB;YAClB,cAAc;YACd,qBAAqB;YACrB,oBAAoB,KAAK,mBAAmB;QAC9C,GAAG;IACL;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,kBAAkB,OAAO;QAE9B,OAAO;YACL,YAAY,iBAAiB,iBAAiB;YAC9C,UAAU,iBAAiB,eAAe;YAC1C,OAAO,mBAAmB,YAAY;QACxC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,YAAY,CAAC,qDAAqD,EACtE,mBACI,wBACA,0BACJ;QAEF,IAAI,cAAc,mBAAmB,MAAM;YACzC,IAAI,UAAU,kBAAkB,eAAe;gBAC7C,iBAAiB;gBACjB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,gEACA,2CACJ;YACJ,OAAO,IAAI,UAAU,gBAAgB;gBACnC,wBAAwB;gBACxB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,0DACA,uCACJ;YACJ,OAAO;gBACL,gBAAgB;gBAChB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,2CACA,0CACJ;YACJ;QACF,OAAO;YACL,eAAe;YACf,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,mGACA,uEACJ;QACJ;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;8BACtC,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,eAAe,kBAAkB;8BACjF,cAAA,8OAAC;wBAAG,WAAU;kCACX,mBAAmB,gCAAgC;;;;;;;;;;;8BAGxD,8OAAC,oJAAA,CAAA,gBAAa;oBACZ,OAAO;oBACP,QAAQ;oBACR,OAAO,mBAAmB,SAAS;;;;;;;;;;;;IAI3C;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB;QAC5C,qBACE,8OAAC;YAAI,WAAW,GAAG,UAAU,YAAY,CAAC;sBACxC,cAAA,8OAAC;gBAAE,WAAW,mBAAmB,eAAe;0BAC7C,mBAAmB,yBAAyB;;;;;;;;;;;IAIrD;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;0BAEtC,8OAAC;gBAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,eAAe,kBAAkB;;kCACjF,8OAAC;wBAAG,WAAU;kCACX,mBAAmB,+BAA+B;;;;;;kCAErD,8OAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kCAC7E,mBACG,8DACA;;;;;;;;;;;;0BAMR,8OAAC;gBAAI,WAAW,CAAC,sCAAsC,EACrD,mBAAmB,gBAAgB,uCACnC;;kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,UAAU;;;;;;0CAEhC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,kBAAkB;0CAC3F,UAAU,KAAK;;;;;;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,aAAa;;;;;;0CAEnC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;;oCACtF,UAAU,qBAAqB,QAAQ,MAAM;oCAAE;;;;;;;;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,WAAW;;;;;;0CAEjC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,mBAAmB;0CAC5F,UAAU,gBAAgB;;;;;;;;;;;;kCAG/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,cAAc;;;;;;0CAEpC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,iBAAiB,gBAAgB;;oCACtF,UAAU,cAAc;oCAAC;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oJAAA,CAAA,UAAgB;oBACf,MAAM,iBAAiB,UAAU;oBACjC,OAAO;oBACP,QAAQ;oBACR,OAAM;oBACN,kBAAkB;oBAClB,OAAO,mBAAmB,qBAAqB;oBAC/C,WAAU;;;;;;;;;;;YAKb,iBAAiB,OAAO,kBACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oJAAA,CAAA,oBAAiB;oBAChB,SAAS;wBACP,MAAM;wBACN,aAAa,mBACT,8DACA;wBACJ,SAAS;oBACX;oBACA,OAAM;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAC7C,8OAAC;wBAEC,SAAS,IAAM,mBAAmB;wBAClC,UAAU,mBAAmB,QAAQ;wBACrC,WAAW,qBAAqB;kCAE/B;uBALI;;;;;;;;;;YAWV,cAAc,sBAAsB,sBACnC,8OAAC;gBAAI,WAAW,CAAC,2BAA2B,EAC1C,oBACK,mBACG,2DACA,2DAEH,mBACG,qDACA,oDAER;;kCACA,8OAAC;wBAAI,WAAU;kCACZ,oBACI,mBAAmB,OAAO,MAC1B,mBAAmB,OAAO;;;;;;kCAGjC,8OAAC;wBAAE,WAAU;kCACV,oBACI,mBAAmB,2CAA2C,mCAC9D,mBAAmB,mDAAmD;;;;;;oBAG5E,CAAC,qBAAqB,iBAAiB,OAAO,kBAC7C,8OAAC;wBAAE,WAAU;kCACV,mBACG,CAAC,yBAAyB,EAAE,iBAAiB,OAAO,CAAC,iBAAiB,aAAa,CAAC,EAAE,GACtF,CAAC,iBAAiB,EAAE,iBAAiB,OAAO,CAAC,iBAAiB,aAAa,CAAC,EAAE;;;;;;;;;;;;0BAQ1F,8OAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,kBAAkB,kBAAkB;0BAC3F,mBACG,CAAC,wBAAwB,EAAE,UAAU,uBAAuB,EAAE,YAAY,EAAE,UAAU,2BAA2B,GAAG,GACpH,CAAC,qBAAqB,EAAE,UAAU,uBAAuB,EAAE,YAAY,EAAE,UAAU,2BAA2B,GAAG;;;;;;;;;;;;AAK7H", "debugId": null}}, {"offset": {"line": 3091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport { ScalperSprintGame } from '@/lib/game-engine/games/scalper-sprint'\nimport { CandleStrikeGame } from '@/lib/game-engine/games/candle-strike'\nimport { ChainMazeGame } from '@/lib/game-engine/games/chain-maze'\nimport CandleStrikeGameComponent from '@/components/games/candle-strike-game'\n\nexport default function Home() {\n  const { user, isAuthenticated, interfaceMode, switchInterfaceMode, signOut } = useUserStore()\n  const [currentGame, setCurrentGame] = useState<ScalperSprintGame | CandleStrikeGame | ChainMazeGame | null>(null)\n  const [gameState, setGameState] = useState<any>(null)\n  const [gameType, setGameType] = useState<'scalper' | 'candle' | 'chain' | null>(null)\n  const [showCandleStrikeComponent, setShowCandleStrikeComponent] = useState(false)\n\n  const startGame = async (type: 'scalper' | 'candle' | 'chain') => {\n    if (type === 'candle') {\n      setShowCandleStrikeComponent(true)\n      setGameType(type)\n      return\n    }\n\n    let game: ScalperSprintGame | ChainMazeGame\n\n    switch (type) {\n      case 'scalper':\n        game = new ScalperSprintGame('beginner')\n        break\n      case 'chain':\n        game = new ChainMazeGame('beginner')\n        break\n      default:\n        return\n    }\n\n    setCurrentGame(game)\n    setGameType(type)\n\n    await game.start()\n\n    // Update game state every second\n    const interval = setInterval(() => {\n      if (game.isGameActive()) {\n        setGameState(game.getState())\n      } else {\n        clearInterval(interval)\n        setCurrentGame(null)\n        setGameState(null)\n        setGameType(null)\n      }\n    }, 1000)\n  }\n\n  const handleCandleStrikeEnd = (score: number) => {\n    setShowCandleStrikeComponent(false)\n    setGameType(null)\n    // Could add score handling here\n    console.log('CandleStrike game ended with score:', score)\n  }\n\n  const executeTrade = async (symbol: string, side: 'buy' | 'sell') => {\n    if (currentGame && gameType === 'scalper') {\n      await (currentGame as ScalperSprintGame).executeTrade(symbol, side, 1)\n      setGameState(currentGame.getState())\n    }\n  }\n\n  const submitCandleAnswer = async (answerIndex: number) => {\n    if (currentGame && gameType === 'candle') {\n      const correct = await (currentGame as CandleStrikeGame).submitAnswer(answerIndex)\n      setGameState(currentGame.getState())\n      return correct\n    }\n    return false\n  }\n\n  const submitChainSolution = async (solution: any) => {\n    if (currentGame && gameType === 'chain') {\n      const correct = await (currentGame as ChainMazeGame).submitPuzzleSolution(solution)\n      setGameState(currentGame.getState())\n      return correct\n    }\n    return false\n  }\n\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  return (\n    <div className={`min-h-screen ${isAdolescentMode\n      ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500'\n      : 'bg-gray-900 text-green-400 font-mono'\n    }`}>\n      {/* Header */}\n      <header className={`p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`}>\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <h1 className={`text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'}\n          </h1>\n\n          <div className=\"flex items-center gap-4\">\n            {isAuthenticated && user && (\n              <>\n                <div className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                  {isAdolescentMode ? `👋 ${user.username}` : `USER: ${user.username.toUpperCase()}`}\n                  <div className={`text-xs ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n                    {isAdolescentMode ? `⭐ Level ${user.level}` : `LVL_${user.level}`} |\n                    {isAdolescentMode ? ` 🪙 ${user.total_quest_coins}` : ` COINS_${user.total_quest_coins}`}\n                  </div>\n                </div>\n\n                <button\n                  onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}\n                  className={`px-3 py-1 text-sm rounded transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-white/20 hover:bg-white/30 text-white'\n                      : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'\n                  }`}\n                >\n                  {isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'}\n                </button>\n\n                <button\n                  onClick={signOut}\n                  className={`px-3 py-1 text-sm rounded transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-red-500/20 hover:bg-red-500/30 text-red-200 border border-red-400'\n                      : 'bg-red-400/20 hover:bg-red-400/30 text-red-400 border border-red-400'\n                  }`}\n                >\n                  {isAdolescentMode ? '🚪 Logout' : 'LOGOUT'}\n                </button>\n              </>\n            )}\n\n            {!isAuthenticated && (\n              <div className=\"flex gap-2\">\n                <a\n                  href=\"/auth/login\"\n                  className={`px-4 py-2 rounded-lg transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-white/20 hover:bg-white/30 text-white'\n                      : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'\n                  }`}\n                >\n                  {isAdolescentMode ? '🔑 Login' : 'LOGIN'}\n                </a>\n                <a\n                  href=\"/auth/register\"\n                  className={`px-4 py-2 rounded-lg transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'\n                      : 'bg-green-400 text-gray-900 hover:bg-green-300'\n                  }`}\n                >\n                  {isAdolescentMode ? '🚀 Join Quest' : 'REGISTER'}\n                </a>\n              </div>\n            )}\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto p-6\">\n        {/* Welcome Section */}\n        <section className={`mb-8 p-6 rounded-lg ${\n          isAdolescentMode\n            ? 'bg-white/10 backdrop-blur-sm text-white'\n            : 'bg-gray-800 border border-green-400'\n        }`}>\n          <h2 className={`text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'}\n          </h2>\n          <p className={`text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n            {isAdolescentMode\n              ? 'Embark on epic trading adventures and master the markets through exciting mini-games!'\n              : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'\n            }\n          </p>\n        </section>\n\n        {/* Game Demo Section */}\n        <section className={`mb-8 p-6 rounded-lg ${\n          isAdolescentMode\n            ? 'bg-white/10 backdrop-blur-sm'\n            : 'bg-gray-800 border border-green-400'\n        }`}>\n          <h3 className={`text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {isAdolescentMode ? '🎮 Mini-Game Demos' : '📊 Trading Simulation Modules'}\n          </h3>\n\n          {!currentGame && !showCandleStrikeComponent ? (\n            <div className=\"space-y-6\">\n              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                {isAdolescentMode\n                  ? 'Choose your adventure! Each game teaches different trading skills.'\n                  : 'Select a trading simulation module to begin training.'\n                }\n              </p>\n\n              <div className=\"grid md:grid-cols-3 gap-4\">\n                {/* Scalper Sprint */}\n                <div className={`p-4 rounded-lg border ${\n                  isAdolescentMode\n                    ? 'bg-white/5 border-white/20'\n                    : 'bg-gray-700 border-green-400/50'\n                }`}>\n                  <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                    {isAdolescentMode ? '⚡ Scalper Sprint' : '📈 SCALPER_MODULE'}\n                  </h4>\n                  <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                    {isAdolescentMode\n                      ? '60-second rapid trading challenge'\n                      : 'High-frequency trading simulation'\n                    }\n                  </p>\n                  <button\n                    onClick={() => startGame('scalper')}\n                    className={`w-full py-2 px-3 rounded text-sm font-bold transition-colors ${\n                      isAdolescentMode\n                        ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'\n                        : 'bg-green-400 text-gray-900 hover:bg-green-300'\n                    }`}\n                  >\n                    {isAdolescentMode ? '🚀 Start' : 'INITIALIZE'}\n                  </button>\n                </div>\n\n                {/* CandleStrike */}\n                <div className={`p-4 rounded-lg border ${\n                  isAdolescentMode\n                    ? 'bg-white/5 border-white/20'\n                    : 'bg-gray-700 border-green-400/50'\n                }`}>\n                  <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                    {isAdolescentMode ? '🕯️ CandleStrike' : '📊 PATTERN_RECOGNITION'}\n                  </h4>\n                  <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                    {isAdolescentMode\n                      ? 'Identify candlestick patterns'\n                      : 'Technical analysis training'\n                    }\n                  </p>\n                  <div className=\"space-y-2\">\n                    <button\n                      onClick={() => startGame('candle')}\n                      className={`w-full py-2 px-3 rounded text-sm font-bold transition-colors ${\n                        isAdolescentMode\n                          ? 'bg-gradient-to-r from-purple-400 to-pink-500 text-white hover:from-purple-500 hover:to-pink-600'\n                          : 'bg-purple-400 text-gray-900 hover:bg-purple-300'\n                      }`}\n                    >\n                      {isAdolescentMode ? '🎯 Quick Start' : 'QUICK_START'}\n                    </button>\n                    <a\n                      href=\"/demo/candle-strike\"\n                      className={`block w-full py-2 px-3 rounded text-sm font-bold text-center transition-colors ${\n                        isAdolescentMode\n                          ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30'\n                          : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'\n                      }`}\n                    >\n                      {isAdolescentMode ? '📊 Full Demo' : 'FULL_DEMO'}\n                    </a>\n                  </div>\n                </div>\n\n                {/* ChainMaze */}\n                <div className={`p-4 rounded-lg border ${\n                  isAdolescentMode\n                    ? 'bg-white/5 border-white/20'\n                    : 'bg-gray-700 border-green-400/50'\n                }`}>\n                  <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                    {isAdolescentMode ? '🔗 ChainMaze' : '⛓️ BLOCKCHAIN_SIM'}\n                  </h4>\n                  <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                    {isAdolescentMode\n                      ? 'Navigate blockchain puzzles'\n                      : 'Consensus mechanism training'\n                    }\n                  </p>\n                  <button\n                    onClick={() => startGame('chain')}\n                    className={`w-full py-2 px-3 rounded text-sm font-bold transition-colors ${\n                      isAdolescentMode\n                        ? 'bg-gradient-to-r from-blue-400 to-cyan-500 text-white hover:from-blue-500 hover:to-cyan-600'\n                        : 'bg-blue-400 text-gray-900 hover:bg-blue-300'\n                    }`}\n                  >\n                    {isAdolescentMode ? '🧩 Start' : 'INITIALIZE'}\n                  </button>\n                </div>\n              </div>\n            </div>\n          ) : showCandleStrikeComponent ? (\n            <CandleStrikeGameComponent\n              difficulty=\"beginner\"\n              onGameEnd={handleCandleStrikeEnd}\n              className=\"w-full\"\n            />\n          ) : (\n            <div className=\"space-y-4\">\n              {/* Game State Display */}\n              {gameState && (\n                <div className={`p-4 rounded ${\n                  isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'\n                }`}>\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                    <div className=\"text-center\">\n                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                        {gameType === 'chain'\n                          ? (isAdolescentMode ? 'Gas Budget' : 'GAS_BUDGET')\n                          : (isAdolescentMode ? 'Balance' : 'BALANCE')\n                        }\n                      </div>\n                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n                        {gameType === 'chain' ? gameState.current_balance : `$${gameState.current_balance.toFixed(2)}`}\n                      </div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                        {isAdolescentMode ? 'Score' : 'SCORE'}\n                      </div>\n                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                        {gameState.score}\n                      </div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                        {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}\n                      </div>\n                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>\n                        {gameState.time_remaining}s\n                      </div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                        {gameType === 'scalper'\n                          ? (isAdolescentMode ? 'Positions' : 'POSITIONS')\n                          : gameType === 'candle'\n                            ? (isAdolescentMode ? 'Patterns' : 'PATTERNS')\n                            : (isAdolescentMode ? 'Puzzles' : 'PUZZLES')\n                        }\n                      </div>\n                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                        {gameType === 'scalper'\n                          ? gameState.positions?.length || 0\n                          : gameType === 'candle'\n                            ? (currentGame as CandleStrikeGame)?.getGameSpecificData()?.patterns_identified || 0\n                            : (currentGame as ChainMazeGame)?.getGameSpecificData()?.puzzles_solved || 0\n                        }\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Game-specific info */}\n                  {gameType === 'candle' && (\n                    <div className={`text-center mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                      <p className=\"text-sm\">\n                        {isAdolescentMode ? '🎯 Identify the candlestick pattern!' : 'IDENTIFY_PATTERN_OBJECTIVE'}\n                      </p>\n                      {(currentGame as CandleStrikeGame)?.getCurrentChallenge() && (\n                        <div className=\"mt-2\">\n                          <p className=\"font-bold\">\n                            {(currentGame as CandleStrikeGame).getCurrentChallenge()?.pattern.description}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  )}\n\n                  {gameType === 'chain' && (\n                    <div className={`text-center mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                      <p className=\"text-sm\">\n                        {isAdolescentMode ? '🧩 Solve blockchain puzzles!' : 'BLOCKCHAIN_PUZZLE_OBJECTIVE'}\n                      </p>\n                      {(currentGame as ChainMazeGame)?.getCurrentPuzzle() && (\n                        <div className=\"mt-2\">\n                          <p className=\"font-bold\">\n                            {(currentGame as ChainMazeGame).getCurrentPuzzle()?.title}\n                          </p>\n                          <p className=\"text-xs\">\n                            {(currentGame as ChainMazeGame).getCurrentPuzzle()?.description}\n                          </p>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n              )}\n\n              {/* Game Controls */}\n              {gameType === 'scalper' && (\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <button\n                    onClick={() => executeTrade('BTCUSD', 'buy')}\n                    className={`p-4 rounded-lg font-bold transition-colors ${\n                      isAdolescentMode\n                        ? 'bg-green-500 hover:bg-green-600 text-white'\n                        : 'bg-green-400 hover:bg-green-300 text-gray-900'\n                    }`}\n                  >\n                    {isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'}\n                  </button>\n                  <button\n                    onClick={() => executeTrade('BTCUSD', 'sell')}\n                    className={`p-4 rounded-lg font-bold transition-colors ${\n                      isAdolescentMode\n                        ? 'bg-red-500 hover:bg-red-600 text-white'\n                        : 'bg-red-400 hover:bg-red-300 text-gray-900'\n                    }`}\n                  >\n                    {isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'}\n                  </button>\n                </div>\n              )}\n\n              {gameType === 'candle' && (currentGame as CandleStrikeGame)?.getCurrentChallenge() && (\n                <div className=\"grid grid-cols-2 gap-2\">\n                  {(currentGame as CandleStrikeGame).getCurrentChallenge()?.options.map((option, index) => (\n                    <button\n                      key={index}\n                      onClick={() => submitCandleAnswer(index)}\n                      className={`p-3 rounded-lg font-bold transition-colors ${\n                        isAdolescentMode\n                          ? 'bg-purple-500 hover:bg-purple-600 text-white'\n                          : 'bg-purple-400 hover:bg-purple-300 text-gray-900'\n                      }`}\n                    >\n                      {option}\n                    </button>\n                  ))}\n                </div>\n              )}\n\n              {gameType === 'chain' && (\n                <div className=\"text-center\">\n                  <button\n                    onClick={() => submitChainSolution({ selectedNode: 'validator_1', gasPrice: 20 })}\n                    className={`px-6 py-3 rounded-lg font-bold transition-colors ${\n                      isAdolescentMode\n                        ? 'bg-blue-500 hover:bg-blue-600 text-white'\n                        : 'bg-blue-400 hover:bg-blue-300 text-gray-900'\n                    }`}\n                  >\n                    {isAdolescentMode ? '🧩 Submit Solution' : 'SUBMIT_SOLUTION'}\n                  </button>\n                </div>\n              )}\n            </div>\n          )}\n        </section>\n\n        {/* Features Preview */}\n        <section className=\"grid md:grid-cols-3 gap-6\">\n          {[\n            {\n              title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',\n              description: isAdolescentMode\n                ? 'Six exciting games to master different trading skills'\n                : 'Comprehensive trading simulation modules',\n              features: ['Scalper Sprint', 'CandleStrike', 'ChainMaze']\n            },\n            {\n              title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',\n              description: isAdolescentMode\n                ? 'Unlock badges and level up your trading hero'\n                : 'Advanced performance tracking and analytics',\n              features: ['Progress Tracking', 'Leaderboards', 'Statistics']\n            },\n            {\n              title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',\n              description: isAdolescentMode\n                ? 'Join guilds and compete with friends'\n                : 'Professional networking and strategy sharing',\n              features: ['Team Challenges', 'Social Features', 'Competitions']\n            }\n          ].map((feature, index) => (\n            <div\n              key={index}\n              className={`p-6 rounded-lg ${\n                isAdolescentMode\n                  ? 'bg-white/10 backdrop-blur-sm text-white'\n                  : 'bg-gray-800 border border-green-400'\n              }`}\n            >\n              <h3 className={`text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                {feature.title}\n              </h3>\n              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                {feature.description}\n              </p>\n              <ul className={`space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`}>\n                {feature.features.map((item, i) => (\n                  <li key={i} className=\"flex items-center\">\n                    <span className={`mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n                      {isAdolescentMode ? '✨' : '▶'}\n                    </span>\n                    {item}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </section>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AASe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,aAAa,EAAE,mBAAmB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAC1F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+D;IAC5G,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyC;IAChF,MAAM,CAAC,2BAA2B,6BAA6B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3E,MAAM,YAAY,OAAO;QACvB,IAAI,SAAS,UAAU;YACrB,6BAA6B;YAC7B,YAAY;YACZ;QACF;QAEA,IAAI;QAEJ,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,0JAAA,CAAA,oBAAiB,CAAC;gBAC7B;YACF,KAAK;gBACH,OAAO,IAAI,sJAAA,CAAA,gBAAa,CAAC;gBACzB;YACF;gBACE;QACJ;QAEA,eAAe;QACf,YAAY;QAEZ,MAAM,KAAK,KAAK;QAEhB,iCAAiC;QACjC,MAAM,WAAW,YAAY;YAC3B,IAAI,KAAK,YAAY,IAAI;gBACvB,aAAa,KAAK,QAAQ;YAC5B,OAAO;gBACL,cAAc;gBACd,eAAe;gBACf,aAAa;gBACb,YAAY;YACd;QACF,GAAG;IACL;IAEA,MAAM,wBAAwB,CAAC;QAC7B,6BAA6B;QAC7B,YAAY;QACZ,gCAAgC;QAChC,QAAQ,GAAG,CAAC,uCAAuC;IACrD;IAEA,MAAM,eAAe,OAAO,QAAgB;QAC1C,IAAI,eAAe,aAAa,WAAW;YACzC,MAAM,AAAC,YAAkC,YAAY,CAAC,QAAQ,MAAM;YACpE,aAAa,YAAY,QAAQ;QACnC;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,eAAe,aAAa,UAAU;YACxC,MAAM,UAAU,MAAM,AAAC,YAAiC,YAAY,CAAC;YACrE,aAAa,YAAY,QAAQ;YACjC,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,eAAe,aAAa,SAAS;YACvC,MAAM,UAAU,MAAM,AAAC,YAA8B,oBAAoB,CAAC;YAC1E,aAAa,YAAY,QAAQ;YACjC,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,mBAAmB,kBAAkB;IAE3C,qBACE,8OAAC;QAAI,WAAW,CAAC,aAAa,EAAE,mBAC5B,+DACA,wCACF;;0BAEA,8OAAC;gBAAO,WAAW,CAAC,IAAI,EAAE,mBAAmB,eAAe,6BAA6B;0BACvF,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,eAAe,kBAAkB;sCACtF,mBAAmB,kCAAkC;;;;;;sCAGxD,8OAAC;4BAAI,WAAU;;gCACZ,mBAAmB,sBAClB;;sDACE,8OAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;;gDAC/E,mBAAmB,CAAC,GAAG,EAAE,KAAK,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK,QAAQ,CAAC,WAAW,IAAI;8DAClF,8OAAC;oDAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,oBAAoB,kBAAkB;;wDACjF,mBAAmB,CAAC,QAAQ,EAAE,KAAK,KAAK,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,EAAE;wDAAC;wDACjE,mBAAmB,CAAC,IAAI,EAAE,KAAK,iBAAiB,EAAE,GAAG,CAAC,OAAO,EAAE,KAAK,iBAAiB,EAAE;;;;;;;;;;;;;sDAI5F,8OAAC;4CACC,SAAS,IAAM,oBAAoB,mBAAmB,UAAU;4CAChE,WAAW,CAAC,4CAA4C,EACtD,mBACI,6CACA,gFACJ;sDAED,mBAAmB,gBAAgB;;;;;;sDAGtC,8OAAC;4CACC,SAAS;4CACT,WAAW,CAAC,4CAA4C,EACtD,mBACI,yEACA,wEACJ;sDAED,mBAAmB,cAAc;;;;;;;;gCAKvC,CAAC,iCACA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAW,CAAC,uCAAuC,EACjD,mBACI,6CACA,gFACJ;sDAED,mBAAmB,aAAa;;;;;;sDAEnC,8OAAC;4CACC,MAAK;4CACL,WAAW,CAAC,uCAAuC,EACjD,mBACI,wGACA,iDACJ;sDAED,mBAAmB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAQ,WAAW,CAAC,oBAAoB,EACvC,mBACI,4CACA,uCACJ;;0CACA,8OAAC;gCAAG,WAAW,CAAC,wBAAwB,EAAE,mBAAmB,eAAe,kBAAkB;0CAC3F,mBAAmB,8BAA8B;;;;;;0CAEpD,8OAAC;gCAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC7E,mBACG,0FACA;;;;;;;;;;;;kCAMR,8OAAC;wBAAQ,WAAW,CAAC,oBAAoB,EACvC,mBACI,iCACA,uCACJ;;0CACA,8OAAC;gCAAG,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,eAAe,kBAAkB;0CAC1F,mBAAmB,uBAAuB;;;;;;4BAG5C,CAAC,eAAe,CAAC,0CAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAW,CAAC,KAAK,EAAE,mBAAmB,kBAAkB,kBAAkB;kDAC1E,mBACG,uEACA;;;;;;kDAIN,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,mBACI,+BACA,mCACJ;;kEACA,8OAAC;wDAAG,WAAW,CAAC,eAAe,EAAE,mBAAmB,eAAe,kBAAkB;kEAClF,mBAAmB,qBAAqB;;;;;;kEAE3C,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAClF,mBACG,sCACA;;;;;;kEAGN,8OAAC;wDACC,SAAS,IAAM,UAAU;wDACzB,WAAW,CAAC,6DAA6D,EACvE,mBACI,wGACA,iDACJ;kEAED,mBAAmB,aAAa;;;;;;;;;;;;0DAKrC,8OAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,mBACI,+BACA,mCACJ;;kEACA,8OAAC;wDAAG,WAAW,CAAC,eAAe,EAAE,mBAAmB,eAAe,kBAAkB;kEAClF,mBAAmB,qBAAqB;;;;;;kEAE3C,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAClF,mBACG,kCACA;;;;;;kEAGN,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,IAAM,UAAU;gEACzB,WAAW,CAAC,6DAA6D,EACvE,mBACI,oGACA,mDACJ;0EAED,mBAAmB,mBAAmB;;;;;;0EAEzC,8OAAC;gEACC,MAAK;gEACL,WAAW,CAAC,+EAA+E,EACzF,mBACI,oEACA,wEACJ;0EAED,mBAAmB,iBAAiB;;;;;;;;;;;;;;;;;;0DAM3C,8OAAC;gDAAI,WAAW,CAAC,sBAAsB,EACrC,mBACI,+BACA,mCACJ;;kEACA,8OAAC;wDAAG,WAAW,CAAC,eAAe,EAAE,mBAAmB,eAAe,kBAAkB;kEAClF,mBAAmB,iBAAiB;;;;;;kEAEvC,8OAAC;wDAAE,WAAW,CAAC,aAAa,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAClF,mBACG,gCACA;;;;;;kEAGN,8OAAC;wDACC,SAAS,IAAM,UAAU;wDACzB,WAAW,CAAC,6DAA6D,EACvE,mBACI,gGACA,+CACJ;kEAED,mBAAmB,aAAa;;;;;;;;;;;;;;;;;;;;;;;uCAKvC,0CACF,8OAAC,uJAAA,CAAA,UAAyB;gCACxB,YAAW;gCACX,WAAW;gCACX,WAAU;;;;;qDAGZ,8OAAC;gCAAI,WAAU;;oCAEZ,2BACC,8OAAC;wCAAI,WAAW,CAAC,YAAY,EAC3B,mBAAmB,gBAAgB,uCACnC;;0DACA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0EAC/E,aAAa,UACT,mBAAmB,eAAe,eAClC,mBAAmB,YAAY;;;;;;0EAGtC,8OAAC;gEAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,kBAAkB;0EAC3F,aAAa,UAAU,UAAU,eAAe,GAAG,CAAC,CAAC,EAAE,UAAU,eAAe,CAAC,OAAO,CAAC,IAAI;;;;;;;;;;;;kEAGlG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0EAC/E,mBAAmB,UAAU;;;;;;0EAEhC,8OAAC;gEAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;0EACtF,UAAU,KAAK;;;;;;;;;;;;kEAGpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0EAC/E,mBAAmB,cAAc;;;;;;0EAEpC,8OAAC;gEAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,iBAAiB,gBAAgB;;oEACtF,UAAU,cAAc;oEAAC;;;;;;;;;;;;;kEAG9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0EAC/E,aAAa,YACT,mBAAmB,cAAc,cAClC,aAAa,WACV,mBAAmB,aAAa,aAChC,mBAAmB,YAAY;;;;;;0EAGxC,8OAAC;gEAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;0EACtF,aAAa,YACV,UAAU,SAAS,EAAE,UAAU,IAC/B,aAAa,WACX,AAAC,aAAkC,uBAAuB,uBAAuB,IACjF,AAAC,aAA+B,uBAAuB,kBAAkB;;;;;;;;;;;;;;;;;;4CAOpF,aAAa,0BACZ,8OAAC;gDAAI,WAAW,CAAC,iBAAiB,EAAE,mBAAmB,kBAAkB,kBAAkB;;kEACzF,8OAAC;wDAAE,WAAU;kEACV,mBAAmB,yCAAyC;;;;;;oDAE7D,aAAkC,uCAClC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAE,WAAU;sEACV,AAAC,YAAiC,mBAAmB,IAAI,QAAQ;;;;;;;;;;;;;;;;;4CAO3E,aAAa,yBACZ,8OAAC;gDAAI,WAAW,CAAC,iBAAiB,EAAE,mBAAmB,kBAAkB,kBAAkB;;kEACzF,8OAAC;wDAAE,WAAU;kEACV,mBAAmB,iCAAiC;;;;;;oDAErD,aAA+B,oCAC/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,AAAC,YAA8B,gBAAgB,IAAI;;;;;;0EAEtD,8OAAC;gEAAE,WAAU;0EACV,AAAC,YAA8B,gBAAgB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;oCAUjE,aAAa,2BACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,aAAa,UAAU;gDACtC,WAAW,CAAC,2CAA2C,EACrD,mBACI,+CACA,iDACJ;0DAED,mBAAmB,mBAAmB;;;;;;0DAEzC,8OAAC;gDACC,SAAS,IAAM,aAAa,UAAU;gDACtC,WAAW,CAAC,2CAA2C,EACrD,mBACI,2CACA,6CACJ;0DAED,mBAAmB,oBAAoB;;;;;;;;;;;;oCAK7C,aAAa,YAAa,aAAkC,uCAC3D,8OAAC;wCAAI,WAAU;kDACZ,AAAC,YAAiC,mBAAmB,IAAI,QAAQ,IAAI,CAAC,QAAQ,sBAC7E,8OAAC;gDAEC,SAAS,IAAM,mBAAmB;gDAClC,WAAW,CAAC,2CAA2C,EACrD,mBACI,iDACA,mDACJ;0DAED;+CARI;;;;;;;;;;oCAcZ,aAAa,yBACZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,oBAAoB;oDAAE,cAAc;oDAAe,UAAU;gDAAG;4CAC/E,WAAW,CAAC,iDAAiD,EAC3D,mBACI,6CACA,+CACJ;sDAED,mBAAmB,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;kCASvD,8OAAC;wBAAQ,WAAU;kCAChB;4BACC;gCACE,OAAO,mBAAmB,kBAAkB;gCAC5C,aAAa,mBACT,0DACA;gCACJ,UAAU;oCAAC;oCAAkB;oCAAgB;iCAAY;4BAC3D;4BACA;gCACE,OAAO,mBAAmB,oBAAoB;gCAC9C,aAAa,mBACT,iDACA;gCACJ,UAAU;oCAAC;oCAAqB;oCAAgB;iCAAa;4BAC/D;4BACA;gCACE,OAAO,mBAAmB,cAAc;gCACxC,aAAa,mBACT,yCACA;gCACJ,UAAU;oCAAC;oCAAmB;oCAAmB;iCAAe;4BAClE;yBACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;gCAEC,WAAW,CAAC,eAAe,EACzB,mBACI,4CACA,uCACJ;;kDAEF,8OAAC;wCAAG,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,eAAe,kBAAkB;kDAC1F,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;wCAAE,WAAW,CAAC,KAAK,EAAE,mBAAmB,kBAAkB,kBAAkB;kDAC1E,QAAQ,WAAW;;;;;;kDAEtB,8OAAC;wCAAG,WAAW,CAAC,UAAU,EAAE,mBAAmB,kBAAkB,kBAAkB;kDAChF,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,kBAC3B,8OAAC;gDAAW,WAAU;;kEACpB,8OAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,mBAAmB,oBAAoB,kBAAkB;kEAC/E,mBAAmB,MAAM;;;;;;oDAE3B;;+CAJM;;;;;;;;;;;+BAfR;;;;;;;;;;;;;;;;;;;;;;AA6BnB", "debugId": null}}]}