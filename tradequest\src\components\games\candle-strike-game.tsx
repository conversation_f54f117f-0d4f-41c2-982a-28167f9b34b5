'use client'

import { useState, useEffect } from 'react'
import { CandleStrikeGame } from '@/lib/game-engine/games/candle-strike'
// import CandlestickChart, { PatternAnnotation, ChartSkeleton } from '@/components/charts/candlestick-chart'
// import PlaybackChart from '@/components/charts/playback-chart'
import { useUserStore } from '@/lib/stores/user-store'
import { marketDataService } from '@/lib/services/market-data'

interface CandleStrikeGameProps {
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  onGameEnd: (score: number) => void
  usePlayback?: boolean
  className?: string
}

export default function CandleStrikeGameComponent({
  difficulty,
  onGameEnd,
  usePlayback = false,
  className = '',
}: CandleStrikeGameProps) {
  const { interfaceMode } = useUserStore()
  const [game, setGame] = useState<CandleStrikeGame | null>(null)
  const [gameState, setGameState] = useState<any>(null)
  const [currentChallenge, setCurrentChallenge] = useState<any>(null)
  const [gameData, setGameData] = useState<any>(null)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)
  const [showResult, setShowResult] = useState(false)
  const [lastAnswerCorrect, setLastAnswerCorrect] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAdolescentMode = interfaceMode === 'adolescent'

  useEffect(() => {
    initializeGame()
  }, [difficulty])

  const initializeGame = async () => {
    setIsLoading(true)
    const newGame = new CandleStrikeGame(difficulty)
    setGame(newGame)
    
    await newGame.start()
    
    // Update game state every second
    const interval = setInterval(() => {
      if (newGame.isGameActive()) {
        setGameState(newGame.getState())
        setCurrentChallenge(newGame.getCurrentChallenge())
        setGameData(newGame.getGameSpecificData())
      } else {
        clearInterval(interval)
        const finalScore = newGame.calculateScore()
        onGameEnd(finalScore)
      }
    }, 1000)

    setIsLoading(false)
  }

  const handleAnswerSubmit = async (answerIndex: number) => {
    if (!game || selectedAnswer !== null || showResult) return

    setSelectedAnswer(answerIndex)
    const correct = await game.submitAnswer(answerIndex)
    setLastAnswerCorrect(correct)
    setShowResult(true)

    // Update game state
    setGameState(game.getState())
    setGameData(game.getGameSpecificData())

    // Auto-advance to next challenge after 2 seconds
    setTimeout(() => {
      setSelectedAnswer(null)
      setShowResult(false)
      setLastAnswerCorrect(null)
      setCurrentChallenge(game.getCurrentChallenge())
    }, 2000)
  }

  const getPatternHighlight = () => {
    if (!currentChallenge) return undefined

    return {
      startIndex: currentChallenge.patternStartIndex,
      endIndex: currentChallenge.patternEndIndex,
      color: isAdolescentMode ? '#fbbf24' : '#10b981',
    }
  }

  const getAnswerButtonStyle = (index: number) => {
    const baseStyle = `p-3 rounded-lg font-bold transition-all duration-300 ${
      isAdolescentMode
        ? 'text-white border-2'
        : 'text-gray-900 border-2'
    }`

    if (showResult && selectedAnswer !== null) {
      if (index === currentChallenge?.correctAnswer) {
        // Correct answer
        return `${baseStyle} ${
          isAdolescentMode
            ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50'
            : 'bg-green-400 border-green-500 shadow-lg'
        }`
      } else if (index === selectedAnswer) {
        // Wrong selected answer
        return `${baseStyle} ${
          isAdolescentMode
            ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50'
            : 'bg-red-400 border-red-500 shadow-lg'
        }`
      } else {
        // Other options
        return `${baseStyle} ${
          isAdolescentMode
            ? 'bg-gray-600 border-gray-500 opacity-50'
            : 'bg-gray-300 border-gray-400 opacity-50'
        }`
      }
    } else {
      // Normal state
      return `${baseStyle} ${
        isAdolescentMode
          ? 'bg-purple-500 hover:bg-purple-600 border-purple-400 hover:shadow-lg hover:shadow-purple-500/30'
          : 'bg-purple-400 hover:bg-purple-300 border-purple-500 hover:shadow-lg'
      }`
    }
  }

  if (isLoading) {
    return (
      <div className={`${className} space-y-6`}>
        <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
          <h3 className="text-xl font-bold mb-2">
            {isAdolescentMode ? '🕯️ Loading CandleStrike...' : '📊 INITIALIZING_PATTERN_RECOGNITION'}
          </h3>
        </div>
        <ChartSkeleton 
          width={800} 
          height={400} 
          theme={isAdolescentMode ? 'dark' : 'dark'} 
        />
      </div>
    )
  }

  if (!game || !gameState || !currentChallenge) {
    return (
      <div className={`${className} text-center`}>
        <p className={isAdolescentMode ? 'text-white' : 'text-green-400'}>
          {isAdolescentMode ? '🎮 Game not ready...' : 'SYSTEM_NOT_READY'}
        </p>
      </div>
    )
  }

  return (
    <div className={`${className} space-y-6`}>
      {/* Game Header */}
      <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
        <h3 className="text-xl font-bold mb-2">
          {isAdolescentMode ? '🕯️ CandleStrike Challenge' : '📊 PATTERN_RECOGNITION_MODULE'}
        </h3>
        <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
          {isAdolescentMode 
            ? 'Identify the candlestick pattern in the highlighted area!' 
            : 'IDENTIFY_CANDLESTICK_PATTERN_IN_HIGHLIGHTED_REGION'
          }
        </p>
      </div>

      {/* Game Stats */}
      <div className={`grid grid-cols-4 gap-4 p-4 rounded-lg ${
        isAdolescentMode ? 'bg-white/10' : 'bg-gray-800 border border-green-400'
      }`}>
        <div className="text-center">
          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
            {isAdolescentMode ? 'Score' : 'SCORE'}
          </div>
          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>
            {gameState.score}
          </div>
        </div>
        <div className="text-center">
          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
            {isAdolescentMode ? 'Accuracy' : 'ACCURACY'}
          </div>
          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {gameData?.accuracy_percentage?.toFixed(1) || 0}%
          </div>
        </div>
        <div className="text-center">
          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
            {isAdolescentMode ? 'Streak' : 'STREAK'}
          </div>
          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-orange-300' : 'text-orange-400'}`}>
            {gameData?.streak_count || 0}
          </div>
        </div>
        <div className="text-center">
          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
            {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}
          </div>
          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>
            {gameState.time_remaining}s
          </div>
        </div>
      </div>

      {/* Chart Display - Temporarily Disabled */}
      <div className="flex justify-center">
        <div className="w-full max-w-4xl h-96 border rounded-lg flex items-center justify-center bg-gray-800">
          <div className="text-center">
            <div className="text-4xl mb-4">📈</div>
            <p className="text-white">
              {isAdolescentMode ? 'Chart Loading...' : 'CHART_SYSTEM_INITIALIZING...'}
            </p>
            <p className="text-gray-400 text-sm mt-2">
              {usePlayback ? 'Playback Mode' : 'Pattern Recognition Mode'}
            </p>
          </div>
        </div>
      </div>

      {/* Pattern Information */}
      {currentChallenge.pattern && (
        <div className="flex justify-center">
          <PatternAnnotation 
            pattern={{
              name: "Pattern to Identify",
              description: isAdolescentMode 
                ? "Look at the highlighted candles and identify the pattern!"
                : "ANALYZE_HIGHLIGHTED_CANDLESTICKS_AND_IDENTIFY_PATTERN",
              bullish: true
            }}
            theme="dark"
          />
        </div>
      )}

      {/* Answer Options */}
      <div className="grid grid-cols-2 gap-4">
        {currentChallenge.options.map((option: string, index: number) => (
          <button
            key={index}
            onClick={() => handleAnswerSubmit(index)}
            disabled={selectedAnswer !== null || showResult}
            className={getAnswerButtonStyle(index)}
          >
            {option}
          </button>
        ))}
      </div>

      {/* Result Feedback */}
      {showResult && lastAnswerCorrect !== null && (
        <div className={`text-center p-4 rounded-lg ${
          lastAnswerCorrect
            ? (isAdolescentMode 
                ? 'bg-green-500/20 border border-green-400 text-green-100'
                : 'bg-green-900/50 border border-green-400 text-green-300'
              )
            : (isAdolescentMode 
                ? 'bg-red-500/20 border border-red-400 text-red-100'
                : 'bg-red-900/50 border border-red-400 text-red-300'
              )
        }`}>
          <div className="text-2xl mb-2">
            {lastAnswerCorrect 
              ? (isAdolescentMode ? '🎉' : '✅') 
              : (isAdolescentMode ? '😅' : '❌')
            }
          </div>
          <p className="font-bold">
            {lastAnswerCorrect 
              ? (isAdolescentMode ? 'Excellent! Correct pattern identified!' : 'CORRECT_PATTERN_IDENTIFICATION')
              : (isAdolescentMode ? 'Not quite! The correct answer was highlighted.' : 'INCORRECT_PATTERN_IDENTIFICATION')
            }
          </p>
          {!lastAnswerCorrect && currentChallenge.pattern && (
            <p className="text-sm mt-2">
              {isAdolescentMode 
                ? `The correct pattern was: ${currentChallenge.options[currentChallenge.correctAnswer]}`
                : `CORRECT_PATTERN: ${currentChallenge.options[currentChallenge.correctAnswer]}`
              }
            </p>
          )}
        </div>
      )}

      {/* Progress Indicator */}
      <div className={`text-center text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
        {isAdolescentMode 
          ? `🎯 Patterns Identified: ${gameData?.patterns_identified || 0} | Correct: ${gameData?.correct_identifications || 0}`
          : `PATTERNS_IDENTIFIED: ${gameData?.patterns_identified || 0} | CORRECT: ${gameData?.correct_identifications || 0}`
        }
      </div>
    </div>
  )
}
