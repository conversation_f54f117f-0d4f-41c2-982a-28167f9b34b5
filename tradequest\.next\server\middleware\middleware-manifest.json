{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kKv0fp95WkJi7j6F/QdtZiLhope0cAZ3c8taB8S9siE=", "__NEXT_PREVIEW_MODE_ID": "66983dbe5d5ecf468cc52bc22e09ecc4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "69fd52c59cd4260534f6ee80a9e2035ee7e883af10e942d71b751206207bbf93", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1227be8cfaa06dc62a8b64e9420eee9bc1a1ce99d261e54d5a178ee4ae933dfe"}}}, "instrumentation": null, "functions": {}}