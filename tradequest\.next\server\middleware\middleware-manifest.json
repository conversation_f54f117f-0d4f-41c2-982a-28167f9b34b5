{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "kKv0fp95WkJi7j6F/QdtZiLhope0cAZ3c8taB8S9siE=", "__NEXT_PREVIEW_MODE_ID": "6a8cbed8b109ea58e08f96b71a6f6efc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8bd0ae236abfe58dc0332568dc35fc56960268291fbd37d378ae129ad83b22b7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4205e503c13fe6b564dc98b01a8f0a447d360e62c7eca7dc1fdd4e5b974c31a6"}}}, "instrumentation": null, "functions": {}}