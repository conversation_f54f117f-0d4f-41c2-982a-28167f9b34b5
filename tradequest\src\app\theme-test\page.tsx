'use client'

import { useState } from 'react'
import { useThemeStore } from '@/lib/stores/theme-store'
import { useUserStore } from '@/lib/stores/user-store'
import { enhancedThemes } from '@/lib/themes/enhanced-color-psychology'
import { useThemeColors, MarketConditionBadge, TradingButton } from '@/components/theme/theme-provider'

export default function ThemeTestPage() {
  const { currentThemeId, setTheme, interfaceMode } = useThemeStore()
  const { switchInterfaceMode } = useUserStore()
  const colors = useThemeColors()
  const [selectedTheme, setSelectedTheme] = useState(currentThemeId)
  
  const isAdolescentMode = interfaceMode === 'adolescent'

  const handleThemeChange = (themeId: string) => {
    setSelectedTheme(themeId)
    setTheme(themeId)
  }

  const currentTheme = enhancedThemes.find(t => t.id === currentThemeId)

  return (
    <div 
      className="min-h-screen p-6"
      style={{ backgroundColor: colors.background }}
    >
      {/* Header */}
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4" style={{ color: colors.textPrimary }}>
            {isAdolescentMode ? '🎨 Theme Testing Lab' : '🎨 THEME_VALIDATION_SYSTEM'}
          </h1>
          <p className="text-lg mb-4" style={{ color: colors.textSecondary }}>
            {isAdolescentMode 
              ? 'Test all 5 color psychology themes based on color theory principles!'
              : 'Comprehensive validation of 5 evidence-based color psychology themes.'
            }
          </p>
          
          <div className="flex gap-4 mb-6">
            <button
              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}
              className="px-4 py-2 rounded-lg font-bold transition-colors"
              style={{ 
                backgroundColor: colors.primary,
                color: colors.background
              }}
            >
              {isAdolescentMode ? '🔄 Switch to Pro Mode' : '🔄 SWITCH_TO_ADOLESCENT'}
            </button>
          </div>
        </div>

        {/* Theme Selector Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {enhancedThemes.map((theme) => (
            <div
              key={theme.id}
              className={`p-6 rounded-lg border-2 cursor-pointer transition-all ${
                selectedTheme === theme.id
                  ? 'shadow-lg transform scale-105'
                  : 'hover:shadow-md'
              }`}
              style={{
                backgroundColor: colors.backgroundSecondary,
                borderColor: selectedTheme === theme.id ? colors.primary : colors.border,
              }}
              onClick={() => handleThemeChange(theme.id)}
            >
              <h3 className="text-lg font-bold mb-2" style={{ color: colors.textPrimary }}>
                {theme.name}
              </h3>
              <p className="text-sm mb-3" style={{ color: colors.textSecondary }}>
                {theme.description}
              </p>
              <p className="text-xs mb-4" style={{ color: colors.textMuted }}>
                🎨 {theme.colorTheory}
              </p>
              
              {/* Color Preview */}
              <div className="flex space-x-2 mb-4">
                <div 
                  className="w-6 h-6 rounded-full border"
                  style={{ 
                    backgroundColor: theme[interfaceMode].primary,
                    borderColor: colors.border
                  }}
                />
                <div 
                  className="w-6 h-6 rounded-full border"
                  style={{ 
                    backgroundColor: theme[interfaceMode].secondary,
                    borderColor: colors.border
                  }}
                />
                <div 
                  className="w-6 h-6 rounded-full border"
                  style={{ 
                    backgroundColor: theme[interfaceMode].bullish,
                    borderColor: colors.border
                  }}
                />
                <div 
                  className="w-6 h-6 rounded-full border"
                  style={{ 
                    backgroundColor: theme[interfaceMode].bearish,
                    borderColor: colors.border
                  }}
                />
              </div>

              {/* Psychology Scores */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs" style={{ color: colors.textMuted }}>
                    {isAdolescentMode ? '😌 Stress Relief' : 'STRESS_REDUCTION'}
                  </span>
                  <div className="flex space-x-1">
                    {Array.from({ length: 10 }, (_, i) => (
                      <div
                        key={i}
                        className={`w-1 h-3 ${
                          i < theme.psychologyProfile.stressReduction
                            ? 'bg-green-400'
                            : 'bg-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs" style={{ color: colors.textMuted }}>
                    {isAdolescentMode ? '🎯 Focus' : 'FOCUS_ENHANCEMENT'}
                  </span>
                  <div className="flex space-x-1">
                    {Array.from({ length: 10 }, (_, i) => (
                      <div
                        key={i}
                        className={`w-1 h-3 ${
                          i < theme.psychologyProfile.focusEnhancement
                            ? 'bg-blue-400'
                            : 'bg-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs" style={{ color: colors.textMuted }}>
                    {isAdolescentMode ? '♿ Accessibility' : 'ACCESSIBILITY'}
                  </span>
                  <div className="flex space-x-1">
                    {Array.from({ length: 10 }, (_, i) => (
                      <div
                        key={i}
                        className={`w-1 h-3 ${
                          i < theme.psychologyProfile.accessibility
                            ? 'bg-purple-400'
                            : 'bg-gray-600'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Current Theme Demo */}
        {currentTheme && (
          <div 
            className="p-8 rounded-lg mb-8"
            style={{ 
              backgroundColor: colors.backgroundSecondary,
              borderColor: colors.border
            }}
          >
            <h2 className="text-2xl font-bold mb-6" style={{ color: colors.textPrimary }}>
              {isAdolescentMode ? '🎨 Current Theme Demo' : '🎨 ACTIVE_THEME_DEMONSTRATION'}
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              {/* Theme Information */}
              <div>
                <h3 className="text-xl font-bold mb-4" style={{ color: colors.textPrimary }}>
                  {currentTheme.name}
                </h3>
                <p className="mb-4" style={{ color: colors.textSecondary }}>
                  {currentTheme.description}
                </p>
                <p className="text-sm mb-4" style={{ color: colors.textMuted }}>
                  <strong>Color Theory:</strong> {currentTheme.colorTheory}
                </p>
                
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span style={{ color: colors.textSecondary }}>
                      {isAdolescentMode ? '😌 Stress Reduction:' : 'STRESS_REDUCTION:'}
                    </span>
                    <span className="font-bold" style={{ color: colors.textPrimary }}>
                      {currentTheme.psychologyProfile.stressReduction}/10
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span style={{ color: colors.textSecondary }}>
                      {isAdolescentMode ? '🎯 Focus Enhancement:' : 'FOCUS_ENHANCEMENT:'}
                    </span>
                    <span className="font-bold" style={{ color: colors.textPrimary }}>
                      {currentTheme.psychologyProfile.focusEnhancement}/10
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span style={{ color: colors.textSecondary }}>
                      {isAdolescentMode ? '♿ Accessibility:' : 'ACCESSIBILITY:'}
                    </span>
                    <span className="font-bold" style={{ color: colors.textPrimary }}>
                      {currentTheme.psychologyProfile.accessibility}/10
                    </span>
                  </div>
                </div>
              </div>

              {/* Interactive Components Demo */}
              <div>
                <h3 className="text-xl font-bold mb-4" style={{ color: colors.textPrimary }}>
                  {isAdolescentMode ? '🎮 Interactive Components' : '🎮 COMPONENT_DEMONSTRATION'}
                </h3>
                
                <div className="space-y-4">
                  {/* Market Condition Badges */}
                  <div>
                    <h4 className="text-sm font-medium mb-2" style={{ color: colors.textSecondary }}>
                      Market Conditions:
                    </h4>
                    <div className="flex gap-2">
                      <MarketConditionBadge condition="bullish">Bullish</MarketConditionBadge>
                      <MarketConditionBadge condition="bearish">Bearish</MarketConditionBadge>
                      <MarketConditionBadge condition="neutral">Neutral</MarketConditionBadge>
                    </div>
                  </div>

                  {/* Trading Buttons */}
                  <div>
                    <h4 className="text-sm font-medium mb-2" style={{ color: colors.textSecondary }}>
                      Trading Actions:
                    </h4>
                    <div className="flex gap-2">
                      <TradingButton action="buy" onClick={() => {}}>
                        {isAdolescentMode ? '📈 Buy' : 'BUY'}
                      </TradingButton>
                      <TradingButton action="sell" onClick={() => {}}>
                        {isAdolescentMode ? '📉 Sell' : 'SELL'}
                      </TradingButton>
                      <TradingButton action="neutral" onClick={() => {}}>
                        {isAdolescentMode ? '⏸️ Hold' : 'HOLD'}
                      </TradingButton>
                    </div>
                  </div>

                  {/* Status Indicators */}
                  <div>
                    <h4 className="text-sm font-medium mb-2" style={{ color: colors.textSecondary }}>
                      Status Indicators:
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      <div 
                        className="p-2 rounded text-center text-sm font-medium"
                        style={{ backgroundColor: colors.success, color: colors.background }}
                      >
                        Success
                      </div>
                      <div 
                        className="p-2 rounded text-center text-sm font-medium"
                        style={{ backgroundColor: colors.warning, color: colors.background }}
                      >
                        Warning
                      </div>
                      <div 
                        className="p-2 rounded text-center text-sm font-medium"
                        style={{ backgroundColor: colors.error, color: colors.background }}
                      >
                        Error
                      </div>
                      <div 
                        className="p-2 rounded text-center text-sm font-medium"
                        style={{ backgroundColor: colors.info, color: colors.background }}
                      >
                        Info
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="text-center">
          <a
            href="/"
            className="inline-block px-6 py-3 rounded-lg font-bold transition-colors"
            style={{ 
              backgroundColor: colors.primary,
              color: colors.background
            }}
          >
            {isAdolescentMode ? '🏠 Back to Quest Hub' : '🏠 RETURN_TO_MAIN'}
          </a>
        </div>
      </div>
    </div>
  )
}
