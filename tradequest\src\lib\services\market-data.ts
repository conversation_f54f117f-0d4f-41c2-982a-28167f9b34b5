import axios from 'axios'
import { MarketData, CandlestickData } from '@/types'
import { API_ENDPOINTS } from '@/lib/constants'

class MarketDataService {
  private coingeckoClient: any
  private alphaVantageClient: any

  constructor() {
    this.coingeckoClient = axios.create({
      baseURL: API_ENDPOINTS.coingecko.base,
      timeout: 10000,
    })

    this.alphaVantageClient = axios.create({
      baseURL: API_ENDPOINTS.alpha_vantage.base,
      timeout: 10000,
    })
  }

  // Cryptocurrency data from CoinGecko
  async getCryptoPrices(symbols: string[]): Promise<MarketData[]> {
    try {
      const ids = symbols.map(symbol => this.symbolToCoinGeckoId(symbol)).join(',')
      const response = await this.coingeckoClient.get(API_ENDPOINTS.coingecko.prices, {
        params: {
          ids,
          vs_currencies: 'usd',
          include_24hr_change: true,
          include_24hr_vol: true,
          include_market_cap: true,
        },
      })

      return this.formatCoinGeckoResponse(response.data, symbols)
    } catch (error) {
      console.error('Error fetching crypto prices:', error)
      return this.generateMockCryptoData(symbols)
    }
  }

  // Stock data from Alpha Vantage
  async getStockPrices(symbols: string[]): Promise<MarketData[]> {
    try {
      const promises = symbols.map(symbol => this.fetchStockPrice(symbol))
      const results = await Promise.all(promises)
      return results.filter(Boolean) as MarketData[]
    } catch (error) {
      console.error('Error fetching stock prices:', error)
      return this.generateMockStockData(symbols)
    }
  }

  // Forex data from Alpha Vantage
  async getForexPrices(pairs: string[]): Promise<MarketData[]> {
    try {
      const promises = pairs.map(pair => this.fetchForexPrice(pair))
      const results = await Promise.all(promises)
      return results.filter(Boolean) as MarketData[]
    } catch (error) {
      console.error('Error fetching forex prices:', error)
      return this.generateMockForexData(pairs)
    }
  }

  // Historical candlestick data
  async getCandlestickData(symbol: string, interval: string = '1h', days: number = 7): Promise<CandlestickData[]> {
    try {
      if (this.isCryptoSymbol(symbol)) {
        return await this.getCryptoCandlestickData(symbol, days)
      } else {
        return await this.getStockCandlestickData(symbol, interval)
      }
    } catch (error) {
      console.error('Error fetching candlestick data:', error)
      return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data
    }
  }

  // Private helper methods
  private async fetchStockPrice(symbol: string): Promise<MarketData | null> {
    try {
      const response = await this.alphaVantageClient.get('', {
        params: {
          function: 'GLOBAL_QUOTE',
          symbol,
          apikey: process.env.ALPHA_VANTAGE_API_KEY,
        },
      })

      const quote = response.data['Global Quote']
      if (!quote) return null

      return {
        symbol,
        price: parseFloat(quote['05. price']),
        change_24h: parseFloat(quote['09. change']),
        change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),
        volume_24h: parseFloat(quote['06. volume']),
        timestamp: new Date().toISOString(),
      }
    } catch (error) {
      return null
    }
  }

  private async fetchForexPrice(pair: string): Promise<MarketData | null> {
    try {
      const [from, to] = pair.split('/')
      const response = await this.alphaVantageClient.get('', {
        params: {
          function: 'CURRENCY_EXCHANGE_RATE',
          from_currency: from,
          to_currency: to,
          apikey: process.env.ALPHA_VANTAGE_API_KEY,
        },
      })

      const rate = response.data['Realtime Currency Exchange Rate']
      if (!rate) return null

      return {
        symbol: pair,
        price: parseFloat(rate['5. Exchange Rate']),
        change_24h: 0, // Alpha Vantage doesn't provide 24h change for forex
        change_percentage_24h: 0,
        volume_24h: 0,
        timestamp: rate['6. Last Refreshed'],
      }
    } catch (error) {
      return null
    }
  }

  private async getCryptoCandlestickData(symbol: string, days: number): Promise<CandlestickData[]> {
    const id = this.symbolToCoinGeckoId(symbol)
    const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {
      params: {
        vs_currency: 'usd',
        days,
        interval: 'hourly',
      },
    })

    const prices = response.data.prices
    const volumes = response.data.total_volumes

    return prices.map((price: [number, number], index: number) => ({
      timestamp: price[0],
      open: index > 0 ? prices[index - 1][1] : price[1],
      high: price[1] * (1 + Math.random() * 0.02), // Simulate high
      low: price[1] * (1 - Math.random() * 0.02), // Simulate low
      close: price[1],
      volume: volumes[index] ? volumes[index][1] : 0,
    }))
  }

  private async getStockCandlestickData(symbol: string, interval: string): Promise<CandlestickData[]> {
    const response = await this.alphaVantageClient.get('', {
      params: {
        function: 'TIME_SERIES_INTRADAY',
        symbol,
        interval,
        apikey: process.env.ALPHA_VANTAGE_API_KEY,
      },
    })

    const timeSeries = response.data[`Time Series (${interval})`]
    if (!timeSeries) return []

    return Object.entries(timeSeries).map(([timestamp, data]: [string, any]) => ({
      timestamp: new Date(timestamp).getTime(),
      open: parseFloat(data['1. open']),
      high: parseFloat(data['2. high']),
      low: parseFloat(data['3. low']),
      close: parseFloat(data['4. close']),
      volume: parseFloat(data['5. volume']),
    }))
  }

  private symbolToCoinGeckoId(symbol: string): string {
    const mapping: Record<string, string> = {
      BTC: 'bitcoin',
      ETH: 'ethereum',
      ADA: 'cardano',
      SOL: 'solana',
      DOT: 'polkadot',
      LINK: 'chainlink',
      UNI: 'uniswap',
      MATIC: 'polygon',
    }
    return mapping[symbol.toUpperCase()] || symbol.toLowerCase()
  }

  private isCryptoSymbol(symbol: string): boolean {
    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']
    return cryptoSymbols.includes(symbol.toUpperCase())
  }

  private formatCoinGeckoResponse(data: any, symbols: string[]): MarketData[] {
    return symbols.map(symbol => {
      const id = this.symbolToCoinGeckoId(symbol)
      const coinData = data[id]
      
      if (!coinData) return this.generateMockCryptoData([symbol])[0]

      return {
        symbol,
        price: coinData.usd,
        change_24h: coinData.usd_24h_change || 0,
        change_percentage_24h: coinData.usd_24h_change || 0,
        volume_24h: coinData.usd_24h_vol || 0,
        market_cap: coinData.usd_market_cap,
        timestamp: new Date().toISOString(),
      }
    })
  }

  // Mock data generators for development and fallback
  private generateMockCryptoData(symbols: string[]): MarketData[] {
    const basePrices: Record<string, number> = {
      BTC: 45000,
      ETH: 3000,
      ADA: 0.5,
      SOL: 100,
    }

    return symbols.map(symbol => ({
      symbol,
      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
      change_24h: (Math.random() - 0.5) * 1000,
      change_percentage_24h: (Math.random() - 0.5) * 10,
      volume_24h: Math.random() * 1000000000,
      market_cap: Math.random() * 100000000000,
      timestamp: new Date().toISOString(),
    }))
  }

  private generateMockStockData(symbols: string[]): MarketData[] {
    const basePrices: Record<string, number> = {
      AAPL: 150,
      GOOGL: 2500,
      TSLA: 800,
      MSFT: 300,
    }

    return symbols.map(symbol => ({
      symbol,
      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
      change_24h: (Math.random() - 0.5) * 20,
      change_percentage_24h: (Math.random() - 0.5) * 5,
      volume_24h: Math.random() * 100000000,
      timestamp: new Date().toISOString(),
    }))
  }

  private generateMockForexData(pairs: string[]): MarketData[] {
    const basePrices: Record<string, number> = {
      'EUR/USD': 1.1,
      'GBP/USD': 1.3,
      'USD/JPY': 110,
      'USD/CHF': 0.9,
    }

    return pairs.map(pair => ({
      symbol: pair,
      price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),
      change_24h: (Math.random() - 0.5) * 0.01,
      change_percentage_24h: (Math.random() - 0.5) * 1,
      volume_24h: 0,
      timestamp: new Date().toISOString(),
    }))
  }

  private generateMockCandlestickData(symbol: string, count: number): CandlestickData[] {
    const data: CandlestickData[] = []
    let price = 100 + Math.random() * 900
    const now = Date.now()

    for (let i = 0; i < count; i++) {
      const timestamp = now - (count - i) * 3600000 // Hourly intervals
      const change = (Math.random() - 0.5) * 10
      const open = price
      const close = price + change
      const high = Math.max(open, close) + Math.random() * 5
      const low = Math.min(open, close) - Math.random() * 5
      const volume = Math.random() * 1000000

      data.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume,
      })

      price = close
    }

    return data
  }
}

export const marketDataService = new MarketDataService()
