{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n  )\n}\n"], "names": [], "mappings": ";;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EACvB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB,EACpC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B;AAE7C", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/stores/user-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { UserProfile, Achievement, GameSession, ThemeConfig } from '@/types'\nimport { createClient } from '@/lib/supabase/client'\n\ninterface UserState {\n  // User data\n  user: UserProfile | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  \n  // Theme and UI\n  interfaceMode: 'adolescent' | 'adult'\n  themeConfig: ThemeConfig\n  \n  // Game state\n  currentGameSession: GameSession | null\n  recentSessions: GameSession[]\n  \n  // Actions\n  setUser: (user: UserProfile | null) => void\n  setAuthenticated: (authenticated: boolean) => void\n  setLoading: (loading: boolean) => void\n  switchInterfaceMode: (mode: 'adolescent' | 'adult') => void\n  updateThemeConfig: (config: Partial<ThemeConfig>) => void\n  addQuestCoins: (amount: number, source: string) => void\n  spendQuestCoins: (amount: number, purpose: string) => boolean\n  addExperience: (points: number) => void\n  unlockAchievement: (achievement: Achievement) => void\n  startGameSession: (session: GameSession) => void\n  endGameSession: (session: GameSession) => void\n  updateUserProfile: (updates: Partial<UserProfile>) => void\n  clearUserData: () => void\n  signOut: () => Promise<void>\n  initializeAuth: () => Promise<void>\n}\n\nconst defaultThemeConfig: ThemeConfig = {\n  mode: 'adolescent',\n  primary_color: '#8B5CF6',\n  secondary_color: '#EC4899',\n  background_style: 'fantasy',\n  font_family: 'fantasy',\n}\n\nexport const useUserStore = create<UserState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      interfaceMode: 'adolescent',\n      themeConfig: defaultThemeConfig,\n      currentGameSession: null,\n      recentSessions: [],\n\n      // User management actions\n      setUser: (user) => {\n        set({ user, isAuthenticated: !!user })\n        \n        // Update interface mode based on user preference or age\n        if (user) {\n          const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult')\n          get().switchInterfaceMode(mode)\n        }\n      },\n\n      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),\n\n      setLoading: (loading) => set({ isLoading: loading }),\n\n      // Theme and UI actions\n      switchInterfaceMode: (mode) => {\n        const newThemeConfig: ThemeConfig = mode === 'adolescent' \n          ? {\n              mode: 'adolescent',\n              primary_color: '#8B5CF6',\n              secondary_color: '#EC4899',\n              background_style: 'fantasy',\n              font_family: 'fantasy',\n            }\n          : {\n              mode: 'adult',\n              primary_color: '#1F2937',\n              secondary_color: '#3B82F6',\n              background_style: 'professional',\n              font_family: 'monospace',\n            }\n\n        set({ \n          interfaceMode: mode, \n          themeConfig: newThemeConfig \n        })\n\n        // Update user preference in database\n        const { user } = get()\n        if (user) {\n          get().updateUserProfile({ interface_mode: mode })\n        }\n      },\n\n      updateThemeConfig: (config) => {\n        set((state) => ({\n          themeConfig: { ...state.themeConfig, ...config }\n        }))\n      },\n\n      // Quest coins management\n      addQuestCoins: (amount, source) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins + amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Added ${amount} QuestCoins from ${source}`)\n      },\n\n      spendQuestCoins: (amount, purpose) => {\n        const { user } = get()\n        if (!user || user.total_quest_coins < amount) {\n          return false\n        }\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins - amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Spent ${amount} QuestCoins on ${purpose}`)\n        return true\n      },\n\n      // Experience and leveling\n      addExperience: (points) => {\n        const { user } = get()\n        if (!user) return\n\n        const newExperience = user.experience_points + points\n        const newLevel = calculateLevel(newExperience)\n        const leveledUp = newLevel > user.level\n\n        const updatedUser = {\n          ...user,\n          experience_points: newExperience,\n          level: newLevel\n        }\n\n        set({ user: updatedUser })\n\n        if (leveledUp) {\n          console.log(`Level up! Now level ${newLevel}`)\n          // In a real app, you'd trigger level up effects, notifications, etc.\n        }\n      },\n\n      // Achievement system\n      unlockAchievement: (achievement) => {\n        const { user } = get()\n        if (!user) return\n\n        // Check if achievement is already unlocked\n        const alreadyUnlocked = user.achievements.some(a => a.id === achievement.id)\n        if (alreadyUnlocked) return\n\n        const updatedUser = {\n          ...user,\n          achievements: [...user.achievements, { ...achievement, unlocked_at: new Date().toISOString() }]\n        }\n\n        set({ user: updatedUser })\n\n        // Award quest coins for achievement\n        get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`)\n\n        console.log(`Achievement unlocked: ${achievement.name}`)\n        // In a real app, you'd show a notification, play sound effects, etc.\n      },\n\n      // Game session management\n      startGameSession: (session) => {\n        set({ currentGameSession: session })\n      },\n\n      endGameSession: (session) => {\n        const { recentSessions } = get()\n        \n        // Add to recent sessions (keep last 10)\n        const updatedSessions = [session, ...recentSessions].slice(0, 10)\n        \n        set({ \n          currentGameSession: null,\n          recentSessions: updatedSessions\n        })\n\n        // Award quest coins and experience\n        get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`)\n        get().addExperience(Math.floor(session.score / 10))\n\n        // Check for achievements\n        checkGameAchievements(session, get())\n      },\n\n      // Profile updates\n      updateUserProfile: (updates) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = { ...user, ...updates }\n        set({ user: updatedUser })\n\n        // In a real app, you'd sync with the database\n        console.log('User profile updated:', updates)\n      },\n\n      // Cleanup\n      clearUserData: () => {\n        set({\n          user: null,\n          isAuthenticated: false,\n          currentGameSession: null,\n          recentSessions: [],\n          interfaceMode: 'adolescent',\n          themeConfig: defaultThemeConfig,\n        })\n      },\n\n      // Authentication methods\n      signOut: async () => {\n        const supabase = createClient()\n        await supabase.auth.signOut()\n        get().clearUserData()\n      },\n\n      initializeAuth: async () => {\n        const supabase = createClient()\n\n        // Get initial session\n        const { data: { session } } = await supabase.auth.getSession()\n\n        if (session?.user) {\n          // Fetch user profile\n          const { data: profile } = await supabase\n            .from('user_profiles')\n            .select('*')\n            .eq('id', session.user.id)\n            .single()\n\n          if (profile) {\n            // Fetch achievements\n            const { data: achievements } = await supabase\n              .from('user_achievements')\n              .select(`\n                achievement_id,\n                unlocked_at,\n                achievements (\n                  id,\n                  name,\n                  description,\n                  icon,\n                  category,\n                  points\n                )\n              `)\n              .eq('user_id', session.user.id)\n\n            const userAchievements = achievements?.map(ua => ({\n              ...ua.achievements,\n              unlocked_at: ua.unlocked_at\n            })) || []\n\n            get().setUser({\n              id: profile.id,\n              email: session.user.email!,\n              username: profile.username,\n              age: profile.age,\n              is_minor: profile.is_minor,\n              interface_mode: profile.interface_mode,\n              avatar_url: profile.avatar_url,\n              total_quest_coins: profile.total_quest_coins,\n              level: profile.level,\n              experience_points: profile.experience_points,\n              achievements: userAchievements,\n              guild_id: profile.guild_id,\n              preferred_language: profile.preferred_language,\n              created_at: profile.created_at,\n              updated_at: profile.updated_at,\n            })\n          }\n        }\n\n        // Listen for auth changes\n        supabase.auth.onAuthStateChange(async (event, session) => {\n          if (event === 'SIGNED_OUT' || !session) {\n            get().clearUserData()\n          } else if (event === 'SIGNED_IN' && session?.user) {\n            // Refresh user data when signed in\n            get().initializeAuth()\n          }\n        })\n      },\n    }),\n    {\n      name: 'tradequest-user-storage',\n      partialize: (state) => ({\n        user: state.user,\n        interfaceMode: state.interfaceMode,\n        themeConfig: state.themeConfig,\n        recentSessions: state.recentSessions,\n      }),\n    }\n  )\n)\n\n// Helper functions\nfunction calculateLevel(experience: number): number {\n  const LEVEL_THRESHOLDS = [\n    0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n    13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n  ]\n\n  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {\n    if (experience >= LEVEL_THRESHOLDS[i]) {\n      return i + 1\n    }\n  }\n  return 1\n}\n\nfunction checkGameAchievements(session: GameSession, store: any) {\n  const achievements: Achievement[] = []\n\n  // First game achievement\n  if (store.recentSessions.length === 0) {\n    achievements.push({\n      id: 'first_game',\n      name: 'First Steps',\n      description: 'Complete your first trading game',\n      icon: '🎮',\n      category: 'trading',\n      points: 50,\n    })\n  }\n\n  // High score achievements\n  if (session.score >= 1000) {\n    achievements.push({\n      id: 'high_score_1000',\n      name: 'Rising Trader',\n      description: 'Score 1000+ points in a single game',\n      icon: '📈',\n      category: 'trading',\n      points: 100,\n    })\n  }\n\n  if (session.score >= 5000) {\n    achievements.push({\n      id: 'high_score_5000',\n      name: 'Expert Trader',\n      description: 'Score 5000+ points in a single game',\n      icon: '🏆',\n      category: 'trading',\n      points: 250,\n    })\n  }\n\n  // Game-specific achievements\n  if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {\n    achievements.push({\n      id: 'speed_scalper',\n      name: 'Lightning Fast',\n      description: 'Complete Scalper Sprint in under 30 seconds',\n      icon: '⚡',\n      category: 'trading',\n      points: 150,\n    })\n  }\n\n  // Unlock achievements\n  achievements.forEach(achievement => {\n    store.unlockAchievement(achievement)\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAkCA,MAAM,qBAAkC;IACtC,MAAM;IACN,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;AACf;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,gBAAgB,EAAE;QAElB,0BAA0B;QAC1B,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;YAEpC,wDAAwD;YACxD,IAAI,MAAM;gBACR,MAAM,OAAO,KAAK,cAAc,IAAI,CAAC,KAAK,QAAQ,GAAG,eAAe,OAAO;gBAC3E,MAAM,mBAAmB,CAAC;YAC5B;QACF;QAEA,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE,iBAAiB;YAAc;QAE1E,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAElD,uBAAuB;QACvB,qBAAqB,CAAC;YACpB,MAAM,iBAA8B,SAAS,eACzC;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf,IACA;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf;YAEJ,IAAI;gBACF,eAAe;gBACf,aAAa;YACf;YAEA,qCAAqC;YACrC,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM;gBACR,MAAM,iBAAiB,CAAC;oBAAE,gBAAgB;gBAAK;YACjD;QACF;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,MAAM;oBAAC;gBACjD,CAAC;QACH;QAEA,yBAAyB;QACzB,eAAe,CAAC,QAAQ;YACtB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,iBAAiB,EAAE,QAAQ;QACzD;QAEA,iBAAiB,CAAC,QAAQ;YACxB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,QAAQ,KAAK,iBAAiB,GAAG,QAAQ;gBAC5C,OAAO;YACT;YAEA,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,eAAe,EAAE,SAAS;YACtD,OAAO;QACT;QAEA,0BAA0B;QAC1B,eAAe,CAAC;YACd,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,gBAAgB,KAAK,iBAAiB,GAAG;YAC/C,MAAM,WAAW,eAAe;YAChC,MAAM,YAAY,WAAW,KAAK,KAAK;YAEvC,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB;gBACnB,OAAO;YACT;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;YAC7C,qEAAqE;YACvE;QACF;QAEA,qBAAqB;QACrB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,2CAA2C;YAC3C,MAAM,kBAAkB,KAAK,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;YAC3E,IAAI,iBAAiB;YAErB,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,cAAc;uBAAI,KAAK,YAAY;oBAAE;wBAAE,GAAG,WAAW;wBAAE,aAAa,IAAI,OAAO,WAAW;oBAAG;iBAAE;YACjG;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,oCAAoC;YACpC,MAAM,aAAa,CAAC,YAAY,MAAM,EAAE,CAAC,aAAa,EAAE,YAAY,IAAI,EAAE;YAE1E,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,IAAI,EAAE;QACvD,qEAAqE;QACvE;QAEA,0BAA0B;QAC1B,kBAAkB,CAAC;YACjB,IAAI;gBAAE,oBAAoB;YAAQ;QACpC;QAEA,gBAAgB,CAAC;YACf,MAAM,EAAE,cAAc,EAAE,GAAG;YAE3B,wCAAwC;YACxC,MAAM,kBAAkB;gBAAC;mBAAY;aAAe,CAAC,KAAK,CAAC,GAAG;YAE9D,IAAI;gBACF,oBAAoB;gBACpB,gBAAgB;YAClB;YAEA,mCAAmC;YACnC,MAAM,aAAa,CAAC,QAAQ,kBAAkB,EAAE,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE;YAC5E,MAAM,aAAa,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;YAE/C,yBAAyB;YACzB,sBAAsB,SAAS;QACjC;QAEA,kBAAkB;QAClB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC1C,IAAI;gBAAE,MAAM;YAAY;YAExB,8CAA8C;YAC9C,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,UAAU;QACV,eAAe;YACb,IAAI;gBACF,MAAM;gBACN,iBAAiB;gBACjB,oBAAoB;gBACpB,gBAAgB,EAAE;gBAClB,eAAe;gBACf,aAAa;YACf;QACF;QAEA,yBAAyB;QACzB,SAAS;YACP,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,MAAM,aAAa;QACrB;QAEA,gBAAgB;YACd,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAE5B,sBAAsB;YACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAE5D,IAAI,SAAS,MAAM;gBACjB,qBAAqB;gBACrB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;gBAET,IAAI,SAAS;oBACX,qBAAqB;oBACrB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;;cAWT,CAAC,EACA,EAAE,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE;oBAEhC,MAAM,mBAAmB,cAAc,IAAI,CAAA,KAAM,CAAC;4BAChD,GAAG,GAAG,YAAY;4BAClB,aAAa,GAAG,WAAW;wBAC7B,CAAC,MAAM,EAAE;oBAET,MAAM,OAAO,CAAC;wBACZ,IAAI,QAAQ,EAAE;wBACd,OAAO,QAAQ,IAAI,CAAC,KAAK;wBACzB,UAAU,QAAQ,QAAQ;wBAC1B,KAAK,QAAQ,GAAG;wBAChB,UAAU,QAAQ,QAAQ;wBAC1B,gBAAgB,QAAQ,cAAc;wBACtC,YAAY,QAAQ,UAAU;wBAC9B,mBAAmB,QAAQ,iBAAiB;wBAC5C,OAAO,QAAQ,KAAK;wBACpB,mBAAmB,QAAQ,iBAAiB;wBAC5C,cAAc;wBACd,UAAU,QAAQ,QAAQ;wBAC1B,oBAAoB,QAAQ,kBAAkB;wBAC9C,YAAY,QAAQ,UAAU;wBAC9B,YAAY,QAAQ,UAAU;oBAChC;gBACF;YACF;YAEA,0BAA0B;YAC1B,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;gBAC5C,IAAI,UAAU,gBAAgB,CAAC,SAAS;oBACtC,MAAM,aAAa;gBACrB,OAAO,IAAI,UAAU,eAAe,SAAS,MAAM;oBACjD,mCAAmC;oBACnC,MAAM,cAAc;gBACtB;YACF;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;YAC9B,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH;AAIJ,mBAAmB;AACnB,SAAS,eAAe,UAAkB;IACxC,MAAM,mBAAmB;QACvB;QAAG;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAChE;IAED,IAAK,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACrD,IAAI,cAAc,gBAAgB,CAAC,EAAE,EAAE;YACrC,OAAO,IAAI;QACb;IACF;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAoB,EAAE,KAAU;IAC7D,MAAM,eAA8B,EAAE;IAEtC,yBAAyB;IACzB,IAAI,MAAM,cAAc,CAAC,MAAM,KAAK,GAAG;QACrC,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,0BAA0B;IAC1B,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,SAAS,KAAK,oBAAoB,QAAQ,gBAAgB,IAAI,IAAI;QAC5E,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,iBAAiB,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number, decimals: number = 2): string {\n  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`\n}\n\nexport function formatNumber(value: number, decimals: number = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(value)\n}\n\nexport function formatLargeNumber(value: number): string {\n  if (value >= 1e9) {\n    return `${(value / 1e9).toFixed(1)}B`\n  }\n  if (value >= 1e6) {\n    return `${(value / 1e6).toFixed(1)}M`\n  }\n  if (value >= 1e3) {\n    return `${(value / 1e3).toFixed(1)}K`\n  }\n  return value.toString()\n}\n\nexport function calculatePnL(entryPrice: number, currentPrice: number, quantity: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity\n}\n\nexport function calculatePnLPercentage(entryPrice: number, currentPrice: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  const percentage = (priceDiff / entryPrice) * 100\n  return side === 'buy' ? percentage : -percentage\n}\n\nexport function generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n}\n\nexport function isMinor(age: number): boolean {\n  return age < 18\n}\n\nexport function validateAge(age: number): boolean {\n  return age >= 13 && age <= 120\n}\n\nexport function sanitizeUsername(username: string): string {\n  return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase()\n}\n\nexport function getTimeRemaining(endTime: Date): {\n  total: number\n  days: number\n  hours: number\n  minutes: number\n  seconds: number\n} {\n  const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString())\n  const seconds = Math.floor((total / 1000) % 60)\n  const minutes = Math.floor((total / 1000 / 60) % 60)\n  const hours = Math.floor((total / (1000 * 60 * 60)) % 24)\n  const days = Math.floor(total / (1000 * 60 * 60 * 24))\n\n  return {\n    total,\n    days,\n    hours,\n    minutes,\n    seconds,\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n\nexport function getRandomElement<T>(array: T[]): T {\n  return array[Math.floor(Math.random() * array.length)]\n}\n\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array]\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1))\n    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]\n  }\n  return shuffled\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\nexport function generateColor(seed: string): string {\n  let hash = 0\n  for (let i = 0; i < seed.length; i++) {\n    hash = seed.charCodeAt(i) + ((hash << 5) - hash)\n  }\n  const hue = hash % 360\n  return `hsl(${hue}, 70%, 50%)`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa,EAAE,WAAmB,CAAC;IAClE,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC;AAC9D;AAEO,SAAS,aAAa,KAAa,EAAE,WAAmB,CAAC;IAC9D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,KAAa;IAC7C,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,aAAa,UAAkB,EAAE,YAAoB,EAAE,QAAgB,EAAE,IAAoB;IAC3G,MAAM,YAAY,eAAe;IACjC,OAAO,SAAS,QAAQ,YAAY,WAAW,CAAC,YAAY;AAC9D;AAEO,SAAS,uBAAuB,UAAkB,EAAE,YAAoB,EAAE,IAAoB;IACnG,MAAM,YAAY,eAAe;IACjC,MAAM,aAAa,AAAC,YAAY,aAAc;IAC9C,OAAO,SAAS,QAAQ,aAAa,CAAC;AACxC;AAEO,SAAS;IACd,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,MAAM;AACf;AAEO,SAAS,YAAY,GAAW;IACrC,OAAO,OAAO,MAAM,OAAO;AAC7B;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,OAAO,CAAC,mBAAmB,IAAI,WAAW;AAC5D;AAEO,SAAS,iBAAiB,OAAa;IAO5C,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,QAAQ,MAAM,KAAK,KAAK,CAAC,IAAI,OAAO,QAAQ;IAC7E,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAQ;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAO,KAAM;IACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,KAAK,EAAE,IAAK;IACtD,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;IAEpD,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,iBAAoB,KAAU;IAC5C,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAEO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC1C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IAC1D;IACA,OAAO;AACT;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,OAAO,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI;IACjD;IACA,MAAM,MAAM,OAAO;IACnB,OAAO,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/constants.ts"], "sourcesContent": ["import { GameType, TradingPair } from '@/types'\n\n// Game Configuration\nexport const GAME_CONFIGS = {\n  scalper_sprint: {\n    name: 'Scalper Sprint',\n    description: '60-second trading challenges with rapid-fire decisions',\n    difficulty: 'beginner',\n    duration_seconds: 60,\n    starting_balance: 10000,\n    min_trade_size: 100,\n    max_positions: 3,\n    quest_coins_base: 50,\n  },\n  candle_strike: {\n    name: 'CandleStrike',\n    description: 'Pattern recognition game with candlestick charts',\n    difficulty: 'beginner',\n    duration_seconds: 120,\n    starting_balance: 0, // Pattern recognition, no trading\n    patterns_to_identify: 5,\n    quest_coins_base: 75,\n  },\n  chain_maze: {\n    name: 'ChainMaze',\n    description: 'Navigate blockchain puzzles and learn consensus mechanisms',\n    difficulty: 'intermediate',\n    duration_seconds: 300,\n    starting_balance: 1000, // Gas fees simulation\n    puzzles_to_solve: 3,\n    quest_coins_base: 100,\n  },\n  swing_trader_odyssey: {\n    name: \"Swing Trader's Odyssey\",\n    description: 'Multi-day position management with risk/reward balancing',\n    difficulty: 'intermediate',\n    duration_seconds: 600, // 10 minutes simulating days\n    starting_balance: 50000,\n    max_positions: 5,\n    quest_coins_base: 150,\n  },\n  day_trader_arena: {\n    name: 'Day Trader Arena',\n    description: 'Real-time multiplayer trading competitions',\n    difficulty: 'advanced',\n    duration_seconds: 900, // 15 minutes\n    starting_balance: 100000,\n    max_positions: 10,\n    quest_coins_base: 200,\n  },\n  portfolio_survivor: {\n    name: 'Portfolio Survivor',\n    description: 'Crisis management with diversification challenges',\n    difficulty: 'advanced',\n    duration_seconds: 1200, // 20 minutes\n    starting_balance: 500000,\n    max_positions: 20,\n    quest_coins_base: 300,\n  },\n} as const\n\n// Trading Pairs\nexport const TRADING_PAIRS: TradingPair[] = [\n  { base: 'BTC', quote: 'USD', symbol: 'BTCUSD', exchange: 'virtual' },\n  { base: 'ETH', quote: 'USD', symbol: 'ETHUSD', exchange: 'virtual' },\n  { base: 'ADA', quote: 'USD', symbol: 'ADAUSD', exchange: 'virtual' },\n  { base: 'SOL', quote: 'USD', symbol: 'SOLUSD', exchange: 'virtual' },\n  { base: 'AAPL', quote: 'USD', symbol: 'AAPL', exchange: 'virtual' },\n  { base: 'GOOGL', quote: 'USD', symbol: 'GOOGL', exchange: 'virtual' },\n  { base: 'TSLA', quote: 'USD', symbol: 'TSLA', exchange: 'virtual' },\n  { base: 'EUR', quote: 'USD', symbol: 'EURUSD', exchange: 'virtual' },\n  { base: 'GBP', quote: 'USD', symbol: 'GBPUSD', exchange: 'virtual' },\n  { base: 'JPY', quote: 'USD', symbol: 'JPYUSD', exchange: 'virtual' },\n]\n\n// Achievement Categories and Points\nexport const ACHIEVEMENT_CATEGORIES = {\n  trading: {\n    name: 'Trading Mastery',\n    color: '#10B981',\n    icon: '📈',\n  },\n  learning: {\n    name: 'Knowledge Seeker',\n    color: '#3B82F6',\n    icon: '🎓',\n  },\n  social: {\n    name: 'Community Builder',\n    color: '#8B5CF6',\n    icon: '👥',\n  },\n  special: {\n    name: 'Special Events',\n    color: '#F59E0B',\n    icon: '⭐',\n  },\n} as const\n\n// Level System\nexport const LEVEL_THRESHOLDS = [\n  0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n  13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n]\n\nexport const QUEST_COIN_MULTIPLIERS = {\n  beginner: 1.0,\n  intermediate: 1.5,\n  advanced: 2.0,\n} as const\n\n// UI Constants\nexport const INTERFACE_MODES = {\n  adolescent: {\n    name: 'Adventure Mode',\n    description: 'Fantasy-themed interface with quests and adventures',\n    primaryColor: '#8B5CF6',\n    secondaryColor: '#EC4899',\n    fontFamily: 'fantasy',\n  },\n  adult: {\n    name: 'Professional Mode',\n    description: 'Bloomberg Terminal-style professional interface',\n    primaryColor: '#1F2937',\n    secondaryColor: '#3B82F6',\n    fontFamily: 'monospace',\n  },\n} as const\n\n// Market Data Update Intervals\nexport const UPDATE_INTERVALS = {\n  real_time: 1000, // 1 second\n  fast: 5000, // 5 seconds\n  normal: 15000, // 15 seconds\n  slow: 60000, // 1 minute\n} as const\n\n// API Endpoints\nexport const API_ENDPOINTS = {\n  coingecko: {\n    base: 'https://api.coingecko.com/api/v3',\n    prices: '/simple/price',\n    history: '/coins/{id}/market_chart',\n  },\n  alpha_vantage: {\n    base: 'https://www.alphavantage.co/query',\n    intraday: '?function=TIME_SERIES_INTRADAY',\n    forex: '?function=FX_INTRADAY',\n  },\n} as const\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  username: {\n    minLength: 3,\n    maxLength: 20,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n  },\n  age: {\n    min: 13,\n    max: 120,\n  },\n  trade: {\n    minAmount: 1,\n    maxAmount: 1000000,\n  },\n} as const\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  auth: {\n    invalid_credentials: 'Invalid email or password',\n    user_not_found: 'User not found',\n    email_already_exists: 'Email already registered',\n    weak_password: 'Password must be at least 8 characters',\n    age_verification_failed: 'Age verification required',\n  },\n  game: {\n    session_expired: 'Game session has expired',\n    invalid_trade: 'Invalid trade parameters',\n    insufficient_balance: 'Insufficient balance for this trade',\n    max_positions_reached: 'Maximum number of positions reached',\n  },\n  general: {\n    network_error: 'Network error, please try again',\n    server_error: 'Server error, please try again later',\n    validation_error: 'Please check your input and try again',\n  },\n} as const\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  auth: {\n    registration_complete: 'Account created successfully!',\n    login_success: 'Welcome back!',\n    logout_success: 'Logged out successfully',\n  },\n  game: {\n    session_complete: 'Game session completed!',\n    achievement_unlocked: 'Achievement unlocked!',\n    level_up: 'Level up! Congratulations!',\n  },\n  general: {\n    save_success: 'Changes saved successfully',\n    update_success: 'Updated successfully',\n  },\n} as const\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,MAAM,eAAe;IAC1B,gBAAgB;QACd,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,kBAAkB;IACpB;IACA,eAAe;QACb,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,sBAAsB;QACtB,kBAAkB;IACpB;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;IACpB;IACA,sBAAsB;QACpB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,kBAAkB;QAChB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;AACF;AAGO,MAAM,gBAA+B;IAC1C;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAS,OAAO;QAAO,QAAQ;QAAS,UAAU;IAAU;IACpE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;CACpE;AAGM,MAAM,yBAAyB;IACpC,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;AACF;AAGO,MAAM,mBAAmB;IAC9B;IAAG;IAAK;IAAK;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACtD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;CAChE;AAEM,MAAM,yBAAyB;IACpC,UAAU;IACV,cAAc;IACd,UAAU;AACZ;AAGO,MAAM,kBAAkB;IAC7B,YAAY;QACV,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;IACA,OAAO;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAGO,MAAM,gBAAgB;IAC3B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,KAAK;QACH,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,WAAW;QACX,WAAW;IACb;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,qBAAqB;QACrB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,yBAAyB;IAC3B;IACA,MAAM;QACJ,iBAAiB;QACjB,eAAe;QACf,sBAAsB;QACtB,uBAAuB;IACzB;IACA,SAAS;QACP,eAAe;QACf,cAAc;QACd,kBAAkB;IACpB;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,uBAAuB;QACvB,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM;QACJ,kBAAkB;QAClB,sBAAsB;QACtB,UAAU;IACZ;IACA,SAAS;QACP,cAAc;QACd,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/services/market-data.ts"], "sourcesContent": ["import axios from 'axios'\nimport { MarketData, CandlestickData } from '@/types'\nimport { API_ENDPOINTS } from '@/lib/constants'\n\nclass MarketDataService {\n  private coingeckoClient: any\n  private alphaVantageClient: any\n\n  constructor() {\n    this.coingeckoClient = axios.create({\n      baseURL: API_ENDPOINTS.coingecko.base,\n      timeout: 10000,\n    })\n\n    this.alphaVantageClient = axios.create({\n      baseURL: API_ENDPOINTS.alpha_vantage.base,\n      timeout: 10000,\n    })\n  }\n\n  // Cryptocurrency data from CoinGecko\n  async getCryptoPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const ids = symbols.map(symbol => this.symbolToCoinGeckoId(symbol)).join(',')\n      const response = await this.coingeckoClient.get(API_ENDPOINTS.coingecko.prices, {\n        params: {\n          ids,\n          vs_currencies: 'usd',\n          include_24hr_change: true,\n          include_24hr_vol: true,\n          include_market_cap: true,\n        },\n      })\n\n      return this.formatCoinGeckoResponse(response.data, symbols)\n    } catch (error) {\n      console.error('Error fetching crypto prices:', error)\n      return this.generateMockCryptoData(symbols)\n    }\n  }\n\n  // Stock data from Alpha Vantage\n  async getStockPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const promises = symbols.map(symbol => this.fetchStockPrice(symbol))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching stock prices:', error)\n      return this.generateMockStockData(symbols)\n    }\n  }\n\n  // Forex data from Alpha Vantage\n  async getForexPrices(pairs: string[]): Promise<MarketData[]> {\n    try {\n      const promises = pairs.map(pair => this.fetchForexPrice(pair))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching forex prices:', error)\n      return this.generateMockForexData(pairs)\n    }\n  }\n\n  // Historical candlestick data\n  async getCandlestickData(symbol: string, interval: string = '1h', days: number = 7): Promise<CandlestickData[]> {\n    try {\n      if (this.isCryptoSymbol(symbol)) {\n        return await this.getCryptoCandlestickData(symbol, days)\n      } else {\n        return await this.getStockCandlestickData(symbol, interval)\n      }\n    } catch (error) {\n      console.error('Error fetching candlestick data:', error)\n      return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data\n    }\n  }\n\n  // Private helper methods\n  private async fetchStockPrice(symbol: string): Promise<MarketData | null> {\n    try {\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'GLOBAL_QUOTE',\n          symbol,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const quote = response.data['Global Quote']\n      if (!quote) return null\n\n      return {\n        symbol,\n        price: parseFloat(quote['05. price']),\n        change_24h: parseFloat(quote['09. change']),\n        change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),\n        volume_24h: parseFloat(quote['06. volume']),\n        timestamp: new Date().toISOString(),\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async fetchForexPrice(pair: string): Promise<MarketData | null> {\n    try {\n      const [from, to] = pair.split('/')\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'CURRENCY_EXCHANGE_RATE',\n          from_currency: from,\n          to_currency: to,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const rate = response.data['Realtime Currency Exchange Rate']\n      if (!rate) return null\n\n      return {\n        symbol: pair,\n        price: parseFloat(rate['5. Exchange Rate']),\n        change_24h: 0, // Alpha Vantage doesn't provide 24h change for forex\n        change_percentage_24h: 0,\n        volume_24h: 0,\n        timestamp: rate['6. Last Refreshed'],\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async getCryptoCandlestickData(symbol: string, days: number): Promise<CandlestickData[]> {\n    const id = this.symbolToCoinGeckoId(symbol)\n    const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {\n      params: {\n        vs_currency: 'usd',\n        days,\n        interval: 'hourly',\n      },\n    })\n\n    const prices = response.data.prices\n    const volumes = response.data.total_volumes\n\n    return prices.map((price: [number, number], index: number) => ({\n      timestamp: price[0],\n      open: index > 0 ? prices[index - 1][1] : price[1],\n      high: price[1] * (1 + Math.random() * 0.02), // Simulate high\n      low: price[1] * (1 - Math.random() * 0.02), // Simulate low\n      close: price[1],\n      volume: volumes[index] ? volumes[index][1] : 0,\n    }))\n  }\n\n  private async getStockCandlestickData(symbol: string, interval: string): Promise<CandlestickData[]> {\n    const response = await this.alphaVantageClient.get('', {\n      params: {\n        function: 'TIME_SERIES_INTRADAY',\n        symbol,\n        interval,\n        apikey: process.env.ALPHA_VANTAGE_API_KEY,\n      },\n    })\n\n    const timeSeries = response.data[`Time Series (${interval})`]\n    if (!timeSeries) return []\n\n    return Object.entries(timeSeries).map(([timestamp, data]: [string, any]) => ({\n      timestamp: new Date(timestamp).getTime(),\n      open: parseFloat(data['1. open']),\n      high: parseFloat(data['2. high']),\n      low: parseFloat(data['3. low']),\n      close: parseFloat(data['4. close']),\n      volume: parseFloat(data['5. volume']),\n    }))\n  }\n\n  private symbolToCoinGeckoId(symbol: string): string {\n    const mapping: Record<string, string> = {\n      BTC: 'bitcoin',\n      ETH: 'ethereum',\n      ADA: 'cardano',\n      SOL: 'solana',\n      DOT: 'polkadot',\n      LINK: 'chainlink',\n      UNI: 'uniswap',\n      MATIC: 'polygon',\n    }\n    return mapping[symbol.toUpperCase()] || symbol.toLowerCase()\n  }\n\n  private isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.includes(symbol.toUpperCase())\n  }\n\n  private formatCoinGeckoResponse(data: any, symbols: string[]): MarketData[] {\n    return symbols.map(symbol => {\n      const id = this.symbolToCoinGeckoId(symbol)\n      const coinData = data[id]\n      \n      if (!coinData) return this.generateMockCryptoData([symbol])[0]\n\n      return {\n        symbol,\n        price: coinData.usd,\n        change_24h: coinData.usd_24h_change || 0,\n        change_percentage_24h: coinData.usd_24h_change || 0,\n        volume_24h: coinData.usd_24h_vol || 0,\n        market_cap: coinData.usd_market_cap,\n        timestamp: new Date().toISOString(),\n      }\n    })\n  }\n\n  // Mock data generators for development and fallback\n  private generateMockCryptoData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      BTC: 45000,\n      ETH: 3000,\n      ADA: 0.5,\n      SOL: 100,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 1000,\n      change_percentage_24h: (Math.random() - 0.5) * 10,\n      volume_24h: Math.random() * 1000000000,\n      market_cap: Math.random() * 100000000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockStockData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      AAPL: 150,\n      GOOGL: 2500,\n      TSLA: 800,\n      MSFT: 300,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 20,\n      change_percentage_24h: (Math.random() - 0.5) * 5,\n      volume_24h: Math.random() * 100000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockForexData(pairs: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      'EUR/USD': 1.1,\n      'GBP/USD': 1.3,\n      'USD/JPY': 110,\n      'USD/CHF': 0.9,\n    }\n\n    return pairs.map(pair => ({\n      symbol: pair,\n      price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),\n      change_24h: (Math.random() - 0.5) * 0.01,\n      change_percentage_24h: (Math.random() - 0.5) * 1,\n      volume_24h: 0,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockCandlestickData(symbol: string, count: number): CandlestickData[] {\n    const data: CandlestickData[] = []\n    let price = 100 + Math.random() * 900\n    const now = Date.now()\n\n    for (let i = 0; i < count; i++) {\n      const timestamp = now - (count - i) * 3600000 // Hourly intervals\n      const change = (Math.random() - 0.5) * 10\n      const open = price\n      const close = price + change\n      const high = Math.max(open, close) + Math.random() * 5\n      const low = Math.min(open, close) - Math.random() * 5\n      const volume = Math.random() * 1000000\n\n      data.push({\n        timestamp,\n        open,\n        high,\n        low,\n        close,\n        volume,\n      })\n\n      price = close\n    }\n\n    return data\n  }\n}\n\nexport const marketDataService = new MarketDataService()\n"], "names": [], "mappings": ";;;AAsFkB;AAtFlB;AAEA;;;AAEA,MAAM;IACI,gBAAoB;IACpB,mBAAuB;IAE/B,aAAc;QACZ,IAAI,CAAC,eAAe,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAClC,SAAS,0HAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI;YACrC,SAAS;QACX;QAEA,IAAI,CAAC,kBAAkB,GAAG,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACrC,SAAS,0HAAA,CAAA,gBAAa,CAAC,aAAa,CAAC,IAAI;YACzC,SAAS;QACX;IACF;IAEA,qCAAqC;IACrC,MAAM,gBAAgB,OAAiB,EAAyB;QAC9D,IAAI;YACF,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC;YACzE,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,0HAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC9E,QAAQ;oBACN;oBACA,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;oBAClB,oBAAoB;gBACtB;YACF;YAEA,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,IAAI,EAAE;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACrC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,OAAiB,EAAyB;QAC7D,IAAI;YACF,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,eAAe,CAAC;YAC5D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,KAAe,EAAyB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,eAAe,CAAC;YACxD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,8BAA8B;IAC9B,MAAM,mBAAmB,MAAc,EAAE,WAAmB,IAAI,EAAE,OAAe,CAAC,EAA8B;QAC9G,IAAI;YACF,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;gBAC/B,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ;YACrD,OAAO;gBACL,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,KAAK,wBAAwB;;QAC/E;IACF;IAEA,yBAAyB;IACzB,MAAc,gBAAgB,MAAc,EAA8B;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV;oBACA,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,QAAQ,SAAS,IAAI,CAAC,eAAe;YAC3C,IAAI,CAAC,OAAO,OAAO;YAEnB,OAAO;gBACL;gBACA,OAAO,WAAW,KAAK,CAAC,YAAY;gBACpC,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,uBAAuB,WAAW,KAAK,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK;gBAC3E,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,gBAAgB,IAAY,EAA8B;QACtE,IAAI;YACF,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC;YAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV,eAAe;oBACf,aAAa;oBACb,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,kCAAkC;YAC7D,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO;gBACL,QAAQ;gBACR,OAAO,WAAW,IAAI,CAAC,mBAAmB;gBAC1C,YAAY;gBACZ,uBAAuB;gBACvB,YAAY;gBACZ,WAAW,IAAI,CAAC,oBAAoB;YACtC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,yBAAyB,MAAc,EAAE,IAAY,EAA8B;QAC/F,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;QACpC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,EAAE;YAC3E,QAAQ;gBACN,aAAa;gBACb;gBACA,UAAU;YACZ;QACF;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,MAAM;QACnC,MAAM,UAAU,SAAS,IAAI,CAAC,aAAa;QAE3C,OAAO,OAAO,GAAG,CAAC,CAAC,OAAyB,QAAkB,CAAC;gBAC7D,WAAW,KAAK,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gBACjD,MAAM,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBAC1C,KAAK,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBACzC,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG;YAC/C,CAAC;IACH;IAEA,MAAc,wBAAwB,MAAc,EAAE,QAAgB,EAA8B;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;YACrD,QAAQ;gBACN,UAAU;gBACV;gBACA;gBACA,QAAQ,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,qBAAqB;YAC3C;QACF;QAEA,MAAM,aAAa,SAAS,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,WAAW,KAAoB,GAAK,CAAC;gBAC3E,WAAW,IAAI,KAAK,WAAW,OAAO;gBACtC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,KAAK,WAAW,IAAI,CAAC,SAAS;gBAC9B,OAAO,WAAW,IAAI,CAAC,WAAW;gBAClC,QAAQ,WAAW,IAAI,CAAC,YAAY;YACtC,CAAC;IACH;IAEQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,UAAkC;YACtC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;IAC5D;IAEQ,eAAe,MAAc,EAAW;QAC9C,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,QAAQ,CAAC,OAAO,WAAW;IAClD;IAEQ,wBAAwB,IAAS,EAAE,OAAiB,EAAgB;QAC1E,OAAO,QAAQ,GAAG,CAAC,CAAA;YACjB,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;YACpC,MAAM,WAAW,IAAI,CAAC,GAAG;YAEzB,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBAAC;aAAO,CAAC,CAAC,EAAE;YAE9D,OAAO;gBACL;gBACA,OAAO,SAAS,GAAG;gBACnB,YAAY,SAAS,cAAc,IAAI;gBACvC,uBAAuB,SAAS,cAAc,IAAI;gBAClD,YAAY,SAAS,WAAW,IAAI;gBACpC,YAAY,SAAS,cAAc;gBACnC,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEA,oDAAoD;IAC5C,uBAAuB,OAAiB,EAAgB;QAC9D,MAAM,aAAqC;YACzC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,OAAiB,EAAgB;QAC7D,MAAM,aAAqC;YACzC,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,KAAe,EAAgB;QAC3D,MAAM,aAAqC;YACzC,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,QAAQ;gBACR,OAAO,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI;gBAC7D,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY;gBACZ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,4BAA4B,MAAc,EAAE,KAAa,EAAqB;QACpF,MAAM,OAA0B,EAAE;QAClC,IAAI,QAAQ,MAAM,KAAK,MAAM,KAAK;QAClC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,mBAAmB;;YACjE,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACvC,MAAM,OAAO;YACb,MAAM,QAAQ,QAAQ;YACtB,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACrD,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACpD,MAAM,SAAS,KAAK,MAAM,KAAK;YAE/B,KAAK,IAAI,CAAC;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,QAAQ;QACV;QAEA,OAAO;IACT;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/base-game.ts"], "sourcesContent": ["import { GameConfig, GameState, Position, GameType } from '@/types'\nimport { generateSessionId, calculatePnL } from '@/lib/utils'\nimport { GAME_CONFIGS, QUEST_COIN_MULTIPLIERS } from '@/lib/constants'\nimport { marketDataService } from '@/lib/services/market-data'\n\nexport class BaseGame {\n  protected config: GameConfig\n  protected state: GameState\n  protected startTime: number\n  protected endTime: number\n  protected isActive: boolean = false\n  protected marketData: Map<string, number> = new Map()\n\n  constructor(gameType: GameType, difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    const gameConfig = GAME_CONFIGS[gameType]\n\n    this.config = {\n      type: gameType,\n      difficulty,\n      duration_seconds: gameConfig.duration_seconds,\n      starting_balance: gameConfig.starting_balance,\n      available_pairs: [], // Will be set by specific game implementations\n      special_rules: {},\n    }\n\n    this.state = {\n      session_id: generateSessionId(),\n      current_balance: this.config.starting_balance,\n      positions: [],\n      time_remaining: this.config.duration_seconds,\n      score: 0,\n      multiplier: QUEST_COIN_MULTIPLIERS[difficulty],\n    }\n\n    this.startTime = Date.now()\n    this.endTime = this.startTime + (this.config.duration_seconds * 1000)\n  }\n\n  // Methods that can be overridden by specific games\n  async initialize(): Promise<void> {\n    // Default implementation - can be overridden\n  }\n\n  update(): void {\n    // Default implementation - can be overridden\n  }\n\n  calculateScore(): number {\n    // Default implementation - can be overridden\n    return 0\n  }\n\n  getGameSpecificData(): any {\n    // Default implementation - can be overridden\n    return {}\n  }\n\n  // Common game lifecycle methods\n  async start(): Promise<void> {\n    await this.initialize()\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  pause(): void {\n    this.isActive = false\n  }\n\n  resume(): void {\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  end(): GameState {\n    this.isActive = false\n    this.state.score = this.calculateScore()\n    this.state.time_remaining = 0\n    return this.state\n  }\n\n  // Trading operations\n  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const currentPrice = this.marketData.get(symbol)\n    if (!currentPrice) return false\n\n    const tradeValue = currentPrice * quantity\n    const requiredBalance = side === 'buy' ? tradeValue : 0\n\n    if (this.state.current_balance < requiredBalance) {\n      return false // Insufficient balance\n    }\n\n    // Check position limits\n    const maxPositions = this.getMaxPositions()\n    if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {\n      return false // Too many positions\n    }\n\n    // Execute the trade\n    const position: Position = {\n      id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      symbol,\n      side,\n      quantity,\n      entry_price: currentPrice,\n      current_price: currentPrice,\n      pnl: 0,\n      timestamp: new Date().toISOString(),\n    }\n\n    // Update balance\n    if (side === 'buy') {\n      this.state.current_balance -= tradeValue\n    } else {\n      this.state.current_balance += tradeValue\n    }\n\n    // Add or update position\n    const existingPositionIndex = this.state.positions.findIndex(p => p.symbol === symbol)\n    if (existingPositionIndex >= 0) {\n      // Update existing position (average price calculation would go here)\n      this.state.positions[existingPositionIndex] = position\n    } else {\n      this.state.positions.push(position)\n    }\n\n    return true\n  }\n\n  async closePosition(positionId: string): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)\n    if (positionIndex === -1) return false\n\n    const position = this.state.positions[positionIndex]\n    const currentPrice = this.marketData.get(position.symbol)\n    if (!currentPrice) return false\n\n    // Calculate final P&L\n    const pnl = calculatePnL(position.entry_price, currentPrice, position.quantity, position.side)\n    \n    // Update balance with P&L\n    this.state.current_balance += pnl\n    if (position.side === 'sell') {\n      // Return the initial trade value for short positions\n      this.state.current_balance += position.entry_price * position.quantity\n    }\n\n    // Remove position\n    this.state.positions.splice(positionIndex, 1)\n\n    return true\n  }\n\n  // Market data updates\n  async updateMarketData(): Promise<void> {\n    try {\n      const symbols = this.config.available_pairs.map(pair => pair.symbol)\n      \n      // In a real implementation, you'd fetch from different services based on asset type\n      const cryptoSymbols = symbols.filter(s => this.isCryptoSymbol(s))\n      const stockSymbols = symbols.filter(s => this.isStockSymbol(s))\n      const forexSymbols = symbols.filter(s => this.isForexSymbol(s))\n\n      const [cryptoData, stockData, forexData] = await Promise.all([\n        cryptoSymbols.length > 0 ? marketDataService.getCryptoPrices(cryptoSymbols) : [],\n        stockSymbols.length > 0 ? marketDataService.getStockPrices(stockSymbols) : [],\n        forexSymbols.length > 0 ? marketDataService.getForexPrices(forexSymbols) : [],\n      ])\n\n      // Update market data map\n      const allData = cryptoData.concat(stockData).concat(forexData)\n      allData.forEach(data => {\n        this.marketData.set(data.symbol, data.price)\n      })\n\n      // Update position P&L\n      this.updatePositionPnL()\n    } catch (error) {\n      console.error('Error updating market data:', error)\n    }\n  }\n\n  // Game state getters\n  getState(): GameState {\n    return { ...this.state }\n  }\n\n  getConfig(): GameConfig {\n    return { ...this.config }\n  }\n\n  isGameActive(): boolean {\n    return this.isActive && this.state.time_remaining > 0\n  }\n\n  getTimeRemaining(): number {\n    if (!this.isActive) return 0\n    const remaining = Math.max(0, this.endTime - Date.now())\n    this.state.time_remaining = Math.floor(remaining / 1000)\n    return this.state.time_remaining\n  }\n\n  // Protected helper methods\n  protected startGameLoop(): void {\n    if (!this.isActive) return\n\n    const gameLoop = () => {\n      if (!this.isActive) return\n\n      this.update()\n      this.getTimeRemaining()\n\n      if (this.state.time_remaining <= 0) {\n        this.end()\n        return\n      }\n\n      setTimeout(gameLoop, 1000) // Update every second\n    }\n\n    gameLoop()\n  }\n\n  protected updatePositionPnL(): void {\n    this.state.positions.forEach(position => {\n      const currentPrice = this.marketData.get(position.symbol)\n      if (currentPrice) {\n        position.current_price = currentPrice\n        position.pnl = calculatePnL(\n          position.entry_price,\n          currentPrice,\n          position.quantity,\n          position.side\n        )\n      }\n    })\n  }\n\n  protected getTotalPnL(): number {\n    return this.state.positions.reduce((total, position) => total + position.pnl, 0)\n  }\n\n  protected getMaxPositions(): number {\n    const gameConfig = GAME_CONFIGS[this.config.type]\n    return (gameConfig as any).max_positions || 5\n  }\n\n  protected hasExistingPosition(symbol: string): boolean {\n    return this.state.positions.some(p => p.symbol === symbol)\n  }\n\n  protected isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.some(crypto => symbol.includes(crypto))\n  }\n\n  protected isStockSymbol(symbol: string): boolean {\n    const stockSymbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN', 'META', 'NVDA']\n    return stockSymbols.includes(symbol)\n  }\n\n  protected isForexSymbol(symbol: string): boolean {\n    return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY')\n  }\n\n  protected generateRandomPrice(basePrice: number, volatility: number = 0.02): number {\n    const change = (Math.random() - 0.5) * 2 * volatility\n    return basePrice * (1 + change)\n  }\n\n  protected simulateMarketMovement(): void {\n    // Simulate realistic market movements for game purposes\n    this.marketData.forEach((price, symbol) => {\n      const volatility = this.getSymbolVolatility(symbol)\n      const newPrice = this.generateRandomPrice(price, volatility)\n      this.marketData.set(symbol, newPrice)\n    })\n  }\n\n  protected getSymbolVolatility(symbol: string): number {\n    if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto\n    if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks\n    if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex\n    return 0.02 // Default 2%\n  }\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM;IACD,OAAkB;IAClB,MAAgB;IAChB,UAAiB;IACjB,QAAe;IACf,WAAoB,MAAK;IACzB,aAAkC,IAAI,MAAK;IAErD,YAAY,QAAkB,EAAE,UAAoD,CAAE;QACpF,MAAM,aAAa,0HAAA,CAAA,eAAY,CAAC,SAAS;QAEzC,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM;YACN;YACA,kBAAkB,WAAW,gBAAgB;YAC7C,kBAAkB,WAAW,gBAAgB;YAC7C,iBAAiB,EAAE;YACnB,eAAe,CAAC;QAClB;QAEA,IAAI,CAAC,KAAK,GAAG;YACX,YAAY,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;YAC5B,iBAAiB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC7C,WAAW,EAAE;YACb,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC5C,OAAO;YACP,YAAY,0HAAA,CAAA,yBAAsB,CAAC,WAAW;QAChD;QAEA,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG;IAClE;IAEA,mDAAmD;IACnD,MAAM,aAA4B;IAChC,6CAA6C;IAC/C;IAEA,SAAe;IACb,6CAA6C;IAC/C;IAEA,iBAAyB;QACvB,6CAA6C;QAC7C,OAAO;IACT;IAEA,sBAA2B;QACzB,6CAA6C;QAC7C,OAAO,CAAC;IACV;IAEA,gCAAgC;IAChC,MAAM,QAAuB;QAC3B,MAAM,IAAI,CAAC,UAAU;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,QAAc;QACZ,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,SAAe;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,MAAiB;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;QACtC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;QAC5B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAc,EAAE,IAAoB,EAAE,QAAgB,EAAoB;QAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,aAAa,eAAe;QAClC,MAAM,kBAAkB,SAAS,QAAQ,aAAa;QAEtD,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,iBAAiB;YAChD,OAAO,MAAM,uBAAuB;;QACtC;QAEA,wBAAwB;QACxB,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS;YACpF,OAAO,MAAM,qBAAqB;;QACpC;QAEA,oBAAoB;QACpB,MAAM,WAAqB;YACzB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE;YACA;YACA;YACA,aAAa;YACb,eAAe;YACf,KAAK;YACL,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC;QAEA,yBAAyB;QACzB,MAAM,wBAAwB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAC/E,IAAI,yBAAyB,GAAG;YAC9B,qEAAqE;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG;QAChD,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QAC5B;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnE,IAAI,kBAAkB,CAAC,GAAG,OAAO;QAEjC,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;QACpD,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;QACxD,IAAI,CAAC,cAAc,OAAO;QAE1B,sBAAsB;QACtB,MAAM,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,WAAW,EAAE,cAAc,SAAS,QAAQ,EAAE,SAAS,IAAI;QAE7F,0BAA0B;QAC1B,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAC9B,IAAI,SAAS,IAAI,KAAK,QAAQ;YAC5B,qDAAqD;YACrD,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,SAAS,WAAW,GAAG,SAAS,QAAQ;QACxE;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe;QAE3C,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YAEnE,oFAAoF;YACpF,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,cAAc,CAAC;YAC9D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAC5D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAE5D,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3D,cAAc,MAAM,GAAG,IAAI,2IAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,iBAAiB,EAAE;gBAChF,aAAa,MAAM,GAAG,IAAI,2IAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;gBAC7E,aAAa,MAAM,GAAG,IAAI,2IAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;aAC9E;YAED,yBAAyB;YACzB,MAAM,UAAU,WAAW,MAAM,CAAC,WAAW,MAAM,CAAC;YACpD,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,KAAK;YAC7C;YAEA,sBAAsB;YACtB,IAAI,CAAC,iBAAiB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,qBAAqB;IACrB,WAAsB;QACpB,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK;QAAC;IACzB;IAEA,YAAwB;QACtB,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,eAAwB;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;IACtD;IAEA,mBAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC3B,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG;QACrD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC,YAAY;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc;IAClC;IAEA,2BAA2B;IACjB,gBAAsB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,MAAM,WAAW;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAEpB,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,gBAAgB;YAErB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,GAAG;gBAClC,IAAI,CAAC,GAAG;gBACR;YACF;YAEA,WAAW,UAAU,MAAM,sBAAsB;;QACnD;QAEA;IACF;IAEU,oBAA0B;QAClC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAC3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;YACxD,IAAI,cAAc;gBAChB,SAAS,aAAa,GAAG;gBACzB,SAAS,GAAG,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EACxB,SAAS,WAAW,EACpB,cACA,SAAS,QAAQ,EACjB,SAAS,IAAI;YAEjB;QACF;IACF;IAEU,cAAsB;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,GAAG,EAAE;IAChF;IAEU,kBAA0B;QAClC,MAAM,aAAa,0HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjD,OAAO,AAAC,WAAmB,aAAa,IAAI;IAC9C;IAEU,oBAAoB,MAAc,EAAW;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IACrD;IAEU,eAAe,MAAc,EAAW;QAChD,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC;IACtD;IAEU,cAAc,MAAc,EAAW;QAC/C,MAAM,eAAe;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;QAC9E,OAAO,aAAa,QAAQ,CAAC;IAC/B;IAEU,cAAc,MAAc,EAAW;QAC/C,OAAO,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC;IACvG;IAEU,oBAAoB,SAAiB,EAAE,aAAqB,IAAI,EAAU;QAClF,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI;QAC3C,OAAO,YAAY,CAAC,IAAI,MAAM;IAChC;IAEU,yBAA+B;QACvC,wDAAwD;QACxD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAC5C,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC,OAAO;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC9B;IACF;IAEU,oBAAoB,MAAc,EAAU;QACpD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACxE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACvE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,0BAA0B;;QACtE,OAAO,KAAK,aAAa;;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/games/scalper-sprint.ts"], "sourcesContent": ["import { BaseGame } from '../base-game'\nimport { GameType, TradingPair } from '@/types'\nimport { TRADING_PAIRS } from '@/lib/constants'\n\ninterface ScalperSprintData {\n  trades_executed: number\n  successful_trades: number\n  largest_gain: number\n  largest_loss: number\n  average_hold_time: number\n  speed_bonus: number\n}\n\nexport class ScalperSprintGame extends BaseGame {\n  private gameData: ScalperSprintData\n  private tradeHistory: Array<{\n    timestamp: number\n    symbol: string\n    side: 'buy' | 'sell'\n    entry_price: number\n    exit_price?: number\n    hold_time?: number\n    pnl?: number\n  }> = []\n\n  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    super('scalper_sprint', difficulty)\n    \n    this.gameData = {\n      trades_executed: 0,\n      successful_trades: 0,\n      largest_gain: 0,\n      largest_loss: 0,\n      average_hold_time: 0,\n      speed_bonus: 0,\n    }\n\n    // Set available trading pairs for scalping (high volatility pairs)\n    this.config.available_pairs = this.getScalpingPairs(difficulty)\n  }\n\n  async initialize(): Promise<void> {\n    // Initialize market data with realistic scalping prices\n    const initialPrices = this.generateInitialPrices()\n    initialPrices.forEach((price, symbol) => {\n      this.marketData.set(symbol, price)\n    })\n\n    // Start market data updates more frequently for scalping\n    this.startMarketDataUpdates()\n  }\n\n  update(): void {\n    // Update market data with high frequency for scalping simulation\n    this.simulateScalpingMarketMovement()\n    this.updatePositionPnL()\n    \n    // Check for auto-close conditions (stop loss, take profit)\n    this.checkAutoCloseConditions()\n    \n    // Update game-specific metrics\n    this.updateGameMetrics()\n  }\n\n  calculateScore(): number {\n    const totalPnL = this.getTotalPnL()\n    const balanceChange = this.state.current_balance - this.config.starting_balance + totalPnL\n    const balanceChangePercentage = (balanceChange / this.config.starting_balance) * 100\n\n    // Base score from P&L percentage\n    let score = Math.max(0, balanceChangePercentage * 10)\n\n    // Bonus for number of successful trades\n    const successRate = this.gameData.trades_executed > 0 \n      ? this.gameData.successful_trades / this.gameData.trades_executed \n      : 0\n    score += successRate * 50\n\n    // Speed bonus for quick decision making\n    score += this.gameData.speed_bonus\n\n    // Penalty for holding positions too long (this is scalping!)\n    const avgHoldTimePenalty = Math.max(0, (this.gameData.average_hold_time - 10) * 2)\n    score -= avgHoldTimePenalty\n\n    // Difficulty multiplier\n    score *= this.state.multiplier\n\n    return Math.round(Math.max(0, score))\n  }\n\n  getGameSpecificData(): ScalperSprintData {\n    return { ...this.gameData }\n  }\n\n  // Override trade execution to add scalping-specific logic\n  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {\n    const success = await super.executeTrade(symbol, side, quantity)\n    \n    if (success) {\n      this.gameData.trades_executed++\n      \n      // Record trade for analytics\n      this.tradeHistory.push({\n        timestamp: Date.now(),\n        symbol,\n        side,\n        entry_price: this.marketData.get(symbol)!,\n      })\n\n      // Speed bonus for quick trades\n      const timeSinceStart = (Date.now() - this.startTime) / 1000\n      if (timeSinceStart < 10) {\n        this.gameData.speed_bonus += 5\n      }\n    }\n\n    return success\n  }\n\n  // Override position closing to track scalping metrics\n  async closePosition(positionId: string): Promise<boolean> {\n    const position = this.state.positions.find(p => p.id === positionId)\n    if (!position) return false\n\n    const success = await super.closePosition(positionId)\n    \n    if (success && position) {\n      const tradeRecord = this.tradeHistory.find(t => \n        t.symbol === position.symbol && \n        t.entry_price === position.entry_price &&\n        !t.exit_price\n      )\n\n      if (tradeRecord) {\n        const holdTime = (Date.now() - tradeRecord.timestamp) / 1000\n        const exitPrice = this.marketData.get(position.symbol)!\n        const pnl = position.pnl\n\n        // Update trade record\n        tradeRecord.exit_price = exitPrice\n        tradeRecord.hold_time = holdTime\n        tradeRecord.pnl = pnl\n\n        // Update game metrics\n        if (pnl > 0) {\n          this.gameData.successful_trades++\n          this.gameData.largest_gain = Math.max(this.gameData.largest_gain, pnl)\n        } else {\n          this.gameData.largest_loss = Math.min(this.gameData.largest_loss, pnl)\n        }\n\n        this.updateAverageHoldTime()\n      }\n    }\n\n    return success\n  }\n\n  private getScalpingPairs(difficulty: 'beginner' | 'intermediate' | 'advanced'): TradingPair[] {\n    const allPairs = TRADING_PAIRS\n    \n    switch (difficulty) {\n      case 'beginner':\n        // Major crypto pairs with high liquidity\n        return allPairs.filter(pair => \n          ['BTCUSD', 'ETHUSD'].includes(pair.symbol)\n        )\n      case 'intermediate':\n        // Add some altcoins and major stocks\n        return allPairs.filter(pair => \n          ['BTCUSD', 'ETHUSD', 'ADAUSD', 'AAPL', 'GOOGL'].includes(pair.symbol)\n        )\n      case 'advanced':\n        // All available pairs including forex\n        return allPairs\n      default:\n        return allPairs.slice(0, 3)\n    }\n  }\n\n  private generateInitialPrices(): Map<string, number> {\n    const prices = new Map<string, number>()\n    \n    // Realistic starting prices for scalping simulation\n    const basePrices: Record<string, number> = {\n      'BTCUSD': 45000 + (Math.random() - 0.5) * 2000,\n      'ETHUSD': 3000 + (Math.random() - 0.5) * 200,\n      'ADAUSD': 0.5 + (Math.random() - 0.5) * 0.1,\n      'SOLUSD': 100 + (Math.random() - 0.5) * 20,\n      'AAPL': 150 + (Math.random() - 0.5) * 10,\n      'GOOGL': 2500 + (Math.random() - 0.5) * 100,\n      'TSLA': 800 + (Math.random() - 0.5) * 50,\n      'EURUSD': 1.1 + (Math.random() - 0.5) * 0.02,\n      'GBPUSD': 1.3 + (Math.random() - 0.5) * 0.02,\n      'JPYUSD': 0.009 + (Math.random() - 0.5) * 0.0002,\n    }\n\n    this.config.available_pairs.forEach(pair => {\n      prices.set(pair.symbol, basePrices[pair.symbol] || 100)\n    })\n\n    return prices\n  }\n\n  private startMarketDataUpdates(): void {\n    // Update market data every 2 seconds for realistic scalping\n    const updateInterval = setInterval(() => {\n      if (!this.isActive) {\n        clearInterval(updateInterval)\n        return\n      }\n      this.simulateScalpingMarketMovement()\n    }, 2000)\n  }\n\n  private simulateScalpingMarketMovement(): void {\n    // Simulate high-frequency price movements typical in scalping\n    this.marketData.forEach((price, symbol) => {\n      // Higher volatility and more frequent small movements\n      const volatility = this.getScalpingVolatility(symbol)\n      const direction = Math.random() > 0.5 ? 1 : -1\n      const change = direction * Math.random() * volatility * price\n      \n      // Add some momentum (trending behavior)\n      const momentum = this.calculateMomentum(symbol)\n      const newPrice = price + change + momentum\n      \n      this.marketData.set(symbol, Math.max(0.01, newPrice))\n    })\n  }\n\n  private getScalpingVolatility(symbol: string): number {\n    // Higher volatility for scalping simulation\n    if (this.isCryptoSymbol(symbol)) return 0.008 // 0.8% per update\n    if (this.isStockSymbol(symbol)) return 0.003 // 0.3% per update\n    if (this.isForexSymbol(symbol)) return 0.001 // 0.1% per update\n    return 0.005\n  }\n\n  private calculateMomentum(symbol: string): number {\n    // Simple momentum calculation based on recent price history\n    // In a real implementation, this would use actual price history\n    return (Math.random() - 0.5) * 0.001 * (this.marketData.get(symbol) || 0)\n  }\n\n  private checkAutoCloseConditions(): void {\n    // Auto-close positions that hit stop loss or take profit levels\n    this.state.positions.forEach(position => {\n      const currentPrice = position.current_price\n      const entryPrice = position.entry_price\n      const pnlPercentage = (position.pnl / (entryPrice * position.quantity)) * 100\n\n      // Auto-close on 5% loss (stop loss) or 3% gain (take profit) for scalping\n      if (pnlPercentage <= -5 || pnlPercentage >= 3) {\n        this.closePosition(position.id)\n      }\n    })\n  }\n\n  private updateGameMetrics(): void {\n    // Update average hold time\n    this.updateAverageHoldTime()\n    \n    // Update speed bonus based on quick decision making\n    const recentTrades = this.tradeHistory.filter(t => \n      Date.now() - t.timestamp < 5000 // Last 5 seconds\n    )\n    \n    if (recentTrades.length >= 2) {\n      this.gameData.speed_bonus += 2 // Bonus for rapid trading\n    }\n  }\n\n  private updateAverageHoldTime(): void {\n    const completedTrades = this.tradeHistory.filter(t => t.hold_time !== undefined)\n    if (completedTrades.length > 0) {\n      const totalHoldTime = completedTrades.reduce((sum, trade) => sum + (trade.hold_time || 0), 0)\n      this.gameData.average_hold_time = totalHoldTime / completedTrades.length\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAWO,MAAM,0BAA0B,+IAAA,CAAA,WAAQ;IACrC,SAA2B;IAC3B,eAQH,EAAE,CAAA;IAEP,YAAY,UAAoD,CAAE;QAChE,KAAK,CAAC,kBAAkB;QAExB,IAAI,CAAC,QAAQ,GAAG;YACd,iBAAiB;YACjB,mBAAmB;YACnB,cAAc;YACd,cAAc;YACd,mBAAmB;YACnB,aAAa;QACf;QAEA,mEAAmE;QACnE,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;IACtD;IAEA,MAAM,aAA4B;QAChC,wDAAwD;QACxD,MAAM,gBAAgB,IAAI,CAAC,qBAAqB;QAChD,cAAc,OAAO,CAAC,CAAC,OAAO;YAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC9B;QAEA,yDAAyD;QACzD,IAAI,CAAC,sBAAsB;IAC7B;IAEA,SAAe;QACb,iEAAiE;QACjE,IAAI,CAAC,8BAA8B;QACnC,IAAI,CAAC,iBAAiB;QAEtB,2DAA2D;QAC3D,IAAI,CAAC,wBAAwB;QAE7B,+BAA+B;QAC/B,IAAI,CAAC,iBAAiB;IACxB;IAEA,iBAAyB;QACvB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG;QAClF,MAAM,0BAA0B,AAAC,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAI;QAEjF,iCAAiC;QACjC,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,0BAA0B;QAElD,wCAAwC;QACxC,MAAM,cAAc,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,IAChD,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,GAC/D;QACJ,SAAS,cAAc;QAEvB,wCAAwC;QACxC,SAAS,IAAI,CAAC,QAAQ,CAAC,WAAW;QAElC,6DAA6D;QAC7D,MAAM,qBAAqB,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,EAAE,IAAI;QAChF,SAAS;QAET,wBAAwB;QACxB,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU;QAE9B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG;IAChC;IAEA,sBAAyC;QACvC,OAAO;YAAE,GAAG,IAAI,CAAC,QAAQ;QAAC;IAC5B;IAEA,0DAA0D;IAC1D,MAAM,aAAa,MAAc,EAAE,IAAoB,EAAE,QAAgB,EAAoB;QAC3F,MAAM,UAAU,MAAM,KAAK,CAAC,aAAa,QAAQ,MAAM;QAEvD,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,eAAe;YAE7B,6BAA6B;YAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACrB,WAAW,KAAK,GAAG;gBACnB;gBACA;gBACA,aAAa,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YACnC;YAEA,+BAA+B;YAC/B,MAAM,iBAAiB,CAAC,KAAK,GAAG,KAAK,IAAI,CAAC,SAAS,IAAI;YACvD,IAAI,iBAAiB,IAAI;gBACvB,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;YAC/B;QACF;QAEA,OAAO;IACT;IAEA,sDAAsD;IACtD,MAAM,cAAc,UAAkB,EAAoB;QACxD,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACzD,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,UAAU,MAAM,KAAK,CAAC,cAAc;QAE1C,IAAI,WAAW,UAAU;YACvB,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA,IACzC,EAAE,MAAM,KAAK,SAAS,MAAM,IAC5B,EAAE,WAAW,KAAK,SAAS,WAAW,IACtC,CAAC,EAAE,UAAU;YAGf,IAAI,aAAa;gBACf,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,YAAY,SAAS,IAAI;gBACxD,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;gBACrD,MAAM,MAAM,SAAS,GAAG;gBAExB,sBAAsB;gBACtB,YAAY,UAAU,GAAG;gBACzB,YAAY,SAAS,GAAG;gBACxB,YAAY,GAAG,GAAG;gBAElB,sBAAsB;gBACtB,IAAI,MAAM,GAAG;oBACX,IAAI,CAAC,QAAQ,CAAC,iBAAiB;oBAC/B,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBACpE,OAAO;oBACL,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;gBACpE;gBAEA,IAAI,CAAC,qBAAqB;YAC5B;QACF;QAEA,OAAO;IACT;IAEQ,iBAAiB,UAAoD,EAAiB;QAC5F,MAAM,WAAW,0HAAA,CAAA,gBAAa;QAE9B,OAAQ;YACN,KAAK;gBACH,yCAAyC;gBACzC,OAAO,SAAS,MAAM,CAAC,CAAA,OACrB;wBAAC;wBAAU;qBAAS,CAAC,QAAQ,CAAC,KAAK,MAAM;YAE7C,KAAK;gBACH,qCAAqC;gBACrC,OAAO,SAAS,MAAM,CAAC,CAAA,OACrB;wBAAC;wBAAU;wBAAU;wBAAU;wBAAQ;qBAAQ,CAAC,QAAQ,CAAC,KAAK,MAAM;YAExE,KAAK;gBACH,sCAAsC;gBACtC,OAAO;YACT;gBACE,OAAO,SAAS,KAAK,CAAC,GAAG;QAC7B;IACF;IAEQ,wBAA6C;QACnD,MAAM,SAAS,IAAI;QAEnB,oDAAoD;QACpD,MAAM,aAAqC;YACzC,UAAU,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YAC1C,UAAU,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACzC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,QAAQ,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACtC,SAAS,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,QAAQ,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACtC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,UAAU,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACxC,UAAU,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;QAC5C;QAEA,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAA;YAClC,OAAO,GAAG,CAAC,KAAK,MAAM,EAAE,UAAU,CAAC,KAAK,MAAM,CAAC,IAAI;QACrD;QAEA,OAAO;IACT;IAEQ,yBAA+B;QACrC,4DAA4D;QAC5D,MAAM,iBAAiB,YAAY;YACjC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,cAAc;gBACd;YACF;YACA,IAAI,CAAC,8BAA8B;QACrC,GAAG;IACL;IAEQ,iCAAuC;QAC7C,8DAA8D;QAC9D,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,sDAAsD;YACtD,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;YAC9C,MAAM,YAAY,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;YAC7C,MAAM,SAAS,YAAY,KAAK,MAAM,KAAK,aAAa;YAExD,wCAAwC;YACxC,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC;YACxC,MAAM,WAAW,QAAQ,SAAS;YAElC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM;QAC7C;IACF;IAEQ,sBAAsB,MAAc,EAAU;QACpD,4CAA4C;QAC5C,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,MAAM,kBAAkB;;QAChE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,MAAM,kBAAkB;;QAC/D,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,MAAM,kBAAkB;;QAC/D,OAAO;IACT;IAEQ,kBAAkB,MAAc,EAAU;QAChD,4DAA4D;QAC5D,gEAAgE;QAChE,OAAO,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC;IAC1E;IAEQ,2BAAiC;QACvC,gEAAgE;QAChE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAC3B,MAAM,eAAe,SAAS,aAAa;YAC3C,MAAM,aAAa,SAAS,WAAW;YACvC,MAAM,gBAAgB,AAAC,SAAS,GAAG,GAAG,CAAC,aAAa,SAAS,QAAQ,IAAK;YAE1E,0EAA0E;YAC1E,IAAI,iBAAiB,CAAC,KAAK,iBAAiB,GAAG;gBAC7C,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;YAChC;QACF;IACF;IAEQ,oBAA0B;QAChC,2BAA2B;QAC3B,IAAI,CAAC,qBAAqB;QAE1B,oDAAoD;QACpD,MAAM,eAAe,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAC5C,KAAK,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK,iBAAiB;;QAGnD,IAAI,aAAa,MAAM,IAAI,GAAG;YAC5B,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE,0BAA0B;;QAC3D;IACF;IAEQ,wBAA8B;QACpC,MAAM,kBAAkB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACtE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,gBAAgB,gBAAgB,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,CAAC,MAAM,SAAS,IAAI,CAAC,GAAG;YAC3F,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,gBAAgB,gBAAgB,MAAM;QAC1E;IACF;AACF", "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport { ScalperSprintGame } from '@/lib/game-engine/games/scalper-sprint'\n\nexport default function Home() {\n  const { interfaceMode, switchInterfaceMode } = useUserStore()\n  const [currentGame, setCurrentGame] = useState<ScalperSprintGame | null>(null)\n  const [gameState, setGameState] = useState<any>(null)\n\n  const startScalperSprint = async () => {\n    const game = new ScalperSprintGame('beginner')\n    setCurrentGame(game)\n\n    await game.start()\n\n    // Update game state every second\n    const interval = setInterval(() => {\n      if (game.isGameActive()) {\n        setGameState(game.getState())\n      } else {\n        clearInterval(interval)\n        setCurrentGame(null)\n        setGameState(null)\n      }\n    }, 1000)\n  }\n\n  const executeTrade = async (symbol: string, side: 'buy' | 'sell') => {\n    if (currentGame) {\n      await currentGame.executeTrade(symbol, side, 1)\n      setGameState(currentGame.getState())\n    }\n  }\n\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  return (\n    <div className={`min-h-screen ${isAdolescentMode\n      ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500'\n      : 'bg-gray-900 text-green-400 font-mono'\n    }`}>\n      {/* Header */}\n      <header className={`p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`}>\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <h1 className={`text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'}\n          </h1>\n\n          <div className=\"flex items-center gap-4\">\n            <button\n              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}\n              className={`px-4 py-2 rounded-lg transition-colors ${\n                isAdolescentMode\n                  ? 'bg-white/20 hover:bg-white/30 text-white'\n                  : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'\n              }`}\n            >\n              Switch to {isAdolescentMode ? 'Professional' : 'Adventure'} Mode\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto p-6\">\n        {/* Welcome Section */}\n        <section className={`mb-8 p-6 rounded-lg ${\n          isAdolescentMode\n            ? 'bg-white/10 backdrop-blur-sm text-white'\n            : 'bg-gray-800 border border-green-400'\n        }`}>\n          <h2 className={`text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'}\n          </h2>\n          <p className={`text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n            {isAdolescentMode\n              ? 'Embark on epic trading adventures and master the markets through exciting mini-games!'\n              : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'\n            }\n          </p>\n        </section>\n\n        {/* Game Demo Section */}\n        <section className={`mb-8 p-6 rounded-lg ${\n          isAdolescentMode\n            ? 'bg-white/10 backdrop-blur-sm'\n            : 'bg-gray-800 border border-green-400'\n        }`}>\n          <h3 className={`text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {isAdolescentMode ? '⚡ Scalper Sprint Challenge' : '📈 High-Frequency Trading Simulation'}\n          </h3>\n\n          {!currentGame ? (\n            <div>\n              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                {isAdolescentMode\n                  ? 'Test your speed and reflexes in this 60-second trading challenge!'\n                  : 'Execute rapid trades in a simulated high-frequency environment.'\n                }\n              </p>\n              <button\n                onClick={startScalperSprint}\n                className={`px-6 py-3 rounded-lg font-bold transition-colors ${\n                  isAdolescentMode\n                    ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'\n                    : 'bg-green-400 text-gray-900 hover:bg-green-300'\n                }`}\n              >\n                {isAdolescentMode ? '🚀 Start Adventure!' : 'INITIALIZE TRADING SESSION'}\n              </button>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {/* Game State Display */}\n              {gameState && (\n                <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 p-4 rounded ${\n                  isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'\n                }`}>\n                  <div className=\"text-center\">\n                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                      {isAdolescentMode ? 'Quest Coins' : 'BALANCE'}\n                    </div>\n                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n                      ${gameState.current_balance.toFixed(2)}\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                      {isAdolescentMode ? 'Score' : 'SCORE'}\n                    </div>\n                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                      {gameState.score}\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                      {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}\n                    </div>\n                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>\n                      {gameState.time_remaining}s\n                    </div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n                      {isAdolescentMode ? 'Positions' : 'POSITIONS'}\n                    </div>\n                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                      {gameState.positions.length}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Trading Controls */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <button\n                  onClick={() => executeTrade('BTCUSD', 'buy')}\n                  className={`p-4 rounded-lg font-bold transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-green-500 hover:bg-green-600 text-white'\n                      : 'bg-green-400 hover:bg-green-300 text-gray-900'\n                  }`}\n                >\n                  {isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'}\n                </button>\n                <button\n                  onClick={() => executeTrade('BTCUSD', 'sell')}\n                  className={`p-4 rounded-lg font-bold transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-red-500 hover:bg-red-600 text-white'\n                      : 'bg-red-400 hover:bg-red-300 text-gray-900'\n                  }`}\n                >\n                  {isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'}\n                </button>\n              </div>\n            </div>\n          )}\n        </section>\n\n        {/* Features Preview */}\n        <section className=\"grid md:grid-cols-3 gap-6\">\n          {[\n            {\n              title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',\n              description: isAdolescentMode\n                ? 'Six exciting games to master different trading skills'\n                : 'Comprehensive trading simulation modules',\n              features: ['Scalper Sprint', 'CandleStrike', 'ChainMaze']\n            },\n            {\n              title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',\n              description: isAdolescentMode\n                ? 'Unlock badges and level up your trading hero'\n                : 'Advanced performance tracking and analytics',\n              features: ['Progress Tracking', 'Leaderboards', 'Statistics']\n            },\n            {\n              title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',\n              description: isAdolescentMode\n                ? 'Join guilds and compete with friends'\n                : 'Professional networking and strategy sharing',\n              features: ['Team Challenges', 'Social Features', 'Competitions']\n            }\n          ].map((feature, index) => (\n            <div\n              key={index}\n              className={`p-6 rounded-lg ${\n                isAdolescentMode\n                  ? 'bg-white/10 backdrop-blur-sm text-white'\n                  : 'bg-gray-800 border border-green-400'\n              }`}\n            >\n              <h3 className={`text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                {feature.title}\n              </h3>\n              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                {feature.description}\n              </p>\n              <ul className={`space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`}>\n                {feature.features.map((item, i) => (\n                  <li key={i} className=\"flex items-center\">\n                    <span className={`mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n                      {isAdolescentMode ? '✨' : '▶'}\n                    </span>\n                    {item}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </section>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEhD,MAAM,qBAAqB;QACzB,MAAM,OAAO,IAAI,6JAAA,CAAA,oBAAiB,CAAC;QACnC,eAAe;QAEf,MAAM,KAAK,KAAK;QAEhB,iCAAiC;QACjC,MAAM,WAAW,YAAY;YAC3B,IAAI,KAAK,YAAY,IAAI;gBACvB,aAAa,KAAK,QAAQ;YAC5B,OAAO;gBACL,cAAc;gBACd,eAAe;gBACf,aAAa;YACf;QACF,GAAG;IACL;IAEA,MAAM,eAAe,OAAO,QAAgB;QAC1C,IAAI,aAAa;YACf,MAAM,YAAY,YAAY,CAAC,QAAQ,MAAM;YAC7C,aAAa,YAAY,QAAQ;QACnC;IACF;IAEA,MAAM,mBAAmB,kBAAkB;IAE3C,qBACE,6LAAC;QAAI,WAAW,CAAC,aAAa,EAAE,mBAC5B,+DACA,wCACF;;0BAEA,6LAAC;gBAAO,WAAW,CAAC,IAAI,EAAE,mBAAmB,eAAe,6BAA6B;0BACvF,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,eAAe,kBAAkB;sCACtF,mBAAmB,kCAAkC;;;;;;sCAGxD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,oBAAoB,mBAAmB,UAAU;gCAChE,WAAW,CAAC,uCAAuC,EACjD,mBACI,6CACA,gFACJ;;oCACH;oCACY,mBAAmB,iBAAiB;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAOnE,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAQ,WAAW,CAAC,oBAAoB,EACvC,mBACI,4CACA,uCACJ;;0CACA,6LAAC;gCAAG,WAAW,CAAC,wBAAwB,EAAE,mBAAmB,eAAe,kBAAkB;0CAC3F,mBAAmB,8BAA8B;;;;;;0CAEpD,6LAAC;gCAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC7E,mBACG,0FACA;;;;;;;;;;;;kCAMR,6LAAC;wBAAQ,WAAW,CAAC,oBAAoB,EACvC,mBACI,iCACA,uCACJ;;0CACA,6LAAC;gCAAG,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,eAAe,kBAAkB;0CAC1F,mBAAmB,+BAA+B;;;;;;4BAGpD,CAAC,4BACA,6LAAC;;kDACC,6LAAC;wCAAE,WAAW,CAAC,KAAK,EAAE,mBAAmB,kBAAkB,kBAAkB;kDAC1E,mBACG,sEACA;;;;;;kDAGN,6LAAC;wCACC,SAAS;wCACT,WAAW,CAAC,iDAAiD,EAC3D,mBACI,wGACA,iDACJ;kDAED,mBAAmB,wBAAwB;;;;;;;;;;;qDAIhD,6LAAC;gCAAI,WAAU;;oCAEZ,2BACC,6LAAC;wCAAI,WAAW,CAAC,kDAAkD,EACjE,mBAAmB,gBAAgB,uCACnC;;0DACA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAC/E,mBAAmB,gBAAgB;;;;;;kEAEtC,6LAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,kBAAkB;;4DAAE;4DAC5F,UAAU,eAAe,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAGxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAC/E,mBAAmB,UAAU;;;;;;kEAEhC,6LAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;kEACtF,UAAU,KAAK;;;;;;;;;;;;0DAGpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAC/E,mBAAmB,cAAc;;;;;;kEAEpC,6LAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,iBAAiB,gBAAgB;;4DACtF,UAAU,cAAc;4DAAC;;;;;;;;;;;;;0DAG9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kEAC/E,mBAAmB,cAAc;;;;;;kEAEpC,6LAAC;wDAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;kEACtF,UAAU,SAAS,CAAC,MAAM;;;;;;;;;;;;;;;;;;kDAOnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,aAAa,UAAU;gDACtC,WAAW,CAAC,2CAA2C,EACrD,mBACI,+CACA,iDACJ;0DAED,mBAAmB,mBAAmB;;;;;;0DAEzC,6LAAC;gDACC,SAAS,IAAM,aAAa,UAAU;gDACtC,WAAW,CAAC,2CAA2C,EACrD,mBACI,2CACA,6CACJ;0DAED,mBAAmB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;kCAQlD,6LAAC;wBAAQ,WAAU;kCAChB;4BACC;gCACE,OAAO,mBAAmB,kBAAkB;gCAC5C,aAAa,mBACT,0DACA;gCACJ,UAAU;oCAAC;oCAAkB;oCAAgB;iCAAY;4BAC3D;4BACA;gCACE,OAAO,mBAAmB,oBAAoB;gCAC9C,aAAa,mBACT,iDACA;gCACJ,UAAU;oCAAC;oCAAqB;oCAAgB;iCAAa;4BAC/D;4BACA;gCACE,OAAO,mBAAmB,cAAc;gCACxC,aAAa,mBACT,yCACA;gCACJ,UAAU;oCAAC;oCAAmB;oCAAmB;iCAAe;4BAClE;yBACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;gCAEC,WAAW,CAAC,eAAe,EACzB,mBACI,4CACA,uCACJ;;kDAEF,6LAAC;wCAAG,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,eAAe,kBAAkB;kDAC1F,QAAQ,KAAK;;;;;;kDAEhB,6LAAC;wCAAE,WAAW,CAAC,KAAK,EAAE,mBAAmB,kBAAkB,kBAAkB;kDAC1E,QAAQ,WAAW;;;;;;kDAEtB,6LAAC;wCAAG,WAAW,CAAC,UAAU,EAAE,mBAAmB,kBAAkB,kBAAkB;kDAChF,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,kBAC3B,6LAAC;gDAAW,WAAU;;kEACpB,6LAAC;wDAAK,WAAW,CAAC,KAAK,EAAE,mBAAmB,oBAAoB,kBAAkB;kEAC/E,mBAAmB,MAAM;;;;;;oDAE3B;;+CAJM;;;;;;;;;;;+BAfR;;;;;;;;;;;;;;;;;;;;;;AA6BnB;GAvOwB;;QACyB,wIAAA,CAAA,eAAY;;;KADrC", "debugId": null}}]}