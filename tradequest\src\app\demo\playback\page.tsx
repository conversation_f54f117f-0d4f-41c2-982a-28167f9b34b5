'use client'

import { useState } from 'react'
// import PlaybackChart from '@/components/charts/playback-chart'
import CandleStrikeGameComponent from '@/components/games/candle-strike-game'
import ThemeSelector from '@/components/theme/theme-selector'
import { useUserStore } from '@/lib/stores/user-store'
import { useThemeColors } from '@/components/theme/theme-provider'
import Link from 'next/link'

export default function PlaybackDemoPage() {
  const { interfaceMode, switchInterfaceMode } = useUserStore()
  const [activeDemo, setActiveDemo] = useState<'playback' | 'candle-strike' | 'themes'>('playback')
  const [symbol, setSymbol] = useState('BTCUSD')
  const [timeframe, setTimeframe] = useState('1h')
  const colors = useThemeColors()
  
  const isAdolescentMode = interfaceMode === 'adolescent'

  const symbols = ['BTCUSD', 'ETHUSD', 'AAPL', 'GOOGL', 'TSLA']
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']

  const handlePatternDetected = (pattern: any) => {
    console.log('Pattern detected:', pattern)
    // You could show a notification here
  }

  const handlePredictionChallenge = (challenge: any) => {
    console.log('Prediction challenge:', challenge)
    // Handle prediction challenge
  }

  const handleMarketEvent = (event: any) => {
    console.log('Market event:', event)
    // Handle market event
  }

  const handleCandleStrikeEnd = (score: number) => {
    console.log('CandleStrike ended with score:', score)
  }

  return (
    <div 
      className="min-h-screen"
      style={{ backgroundColor: colors.background }}
    >
      {/* Header */}
      <header className="p-6 border-b" style={{ borderColor: colors.border }}>
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div>
            <Link 
              href="/"
              className="text-sm hover:underline mb-2 block"
              style={{ color: colors.textSecondary }}
            >
              ← {isAdolescentMode ? 'Back to Quest Hub' : 'RETURN_TO_MAIN'}
            </Link>
            <h1 className="text-3xl font-bold" style={{ color: colors.textPrimary }}>
              {isAdolescentMode ? '🚀 Advanced Trading Features Demo' : '📊 ADVANCED_TRADING_SYSTEMS_DEMO'}
            </h1>
            <p className="mt-2" style={{ color: colors.textSecondary }}>
              {isAdolescentMode 
                ? 'Experience the future of trading education with real-time chart playback, pattern recognition, and adaptive themes!'
                : 'Comprehensive demonstration of chart playback systems, pattern recognition algorithms, and evidence-based interface themes.'
              }
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}
              className="px-4 py-2 rounded-lg transition-colors"
              style={{ 
                backgroundColor: colors.backgroundSecondary,
                borderColor: colors.border,
                color: colors.textPrimary
              }}
            >
              {isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'}
            </button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="p-6 border-b" style={{ borderColor: colors.border }}>
        <div className="max-w-7xl mx-auto">
          <div className="flex space-x-4">
            {[
              { id: 'playback', label: isAdolescentMode ? '📈 Chart Playback' : '📈 CHART_PLAYBACK', icon: '📈' },
              { id: 'candle-strike', label: isAdolescentMode ? '🕯️ Pattern Recognition' : '🕯️ PATTERN_RECOGNITION', icon: '🕯️' },
              { id: 'themes', label: isAdolescentMode ? '🎨 Theme System' : '🎨 THEME_SYSTEM', icon: '🎨' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveDemo(tab.id as any)}
                className={`px-6 py-3 rounded-lg font-bold transition-all ${
                  activeDemo === tab.id ? 'shadow-lg' : ''
                }`}
                style={{
                  backgroundColor: activeDemo === tab.id ? colors.primary : colors.backgroundSecondary,
                  color: activeDemo === tab.id ? colors.background : colors.textPrimary,
                  borderColor: colors.border,
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {activeDemo === 'playback' && (
          <div className="space-y-6">
            {/* Controls */}
            <div 
              className="p-6 rounded-lg"
              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
            >
              <h2 className="text-xl font-bold mb-4" style={{ color: colors.textPrimary }}>
                {isAdolescentMode ? '⚙️ Chart Configuration' : '⚙️ CHART_CONFIGURATION'}
              </h2>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: colors.textSecondary }}>
                    {isAdolescentMode ? 'Trading Pair' : 'TRADING_PAIR'}
                  </label>
                  <select
                    value={symbol}
                    onChange={(e) => setSymbol(e.target.value)}
                    className="w-full p-2 rounded border"
                    style={{ 
                      backgroundColor: colors.backgroundTertiary,
                      borderColor: colors.border,
                      color: colors.textPrimary
                    }}
                  >
                    {symbols.map(s => (
                      <option key={s} value={s}>{s}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2" style={{ color: colors.textSecondary }}>
                    {isAdolescentMode ? 'Time Frame' : 'TIMEFRAME'}
                  </label>
                  <select
                    value={timeframe}
                    onChange={(e) => setTimeframe(e.target.value)}
                    className="w-full p-2 rounded border"
                    style={{ 
                      backgroundColor: colors.backgroundTertiary,
                      borderColor: colors.border,
                      color: colors.textPrimary
                    }}
                  >
                    {timeframes.map(tf => (
                      <option key={tf} value={tf}>{tf}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Playback Chart */}
            <div 
              className="p-6 rounded-lg"
              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
            >
              <h2 className="text-xl font-bold mb-4" style={{ color: colors.textPrimary }}>
                {isAdolescentMode ? '📊 Interactive Historical Chart' : '📊 INTERACTIVE_HISTORICAL_CHART'}
              </h2>
              
              <div className="w-full h-96 border rounded-lg flex items-center justify-center" style={{ backgroundColor: colors.backgroundTertiary, borderColor: colors.border }}>
                <div className="text-center">
                  <div className="text-4xl mb-4">📈</div>
                  <p style={{ color: colors.textPrimary }}>
                    {isAdolescentMode ? 'Interactive Chart Coming Soon!' : 'CHART_SYSTEM_UNDER_DEVELOPMENT'}
                  </p>
                  <p style={{ color: colors.textSecondary }} className="text-sm mt-2">
                    Symbol: {symbol} | Timeframe: {timeframe}
                  </p>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div 
              className="p-6 rounded-lg"
              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
            >
              <h3 className="text-lg font-bold mb-4" style={{ color: colors.textPrimary }}>
                {isAdolescentMode ? '✨ Playback Features' : '📋 PLAYBACK_FEATURES'}
              </h3>
              
              <div className="grid md:grid-cols-2 gap-4">
                {[
                  {
                    title: isAdolescentMode ? '⏯️ Time Travel Controls' : '⏯️ TEMPORAL_CONTROLS',
                    description: isAdolescentMode 
                      ? 'Play, pause, and scrub through historical data at different speeds'
                      : 'Play/pause/scrub controls with variable speed adjustment'
                  },
                  {
                    title: isAdolescentMode ? '🔍 Pattern Detection' : '🔍 PATTERN_DETECTION',
                    description: isAdolescentMode 
                      ? 'Real-time identification of candlestick patterns as they form'
                      : 'Real-time candlestick pattern recognition algorithms'
                  },
                  {
                    title: isAdolescentMode ? '🎯 Prediction Challenges' : '🎯 PREDICTION_CHALLENGES',
                    description: isAdolescentMode 
                      ? 'Test your market prediction skills at key moments'
                      : 'Interactive prediction challenges at critical market points'
                  },
                  {
                    title: isAdolescentMode ? '📰 Market Events' : '📰 MARKET_EVENTS',
                    description: isAdolescentMode 
                      ? 'Learn how news and events affect price movements'
                      : 'Historical market events with impact analysis'
                  },
                ].map((feature, index) => (
                  <div key={index} className="p-4 rounded border" style={{ borderColor: colors.border }}>
                    <h4 className="font-bold mb-2" style={{ color: colors.textPrimary }}>
                      {feature.title}
                    </h4>
                    <p className="text-sm" style={{ color: colors.textSecondary }}>
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeDemo === 'candle-strike' && (
          <div className="space-y-6">
            <div 
              className="p-6 rounded-lg"
              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
            >
              <h2 className="text-xl font-bold mb-4" style={{ color: colors.textPrimary }}>
                {isAdolescentMode ? '🕯️ Enhanced Pattern Recognition Game' : '🕯️ ENHANCED_PATTERN_RECOGNITION'}
              </h2>
              
              <CandleStrikeGameComponent
                difficulty="beginner"
                onGameEnd={handleCandleStrikeEnd}
                usePlayback={true}
                className="w-full"
              />
            </div>
          </div>
        )}

        {activeDemo === 'themes' && (
          <div className="space-y-6">
            <div 
              className="p-6 rounded-lg"
              style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}
            >
              <h2 className="text-xl font-bold mb-4" style={{ color: colors.textPrimary }}>
                {isAdolescentMode ? '🎨 Evidence-Based Color Psychology Themes' : '🎨 COLOR_PSYCHOLOGY_THEME_SYSTEM'}
              </h2>
              
              <ThemeSelector showPsychologyInfo={true} />
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
