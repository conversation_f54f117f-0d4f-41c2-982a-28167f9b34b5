// User and Authentication Types
export interface User {
  id: string
  email: string
  username: string
  age?: number
  is_minor: boolean
  interface_mode: 'adolescent' | 'adult'
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface UserProfile extends User {
  total_quest_coins: number
  level: number
  experience_points: number
  achievements: Achievement[]
  guild_id?: string
  preferred_language: string
}

// Game and Progress Types
export interface Achievement {
  id: string
  name: string
  description: string
  icon: string
  category: 'trading' | 'learning' | 'social' | 'special'
  points: number
  unlocked_at?: string
}

export interface GameSession {
  id: string
  user_id: string
  game_type: GameType
  score: number
  quest_coins_earned: number
  duration_seconds: number
  completed_at: string
  difficulty_level: 'beginner' | 'intermediate' | 'advanced'
}

export type GameType = 
  | 'scalper_sprint'
  | 'candle_strike'
  | 'chain_maze'
  | 'swing_trader_odyssey'
  | 'day_trader_arena'
  | 'portfolio_survivor'

// Market Data Types
export interface MarketData {
  symbol: string
  price: number
  change_24h: number
  change_percentage_24h: number
  volume_24h: number
  market_cap?: number
  timestamp: string
}

export interface CandlestickData {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TradingPair {
  base: string
  quote: string
  symbol: string
  exchange: string
}

// Educational Content Types
export interface LearningModule {
  id: string
  title: string
  description: string
  category: 'crypto' | 'stocks' | 'forex' | 'fundamentals'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimated_duration_minutes: number
  content: LearningContent[]
  quiz_questions: QuizQuestion[]
  prerequisites: string[]
}

export interface LearningContent {
  id: string
  type: 'text' | 'video' | 'interactive' | 'simulation'
  title: string
  content: string
  order: number
}

export interface QuizQuestion {
  id: string
  question: string
  options: string[]
  correct_answer: number
  explanation: string
}

// Social and Multiplayer Types
export interface Guild {
  id: string
  name: string
  description: string
  member_count: number
  total_quest_coins: number
  level: number
  created_at: string
  is_public: boolean
}

export interface Leaderboard {
  period: 'daily' | 'weekly' | 'monthly' | 'all_time'
  game_type?: GameType
  entries: LeaderboardEntry[]
}

export interface LeaderboardEntry {
  rank: number
  user_id: string
  username: string
  score: number
  avatar_url?: string
}

// UI and Theme Types
export interface ThemeConfig {
  mode: 'adolescent' | 'adult'
  primary_color: string
  secondary_color: string
  background_style: 'fantasy' | 'professional'
  font_family: string
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
  error?: string
}

// Game Engine Types
export interface GameConfig {
  type: GameType
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  duration_seconds: number
  starting_balance: number
  available_pairs: TradingPair[]
  special_rules?: Record<string, any>
}

export interface GameState {
  session_id: string
  current_balance: number
  positions: Position[]
  time_remaining: number
  score: number
  multiplier: number
}

export interface Position {
  id: string
  symbol: string
  side: 'buy' | 'sell'
  quantity: number
  entry_price: number
  current_price: number
  pnl: number
  timestamp: string
}

// Virtual Currency Types
export interface QuestCoinTransaction {
  id: string
  user_id: string
  amount: number
  type: 'earned' | 'spent' | 'bonus' | 'penalty'
  source: string
  description: string
  created_at: string
}

// Notification Types
export interface Notification {
  id: string
  user_id: string
  type: 'achievement' | 'game_result' | 'social' | 'system'
  title: string
  message: string
  is_read: boolean
  created_at: string
  action_url?: string
}
