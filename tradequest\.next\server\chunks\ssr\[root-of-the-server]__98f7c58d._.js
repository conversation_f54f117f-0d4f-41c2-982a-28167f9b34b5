module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-ssr] (ecmascript)");
;
function createClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createBrowserClient"])(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
}
}}),
"[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useUserStore": (()=>useUserStore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/react.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zustand/esm/middleware.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/client.ts [app-ssr] (ecmascript)");
;
;
;
const defaultThemeConfig = {
    mode: 'adolescent',
    primary_color: '#8B5CF6',
    secondary_color: '#EC4899',
    background_style: 'fantasy',
    font_family: 'fantasy'
};
const useUserStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$react$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["create"])()((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zustand$2f$esm$2f$middleware$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["persist"])((set, get)=>({
        // Initial state
        user: null,
        isAuthenticated: false,
        isLoading: false,
        interfaceMode: 'adolescent',
        themeConfig: defaultThemeConfig,
        currentGameSession: null,
        recentSessions: [],
        // User management actions
        setUser: (user)=>{
            set({
                user,
                isAuthenticated: !!user
            });
            // Update interface mode based on user preference or age
            if (user) {
                const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult');
                get().switchInterfaceMode(mode);
            }
        },
        setAuthenticated: (authenticated)=>set({
                isAuthenticated: authenticated
            }),
        setLoading: (loading)=>set({
                isLoading: loading
            }),
        // Theme and UI actions
        switchInterfaceMode: (mode)=>{
            const newThemeConfig = mode === 'adolescent' ? {
                mode: 'adolescent',
                primary_color: '#8B5CF6',
                secondary_color: '#EC4899',
                background_style: 'fantasy',
                font_family: 'fantasy'
            } : {
                mode: 'adult',
                primary_color: '#1F2937',
                secondary_color: '#3B82F6',
                background_style: 'professional',
                font_family: 'monospace'
            };
            set({
                interfaceMode: mode,
                themeConfig: newThemeConfig
            });
            // Update user preference in database
            const { user } = get();
            if (user) {
                get().updateUserProfile({
                    interface_mode: mode
                });
            }
        },
        updateThemeConfig: (config)=>{
            set((state)=>({
                    themeConfig: {
                        ...state.themeConfig,
                        ...config
                    }
                }));
        },
        // Quest coins management
        addQuestCoins: (amount, source)=>{
            const { user } = get();
            if (!user) return;
            const updatedUser = {
                ...user,
                total_quest_coins: user.total_quest_coins + amount
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd also update the database and create a transaction record
            console.log(`Added ${amount} QuestCoins from ${source}`);
        },
        spendQuestCoins: (amount, purpose)=>{
            const { user } = get();
            if (!user || user.total_quest_coins < amount) {
                return false;
            }
            const updatedUser = {
                ...user,
                total_quest_coins: user.total_quest_coins - amount
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd also update the database and create a transaction record
            console.log(`Spent ${amount} QuestCoins on ${purpose}`);
            return true;
        },
        // Experience and leveling
        addExperience: (points)=>{
            const { user } = get();
            if (!user) return;
            const newExperience = user.experience_points + points;
            const newLevel = calculateLevel(newExperience);
            const leveledUp = newLevel > user.level;
            const updatedUser = {
                ...user,
                experience_points: newExperience,
                level: newLevel
            };
            set({
                user: updatedUser
            });
            if (leveledUp) {
                console.log(`Level up! Now level ${newLevel}`);
            // In a real app, you'd trigger level up effects, notifications, etc.
            }
        },
        // Achievement system
        unlockAchievement: (achievement)=>{
            const { user } = get();
            if (!user) return;
            // Check if achievement is already unlocked
            const alreadyUnlocked = user.achievements.some((a)=>a.id === achievement.id);
            if (alreadyUnlocked) return;
            const updatedUser = {
                ...user,
                achievements: [
                    ...user.achievements,
                    {
                        ...achievement,
                        unlocked_at: new Date().toISOString()
                    }
                ]
            };
            set({
                user: updatedUser
            });
            // Award quest coins for achievement
            get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`);
            console.log(`Achievement unlocked: ${achievement.name}`);
        // In a real app, you'd show a notification, play sound effects, etc.
        },
        // Game session management
        startGameSession: (session)=>{
            set({
                currentGameSession: session
            });
        },
        endGameSession: (session)=>{
            const { recentSessions } = get();
            // Add to recent sessions (keep last 10)
            const updatedSessions = [
                session,
                ...recentSessions
            ].slice(0, 10);
            set({
                currentGameSession: null,
                recentSessions: updatedSessions
            });
            // Award quest coins and experience
            get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`);
            get().addExperience(Math.floor(session.score / 10));
            // Check for achievements
            checkGameAchievements(session, get());
        },
        // Profile updates
        updateUserProfile: (updates)=>{
            const { user } = get();
            if (!user) return;
            const updatedUser = {
                ...user,
                ...updates
            };
            set({
                user: updatedUser
            });
            // In a real app, you'd sync with the database
            console.log('User profile updated:', updates);
        },
        // Cleanup
        clearUserData: ()=>{
            set({
                user: null,
                isAuthenticated: false,
                currentGameSession: null,
                recentSessions: [],
                interfaceMode: 'adolescent',
                themeConfig: defaultThemeConfig
            });
        },
        // Authentication methods
        signOut: async ()=>{
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
            await supabase.auth.signOut();
            get().clearUserData();
        },
        initializeAuth: async ()=>{
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createClient"])();
            // Get initial session
            const { data: { session } } = await supabase.auth.getSession();
            if (session?.user) {
                // Fetch user profile
                const { data: profile } = await supabase.from('user_profiles').select('*').eq('id', session.user.id).single();
                if (profile) {
                    // Fetch achievements
                    const { data: achievements } = await supabase.from('user_achievements').select(`
                achievement_id,
                unlocked_at,
                achievements (
                  id,
                  name,
                  description,
                  icon,
                  category,
                  points
                )
              `).eq('user_id', session.user.id);
                    const userAchievements = achievements?.map((ua)=>({
                            ...ua.achievements,
                            unlocked_at: ua.unlocked_at
                        })) || [];
                    get().setUser({
                        id: profile.id,
                        email: session.user.email,
                        username: profile.username,
                        age: profile.age,
                        is_minor: profile.is_minor,
                        interface_mode: profile.interface_mode,
                        avatar_url: profile.avatar_url,
                        total_quest_coins: profile.total_quest_coins,
                        level: profile.level,
                        experience_points: profile.experience_points,
                        achievements: userAchievements,
                        guild_id: profile.guild_id,
                        preferred_language: profile.preferred_language,
                        created_at: profile.created_at,
                        updated_at: profile.updated_at
                    });
                }
            }
            // Listen for auth changes
            supabase.auth.onAuthStateChange(async (event, session)=>{
                if (event === 'SIGNED_OUT' || !session) {
                    get().clearUserData();
                } else if (event === 'SIGNED_IN' && session?.user) {
                    // Refresh user data when signed in
                    get().initializeAuth();
                }
            });
        }
    }), {
    name: 'tradequest-user-storage',
    partialize: (state)=>({
            user: state.user,
            interfaceMode: state.interfaceMode,
            themeConfig: state.themeConfig,
            recentSessions: state.recentSessions
        })
}));
// Helper functions
function calculateLevel(experience) {
    const LEVEL_THRESHOLDS = [
        0,
        100,
        250,
        500,
        1000,
        1750,
        2750,
        4000,
        5500,
        7500,
        10000,
        13000,
        16500,
        20500,
        25000,
        30000,
        35500,
        41500,
        48000,
        55000,
        62500
    ];
    for(let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--){
        if (experience >= LEVEL_THRESHOLDS[i]) {
            return i + 1;
        }
    }
    return 1;
}
function checkGameAchievements(session, store) {
    const achievements = [];
    // First game achievement
    if (store.recentSessions.length === 0) {
        achievements.push({
            id: 'first_game',
            name: 'First Steps',
            description: 'Complete your first trading game',
            icon: '🎮',
            category: 'trading',
            points: 50
        });
    }
    // High score achievements
    if (session.score >= 1000) {
        achievements.push({
            id: 'high_score_1000',
            name: 'Rising Trader',
            description: 'Score 1000+ points in a single game',
            icon: '📈',
            category: 'trading',
            points: 100
        });
    }
    if (session.score >= 5000) {
        achievements.push({
            id: 'high_score_5000',
            name: 'Expert Trader',
            description: 'Score 5000+ points in a single game',
            icon: '🏆',
            category: 'trading',
            points: 250
        });
    }
    // Game-specific achievements
    if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {
        achievements.push({
            id: 'speed_scalper',
            name: 'Lightning Fast',
            description: 'Complete Scalper Sprint in under 30 seconds',
            icon: '⚡',
            category: 'trading',
            points: 150
        });
    }
    // Unlock achievements
    achievements.forEach((achievement)=>{
        store.unlockAchievement(achievement);
    });
}
}}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculatePnL": (()=>calculatePnL),
    "calculatePnLPercentage": (()=>calculatePnLPercentage),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatLargeNumber": (()=>formatLargeNumber),
    "formatNumber": (()=>formatNumber),
    "formatPercentage": (()=>formatPercentage),
    "generateColor": (()=>generateColor),
    "generateSessionId": (()=>generateSessionId),
    "getRandomElement": (()=>getRandomElement),
    "getTimeRemaining": (()=>getTimeRemaining),
    "isMinor": (()=>isMinor),
    "sanitizeUsername": (()=>sanitizeUsername),
    "shuffleArray": (()=>shuffleArray),
    "sleep": (()=>sleep),
    "throttle": (()=>throttle),
    "validateAge": (()=>validateAge)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}
function formatPercentage(value, decimals = 2) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
}
function formatNumber(value, decimals = 2) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(value);
}
function formatLargeNumber(value) {
    if (value >= 1e9) {
        return `${(value / 1e9).toFixed(1)}B`;
    }
    if (value >= 1e6) {
        return `${(value / 1e6).toFixed(1)}M`;
    }
    if (value >= 1e3) {
        return `${(value / 1e3).toFixed(1)}K`;
    }
    return value.toString();
}
function calculatePnL(entryPrice, currentPrice, quantity, side) {
    const priceDiff = currentPrice - entryPrice;
    return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity;
}
function calculatePnLPercentage(entryPrice, currentPrice, side) {
    const priceDiff = currentPrice - entryPrice;
    const percentage = priceDiff / entryPrice * 100;
    return side === 'buy' ? percentage : -percentage;
}
function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function isMinor(age) {
    return age < 18;
}
function validateAge(age) {
    return age >= 13 && age <= 120;
}
function sanitizeUsername(username) {
    return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();
}
function getTimeRemaining(endTime) {
    const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString());
    const seconds = Math.floor(total / 1000 % 60);
    const minutes = Math.floor(total / 1000 / 60 % 60);
    const hours = Math.floor(total / (1000 * 60 * 60) % 24);
    const days = Math.floor(total / (1000 * 60 * 60 * 24));
    return {
        total,
        days,
        hours,
        minutes,
        seconds
    };
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}
function shuffleArray(array) {
    const shuffled = [
        ...array
    ];
    for(let i = shuffled.length - 1; i > 0; i--){
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [
            shuffled[j],
            shuffled[i]
        ];
    }
    return shuffled;
}
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function generateColor(seed) {
    let hash = 0;
    for(let i = 0; i < seed.length; i++){
        hash = seed.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = hash % 360;
    return `hsl(${hue}, 70%, 50%)`;
}
}}),
"[project]/src/lib/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACHIEVEMENT_CATEGORIES": (()=>ACHIEVEMENT_CATEGORIES),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "GAME_CONFIGS": (()=>GAME_CONFIGS),
    "INTERFACE_MODES": (()=>INTERFACE_MODES),
    "LEVEL_THRESHOLDS": (()=>LEVEL_THRESHOLDS),
    "QUEST_COIN_MULTIPLIERS": (()=>QUEST_COIN_MULTIPLIERS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TRADING_PAIRS": (()=>TRADING_PAIRS),
    "UPDATE_INTERVALS": (()=>UPDATE_INTERVALS),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
const GAME_CONFIGS = {
    scalper_sprint: {
        name: 'Scalper Sprint',
        description: '60-second trading challenges with rapid-fire decisions',
        difficulty: 'beginner',
        duration_seconds: 60,
        starting_balance: 10000,
        min_trade_size: 100,
        max_positions: 3,
        quest_coins_base: 50
    },
    candle_strike: {
        name: 'CandleStrike',
        description: 'Pattern recognition game with candlestick charts',
        difficulty: 'beginner',
        duration_seconds: 120,
        starting_balance: 0,
        patterns_to_identify: 5,
        quest_coins_base: 75
    },
    chain_maze: {
        name: 'ChainMaze',
        description: 'Navigate blockchain puzzles and learn consensus mechanisms',
        difficulty: 'intermediate',
        duration_seconds: 300,
        starting_balance: 1000,
        puzzles_to_solve: 3,
        quest_coins_base: 100
    },
    swing_trader_odyssey: {
        name: "Swing Trader's Odyssey",
        description: 'Multi-day position management with risk/reward balancing',
        difficulty: 'intermediate',
        duration_seconds: 600,
        starting_balance: 50000,
        max_positions: 5,
        quest_coins_base: 150
    },
    day_trader_arena: {
        name: 'Day Trader Arena',
        description: 'Real-time multiplayer trading competitions',
        difficulty: 'advanced',
        duration_seconds: 900,
        starting_balance: 100000,
        max_positions: 10,
        quest_coins_base: 200
    },
    portfolio_survivor: {
        name: 'Portfolio Survivor',
        description: 'Crisis management with diversification challenges',
        difficulty: 'advanced',
        duration_seconds: 1200,
        starting_balance: 500000,
        max_positions: 20,
        quest_coins_base: 300
    }
};
const TRADING_PAIRS = [
    {
        base: 'BTC',
        quote: 'USD',
        symbol: 'BTCUSD',
        exchange: 'virtual'
    },
    {
        base: 'ETH',
        quote: 'USD',
        symbol: 'ETHUSD',
        exchange: 'virtual'
    },
    {
        base: 'ADA',
        quote: 'USD',
        symbol: 'ADAUSD',
        exchange: 'virtual'
    },
    {
        base: 'SOL',
        quote: 'USD',
        symbol: 'SOLUSD',
        exchange: 'virtual'
    },
    {
        base: 'AAPL',
        quote: 'USD',
        symbol: 'AAPL',
        exchange: 'virtual'
    },
    {
        base: 'GOOGL',
        quote: 'USD',
        symbol: 'GOOGL',
        exchange: 'virtual'
    },
    {
        base: 'TSLA',
        quote: 'USD',
        symbol: 'TSLA',
        exchange: 'virtual'
    },
    {
        base: 'EUR',
        quote: 'USD',
        symbol: 'EURUSD',
        exchange: 'virtual'
    },
    {
        base: 'GBP',
        quote: 'USD',
        symbol: 'GBPUSD',
        exchange: 'virtual'
    },
    {
        base: 'JPY',
        quote: 'USD',
        symbol: 'JPYUSD',
        exchange: 'virtual'
    }
];
const ACHIEVEMENT_CATEGORIES = {
    trading: {
        name: 'Trading Mastery',
        color: '#10B981',
        icon: '📈'
    },
    learning: {
        name: 'Knowledge Seeker',
        color: '#3B82F6',
        icon: '🎓'
    },
    social: {
        name: 'Community Builder',
        color: '#8B5CF6',
        icon: '👥'
    },
    special: {
        name: 'Special Events',
        color: '#F59E0B',
        icon: '⭐'
    }
};
const LEVEL_THRESHOLDS = [
    0,
    100,
    250,
    500,
    1000,
    1750,
    2750,
    4000,
    5500,
    7500,
    10000,
    13000,
    16500,
    20500,
    25000,
    30000,
    35500,
    41500,
    48000,
    55000,
    62500
];
const QUEST_COIN_MULTIPLIERS = {
    beginner: 1.0,
    intermediate: 1.5,
    advanced: 2.0
};
const INTERFACE_MODES = {
    adolescent: {
        name: 'Adventure Mode',
        description: 'Fantasy-themed interface with quests and adventures',
        primaryColor: '#8B5CF6',
        secondaryColor: '#EC4899',
        fontFamily: 'fantasy'
    },
    adult: {
        name: 'Professional Mode',
        description: 'Bloomberg Terminal-style professional interface',
        primaryColor: '#1F2937',
        secondaryColor: '#3B82F6',
        fontFamily: 'monospace'
    }
};
const UPDATE_INTERVALS = {
    real_time: 1000,
    fast: 5000,
    normal: 15000,
    slow: 60000
};
const API_ENDPOINTS = {
    coingecko: {
        base: 'https://api.coingecko.com/api/v3',
        prices: '/simple/price',
        history: '/coins/{id}/market_chart'
    },
    alpha_vantage: {
        base: 'https://www.alphavantage.co/query',
        intraday: '?function=TIME_SERIES_INTRADAY',
        forex: '?function=FX_INTRADAY'
    }
};
const VALIDATION_RULES = {
    username: {
        minLength: 3,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_-]+$/
    },
    age: {
        min: 13,
        max: 120
    },
    trade: {
        minAmount: 1,
        maxAmount: 1000000
    }
};
const ERROR_MESSAGES = {
    auth: {
        invalid_credentials: 'Invalid email or password',
        user_not_found: 'User not found',
        email_already_exists: 'Email already registered',
        weak_password: 'Password must be at least 8 characters',
        age_verification_failed: 'Age verification required'
    },
    game: {
        session_expired: 'Game session has expired',
        invalid_trade: 'Invalid trade parameters',
        insufficient_balance: 'Insufficient balance for this trade',
        max_positions_reached: 'Maximum number of positions reached'
    },
    general: {
        network_error: 'Network error, please try again',
        server_error: 'Server error, please try again later',
        validation_error: 'Please check your input and try again'
    }
};
const SUCCESS_MESSAGES = {
    auth: {
        registration_complete: 'Account created successfully!',
        login_success: 'Welcome back!',
        logout_success: 'Logged out successfully'
    },
    game: {
        session_complete: 'Game session completed!',
        achievement_unlocked: 'Achievement unlocked!',
        level_up: 'Level up! Congratulations!'
    },
    general: {
        save_success: 'Changes saved successfully',
        update_success: 'Updated successfully'
    }
};
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[project]/src/lib/services/market-data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "marketDataService": (()=>marketDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
;
;
class MarketDataService {
    coingeckoClient;
    alphaVantageClient;
    constructor(){
        this.coingeckoClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].coingecko.base,
            timeout: 10000
        });
        this.alphaVantageClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].alpha_vantage.base,
            timeout: 10000
        });
    }
    // Cryptocurrency data from CoinGecko
    async getCryptoPrices(symbols) {
        try {
            const ids = symbols.map((symbol)=>this.symbolToCoinGeckoId(symbol)).join(',');
            const response = await this.coingeckoClient.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].coingecko.prices, {
                params: {
                    ids,
                    vs_currencies: 'usd',
                    include_24hr_change: true,
                    include_24hr_vol: true,
                    include_market_cap: true
                }
            });
            return this.formatCoinGeckoResponse(response.data, symbols);
        } catch (error) {
            console.error('Error fetching crypto prices:', error);
            return this.generateMockCryptoData(symbols);
        }
    }
    // Stock data from Alpha Vantage
    async getStockPrices(symbols) {
        try {
            const promises = symbols.map((symbol)=>this.fetchStockPrice(symbol));
            const results = await Promise.all(promises);
            return results.filter(Boolean);
        } catch (error) {
            console.error('Error fetching stock prices:', error);
            return this.generateMockStockData(symbols);
        }
    }
    // Forex data from Alpha Vantage
    async getForexPrices(pairs) {
        try {
            const promises = pairs.map((pair)=>this.fetchForexPrice(pair));
            const results = await Promise.all(promises);
            return results.filter(Boolean);
        } catch (error) {
            console.error('Error fetching forex prices:', error);
            return this.generateMockForexData(pairs);
        }
    }
    // Historical candlestick data
    async getCandlestickData(symbol, interval = '1h', days = 7) {
        try {
            if (this.isCryptoSymbol(symbol)) {
                return await this.getCryptoCandlestickData(symbol, days);
            } else {
                return await this.getStockCandlestickData(symbol, interval);
            }
        } catch (error) {
            console.error('Error fetching candlestick data:', error);
            return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data
            ;
        }
    }
    // Private helper methods
    async fetchStockPrice(symbol) {
        try {
            const response = await this.alphaVantageClient.get('', {
                params: {
                    function: 'GLOBAL_QUOTE',
                    symbol,
                    apikey: process.env.ALPHA_VANTAGE_API_KEY
                }
            });
            const quote = response.data['Global Quote'];
            if (!quote) return null;
            return {
                symbol,
                price: parseFloat(quote['05. price']),
                change_24h: parseFloat(quote['09. change']),
                change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),
                volume_24h: parseFloat(quote['06. volume']),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return null;
        }
    }
    async fetchForexPrice(pair) {
        try {
            const [from, to] = pair.split('/');
            const response = await this.alphaVantageClient.get('', {
                params: {
                    function: 'CURRENCY_EXCHANGE_RATE',
                    from_currency: from,
                    to_currency: to,
                    apikey: process.env.ALPHA_VANTAGE_API_KEY
                }
            });
            const rate = response.data['Realtime Currency Exchange Rate'];
            if (!rate) return null;
            return {
                symbol: pair,
                price: parseFloat(rate['5. Exchange Rate']),
                change_24h: 0,
                change_percentage_24h: 0,
                volume_24h: 0,
                timestamp: rate['6. Last Refreshed']
            };
        } catch (error) {
            return null;
        }
    }
    async getCryptoCandlestickData(symbol, days) {
        const id = this.symbolToCoinGeckoId(symbol);
        const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {
            params: {
                vs_currency: 'usd',
                days,
                interval: 'hourly'
            }
        });
        const prices = response.data.prices;
        const volumes = response.data.total_volumes;
        return prices.map((price, index)=>({
                timestamp: price[0],
                open: index > 0 ? prices[index - 1][1] : price[1],
                high: price[1] * (1 + Math.random() * 0.02),
                low: price[1] * (1 - Math.random() * 0.02),
                close: price[1],
                volume: volumes[index] ? volumes[index][1] : 0
            }));
    }
    async getStockCandlestickData(symbol, interval) {
        const response = await this.alphaVantageClient.get('', {
            params: {
                function: 'TIME_SERIES_INTRADAY',
                symbol,
                interval,
                apikey: process.env.ALPHA_VANTAGE_API_KEY
            }
        });
        const timeSeries = response.data[`Time Series (${interval})`];
        if (!timeSeries) return [];
        return Object.entries(timeSeries).map(([timestamp, data])=>({
                timestamp: new Date(timestamp).getTime(),
                open: parseFloat(data['1. open']),
                high: parseFloat(data['2. high']),
                low: parseFloat(data['3. low']),
                close: parseFloat(data['4. close']),
                volume: parseFloat(data['5. volume'])
            }));
    }
    symbolToCoinGeckoId(symbol) {
        const mapping = {
            BTC: 'bitcoin',
            ETH: 'ethereum',
            ADA: 'cardano',
            SOL: 'solana',
            DOT: 'polkadot',
            LINK: 'chainlink',
            UNI: 'uniswap',
            MATIC: 'polygon'
        };
        return mapping[symbol.toUpperCase()] || symbol.toLowerCase();
    }
    isCryptoSymbol(symbol) {
        const cryptoSymbols = [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'LINK',
            'UNI',
            'MATIC'
        ];
        return cryptoSymbols.includes(symbol.toUpperCase());
    }
    formatCoinGeckoResponse(data, symbols) {
        return symbols.map((symbol)=>{
            const id = this.symbolToCoinGeckoId(symbol);
            const coinData = data[id];
            if (!coinData) return this.generateMockCryptoData([
                symbol
            ])[0];
            return {
                symbol,
                price: coinData.usd,
                change_24h: coinData.usd_24h_change || 0,
                change_percentage_24h: coinData.usd_24h_change || 0,
                volume_24h: coinData.usd_24h_vol || 0,
                market_cap: coinData.usd_market_cap,
                timestamp: new Date().toISOString()
            };
        });
    }
    // Mock data generators for development and fallback
    generateMockCryptoData(symbols) {
        const basePrices = {
            BTC: 45000,
            ETH: 3000,
            ADA: 0.5,
            SOL: 100
        };
        return symbols.map((symbol)=>({
                symbol,
                price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
                change_24h: (Math.random() - 0.5) * 1000,
                change_percentage_24h: (Math.random() - 0.5) * 10,
                volume_24h: Math.random() * 1000000000,
                market_cap: Math.random() * 100000000000,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockStockData(symbols) {
        const basePrices = {
            AAPL: 150,
            GOOGL: 2500,
            TSLA: 800,
            MSFT: 300
        };
        return symbols.map((symbol)=>({
                symbol,
                price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
                change_24h: (Math.random() - 0.5) * 20,
                change_percentage_24h: (Math.random() - 0.5) * 5,
                volume_24h: Math.random() * 100000000,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockForexData(pairs) {
        const basePrices = {
            'EUR/USD': 1.1,
            'GBP/USD': 1.3,
            'USD/JPY': 110,
            'USD/CHF': 0.9
        };
        return pairs.map((pair)=>({
                symbol: pair,
                price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),
                change_24h: (Math.random() - 0.5) * 0.01,
                change_percentage_24h: (Math.random() - 0.5) * 1,
                volume_24h: 0,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockCandlestickData(symbol, count) {
        const data = [];
        let price = 100 + Math.random() * 900;
        const now = Date.now();
        for(let i = 0; i < count; i++){
            const timestamp = now - (count - i) * 3600000 // Hourly intervals
            ;
            const change = (Math.random() - 0.5) * 10;
            const open = price;
            const close = price + change;
            const high = Math.max(open, close) + Math.random() * 5;
            const low = Math.min(open, close) - Math.random() * 5;
            const volume = Math.random() * 1000000;
            data.push({
                timestamp,
                open,
                high,
                low,
                close,
                volume
            });
            price = close;
        }
        return data;
    }
}
const marketDataService = new MarketDataService();
}}),
"[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseGame": (()=>BaseGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/market-data.ts [app-ssr] (ecmascript)");
;
;
;
class BaseGame {
    config;
    state;
    startTime;
    endTime;
    isActive = false;
    marketData = new Map();
    constructor(gameType, difficulty){
        const gameConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GAME_CONFIGS"][gameType];
        this.config = {
            type: gameType,
            difficulty,
            duration_seconds: gameConfig.duration_seconds,
            starting_balance: gameConfig.starting_balance,
            available_pairs: [],
            special_rules: {}
        };
        this.state = {
            session_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateSessionId"])(),
            current_balance: this.config.starting_balance,
            positions: [],
            time_remaining: this.config.duration_seconds,
            score: 0,
            multiplier: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUEST_COIN_MULTIPLIERS"][difficulty]
        };
        this.startTime = Date.now();
        this.endTime = this.startTime + this.config.duration_seconds * 1000;
    }
    // Methods that can be overridden by specific games
    async initialize() {
    // Default implementation - can be overridden
    }
    update() {
    // Default implementation - can be overridden
    }
    calculateScore() {
        // Default implementation - can be overridden
        return 0;
    }
    getGameSpecificData() {
        // Default implementation - can be overridden
        return {};
    }
    // Common game lifecycle methods
    async start() {
        await this.initialize();
        this.isActive = true;
        this.startGameLoop();
    }
    pause() {
        this.isActive = false;
    }
    resume() {
        this.isActive = true;
        this.startGameLoop();
    }
    end() {
        this.isActive = false;
        this.state.score = this.calculateScore();
        this.state.time_remaining = 0;
        return this.state;
    }
    // Trading operations
    async executeTrade(symbol, side, quantity) {
        if (!this.isActive) return false;
        const currentPrice = this.marketData.get(symbol);
        if (!currentPrice) return false;
        const tradeValue = currentPrice * quantity;
        const requiredBalance = side === 'buy' ? tradeValue : 0;
        if (this.state.current_balance < requiredBalance) {
            return false // Insufficient balance
            ;
        }
        // Check position limits
        const maxPositions = this.getMaxPositions();
        if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {
            return false // Too many positions
            ;
        }
        // Execute the trade
        const position = {
            id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            symbol,
            side,
            quantity,
            entry_price: currentPrice,
            current_price: currentPrice,
            pnl: 0,
            timestamp: new Date().toISOString()
        };
        // Update balance
        if (side === 'buy') {
            this.state.current_balance -= tradeValue;
        } else {
            this.state.current_balance += tradeValue;
        }
        // Add or update position
        const existingPositionIndex = this.state.positions.findIndex((p)=>p.symbol === symbol);
        if (existingPositionIndex >= 0) {
            // Update existing position (average price calculation would go here)
            this.state.positions[existingPositionIndex] = position;
        } else {
            this.state.positions.push(position);
        }
        return true;
    }
    async closePosition(positionId) {
        if (!this.isActive) return false;
        const positionIndex = this.state.positions.findIndex((p)=>p.id === positionId);
        if (positionIndex === -1) return false;
        const position = this.state.positions[positionIndex];
        const currentPrice = this.marketData.get(position.symbol);
        if (!currentPrice) return false;
        // Calculate final P&L
        const pnl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculatePnL"])(position.entry_price, currentPrice, position.quantity, position.side);
        // Update balance with P&L
        this.state.current_balance += pnl;
        if (position.side === 'sell') {
            // Return the initial trade value for short positions
            this.state.current_balance += position.entry_price * position.quantity;
        }
        // Remove position
        this.state.positions.splice(positionIndex, 1);
        return true;
    }
    // Market data updates
    async updateMarketData() {
        try {
            const symbols = this.config.available_pairs.map((pair)=>pair.symbol);
            // In a real implementation, you'd fetch from different services based on asset type
            const cryptoSymbols = symbols.filter((s)=>this.isCryptoSymbol(s));
            const stockSymbols = symbols.filter((s)=>this.isStockSymbol(s));
            const forexSymbols = symbols.filter((s)=>this.isForexSymbol(s));
            const [cryptoData, stockData, forexData] = await Promise.all([
                cryptoSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["marketDataService"].getCryptoPrices(cryptoSymbols) : [],
                stockSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["marketDataService"].getStockPrices(stockSymbols) : [],
                forexSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["marketDataService"].getForexPrices(forexSymbols) : []
            ]);
            // Update market data map
            const allData = cryptoData.concat(stockData).concat(forexData);
            allData.forEach((data)=>{
                this.marketData.set(data.symbol, data.price);
            });
            // Update position P&L
            this.updatePositionPnL();
        } catch (error) {
            console.error('Error updating market data:', error);
        }
    }
    // Game state getters
    getState() {
        return {
            ...this.state
        };
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    isGameActive() {
        return this.isActive && this.state.time_remaining > 0;
    }
    getTimeRemaining() {
        if (!this.isActive) return 0;
        const remaining = Math.max(0, this.endTime - Date.now());
        this.state.time_remaining = Math.floor(remaining / 1000);
        return this.state.time_remaining;
    }
    // Protected helper methods
    startGameLoop() {
        if (!this.isActive) return;
        const gameLoop = ()=>{
            if (!this.isActive) return;
            this.update();
            this.getTimeRemaining();
            if (this.state.time_remaining <= 0) {
                this.end();
                return;
            }
            setTimeout(gameLoop, 1000) // Update every second
            ;
        };
        gameLoop();
    }
    updatePositionPnL() {
        this.state.positions.forEach((position)=>{
            const currentPrice = this.marketData.get(position.symbol);
            if (currentPrice) {
                position.current_price = currentPrice;
                position.pnl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculatePnL"])(position.entry_price, currentPrice, position.quantity, position.side);
            }
        });
    }
    getTotalPnL() {
        return this.state.positions.reduce((total, position)=>total + position.pnl, 0);
    }
    getMaxPositions() {
        const gameConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GAME_CONFIGS"][this.config.type];
        return gameConfig.max_positions || 5;
    }
    hasExistingPosition(symbol) {
        return this.state.positions.some((p)=>p.symbol === symbol);
    }
    isCryptoSymbol(symbol) {
        const cryptoSymbols = [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'LINK',
            'UNI',
            'MATIC'
        ];
        return cryptoSymbols.some((crypto)=>symbol.includes(crypto));
    }
    isStockSymbol(symbol) {
        const stockSymbols = [
            'AAPL',
            'GOOGL',
            'TSLA',
            'MSFT',
            'AMZN',
            'META',
            'NVDA'
        ];
        return stockSymbols.includes(symbol);
    }
    isForexSymbol(symbol) {
        return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY');
    }
    generateRandomPrice(basePrice, volatility = 0.02) {
        const change = (Math.random() - 0.5) * 2 * volatility;
        return basePrice * (1 + change);
    }
    simulateMarketMovement() {
        // Simulate realistic market movements for game purposes
        this.marketData.forEach((price, symbol)=>{
            const volatility = this.getSymbolVolatility(symbol);
            const newPrice = this.generateRandomPrice(price, volatility);
            this.marketData.set(symbol, newPrice);
        });
    }
    getSymbolVolatility(symbol) {
        if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto
        ;
        if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks
        ;
        if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex
        ;
        return 0.02 // Default 2%
        ;
    }
}
}}),
"[project]/src/lib/game-engine/games/scalper-sprint.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScalperSprintGame": (()=>ScalperSprintGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
;
;
class ScalperSprintGame extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseGame"] {
    gameData;
    tradeHistory = [];
    constructor(difficulty){
        super('scalper_sprint', difficulty);
        this.gameData = {
            trades_executed: 0,
            successful_trades: 0,
            largest_gain: 0,
            largest_loss: 0,
            average_hold_time: 0,
            speed_bonus: 0
        };
        // Set available trading pairs for scalping (high volatility pairs)
        this.config.available_pairs = this.getScalpingPairs(difficulty);
    }
    async initialize() {
        // Initialize market data with realistic scalping prices
        const initialPrices = this.generateInitialPrices();
        initialPrices.forEach((price, symbol)=>{
            this.marketData.set(symbol, price);
        });
        // Start market data updates more frequently for scalping
        this.startMarketDataUpdates();
    }
    update() {
        // Update market data with high frequency for scalping simulation
        this.simulateScalpingMarketMovement();
        this.updatePositionPnL();
        // Check for auto-close conditions (stop loss, take profit)
        this.checkAutoCloseConditions();
        // Update game-specific metrics
        this.updateGameMetrics();
    }
    calculateScore() {
        const totalPnL = this.getTotalPnL();
        const balanceChange = this.state.current_balance - this.config.starting_balance + totalPnL;
        const balanceChangePercentage = balanceChange / this.config.starting_balance * 100;
        // Base score from P&L percentage
        let score = Math.max(0, balanceChangePercentage * 10);
        // Bonus for number of successful trades
        const successRate = this.gameData.trades_executed > 0 ? this.gameData.successful_trades / this.gameData.trades_executed : 0;
        score += successRate * 50;
        // Speed bonus for quick decision making
        score += this.gameData.speed_bonus;
        // Penalty for holding positions too long (this is scalping!)
        const avgHoldTimePenalty = Math.max(0, (this.gameData.average_hold_time - 10) * 2);
        score -= avgHoldTimePenalty;
        // Difficulty multiplier
        score *= this.state.multiplier;
        return Math.round(Math.max(0, score));
    }
    getGameSpecificData() {
        return {
            ...this.gameData
        };
    }
    // Override trade execution to add scalping-specific logic
    async executeTrade(symbol, side, quantity) {
        const success = await super.executeTrade(symbol, side, quantity);
        if (success) {
            this.gameData.trades_executed++;
            // Record trade for analytics
            this.tradeHistory.push({
                timestamp: Date.now(),
                symbol,
                side,
                entry_price: this.marketData.get(symbol)
            });
            // Speed bonus for quick trades
            const timeSinceStart = (Date.now() - this.startTime) / 1000;
            if (timeSinceStart < 10) {
                this.gameData.speed_bonus += 5;
            }
        }
        return success;
    }
    // Override position closing to track scalping metrics
    async closePosition(positionId) {
        const position = this.state.positions.find((p)=>p.id === positionId);
        if (!position) return false;
        const success = await super.closePosition(positionId);
        if (success && position) {
            const tradeRecord = this.tradeHistory.find((t)=>t.symbol === position.symbol && t.entry_price === position.entry_price && !t.exit_price);
            if (tradeRecord) {
                const holdTime = (Date.now() - tradeRecord.timestamp) / 1000;
                const exitPrice = this.marketData.get(position.symbol);
                const pnl = position.pnl;
                // Update trade record
                tradeRecord.exit_price = exitPrice;
                tradeRecord.hold_time = holdTime;
                tradeRecord.pnl = pnl;
                // Update game metrics
                if (pnl > 0) {
                    this.gameData.successful_trades++;
                    this.gameData.largest_gain = Math.max(this.gameData.largest_gain, pnl);
                } else {
                    this.gameData.largest_loss = Math.min(this.gameData.largest_loss, pnl);
                }
                this.updateAverageHoldTime();
            }
        }
        return success;
    }
    getScalpingPairs(difficulty) {
        const allPairs = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRADING_PAIRS"];
        switch(difficulty){
            case 'beginner':
                // Major crypto pairs with high liquidity
                return allPairs.filter((pair)=>[
                        'BTCUSD',
                        'ETHUSD'
                    ].includes(pair.symbol));
            case 'intermediate':
                // Add some altcoins and major stocks
                return allPairs.filter((pair)=>[
                        'BTCUSD',
                        'ETHUSD',
                        'ADAUSD',
                        'AAPL',
                        'GOOGL'
                    ].includes(pair.symbol));
            case 'advanced':
                // All available pairs including forex
                return allPairs;
            default:
                return allPairs.slice(0, 3);
        }
    }
    generateInitialPrices() {
        const prices = new Map();
        // Realistic starting prices for scalping simulation
        const basePrices = {
            'BTCUSD': 45000 + (Math.random() - 0.5) * 2000,
            'ETHUSD': 3000 + (Math.random() - 0.5) * 200,
            'ADAUSD': 0.5 + (Math.random() - 0.5) * 0.1,
            'SOLUSD': 100 + (Math.random() - 0.5) * 20,
            'AAPL': 150 + (Math.random() - 0.5) * 10,
            'GOOGL': 2500 + (Math.random() - 0.5) * 100,
            'TSLA': 800 + (Math.random() - 0.5) * 50,
            'EURUSD': 1.1 + (Math.random() - 0.5) * 0.02,
            'GBPUSD': 1.3 + (Math.random() - 0.5) * 0.02,
            'JPYUSD': 0.009 + (Math.random() - 0.5) * 0.0002
        };
        this.config.available_pairs.forEach((pair)=>{
            prices.set(pair.symbol, basePrices[pair.symbol] || 100);
        });
        return prices;
    }
    startMarketDataUpdates() {
        // Update market data every 2 seconds for realistic scalping
        const updateInterval = setInterval(()=>{
            if (!this.isActive) {
                clearInterval(updateInterval);
                return;
            }
            this.simulateScalpingMarketMovement();
        }, 2000);
    }
    simulateScalpingMarketMovement() {
        // Simulate high-frequency price movements typical in scalping
        this.marketData.forEach((price, symbol)=>{
            // Higher volatility and more frequent small movements
            const volatility = this.getScalpingVolatility(symbol);
            const direction = Math.random() > 0.5 ? 1 : -1;
            const change = direction * Math.random() * volatility * price;
            // Add some momentum (trending behavior)
            const momentum = this.calculateMomentum(symbol);
            const newPrice = price + change + momentum;
            this.marketData.set(symbol, Math.max(0.01, newPrice));
        });
    }
    getScalpingVolatility(symbol) {
        // Higher volatility for scalping simulation
        if (this.isCryptoSymbol(symbol)) return 0.008 // 0.8% per update
        ;
        if (this.isStockSymbol(symbol)) return 0.003 // 0.3% per update
        ;
        if (this.isForexSymbol(symbol)) return 0.001 // 0.1% per update
        ;
        return 0.005;
    }
    calculateMomentum(symbol) {
        // Simple momentum calculation based on recent price history
        // In a real implementation, this would use actual price history
        return (Math.random() - 0.5) * 0.001 * (this.marketData.get(symbol) || 0);
    }
    checkAutoCloseConditions() {
        // Auto-close positions that hit stop loss or take profit levels
        this.state.positions.forEach((position)=>{
            const currentPrice = position.current_price;
            const entryPrice = position.entry_price;
            const pnlPercentage = position.pnl / (entryPrice * position.quantity) * 100;
            // Auto-close on 5% loss (stop loss) or 3% gain (take profit) for scalping
            if (pnlPercentage <= -5 || pnlPercentage >= 3) {
                this.closePosition(position.id);
            }
        });
    }
    updateGameMetrics() {
        // Update average hold time
        this.updateAverageHoldTime();
        // Update speed bonus based on quick decision making
        const recentTrades = this.tradeHistory.filter((t)=>Date.now() - t.timestamp < 5000 // Last 5 seconds
        );
        if (recentTrades.length >= 2) {
            this.gameData.speed_bonus += 2 // Bonus for rapid trading
            ;
        }
    }
    updateAverageHoldTime() {
        const completedTrades = this.tradeHistory.filter((t)=>t.hold_time !== undefined);
        if (completedTrades.length > 0) {
            const totalHoldTime = completedTrades.reduce((sum, trade)=>sum + (trade.hold_time || 0), 0);
            this.gameData.average_hold_time = totalHoldTime / completedTrades.length;
        }
    }
}
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$scalper$2d$sprint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/games/scalper-sprint.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function Home() {
    const { interfaceMode, switchInterfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserStore"])();
    const [currentGame, setCurrentGame] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const startScalperSprint = async ()=>{
        const game = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$scalper$2d$sprint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ScalperSprintGame"]('beginner');
        setCurrentGame(game);
        await game.start();
        // Update game state every second
        const interval = setInterval(()=>{
            if (game.isGameActive()) {
                setGameState(game.getState());
            } else {
                clearInterval(interval);
                setCurrentGame(null);
                setGameState(null);
            }
        }, 1000);
    };
    const executeTrade = async (symbol, side)=>{
        if (currentGame) {
            await currentGame.executeTrade(symbol, side, 1);
            setGameState(currentGame.getState());
        }
    };
    const isAdolescentMode = interfaceMode === 'adolescent';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen ${isAdolescentMode ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' : 'bg-gray-900 text-green-400 font-mono'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: `p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: `text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                            children: isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 47,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent'),
                                className: `px-4 py-2 rounded-lg transition-colors ${isAdolescentMode ? 'bg-white/20 hover:bg-white/30 text-white' : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'}`,
                                children: [
                                    "Switch to ",
                                    isAdolescentMode ? 'Professional' : 'Adventure',
                                    " Mode"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 52,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 51,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 46,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 45,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-7xl mx-auto p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: `mb-8 p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: `text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 74,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: `text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Embark on epic trading adventures and master the markets through exciting mini-games!' : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 77,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: `mb-8 p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm' : 'bg-gray-800 border border-green-400'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: `text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: isAdolescentMode ? '⚡ Scalper Sprint Challenge' : '📈 High-Frequency Trading Simulation'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 91,
                                columnNumber: 11
                            }, this),
                            !currentGame ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                        children: isAdolescentMode ? 'Test your speed and reflexes in this 60-second trading challenge!' : 'Execute rapid trades in a simulated high-frequency environment.'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 97,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: startScalperSprint,
                                        className: `px-6 py-3 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600' : 'bg-green-400 text-gray-900 hover:bg-green-300'}`,
                                        children: isAdolescentMode ? '🚀 Start Adventure!' : 'INITIALIZE TRADING SESSION'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 103,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: [
                                    gameState && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `grid grid-cols-2 md:grid-cols-4 gap-4 p-4 rounded ${isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Quest Coins' : 'BALANCE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 122,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                        children: [
                                                            "$",
                                                            gameState.current_balance.toFixed(2)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 125,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 121,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Score' : 'SCORE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 130,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: gameState.score
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 133,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 129,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 138,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`,
                                                        children: [
                                                            gameState.time_remaining,
                                                            "s"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 141,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 137,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Positions' : 'POSITIONS'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 146,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: gameState.positions.length
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 149,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 145,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 118,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>executeTrade('BTCUSD', 'buy'),
                                                className: `p-4 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-green-400 hover:bg-green-300 text-gray-900'}`,
                                                children: isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 158,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>executeTrade('BTCUSD', 'sell'),
                                                className: `p-4 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-red-400 hover:bg-red-300 text-gray-900'}`,
                                                children: isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 168,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 157,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "grid md:grid-cols-3 gap-6",
                        children: [
                            {
                                title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',
                                description: isAdolescentMode ? 'Six exciting games to master different trading skills' : 'Comprehensive trading simulation modules',
                                features: [
                                    'Scalper Sprint',
                                    'CandleStrike',
                                    'ChainMaze'
                                ]
                            },
                            {
                                title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',
                                description: isAdolescentMode ? 'Unlock badges and level up your trading hero' : 'Advanced performance tracking and analytics',
                                features: [
                                    'Progress Tracking',
                                    'Leaderboards',
                                    'Statistics'
                                ]
                            },
                            {
                                title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',
                                description: isAdolescentMode ? 'Join guilds and compete with friends' : 'Professional networking and strategy sharing',
                                features: [
                                    'Team Challenges',
                                    'Social Features',
                                    'Competitions'
                                ]
                            }
                        ].map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: `text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                        children: feature.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 216,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                        children: feature.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 219,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: `space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`,
                                        children: feature.features.map((item, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: `mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                        children: isAdolescentMode ? '✨' : '▶'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 225,
                                                        columnNumber: 21
                                                    }, this),
                                                    item
                                                ]
                                            }, i, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 224,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 208,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 184,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 67,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__98f7c58d._.js.map