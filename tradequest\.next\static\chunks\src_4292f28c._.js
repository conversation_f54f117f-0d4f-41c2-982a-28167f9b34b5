(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculatePnL": (()=>calculatePnL),
    "calculatePnLPercentage": (()=>calculatePnLPercentage),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatLargeNumber": (()=>formatLargeNumber),
    "formatNumber": (()=>formatNumber),
    "formatPercentage": (()=>formatPercentage),
    "generateColor": (()=>generateColor),
    "generateSessionId": (()=>generateSessionId),
    "getRandomElement": (()=>getRandomElement),
    "getTimeRemaining": (()=>getTimeRemaining),
    "isMinor": (()=>isMinor),
    "sanitizeUsername": (()=>sanitizeUsername),
    "shuffleArray": (()=>shuffleArray),
    "sleep": (()=>sleep),
    "throttle": (()=>throttle),
    "validateAge": (()=>validateAge)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}
function formatPercentage(value, decimals = 2) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
}
function formatNumber(value, decimals = 2) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(value);
}
function formatLargeNumber(value) {
    if (value >= 1e9) {
        return `${(value / 1e9).toFixed(1)}B`;
    }
    if (value >= 1e6) {
        return `${(value / 1e6).toFixed(1)}M`;
    }
    if (value >= 1e3) {
        return `${(value / 1e3).toFixed(1)}K`;
    }
    return value.toString();
}
function calculatePnL(entryPrice, currentPrice, quantity, side) {
    const priceDiff = currentPrice - entryPrice;
    return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity;
}
function calculatePnLPercentage(entryPrice, currentPrice, side) {
    const priceDiff = currentPrice - entryPrice;
    const percentage = priceDiff / entryPrice * 100;
    return side === 'buy' ? percentage : -percentage;
}
function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function isMinor(age) {
    return age < 18;
}
function validateAge(age) {
    return age >= 13 && age <= 120;
}
function sanitizeUsername(username) {
    return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();
}
function getTimeRemaining(endTime) {
    const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString());
    const seconds = Math.floor(total / 1000 % 60);
    const minutes = Math.floor(total / 1000 / 60 % 60);
    const hours = Math.floor(total / (1000 * 60 * 60) % 24);
    const days = Math.floor(total / (1000 * 60 * 60 * 24));
    return {
        total,
        days,
        hours,
        minutes,
        seconds
    };
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}
function shuffleArray(array) {
    const shuffled = [
        ...array
    ];
    for(let i = shuffled.length - 1; i > 0; i--){
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [
            shuffled[j],
            shuffled[i]
        ];
    }
    return shuffled;
}
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function generateColor(seed) {
    let hash = 0;
    for(let i = 0; i < seed.length; i++){
        hash = seed.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = hash % 360;
    return `hsl(${hue}, 70%, 50%)`;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACHIEVEMENT_CATEGORIES": (()=>ACHIEVEMENT_CATEGORIES),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "GAME_CONFIGS": (()=>GAME_CONFIGS),
    "INTERFACE_MODES": (()=>INTERFACE_MODES),
    "LEVEL_THRESHOLDS": (()=>LEVEL_THRESHOLDS),
    "QUEST_COIN_MULTIPLIERS": (()=>QUEST_COIN_MULTIPLIERS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TRADING_PAIRS": (()=>TRADING_PAIRS),
    "UPDATE_INTERVALS": (()=>UPDATE_INTERVALS),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
const GAME_CONFIGS = {
    scalper_sprint: {
        name: 'Scalper Sprint',
        description: '60-second trading challenges with rapid-fire decisions',
        difficulty: 'beginner',
        duration_seconds: 60,
        starting_balance: 10000,
        min_trade_size: 100,
        max_positions: 3,
        quest_coins_base: 50
    },
    candle_strike: {
        name: 'CandleStrike',
        description: 'Pattern recognition game with candlestick charts',
        difficulty: 'beginner',
        duration_seconds: 120,
        starting_balance: 0,
        patterns_to_identify: 5,
        quest_coins_base: 75
    },
    chain_maze: {
        name: 'ChainMaze',
        description: 'Navigate blockchain puzzles and learn consensus mechanisms',
        difficulty: 'intermediate',
        duration_seconds: 300,
        starting_balance: 1000,
        puzzles_to_solve: 3,
        quest_coins_base: 100
    },
    swing_trader_odyssey: {
        name: "Swing Trader's Odyssey",
        description: 'Multi-day position management with risk/reward balancing',
        difficulty: 'intermediate',
        duration_seconds: 600,
        starting_balance: 50000,
        max_positions: 5,
        quest_coins_base: 150
    },
    day_trader_arena: {
        name: 'Day Trader Arena',
        description: 'Real-time multiplayer trading competitions',
        difficulty: 'advanced',
        duration_seconds: 900,
        starting_balance: 100000,
        max_positions: 10,
        quest_coins_base: 200
    },
    portfolio_survivor: {
        name: 'Portfolio Survivor',
        description: 'Crisis management with diversification challenges',
        difficulty: 'advanced',
        duration_seconds: 1200,
        starting_balance: 500000,
        max_positions: 20,
        quest_coins_base: 300
    }
};
const TRADING_PAIRS = [
    {
        base: 'BTC',
        quote: 'USD',
        symbol: 'BTCUSD',
        exchange: 'virtual'
    },
    {
        base: 'ETH',
        quote: 'USD',
        symbol: 'ETHUSD',
        exchange: 'virtual'
    },
    {
        base: 'ADA',
        quote: 'USD',
        symbol: 'ADAUSD',
        exchange: 'virtual'
    },
    {
        base: 'SOL',
        quote: 'USD',
        symbol: 'SOLUSD',
        exchange: 'virtual'
    },
    {
        base: 'AAPL',
        quote: 'USD',
        symbol: 'AAPL',
        exchange: 'virtual'
    },
    {
        base: 'GOOGL',
        quote: 'USD',
        symbol: 'GOOGL',
        exchange: 'virtual'
    },
    {
        base: 'TSLA',
        quote: 'USD',
        symbol: 'TSLA',
        exchange: 'virtual'
    },
    {
        base: 'EUR',
        quote: 'USD',
        symbol: 'EURUSD',
        exchange: 'virtual'
    },
    {
        base: 'GBP',
        quote: 'USD',
        symbol: 'GBPUSD',
        exchange: 'virtual'
    },
    {
        base: 'JPY',
        quote: 'USD',
        symbol: 'JPYUSD',
        exchange: 'virtual'
    }
];
const ACHIEVEMENT_CATEGORIES = {
    trading: {
        name: 'Trading Mastery',
        color: '#10B981',
        icon: '📈'
    },
    learning: {
        name: 'Knowledge Seeker',
        color: '#3B82F6',
        icon: '🎓'
    },
    social: {
        name: 'Community Builder',
        color: '#8B5CF6',
        icon: '👥'
    },
    special: {
        name: 'Special Events',
        color: '#F59E0B',
        icon: '⭐'
    }
};
const LEVEL_THRESHOLDS = [
    0,
    100,
    250,
    500,
    1000,
    1750,
    2750,
    4000,
    5500,
    7500,
    10000,
    13000,
    16500,
    20500,
    25000,
    30000,
    35500,
    41500,
    48000,
    55000,
    62500
];
const QUEST_COIN_MULTIPLIERS = {
    beginner: 1.0,
    intermediate: 1.5,
    advanced: 2.0
};
const INTERFACE_MODES = {
    adolescent: {
        name: 'Adventure Mode',
        description: 'Fantasy-themed interface with quests and adventures',
        primaryColor: '#8B5CF6',
        secondaryColor: '#EC4899',
        fontFamily: 'fantasy'
    },
    adult: {
        name: 'Professional Mode',
        description: 'Bloomberg Terminal-style professional interface',
        primaryColor: '#1F2937',
        secondaryColor: '#3B82F6',
        fontFamily: 'monospace'
    }
};
const UPDATE_INTERVALS = {
    real_time: 1000,
    fast: 5000,
    normal: 15000,
    slow: 60000
};
const API_ENDPOINTS = {
    coingecko: {
        base: 'https://api.coingecko.com/api/v3',
        prices: '/simple/price',
        history: '/coins/{id}/market_chart'
    },
    alpha_vantage: {
        base: 'https://www.alphavantage.co/query',
        intraday: '?function=TIME_SERIES_INTRADAY',
        forex: '?function=FX_INTRADAY'
    }
};
const VALIDATION_RULES = {
    username: {
        minLength: 3,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_-]+$/
    },
    age: {
        min: 13,
        max: 120
    },
    trade: {
        minAmount: 1,
        maxAmount: 1000000
    }
};
const ERROR_MESSAGES = {
    auth: {
        invalid_credentials: 'Invalid email or password',
        user_not_found: 'User not found',
        email_already_exists: 'Email already registered',
        weak_password: 'Password must be at least 8 characters',
        age_verification_failed: 'Age verification required'
    },
    game: {
        session_expired: 'Game session has expired',
        invalid_trade: 'Invalid trade parameters',
        insufficient_balance: 'Insufficient balance for this trade',
        max_positions_reached: 'Maximum number of positions reached'
    },
    general: {
        network_error: 'Network error, please try again',
        server_error: 'Server error, please try again later',
        validation_error: 'Please check your input and try again'
    }
};
const SUCCESS_MESSAGES = {
    auth: {
        registration_complete: 'Account created successfully!',
        login_success: 'Welcome back!',
        logout_success: 'Logged out successfully'
    },
    game: {
        session_complete: 'Game session completed!',
        achievement_unlocked: 'Achievement unlocked!',
        level_up: 'Level up! Congratulations!'
    },
    general: {
        save_success: 'Changes saved successfully',
        update_success: 'Updated successfully'
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/market-data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "marketDataService": (()=>marketDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-client] (ecmascript)");
;
;
class MarketDataService {
    coingeckoClient;
    alphaVantageClient;
    constructor(){
        this.coingeckoClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].coingecko.base,
            timeout: 10000
        });
        this.alphaVantageClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].alpha_vantage.base,
            timeout: 10000
        });
    }
    // Cryptocurrency data from CoinGecko
    async getCryptoPrices(symbols) {
        try {
            const ids = symbols.map((symbol)=>this.symbolToCoinGeckoId(symbol)).join(',');
            const response = await this.coingeckoClient.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].coingecko.prices, {
                params: {
                    ids,
                    vs_currencies: 'usd',
                    include_24hr_change: true,
                    include_24hr_vol: true,
                    include_market_cap: true
                }
            });
            return this.formatCoinGeckoResponse(response.data, symbols);
        } catch (error) {
            console.error('Error fetching crypto prices:', error);
            return this.generateMockCryptoData(symbols);
        }
    }
    // Stock data from Alpha Vantage
    async getStockPrices(symbols) {
        try {
            const promises = symbols.map((symbol)=>this.fetchStockPrice(symbol));
            const results = await Promise.all(promises);
            return results.filter(Boolean);
        } catch (error) {
            console.error('Error fetching stock prices:', error);
            return this.generateMockStockData(symbols);
        }
    }
    // Forex data from Alpha Vantage
    async getForexPrices(pairs) {
        try {
            const promises = pairs.map((pair)=>this.fetchForexPrice(pair));
            const results = await Promise.all(promises);
            return results.filter(Boolean);
        } catch (error) {
            console.error('Error fetching forex prices:', error);
            return this.generateMockForexData(pairs);
        }
    }
    // Historical candlestick data with enhanced pattern detection
    async getCandlestickData(symbol, interval = '1h', days = 7) {
        try {
            if (this.isCryptoSymbol(symbol)) {
                return await this.getCryptoCandlestickData(symbol, days);
            } else {
                return await this.getStockCandlestickData(symbol, interval);
            }
        } catch (error) {
            console.error('Error fetching candlestick data:', error);
            return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data
            ;
        }
    }
    // Get historical data with specific patterns for educational purposes
    async getHistoricalDataWithPatterns(symbol, patternType, count = 10) {
        try {
            // For demo purposes, we'll use a combination of real data and pattern-enhanced data
            const baseData = await this.getCandlestickData(symbol, '1h', 30) // 30 days of data
            ;
            // Find or create segments with the requested pattern
            return this.extractPatternSegments(baseData, patternType, count);
        } catch (error) {
            console.error('Error fetching pattern data:', error);
            return this.generatePatternDatasets(symbol, patternType, count);
        }
    }
    // TradingView-style data format
    async getTradingViewData(symbol, resolution = '60', from, to) {
        try {
            const data = await this.getCandlestickData(symbol, '1h', 7);
            return {
                s: 'ok',
                t: data.map((d)=>Math.floor(d.timestamp / 1000)),
                o: data.map((d)=>d.open),
                h: data.map((d)=>d.high),
                l: data.map((d)=>d.low),
                c: data.map((d)=>d.close),
                v: data.map((d)=>d.volume)
            };
        } catch (error) {
            return {
                s: 'error',
                t: [],
                o: [],
                h: [],
                l: [],
                c: [],
                v: []
            };
        }
    }
    // Private helper methods
    async fetchStockPrice(symbol) {
        try {
            const response = await this.alphaVantageClient.get('', {
                params: {
                    function: 'GLOBAL_QUOTE',
                    symbol,
                    apikey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.ALPHA_VANTAGE_API_KEY
                }
            });
            const quote = response.data['Global Quote'];
            if (!quote) return null;
            return {
                symbol,
                price: parseFloat(quote['05. price']),
                change_24h: parseFloat(quote['09. change']),
                change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),
                volume_24h: parseFloat(quote['06. volume']),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return null;
        }
    }
    async fetchForexPrice(pair) {
        try {
            const [from, to] = pair.split('/');
            const response = await this.alphaVantageClient.get('', {
                params: {
                    function: 'CURRENCY_EXCHANGE_RATE',
                    from_currency: from,
                    to_currency: to,
                    apikey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.ALPHA_VANTAGE_API_KEY
                }
            });
            const rate = response.data['Realtime Currency Exchange Rate'];
            if (!rate) return null;
            return {
                symbol: pair,
                price: parseFloat(rate['5. Exchange Rate']),
                change_24h: 0,
                change_percentage_24h: 0,
                volume_24h: 0,
                timestamp: rate['6. Last Refreshed']
            };
        } catch (error) {
            return null;
        }
    }
    async getCryptoCandlestickData(symbol, days) {
        const id = this.symbolToCoinGeckoId(symbol);
        const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {
            params: {
                vs_currency: 'usd',
                days,
                interval: 'hourly'
            }
        });
        const prices = response.data.prices;
        const volumes = response.data.total_volumes;
        return prices.map((price, index)=>({
                timestamp: price[0],
                open: index > 0 ? prices[index - 1][1] : price[1],
                high: price[1] * (1 + Math.random() * 0.02),
                low: price[1] * (1 - Math.random() * 0.02),
                close: price[1],
                volume: volumes[index] ? volumes[index][1] : 0
            }));
    }
    async getStockCandlestickData(symbol, interval) {
        const response = await this.alphaVantageClient.get('', {
            params: {
                function: 'TIME_SERIES_INTRADAY',
                symbol,
                interval,
                apikey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.ALPHA_VANTAGE_API_KEY
            }
        });
        const timeSeries = response.data[`Time Series (${interval})`];
        if (!timeSeries) return [];
        return Object.entries(timeSeries).map(([timestamp, data])=>({
                timestamp: new Date(timestamp).getTime(),
                open: parseFloat(data['1. open']),
                high: parseFloat(data['2. high']),
                low: parseFloat(data['3. low']),
                close: parseFloat(data['4. close']),
                volume: parseFloat(data['5. volume'])
            }));
    }
    symbolToCoinGeckoId(symbol) {
        const mapping = {
            BTC: 'bitcoin',
            ETH: 'ethereum',
            ADA: 'cardano',
            SOL: 'solana',
            DOT: 'polkadot',
            LINK: 'chainlink',
            UNI: 'uniswap',
            MATIC: 'polygon'
        };
        return mapping[symbol.toUpperCase()] || symbol.toLowerCase();
    }
    isCryptoSymbol(symbol) {
        const cryptoSymbols = [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'LINK',
            'UNI',
            'MATIC'
        ];
        return cryptoSymbols.includes(symbol.toUpperCase());
    }
    formatCoinGeckoResponse(data, symbols) {
        return symbols.map((symbol)=>{
            const id = this.symbolToCoinGeckoId(symbol);
            const coinData = data[id];
            if (!coinData) return this.generateMockCryptoData([
                symbol
            ])[0];
            return {
                symbol,
                price: coinData.usd,
                change_24h: coinData.usd_24h_change || 0,
                change_percentage_24h: coinData.usd_24h_change || 0,
                volume_24h: coinData.usd_24h_vol || 0,
                market_cap: coinData.usd_market_cap,
                timestamp: new Date().toISOString()
            };
        });
    }
    // Mock data generators for development and fallback
    generateMockCryptoData(symbols) {
        const basePrices = {
            BTC: 45000,
            ETH: 3000,
            ADA: 0.5,
            SOL: 100
        };
        return symbols.map((symbol)=>({
                symbol,
                price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
                change_24h: (Math.random() - 0.5) * 1000,
                change_percentage_24h: (Math.random() - 0.5) * 10,
                volume_24h: Math.random() * 1000000000,
                market_cap: Math.random() * 100000000000,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockStockData(symbols) {
        const basePrices = {
            AAPL: 150,
            GOOGL: 2500,
            TSLA: 800,
            MSFT: 300
        };
        return symbols.map((symbol)=>({
                symbol,
                price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
                change_24h: (Math.random() - 0.5) * 20,
                change_percentage_24h: (Math.random() - 0.5) * 5,
                volume_24h: Math.random() * 100000000,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockForexData(pairs) {
        const basePrices = {
            'EUR/USD': 1.1,
            'GBP/USD': 1.3,
            'USD/JPY': 110,
            'USD/CHF': 0.9
        };
        return pairs.map((pair)=>({
                symbol: pair,
                price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),
                change_24h: (Math.random() - 0.5) * 0.01,
                change_percentage_24h: (Math.random() - 0.5) * 1,
                volume_24h: 0,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockCandlestickData(symbol, count) {
        const data = [];
        let price = 100 + Math.random() * 900;
        const now = Date.now();
        for(let i = 0; i < count; i++){
            const timestamp = now - (count - i) * 3600000 // Hourly intervals
            ;
            const change = (Math.random() - 0.5) * 10;
            const open = price;
            const close = price + change;
            const high = Math.max(open, close) + Math.random() * 5;
            const low = Math.min(open, close) - Math.random() * 5;
            const volume = Math.random() * 1000000;
            data.push({
                timestamp,
                open,
                high,
                low,
                close,
                volume
            });
            price = close;
        }
        return data;
    }
    // Extract segments containing specific patterns from real data
    extractPatternSegments(data, patternType, count) {
        const segments = [];
        const segmentLength = 20 // 20 candles per segment
        ;
        // Scan through data looking for patterns
        for(let i = 0; i <= data.length - segmentLength && segments.length < count; i++){
            const segment = data.slice(i, i + segmentLength);
            if (this.containsPattern(segment, patternType)) {
                segments.push(segment);
                i += segmentLength - 1 // Skip ahead to avoid overlapping segments
                ;
            }
        }
        // If we don't have enough real patterns, generate some
        while(segments.length < count){
            segments.push(this.generateSegmentWithPattern(patternType, segmentLength));
        }
        return segments;
    }
    // Check if a segment contains a specific pattern
    containsPattern(segment, patternType) {
        switch(patternType){
            case 'hammer':
                return this.detectHammer(segment);
            case 'doji':
                return this.detectDoji(segment);
            case 'engulfing_bullish':
                return this.detectBullishEngulfing(segment);
            case 'engulfing_bearish':
                return this.detectBearishEngulfing(segment);
            case 'morning_star':
                return this.detectMorningStar(segment);
            case 'evening_star':
                return this.detectEveningStar(segment);
            default:
                return false;
        }
    }
    // Pattern detection algorithms
    detectHammer(segment) {
        for(let i = 1; i < segment.length - 1; i++){
            const candle = segment[i];
            const bodySize = Math.abs(candle.close - candle.open);
            const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
            const upperShadow = candle.high - Math.max(candle.open, candle.close);
            const totalRange = candle.high - candle.low;
            // Hammer criteria: small body, long lower shadow, small upper shadow
            if (bodySize < totalRange * 0.3 && lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5) {
                return true;
            }
        }
        return false;
    }
    detectDoji(segment) {
        for(let i = 0; i < segment.length; i++){
            const candle = segment[i];
            const bodySize = Math.abs(candle.close - candle.open);
            const totalRange = candle.high - candle.low;
            // Doji criteria: very small body relative to total range
            if (bodySize < totalRange * 0.1 && totalRange > 0) {
                return true;
            }
        }
        return false;
    }
    detectBullishEngulfing(segment) {
        for(let i = 1; i < segment.length; i++){
            const prev = segment[i - 1];
            const curr = segment[i];
            // Previous candle is bearish, current is bullish and engulfs previous
            if (prev.close < prev.open && // Previous bearish
            curr.close > curr.open && // Current bullish
            curr.open < prev.close && // Current opens below previous close
            curr.close > prev.open) {
                return true;
            }
        }
        return false;
    }
    detectBearishEngulfing(segment) {
        for(let i = 1; i < segment.length; i++){
            const prev = segment[i - 1];
            const curr = segment[i];
            // Previous candle is bearish, current is bullish and engulfs previous
            if (prev.close > prev.open && // Previous bullish
            curr.close < curr.open && // Current bearish
            curr.open > prev.close && // Current opens above previous close
            curr.close < prev.open) {
                return true;
            }
        }
        return false;
    }
    detectMorningStar(segment) {
        for(let i = 2; i < segment.length; i++){
            const first = segment[i - 2];
            const second = segment[i - 1];
            const third = segment[i];
            // Three candle pattern: bearish, small body, bullish
            if (first.close < first.open && // First bearish
            Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small
            third.close > third.open && // Third bullish
            third.close > (first.open + first.close) / 2) {
                return true;
            }
        }
        return false;
    }
    detectEveningStar(segment) {
        for(let i = 2; i < segment.length; i++){
            const first = segment[i - 2];
            const second = segment[i - 1];
            const third = segment[i];
            // Three candle pattern: bullish, small body, bearish
            if (first.close > first.open && // First bullish
            Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small
            third.close < third.open && // Third bearish
            third.close < (first.open + first.close) / 2) {
                return true;
            }
        }
        return false;
    }
    // Generate a segment with a specific pattern
    generateSegmentWithPattern(patternType, length) {
        const segment = [];
        let currentPrice = 100 + Math.random() * 50;
        const now = Date.now();
        // Generate leading candles
        const patternPosition = Math.floor(length * 0.4) + Math.floor(Math.random() * Math.floor(length * 0.3));
        for(let i = 0; i < patternPosition; i++){
            const candle = this.generateRandomCandle(currentPrice, i, now);
            segment.push(candle);
            currentPrice = candle.close;
        }
        // Generate pattern candles
        const patternCandles = this.generateSpecificPattern(patternType, currentPrice, patternPosition, now);
        segment.push(...patternCandles);
        currentPrice = patternCandles[patternCandles.length - 1].close;
        // Generate trailing candles
        for(let i = patternPosition + patternCandles.length; i < length; i++){
            const candle = this.generateRandomCandle(currentPrice, i, now);
            segment.push(candle);
            currentPrice = candle.close;
        }
        return segment;
    }
    generateSpecificPattern(patternType, startPrice, startIndex, baseTime) {
        switch(patternType){
            case 'hammer':
                return this.generateHammerCandle(startPrice, startIndex, baseTime);
            case 'doji':
                return this.generateDojiCandle(startPrice, startIndex, baseTime);
            case 'engulfing_bullish':
                return this.generateBullishEngulfingPattern(startPrice, startIndex, baseTime);
            case 'engulfing_bearish':
                return this.generateBearishEngulfingPattern(startPrice, startIndex, baseTime);
            case 'morning_star':
                return this.generateMorningStarPattern(startPrice, startIndex, baseTime);
            case 'evening_star':
                return this.generateEveningStarPattern(startPrice, startIndex, baseTime);
            default:
                return this.generateHammerCandle(startPrice, startIndex, baseTime);
        }
    }
}
const marketDataService = new MarketDataService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/game-engine/base-game.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseGame": (()=>BaseGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/market-data.ts [app-client] (ecmascript)");
;
;
;
class BaseGame {
    config;
    state;
    startTime;
    endTime;
    isActive = false;
    marketData = new Map();
    constructor(gameType, difficulty){
        const gameConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GAME_CONFIGS"][gameType];
        this.config = {
            type: gameType,
            difficulty,
            duration_seconds: gameConfig.duration_seconds,
            starting_balance: gameConfig.starting_balance,
            available_pairs: [],
            special_rules: {}
        };
        this.state = {
            session_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateSessionId"])(),
            current_balance: this.config.starting_balance,
            positions: [],
            time_remaining: this.config.duration_seconds,
            score: 0,
            multiplier: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QUEST_COIN_MULTIPLIERS"][difficulty]
        };
        this.startTime = Date.now();
        this.endTime = this.startTime + this.config.duration_seconds * 1000;
    }
    // Methods that can be overridden by specific games
    async initialize() {
    // Default implementation - can be overridden
    }
    update() {
    // Default implementation - can be overridden
    }
    calculateScore() {
        // Default implementation - can be overridden
        return 0;
    }
    getGameSpecificData() {
        // Default implementation - can be overridden
        return {};
    }
    // Common game lifecycle methods
    async start() {
        await this.initialize();
        this.isActive = true;
        this.startGameLoop();
    }
    pause() {
        this.isActive = false;
    }
    resume() {
        this.isActive = true;
        this.startGameLoop();
    }
    end() {
        this.isActive = false;
        this.state.score = this.calculateScore();
        this.state.time_remaining = 0;
        return this.state;
    }
    // Trading operations
    async executeTrade(symbol, side, quantity) {
        if (!this.isActive) return false;
        const currentPrice = this.marketData.get(symbol);
        if (!currentPrice) return false;
        const tradeValue = currentPrice * quantity;
        const requiredBalance = side === 'buy' ? tradeValue : 0;
        if (this.state.current_balance < requiredBalance) {
            return false // Insufficient balance
            ;
        }
        // Check position limits
        const maxPositions = this.getMaxPositions();
        if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {
            return false // Too many positions
            ;
        }
        // Execute the trade
        const position = {
            id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            symbol,
            side,
            quantity,
            entry_price: currentPrice,
            current_price: currentPrice,
            pnl: 0,
            timestamp: new Date().toISOString()
        };
        // Update balance
        if (side === 'buy') {
            this.state.current_balance -= tradeValue;
        } else {
            this.state.current_balance += tradeValue;
        }
        // Add or update position
        const existingPositionIndex = this.state.positions.findIndex((p)=>p.symbol === symbol);
        if (existingPositionIndex >= 0) {
            // Update existing position (average price calculation would go here)
            this.state.positions[existingPositionIndex] = position;
        } else {
            this.state.positions.push(position);
        }
        return true;
    }
    async closePosition(positionId) {
        if (!this.isActive) return false;
        const positionIndex = this.state.positions.findIndex((p)=>p.id === positionId);
        if (positionIndex === -1) return false;
        const position = this.state.positions[positionIndex];
        const currentPrice = this.marketData.get(position.symbol);
        if (!currentPrice) return false;
        // Calculate final P&L
        const pnl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculatePnL"])(position.entry_price, currentPrice, position.quantity, position.side);
        // Update balance with P&L
        this.state.current_balance += pnl;
        if (position.side === 'sell') {
            // Return the initial trade value for short positions
            this.state.current_balance += position.entry_price * position.quantity;
        }
        // Remove position
        this.state.positions.splice(positionIndex, 1);
        return true;
    }
    // Market data updates
    async updateMarketData() {
        try {
            const symbols = this.config.available_pairs.map((pair)=>pair.symbol);
            // In a real implementation, you'd fetch from different services based on asset type
            const cryptoSymbols = symbols.filter((s)=>this.isCryptoSymbol(s));
            const stockSymbols = symbols.filter((s)=>this.isStockSymbol(s));
            const forexSymbols = symbols.filter((s)=>this.isForexSymbol(s));
            const [cryptoData, stockData, forexData] = await Promise.all([
                cryptoSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["marketDataService"].getCryptoPrices(cryptoSymbols) : [],
                stockSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["marketDataService"].getStockPrices(stockSymbols) : [],
                forexSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["marketDataService"].getForexPrices(forexSymbols) : []
            ]);
            // Update market data map
            const allData = cryptoData.concat(stockData).concat(forexData);
            allData.forEach((data)=>{
                this.marketData.set(data.symbol, data.price);
            });
            // Update position P&L
            this.updatePositionPnL();
        } catch (error) {
            console.error('Error updating market data:', error);
        }
    }
    // Game state getters
    getState() {
        return {
            ...this.state
        };
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    isGameActive() {
        return this.isActive && this.state.time_remaining > 0;
    }
    getTimeRemaining() {
        if (!this.isActive) return 0;
        const remaining = Math.max(0, this.endTime - Date.now());
        this.state.time_remaining = Math.floor(remaining / 1000);
        return this.state.time_remaining;
    }
    // Protected helper methods
    startGameLoop() {
        if (!this.isActive) return;
        const gameLoop = ()=>{
            if (!this.isActive) return;
            this.update();
            this.getTimeRemaining();
            if (this.state.time_remaining <= 0) {
                this.end();
                return;
            }
            setTimeout(gameLoop, 1000) // Update every second
            ;
        };
        gameLoop();
    }
    updatePositionPnL() {
        this.state.positions.forEach((position)=>{
            const currentPrice = this.marketData.get(position.symbol);
            if (currentPrice) {
                position.current_price = currentPrice;
                position.pnl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculatePnL"])(position.entry_price, currentPrice, position.quantity, position.side);
            }
        });
    }
    getTotalPnL() {
        return this.state.positions.reduce((total, position)=>total + position.pnl, 0);
    }
    getMaxPositions() {
        const gameConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GAME_CONFIGS"][this.config.type];
        return gameConfig.max_positions || 5;
    }
    hasExistingPosition(symbol) {
        return this.state.positions.some((p)=>p.symbol === symbol);
    }
    isCryptoSymbol(symbol) {
        const cryptoSymbols = [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'LINK',
            'UNI',
            'MATIC'
        ];
        return cryptoSymbols.some((crypto)=>symbol.includes(crypto));
    }
    isStockSymbol(symbol) {
        const stockSymbols = [
            'AAPL',
            'GOOGL',
            'TSLA',
            'MSFT',
            'AMZN',
            'META',
            'NVDA'
        ];
        return stockSymbols.includes(symbol);
    }
    isForexSymbol(symbol) {
        return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY');
    }
    generateRandomPrice(basePrice, volatility = 0.02) {
        const change = (Math.random() - 0.5) * 2 * volatility;
        return basePrice * (1 + change);
    }
    simulateMarketMovement() {
        // Simulate realistic market movements for game purposes
        this.marketData.forEach((price, symbol)=>{
            const volatility = this.getSymbolVolatility(symbol);
            const newPrice = this.generateRandomPrice(price, volatility);
            this.marketData.set(symbol, newPrice);
        });
    }
    getSymbolVolatility(symbol) {
        if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto
        ;
        if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks
        ;
        if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex
        ;
        return 0.02 // Default 2%
        ;
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/game-engine/games/candle-strike.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CandleStrikeGame": (()=>CandleStrikeGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/base-game.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-client] (ecmascript)");
;
;
class CandleStrikeGame extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseGame"] {
    gameData;
    currentChallenge = null;
    challengeHistory = [];
    challengeStartTime = 0;
    availablePatterns;
    constructor(difficulty){
        super('candle_strike', difficulty);
        this.gameData = {
            patterns_identified: 0,
            correct_identifications: 0,
            wrong_identifications: 0,
            current_pattern: null,
            patterns_completed: [],
            accuracy_percentage: 0,
            speed_bonus: 0,
            streak_count: 0,
            max_streak: 0
        };
        this.availablePatterns = this.getPatternsByDifficulty(difficulty);
        this.config.available_pairs = [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TRADING_PAIRS"][0]
        ] // Use BTC for pattern recognition
        ;
    }
    async initialize() {
        // Generate first challenge
        await this.generateNewChallenge();
    }
    update() {
        // Update game metrics
        this.updateGameMetrics();
    }
    calculateScore() {
        const baseScore = this.gameData.correct_identifications * 100;
        const accuracyBonus = this.gameData.accuracy_percentage * 2;
        const speedBonus = this.gameData.speed_bonus;
        const streakBonus = this.gameData.max_streak * 50;
        let totalScore = baseScore + accuracyBonus + speedBonus + streakBonus;
        // Difficulty multiplier
        totalScore *= this.state.multiplier;
        return Math.round(Math.max(0, totalScore));
    }
    getGameSpecificData() {
        return {
            ...this.gameData
        };
    }
    getCurrentChallenge() {
        return this.currentChallenge;
    }
    async submitAnswer(answerIndex) {
        if (!this.currentChallenge || !this.isActive) return false;
        const timeToAnswer = Date.now() - this.challengeStartTime;
        const correct = answerIndex === this.currentChallenge.correctAnswer;
        // Record the attempt
        this.challengeHistory.push({
            challenge: this.currentChallenge,
            userAnswer: answerIndex,
            correct,
            timeToAnswer,
            timestamp: Date.now()
        });
        // Update game data
        this.gameData.patterns_identified++;
        if (correct) {
            this.gameData.correct_identifications++;
            this.gameData.streak_count++;
            this.gameData.max_streak = Math.max(this.gameData.max_streak, this.gameData.streak_count);
            // Speed bonus for quick correct answers (under 10 seconds)
            if (timeToAnswer < 10000) {
                const speedBonus = Math.max(0, 50 - Math.floor(timeToAnswer / 200));
                this.gameData.speed_bonus += speedBonus;
            }
            // Add pattern to completed list if not already there
            if (!this.gameData.patterns_completed.find((p)=>p.id === this.currentChallenge.pattern.id)) {
                this.gameData.patterns_completed.push(this.currentChallenge.pattern);
            }
        } else {
            this.gameData.wrong_identifications++;
            this.gameData.streak_count = 0;
        }
        // Update accuracy
        this.gameData.accuracy_percentage = this.gameData.correct_identifications / this.gameData.patterns_identified * 100;
        // Generate next challenge if game is still active
        if (this.isActive && this.state.time_remaining > 0) {
            await this.generateNewChallenge();
        }
        return correct;
    }
    async generateNewChallenge() {
        // Select a random pattern based on difficulty and progress
        const pattern = this.selectNextPattern();
        // Generate candlestick data with the pattern
        const candleData = this.generateCandlestickDataWithPattern(pattern);
        // Find where the pattern occurs in the data
        const patternLocation = this.findPatternInData(candleData, pattern);
        // Generate multiple choice options
        const options = this.generatePatternOptions(pattern);
        this.currentChallenge = {
            pattern,
            candleData,
            patternStartIndex: patternLocation.start,
            patternEndIndex: patternLocation.end,
            options,
            correctAnswer: 0
        };
        // Shuffle options and update correct answer index
        this.shuffleOptions();
        this.gameData.current_pattern = pattern;
        this.challengeStartTime = Date.now();
    }
    selectNextPattern() {
        // Prioritize patterns not yet completed
        const uncompletedPatterns = this.availablePatterns.filter((p)=>!this.gameData.patterns_completed.find((completed)=>completed.id === p.id));
        const patternsToChooseFrom = uncompletedPatterns.length > 0 ? uncompletedPatterns : this.availablePatterns;
        return patternsToChooseFrom[Math.floor(Math.random() * patternsToChooseFrom.length)];
    }
    generateCandlestickDataWithPattern(pattern) {
        const totalCandles = 50;
        const patternPosition = Math.floor(Math.random() * (totalCandles - pattern.maxCandles - 10)) + 10;
        const data = [];
        let currentPrice = 100 + Math.random() * 50;
        // Generate candles before pattern
        for(let i = 0; i < patternPosition; i++){
            const candle = this.generateRandomCandle(currentPrice, i);
            data.push(candle);
            currentPrice = candle.close;
        }
        // Generate pattern candles
        const patternCandles = this.generatePatternCandles(pattern, currentPrice, patternPosition);
        data.push(...patternCandles);
        currentPrice = patternCandles[patternCandles.length - 1].close;
        // Generate candles after pattern
        for(let i = patternPosition + patternCandles.length; i < totalCandles; i++){
            const candle = this.generateRandomCandle(currentPrice, i);
            data.push(candle);
            currentPrice = candle.close;
        }
        return data;
    }
    generateRandomCandle(basePrice, index) {
        const volatility = 0.02;
        const change = (Math.random() - 0.5) * volatility * basePrice;
        const open = basePrice;
        const close = basePrice + change;
        const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
        const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
        return {
            timestamp: Date.now() - (50 - index) * 3600000,
            open,
            high,
            low,
            close,
            volume: Math.random() * 1000000
        };
    }
    generatePatternCandles(pattern, startPrice, startIndex) {
        // This is a simplified pattern generation - in a real implementation,
        // you'd have specific algorithms for each pattern type
        const candles = [];
        let currentPrice = startPrice;
        switch(pattern.id){
            case 'hammer':
                return this.generateHammerPattern(startPrice, startIndex);
            case 'doji':
                return this.generateDojiPattern(startPrice, startIndex);
            case 'engulfing_bullish':
                return this.generateEngulfingPattern(startPrice, startIndex, true);
            case 'engulfing_bearish':
                return this.generateEngulfingPattern(startPrice, startIndex, false);
            case 'morning_star':
                return this.generateMorningStarPattern(startPrice, startIndex);
            case 'evening_star':
                return this.generateEveningStarPattern(startPrice, startIndex);
            default:
                return this.generateHammerPattern(startPrice, startIndex);
        }
    }
    generateHammerPattern(startPrice, startIndex) {
        const open = startPrice;
        const close = startPrice + Math.random() * 0.01 * startPrice // Small body
        ;
        const high = Math.max(open, close) + Math.random() * 0.005 * startPrice // Small upper shadow
        ;
        const low = Math.min(open, close) - (0.02 + Math.random() * 0.01) * startPrice // Long lower shadow
        ;
        return [
            {
                timestamp: Date.now() - (50 - startIndex) * 3600000,
                open,
                high,
                low,
                close,
                volume: Math.random() * 1000000
            }
        ];
    }
    generateDojiPattern(startPrice, startIndex) {
        const open = startPrice;
        const close = startPrice + (Math.random() - 0.5) * 0.002 * startPrice // Very small body
        ;
        const high = Math.max(open, close) + (0.01 + Math.random() * 0.01) * startPrice;
        const low = Math.min(open, close) - (0.01 + Math.random() * 0.01) * startPrice;
        return [
            {
                timestamp: Date.now() - (50 - startIndex) * 3600000,
                open,
                high,
                low,
                close,
                volume: Math.random() * 1000000
            }
        ];
    }
    generateEngulfingPattern(startPrice, startIndex, bullish) {
        const candles = [];
        // First candle (small)
        const firstOpen = startPrice;
        const firstClose = bullish ? startPrice - 0.01 * startPrice : startPrice + 0.01 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex) * 3600000,
            open: firstOpen,
            high: Math.max(firstOpen, firstClose) + 0.002 * startPrice,
            low: Math.min(firstOpen, firstClose) - 0.002 * startPrice,
            close: firstClose,
            volume: Math.random() * 1000000
        });
        // Second candle (engulfing)
        const secondOpen = bullish ? firstClose - 0.005 * startPrice : firstClose + 0.005 * startPrice;
        const secondClose = bullish ? firstOpen + 0.015 * startPrice : firstOpen - 0.015 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
            open: secondOpen,
            high: Math.max(secondOpen, secondClose) + 0.002 * startPrice,
            low: Math.min(secondOpen, secondClose) - 0.002 * startPrice,
            close: secondClose,
            volume: Math.random() * 1000000
        });
        return candles;
    }
    generateMorningStarPattern(startPrice, startIndex) {
        const candles = [];
        // First candle (bearish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex) * 3600000,
            open: startPrice,
            high: startPrice + 0.002 * startPrice,
            low: startPrice - 0.015 * startPrice,
            close: startPrice - 0.012 * startPrice,
            volume: Math.random() * 1000000
        });
        // Second candle (small body/doji)
        const secondPrice = startPrice - 0.015 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
            open: secondPrice,
            high: secondPrice + 0.005 * startPrice,
            low: secondPrice - 0.005 * startPrice,
            close: secondPrice + 0.001 * startPrice,
            volume: Math.random() * 1000000
        });
        // Third candle (bullish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 2) * 3600000,
            open: secondPrice + 0.002 * startPrice,
            high: startPrice - 0.002 * startPrice,
            low: secondPrice,
            close: startPrice - 0.003 * startPrice,
            volume: Math.random() * 1000000
        });
        return candles;
    }
    generateEveningStarPattern(startPrice, startIndex) {
        // Similar to morning star but inverted
        const candles = [];
        // First candle (bullish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex) * 3600000,
            open: startPrice,
            high: startPrice + 0.015 * startPrice,
            low: startPrice - 0.002 * startPrice,
            close: startPrice + 0.012 * startPrice,
            volume: Math.random() * 1000000
        });
        // Second candle (small body/doji)
        const secondPrice = startPrice + 0.015 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
            open: secondPrice,
            high: secondPrice + 0.005 * startPrice,
            low: secondPrice - 0.005 * startPrice,
            close: secondPrice - 0.001 * startPrice,
            volume: Math.random() * 1000000
        });
        // Third candle (bearish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 2) * 3600000,
            open: secondPrice - 0.002 * startPrice,
            high: secondPrice,
            low: startPrice + 0.002 * startPrice,
            close: startPrice + 0.003 * startPrice,
            volume: Math.random() * 1000000
        });
        return candles;
    }
    findPatternInData(data, pattern) {
        // For simplicity, we know where we placed the pattern
        // In a real implementation, you'd search for the pattern in the data
        const totalCandles = data.length;
        const patternPosition = Math.floor(totalCandles * 0.4) // Roughly where we placed it
        ;
        return {
            start: patternPosition,
            end: patternPosition + pattern.maxCandles - 1
        };
    }
    generatePatternOptions(correctPattern) {
        const allPatterns = this.getAllPatterns();
        const wrongPatterns = allPatterns.filter((p)=>p.id !== correctPattern.id).sort(()=>Math.random() - 0.5).slice(0, 3);
        return [
            correctPattern.name,
            ...wrongPatterns.map((p)=>p.name)
        ];
    }
    shuffleOptions() {
        if (!this.currentChallenge) return;
        const options = [
            ...this.currentChallenge.options
        ];
        const correctAnswer = options[0];
        // Fisher-Yates shuffle
        for(let i = options.length - 1; i > 0; i--){
            const j = Math.floor(Math.random() * (i + 1));
            [options[i], options[j]] = [
                options[j],
                options[i]
            ];
        }
        this.currentChallenge.options = options;
        this.currentChallenge.correctAnswer = options.indexOf(correctAnswer);
    }
    updateGameMetrics() {
        // Update accuracy percentage
        if (this.gameData.patterns_identified > 0) {
            this.gameData.accuracy_percentage = this.gameData.correct_identifications / this.gameData.patterns_identified * 100;
        }
    }
    getPatternsByDifficulty(difficulty) {
        const allPatterns = this.getAllPatterns();
        return allPatterns.filter((p)=>p.difficulty === difficulty || difficulty === 'advanced' && p.difficulty !== 'advanced');
    }
    getAllPatterns() {
        return [
            {
                id: 'hammer',
                name: 'Hammer',
                description: 'Bullish reversal pattern with long lower shadow',
                bullish: true,
                difficulty: 'beginner',
                minCandles: 1,
                maxCandles: 1
            },
            {
                id: 'doji',
                name: 'Doji',
                description: 'Indecision pattern with very small body',
                bullish: false,
                difficulty: 'beginner',
                minCandles: 1,
                maxCandles: 1
            },
            {
                id: 'engulfing_bullish',
                name: 'Bullish Engulfing',
                description: 'Two-candle bullish reversal pattern',
                bullish: true,
                difficulty: 'intermediate',
                minCandles: 2,
                maxCandles: 2
            },
            {
                id: 'engulfing_bearish',
                name: 'Bearish Engulfing',
                description: 'Two-candle bearish reversal pattern',
                bullish: false,
                difficulty: 'intermediate',
                minCandles: 2,
                maxCandles: 2
            },
            {
                id: 'morning_star',
                name: 'Morning Star',
                description: 'Three-candle bullish reversal pattern',
                bullish: true,
                difficulty: 'advanced',
                minCandles: 3,
                maxCandles: 3
            },
            {
                id: 'evening_star',
                name: 'Evening Star',
                description: 'Three-candle bearish reversal pattern',
                bullish: false,
                difficulty: 'advanced',
                minCandles: 3,
                maxCandles: 3
            }
        ];
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/charts/candlestick-chart.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChartSkeleton": (()=>ChartSkeleton),
    "PatternAnnotation": (()=>PatternAnnotation),
    "default": (()=>CandlestickChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lightweight-charts/dist/lightweight-charts.development.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function CandlestickChart({ data, width = 800, height = 400, theme = 'dark', patternHighlight, onPatternClick, showVolume = true, title, className = '' }) {
    _s();
    const chartContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const chartRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const candlestickSeriesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const volumeSeriesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandlestickChart.useEffect": ()=>{
            if (!chartContainerRef.current || data.length === 0) return;
            // Create chart
            const chart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createChart"])(chartContainerRef.current, {
                width,
                height,
                layout: {
                    background: {
                        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ColorType"].Solid,
                        color: theme === 'dark' ? '#1a1a1a' : '#ffffff'
                    },
                    textColor: theme === 'dark' ? '#d1d5db' : '#374151'
                },
                grid: {
                    vertLines: {
                        color: theme === 'dark' ? '#374151' : '#e5e7eb'
                    },
                    horzLines: {
                        color: theme === 'dark' ? '#374151' : '#e5e7eb'
                    }
                },
                crosshair: {
                    mode: 1
                },
                rightPriceScale: {
                    borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'
                },
                timeScale: {
                    borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
                    timeVisible: true,
                    secondsVisible: false
                }
            });
            chartRef.current = chart;
            // Add candlestick series
            const candlestickSeries = chart.addCandlestickSeries({
                upColor: '#10b981',
                downColor: '#ef4444',
                borderDownColor: '#ef4444',
                borderUpColor: '#10b981',
                wickDownColor: '#ef4444',
                wickUpColor: '#10b981'
            });
            candlestickSeriesRef.current = candlestickSeries;
            // Add volume series if enabled
            if (showVolume) {
                const volumeSeries = chart.addHistogramSeries({
                    color: theme === 'dark' ? '#6b7280' : '#9ca3af',
                    priceFormat: {
                        type: 'volume'
                    },
                    priceScaleId: '',
                    scaleMargins: {
                        top: 0.7,
                        bottom: 0
                    }
                });
                volumeSeriesRef.current = volumeSeries;
            }
            // Convert data format
            const chartData = data.map({
                "CandlestickChart.useEffect.chartData": (candle)=>({
                        time: Math.floor(candle.timestamp / 1000),
                        open: candle.open,
                        high: candle.high,
                        low: candle.low,
                        close: candle.close
                    })
            }["CandlestickChart.useEffect.chartData"]);
            const volumeData = data.map({
                "CandlestickChart.useEffect.volumeData": (candle)=>({
                        time: Math.floor(candle.timestamp / 1000),
                        value: candle.volume,
                        color: candle.close >= candle.open ? '#10b98150' : '#ef444450'
                    })
            }["CandlestickChart.useEffect.volumeData"]);
            // Set data
            candlestickSeries.setData(chartData);
            if (showVolume && volumeSeriesRef.current) {
                volumeSeriesRef.current.setData(volumeData);
            }
            // Fit content
            chart.timeScale().fitContent();
            setIsLoading(false);
            // Cleanup
            return ({
                "CandlestickChart.useEffect": ()=>{
                    chart.remove();
                }
            })["CandlestickChart.useEffect"];
        }
    }["CandlestickChart.useEffect"], [
        data,
        width,
        height,
        theme,
        showVolume
    ]);
    // Handle pattern highlighting
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandlestickChart.useEffect": ()=>{
            if (!chartRef.current || !candlestickSeriesRef.current || !patternHighlight) return;
            // Add pattern highlight markers
            const markers = [];
            // Start marker
            markers.push({
                time: Math.floor(data[patternHighlight.startIndex]?.timestamp / 1000),
                position: 'belowBar',
                color: patternHighlight.color,
                shape: 'arrowUp',
                text: 'Pattern Start'
            });
            // End marker
            markers.push({
                time: Math.floor(data[patternHighlight.endIndex]?.timestamp / 1000),
                position: 'belowBar',
                color: patternHighlight.color,
                shape: 'arrowUp',
                text: 'Pattern End'
            });
            candlestickSeriesRef.current.setMarkers(markers);
        }
    }["CandlestickChart.useEffect"], [
        patternHighlight,
        data
    ]);
    // Handle click events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandlestickChart.useEffect": ()=>{
            if (!chartRef.current || !onPatternClick) return;
            const handleClick = {
                "CandlestickChart.useEffect.handleClick": (param)=>{
                    if (param.time) {
                        const clickedIndex = data.findIndex({
                            "CandlestickChart.useEffect.handleClick.clickedIndex": (candle)=>Math.floor(candle.timestamp / 1000) === param.time
                        }["CandlestickChart.useEffect.handleClick.clickedIndex"]);
                        if (clickedIndex !== -1) {
                            // For simplicity, assume pattern is 3 candles around clicked point
                            const startIndex = Math.max(0, clickedIndex - 1);
                            const endIndex = Math.min(data.length - 1, clickedIndex + 1);
                            onPatternClick(startIndex, endIndex);
                        }
                    }
                }
            }["CandlestickChart.useEffect.handleClick"];
            chartRef.current.subscribeClick(handleClick);
            return ({
                "CandlestickChart.useEffect": ()=>{
                    if (chartRef.current) {
                        chartRef.current.unsubscribeClick(handleClick);
                    }
                }
            })["CandlestickChart.useEffect"];
        }
    }["CandlestickChart.useEffect"], [
        data,
        onPatternClick
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `relative ${className}`,
        children: [
            title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center mb-2 font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`,
                children: title
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 188,
                columnNumber: 9
            }, this),
            isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 flex items-center justify-center bg-black/20 rounded",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"
                }, void 0, false, {
                    fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                    lineNumber: 197,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 196,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: chartContainerRef,
                className: "rounded border",
                style: {
                    width: `${width}px`,
                    height: `${height}px`,
                    borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 201,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `mt-2 flex justify-between text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "📊 Candlestick Chart"
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 215,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            data.length,
                            " candles"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 216,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
        lineNumber: 186,
        columnNumber: 5
    }, this);
}
_s(CandlestickChart, "4dxvu7vvhoJkVRIqiP2D2wX4lgc=");
_c = CandlestickChart;
function PatternAnnotation({ pattern, theme = 'dark' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `p-3 rounded-lg border ${theme === 'dark' ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2 mb-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: `text-lg ${pattern.bullish ? 'text-green-400' : 'text-red-400'}`,
                        children: pattern.bullish ? '📈' : '📉'
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 237,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-bold",
                        children: pattern.name
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 236,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: `text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`,
                children: pattern.description
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 242,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
        lineNumber: 231,
        columnNumber: 5
    }, this);
}
_c1 = PatternAnnotation;
function ChartSkeleton({ width = 800, height = 400, theme = 'dark' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `animate-pulse rounded border ${theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-gray-200 border-gray-300'}`,
        style: {
            width: `${width}px`,
            height: `${height}px`
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center h-full",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-current mx-auto mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 268,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Loading chart data..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 269,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 267,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/charts/candlestick-chart.tsx",
            lineNumber: 266,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
        lineNumber: 260,
        columnNumber: 5
    }, this);
}
_c2 = ChartSkeleton;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "CandlestickChart");
__turbopack_context__.k.register(_c1, "PatternAnnotation");
__turbopack_context__.k.register(_c2, "ChartSkeleton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/enhanced-market-data.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Enhanced Market Data Service with Real Historical Data Integration
 * Supports multiple data providers and timeframes for immersive chart playback
 */ __turbopack_context__.s({
    "enhancedMarketDataService": (()=>enhancedMarketDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
class EnhancedMarketDataService {
    providers = new Map();
    cache = new Map();
    CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
    ;
    constructor(){
        this.initializeProviders();
    }
    initializeProviders() {
        // Alpha Vantage Provider
        this.providers.set('alphavantage', {
            name: 'Alpha Vantage',
            baseUrl: 'https://www.alphavantage.co/query',
            apiKey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.ALPHA_VANTAGE_API_KEY,
            rateLimit: 5,
            supportedSymbols: [
                'AAPL',
                'GOOGL',
                'MSFT',
                'TSLA',
                'SPY',
                'QQQ'
            ],
            supportedTimeframes: [
                '1m',
                '5m',
                '15m',
                '30m',
                '60m',
                '1d'
            ]
        });
        // Polygon.io Provider
        this.providers.set('polygon', {
            name: 'Polygon.io',
            baseUrl: 'https://api.polygon.io/v2/aggs/ticker',
            apiKey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.POLYGON_API_KEY,
            rateLimit: 5,
            supportedSymbols: [
                'AAPL',
                'GOOGL',
                'MSFT',
                'TSLA',
                'SPY',
                'QQQ'
            ],
            supportedTimeframes: [
                '1m',
                '5m',
                '15m',
                '1h',
                '1d'
            ]
        });
        // CoinGecko for Crypto (Free)
        this.providers.set('coingecko', {
            name: 'CoinGecko',
            baseUrl: 'https://api.coingecko.com/api/v3',
            rateLimit: 10,
            supportedSymbols: [
                'bitcoin',
                'ethereum',
                'cardano',
                'solana',
                'polkadot'
            ],
            supportedTimeframes: [
                '1h',
                '4h',
                '1d'
            ]
        });
    }
    // Get historical data with automatic provider selection
    async getHistoricalData(request) {
        const cacheKey = this.generateCacheKey(request);
        // Check cache first
        const cached = this.cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
            return cached.data;
        }
        try {
            let data = [];
            // Try different providers based on symbol type
            if (this.isCryptoSymbol(request.symbol)) {
                data = await this.fetchFromCoinGecko(request);
            } else if (this.isStockSymbol(request.symbol)) {
                // Try Alpha Vantage first, fallback to Polygon
                try {
                    data = await this.fetchFromAlphaVantage(request);
                } catch (error) {
                    console.warn('Alpha Vantage failed, trying Polygon:', error);
                    data = await this.fetchFromPolygon(request);
                }
            }
            // If no real data available, generate realistic mock data
            if (data.length === 0) {
                console.warn('No real data available, generating mock data for:', request.symbol);
                data = this.generateRealisticHistoricalData(request);
            }
            // Cache the result
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });
            return data;
        } catch (error) {
            console.error('Error fetching historical data:', error);
            // Fallback to mock data
            return this.generateRealisticHistoricalData(request);
        }
    }
    // Fetch from Alpha Vantage
    async fetchFromAlphaVantage(request) {
        const provider = this.providers.get('alphavantage');
        if (!provider.apiKey) {
            throw new Error('Alpha Vantage API key not configured');
        }
        const timeframeMap = {
            '1m': '1min',
            '5m': '5min',
            '15m': '15min',
            '1h': '60min',
            '1d': 'daily'
        };
        const interval = timeframeMap[request.timeframe] || 'daily';
        const functionType = request.timeframe === '1d' ? 'TIME_SERIES_DAILY' : 'TIME_SERIES_INTRADAY';
        const params = new URLSearchParams({
            function: functionType,
            symbol: request.symbol,
            apikey: provider.apiKey,
            outputsize: 'full',
            ...functionType === 'TIME_SERIES_INTRADAY' && {
                interval
            }
        });
        const response = await fetch(`${provider.baseUrl}?${params}`);
        const data = await response.json();
        if (data['Error Message']) {
            throw new Error(data['Error Message']);
        }
        // Parse Alpha Vantage response
        const timeSeriesKey = Object.keys(data).find((key)=>key.includes('Time Series'));
        if (!timeSeriesKey) {
            throw new Error('Invalid Alpha Vantage response format');
        }
        const timeSeries = data[timeSeriesKey];
        const candlesticks = [];
        for (const [timestamp, values] of Object.entries(timeSeries)){
            const candle = values;
            candlesticks.push({
                timestamp: new Date(timestamp).getTime(),
                open: parseFloat(candle['1. open']),
                high: parseFloat(candle['2. high']),
                low: parseFloat(candle['3. low']),
                close: parseFloat(candle['4. close']),
                volume: parseFloat(candle['5. volume'])
            });
        }
        // Filter by date range and sort
        return candlesticks.filter((candle)=>{
            const date = new Date(candle.timestamp);
            return date >= request.startDate && date <= request.endDate;
        }).sort((a, b)=>a.timestamp - b.timestamp).slice(0, request.limit || 1000);
    }
    // Fetch from Polygon.io
    async fetchFromPolygon(request) {
        const provider = this.providers.get('polygon');
        if (!provider.apiKey) {
            throw new Error('Polygon API key not configured');
        }
        const timeframeMap = {
            '1m': {
                multiplier: 1,
                timespan: 'minute'
            },
            '5m': {
                multiplier: 5,
                timespan: 'minute'
            },
            '15m': {
                multiplier: 15,
                timespan: 'minute'
            },
            '1h': {
                multiplier: 1,
                timespan: 'hour'
            },
            '1d': {
                multiplier: 1,
                timespan: 'day'
            }
        };
        const { multiplier, timespan } = timeframeMap[request.timeframe] || {
            multiplier: 1,
            timespan: 'day'
        };
        const startDate = request.startDate.toISOString().split('T')[0];
        const endDate = request.endDate.toISOString().split('T')[0];
        const url = `${provider.baseUrl}/${request.symbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?apikey=${provider.apiKey}`;
        const response = await fetch(url);
        const data = await response.json();
        if (data.status !== 'OK') {
            throw new Error(data.error || 'Polygon API error');
        }
        // Parse Polygon response
        const candlesticks = data.results?.map((result)=>({
                timestamp: result.t,
                open: result.o,
                high: result.h,
                low: result.l,
                close: result.c,
                volume: result.v
            })) || [];
        return candlesticks.sort((a, b)=>a.timestamp - b.timestamp).slice(0, request.limit || 1000);
    }
    // Fetch from CoinGecko
    async fetchFromCoinGecko(request) {
        const provider = this.providers.get('coingecko');
        // CoinGecko uses different symbol format
        const coinId = this.getCoinGeckoId(request.symbol);
        const days = Math.ceil((request.endDate.getTime() - request.startDate.getTime()) / (1000 * 60 * 60 * 24));
        const url = `${provider.baseUrl}/coins/${coinId}/ohlc?vs_currency=usd&days=${days}`;
        const response = await fetch(url);
        const data = await response.json();
        if (!Array.isArray(data)) {
            throw new Error('Invalid CoinGecko response');
        }
        // Parse CoinGecko OHLC data
        const candlesticks = data.map((ohlc)=>({
                timestamp: ohlc[0],
                open: ohlc[1],
                high: ohlc[2],
                low: ohlc[3],
                close: ohlc[4],
                volume: Math.random() * 1000000
            }));
        return candlesticks.filter((candle)=>{
            const date = new Date(candle.timestamp);
            return date >= request.startDate && date <= request.endDate;
        }).sort((a, b)=>a.timestamp - b.timestamp).slice(0, request.limit || 1000);
    }
    // Generate realistic historical data with patterns and events
    generateRealisticHistoricalData(request) {
        const { symbol, timeframe, startDate, endDate, limit = 1000 } = request;
        const timeframeMs = this.getTimeframeMs(timeframe);
        const totalCandles = Math.min(Math.floor((endDate.getTime() - startDate.getTime()) / timeframeMs), limit);
        const candlesticks = [];
        let currentPrice = this.getBasePrice(symbol);
        let currentTime = startDate.getTime();
        // Add market events and patterns
        const events = this.generateMarketEvents(startDate, endDate, symbol);
        for(let i = 0; i < totalCandles; i++){
            // Check for events at this timestamp
            const currentEvent = events.find((event)=>Math.abs(event.timestamp - currentTime) < timeframeMs);
            // Generate candle with event influence
            const candle = this.generateCandleWithEvent(currentPrice, currentTime, symbol, timeframe, currentEvent);
            candlesticks.push(candle);
            currentPrice = candle.close;
            currentTime += timeframeMs;
        }
        return candlesticks;
    }
    // Generate market events for educational purposes
    generateMarketEvents(startDate, endDate, symbol) {
        const events = [];
        const duration = endDate.getTime() - startDate.getTime();
        const eventCount = Math.floor(duration / (1000 * 60 * 60 * 24 * 7)) // Weekly events
        ;
        for(let i = 0; i < eventCount; i++){
            const timestamp = startDate.getTime() + duration * Math.random();
            events.push({
                timestamp,
                title: this.getRandomEventTitle(symbol),
                description: this.getRandomEventDescription(symbol),
                impact: [
                    'low',
                    'medium',
                    'high'
                ][Math.floor(Math.random() * 3)],
                category: [
                    'earnings',
                    'news',
                    'economic',
                    'technical'
                ][Math.floor(Math.random() * 4)]
            });
        }
        return events.sort((a, b)=>a.timestamp - b.timestamp);
    }
    // Helper methods
    generateCacheKey(request) {
        return `${request.symbol}_${request.timeframe}_${request.startDate.getTime()}_${request.endDate.getTime()}`;
    }
    isCryptoSymbol(symbol) {
        return [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'bitcoin',
            'ethereum'
        ].includes(symbol.toLowerCase());
    }
    isStockSymbol(symbol) {
        return [
            'AAPL',
            'GOOGL',
            'MSFT',
            'TSLA',
            'SPY',
            'QQQ'
        ].includes(symbol.toUpperCase());
    }
    getCoinGeckoId(symbol) {
        const mapping = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum',
            'ADA': 'cardano',
            'SOL': 'solana',
            'DOT': 'polkadot'
        };
        return mapping[symbol.toUpperCase()] || 'bitcoin';
    }
    getTimeframeMs(timeframe) {
        const mapping = {
            '1m': 60 * 1000,
            '5m': 5 * 60 * 1000,
            '15m': 15 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        };
        return mapping[timeframe] || 60 * 60 * 1000;
    }
    getBasePrice(symbol) {
        const prices = {
            'AAPL': 150,
            'GOOGL': 2500,
            'MSFT': 300,
            'TSLA': 800,
            'SPY': 400,
            'BTC': 45000,
            'ETH': 3000
        };
        return prices[symbol.toUpperCase()] || 100;
    }
    generateCandleWithEvent(basePrice, timestamp, symbol, timeframe, event) {
        let volatility = 0.02 // 2% base volatility
        ;
        // Increase volatility for events
        if (event) {
            const eventMultiplier = {
                low: 1.5,
                medium: 2.5,
                high: 4.0
            };
            volatility *= eventMultiplier[event.impact];
        }
        const change = (Math.random() - 0.5) * volatility * basePrice;
        const open = basePrice;
        const close = basePrice + change;
        const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
        const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
        return {
            timestamp,
            open,
            high,
            low,
            close,
            volume: (500000 + Math.random() * 1000000) * (event ? 2 : 1)
        };
    }
    getRandomEventTitle(symbol) {
        const titles = [
            `${symbol} Earnings Report Released`,
            `Major ${symbol} Partnership Announced`,
            `${symbol} Stock Split Declared`,
            `Analyst Upgrades ${symbol} Rating`,
            `${symbol} CEO Interview on Market Outlook`,
            `Regulatory News Affects ${symbol}`,
            `${symbol} Technical Breakout Detected`
        ];
        return titles[Math.floor(Math.random() * titles.length)];
    }
    getRandomEventDescription(symbol) {
        const descriptions = [
            `Quarterly earnings exceeded expectations with strong revenue growth.`,
            `Strategic partnership announcement drives investor confidence.`,
            `Stock split announcement indicates management confidence in future growth.`,
            `Analyst upgrade based on improved fundamentals and market position.`,
            `CEO provides positive outlook on company direction and market opportunities.`,
            `Regulatory developments create uncertainty in the market.`,
            `Technical analysis indicates potential breakout from consolidation pattern.`
        ];
        return descriptions[Math.floor(Math.random() * descriptions.length)];
    }
}
const enhancedMarketDataService = new EnhancedMarketDataService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/chart-playback/playback-engine.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Interactive Historical Chart Playback Engine
 * Provides time-travel functionality for educational trading experiences
 */ __turbopack_context__.s({
    "ChartPlaybackEngine": (()=>ChartPlaybackEngine)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$enhanced$2d$market$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/enhanced-market-data.ts [app-client] (ecmascript)");
;
class ChartPlaybackEngine {
    data = [];
    events = [];
    predictions = [];
    state;
    intervalId = null;
    eventCallbacks = new Map();
    patternDetector;
    constructor(){
        this.state = {
            isPlaying: false,
            isPaused: false,
            currentIndex: 0,
            totalCandles: 0,
            speed: 1.0,
            timeframe: '1h',
            symbol: 'BTCUSD',
            startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            endDate: new Date()
        };
        this.patternDetector = new PatternDetector();
        this.initializeEventTypes();
    }
    initializeEventTypes() {
        this.eventCallbacks.set('candle_update', []);
        this.eventCallbacks.set('pattern_detected', []);
        this.eventCallbacks.set('market_event', []);
        this.eventCallbacks.set('prediction_point', []);
        this.eventCallbacks.set('state_change', []);
    }
    // Initialize playback with historical data
    async initialize(symbol, timeframe, startDate, endDate) {
        this.state.symbol = symbol;
        this.state.timeframe = timeframe;
        this.state.startDate = startDate;
        this.state.endDate = endDate;
        this.state.currentIndex = 0;
        // Fetch historical data
        const request = {
            symbol,
            timeframe: timeframe,
            startDate,
            endDate,
            limit: 2000
        };
        try {
            this.data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$enhanced$2d$market$2d$data$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enhancedMarketDataService"].getHistoricalData(request);
            this.state.totalCandles = this.data.length;
            // Generate educational events and prediction points
            this.generateEducationalEvents();
            this.generatePredictionChallenges();
            this.emitEvent('state_change', this.state);
        } catch (error) {
            console.error('Failed to initialize playback:', error);
            throw error;
        }
    }
    // Playback controls
    play() {
        if (this.state.isPlaying) return;
        this.state.isPlaying = true;
        this.state.isPaused = false;
        const baseInterval = 1000 // 1 second base interval
        ;
        const interval = baseInterval / this.state.speed;
        this.intervalId = window.setInterval(()=>{
            this.nextCandle();
        }, interval);
        this.emitEvent('state_change', this.state);
    }
    pause() {
        if (!this.state.isPlaying) return;
        this.state.isPlaying = false;
        this.state.isPaused = true;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.emitEvent('state_change', this.state);
    }
    stop() {
        this.state.isPlaying = false;
        this.state.isPaused = false;
        this.state.currentIndex = 0;
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.emitEvent('state_change', this.state);
    }
    setSpeed(speed) {
        const validSpeeds = [
            0.25,
            0.5,
            1.0,
            2.0,
            4.0
        ];
        this.state.speed = validSpeeds.includes(speed) ? speed : 1.0;
        // Restart playback with new speed if currently playing
        if (this.state.isPlaying) {
            this.pause();
            this.play();
        }
        this.emitEvent('state_change', this.state);
    }
    jumpToIndex(index) {
        const newIndex = Math.max(0, Math.min(index, this.data.length - 1));
        this.state.currentIndex = newIndex;
        this.processCurrentCandle();
        this.emitEvent('state_change', this.state);
    }
    jumpToDate(date) {
        const targetTimestamp = date.getTime();
        const closestIndex = this.data.findIndex((candle)=>candle.timestamp >= targetTimestamp);
        if (closestIndex !== -1) {
            this.jumpToIndex(closestIndex);
        }
    }
    nextCandle() {
        if (this.state.currentIndex >= this.data.length - 1) {
            this.stop();
            return;
        }
        this.state.currentIndex++;
        this.processCurrentCandle();
        this.emitEvent('state_change', this.state);
    }
    previousCandle() {
        if (this.state.currentIndex <= 0) return;
        this.state.currentIndex--;
        this.processCurrentCandle();
        this.emitEvent('state_change', this.state);
    }
    async setTimeframe(timeframe) {
        this.pause();
        await this.initialize(this.state.symbol, timeframe, this.state.startDate, this.state.endDate);
    }
    // Event system
    on(eventType, callback) {
        if (!this.eventCallbacks.has(eventType)) {
            this.eventCallbacks.set(eventType, []);
        }
        this.eventCallbacks.get(eventType).push(callback);
    }
    off(eventType, callback) {
        const callbacks = this.eventCallbacks.get(eventType);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
    emitEvent(eventType, data) {
        const callbacks = this.eventCallbacks.get(eventType) || [];
        callbacks.forEach((callback)=>{
            try {
                callback(data);
            } catch (error) {
                console.error('Error in playback event callback:', error);
            }
        });
    }
    // Process current candle and detect events
    processCurrentCandle() {
        const currentCandle = this.getCurrentCandle();
        if (!currentCandle) return;
        // Emit candle update
        this.emitEvent('candle_update', {
            candle: currentCandle,
            index: this.state.currentIndex,
            visibleData: this.getVisibleData()
        });
        // Check for patterns
        this.detectPatterns();
        // Check for market events
        this.checkMarketEvents();
        // Check for prediction points
        this.checkPredictionPoints();
    }
    detectPatterns() {
        const recentCandles = this.getRecentCandles(20) // Last 20 candles for pattern detection
        ;
        const patterns = this.patternDetector.detectPatterns(recentCandles);
        patterns.forEach((pattern)=>{
            this.emitEvent('pattern_detected', {
                pattern,
                timestamp: this.getCurrentCandle()?.timestamp,
                candles: recentCandles
            });
        });
    }
    checkMarketEvents() {
        const currentTimestamp = this.getCurrentCandle()?.timestamp;
        if (!currentTimestamp) return;
        const relevantEvents = this.events.filter((event)=>Math.abs(event.timestamp - currentTimestamp) < 60 * 60 * 1000 // Within 1 hour
        );
        relevantEvents.forEach((event)=>{
            this.emitEvent('market_event', event);
        });
    }
    checkPredictionPoints() {
        const prediction = this.predictions.find((p)=>p.timestamp === this.getCurrentCandle()?.timestamp);
        if (prediction) {
            this.pause() // Pause for prediction challenge
            ;
            this.emitEvent('prediction_point', prediction);
        }
    }
    // Data access methods
    getCurrentCandle() {
        return this.data[this.state.currentIndex] || null;
    }
    getVisibleData() {
        return this.data.slice(0, this.state.currentIndex + 1);
    }
    getRecentCandles(count) {
        const start = Math.max(0, this.state.currentIndex - count + 1);
        return this.data.slice(start, this.state.currentIndex + 1);
    }
    getState() {
        return {
            ...this.state
        };
    }
    getAllData() {
        return [
            ...this.data
        ];
    }
    getEvents() {
        return [
            ...this.events
        ];
    }
    getPredictions() {
        return [
            ...this.predictions
        ];
    }
    // Generate educational content
    generateEducationalEvents() {
        // This would be populated with real market events in production
        this.events = [];
        // Generate some sample events for demonstration
        const eventCount = Math.floor(this.data.length / 50) // One event per ~50 candles
        ;
        for(let i = 0; i < eventCount; i++){
            const randomIndex = Math.floor(Math.random() * this.data.length);
            const candle = this.data[randomIndex];
            this.events.push({
                timestamp: candle.timestamp,
                title: `Market Event ${i + 1}`,
                description: `Educational market event for learning purposes`,
                impact: [
                    'low',
                    'medium',
                    'high'
                ][Math.floor(Math.random() * 3)],
                category: [
                    'earnings',
                    'news',
                    'economic',
                    'technical'
                ][Math.floor(Math.random() * 4)]
            });
        }
        this.events.sort((a, b)=>a.timestamp - b.timestamp);
    }
    generatePredictionChallenges() {
        this.predictions = [];
        // Generate prediction points at key moments
        const predictionCount = Math.floor(this.data.length / 100) // One prediction per ~100 candles
        ;
        for(let i = 0; i < predictionCount; i++){
            const randomIndex = Math.floor(Math.random() * (this.data.length - 10)) + 5 // Ensure we have future data
            ;
            const candle = this.data[randomIndex];
            const futureCandle = this.data[randomIndex + 5] // Look 5 candles ahead
            ;
            const isUpward = futureCandle.close > candle.close;
            this.predictions.push({
                id: `prediction_${i}`,
                timestamp: candle.timestamp,
                question: `What do you think will happen to the price in the next few periods?`,
                options: [
                    'Price will go up',
                    'Price will go down',
                    'Price will stay flat'
                ],
                correctAnswer: isUpward ? 0 : 1,
                explanation: `The price ${isUpward ? 'increased' : 'decreased'} from ${candle.close.toFixed(2)} to ${futureCandle.close.toFixed(2)}`,
                difficulty: 'medium',
                points: 10
            });
        }
        this.predictions.sort((a, b)=>a.timestamp - b.timestamp);
    }
}
// Pattern detection helper class
class PatternDetector {
    detectPatterns(candles) {
        const patterns = [];
        if (candles.length < 3) return patterns;
        // Simple pattern detection (can be expanded)
        if (this.isHammer(candles[candles.length - 1])) {
            patterns.push({
                name: 'Hammer',
                confidence: 0.8,
                description: 'Potential bullish reversal pattern detected'
            });
        }
        if (this.isDoji(candles[candles.length - 1])) {
            patterns.push({
                name: 'Doji',
                confidence: 0.7,
                description: 'Market indecision pattern detected'
            });
        }
        if (this.isEngulfing(candles.slice(-2))) {
            patterns.push({
                name: 'Engulfing',
                confidence: 0.9,
                description: 'Strong reversal pattern detected'
            });
        }
        return patterns;
    }
    isHammer(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
        const upperShadow = candle.high - Math.max(candle.open, candle.close);
        const totalRange = candle.high - candle.low;
        return bodySize < totalRange * 0.3 && lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5;
    }
    isDoji(candle) {
        const bodySize = Math.abs(candle.close - candle.open);
        const totalRange = candle.high - candle.low;
        return bodySize < totalRange * 0.1 && totalRange > 0;
    }
    isEngulfing(candles) {
        if (candles.length < 2) return false;
        const [prev, curr] = candles;
        // Bullish engulfing
        if (prev.close < prev.open && curr.close > curr.open) {
            return curr.open < prev.close && curr.close > prev.open;
        }
        // Bearish engulfing
        if (prev.close > prev.open && curr.close < curr.open) {
            return curr.open > prev.close && curr.close < prev.open;
        }
        return false;
    }
}
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/charts/playback-chart.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PlaybackChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lightweight-charts/dist/lightweight-charts.development.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$chart$2d$playback$2f$playback$2d$engine$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/chart-playback/playback-engine.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/theme/theme-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function PlaybackChart({ symbol, timeframe, startDate, endDate, onPredictionChallenge, onPatternDetected, onMarketEvent, className = '' }) {
    _s();
    const chartContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const chartRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const candlestickSeriesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const volumeSeriesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const playbackEngineRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [playbackState, setPlaybackState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [currentPrediction, setCurrentPrediction] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [detectedPatterns, setDetectedPatterns] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [marketEvents, setMarketEvents] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const colors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeColors"])();
    const { interfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"])();
    const isAdolescentMode = interfaceMode === 'adolescent';
    // Initialize playback engine and chart
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PlaybackChart.useEffect": ()=>{
            if (!chartContainerRef.current) return;
            const initializeChart = {
                "PlaybackChart.useEffect.initializeChart": async ()=>{
                    setIsLoading(true);
                    try {
                        // Create chart
                        const chart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createChart"])(chartContainerRef.current, {
                            width: 800,
                            height: 500,
                            layout: {
                                background: {
                                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ColorType"].Solid,
                                    color: colors.background
                                },
                                textColor: colors.textPrimary
                            },
                            grid: {
                                vertLines: {
                                    color: colors.chartGrid
                                },
                                horzLines: {
                                    color: colors.chartGrid
                                }
                            },
                            crosshair: {
                                mode: 1
                            },
                            rightPriceScale: {
                                borderColor: colors.border
                            },
                            timeScale: {
                                borderColor: colors.border,
                                timeVisible: true,
                                secondsVisible: false
                            }
                        });
                        chartRef.current = chart;
                        // Add candlestick series
                        const candlestickSeries = chart.addCandlestickSeries({
                            upColor: colors.bullish,
                            downColor: colors.bearish,
                            borderDownColor: colors.bearish,
                            borderUpColor: colors.bullish,
                            wickDownColor: colors.bearish,
                            wickUpColor: colors.bullish
                        });
                        candlestickSeriesRef.current = candlestickSeries;
                        // Add volume series
                        const volumeSeries = chart.addHistogramSeries({
                            color: colors.chartVolume,
                            priceFormat: {
                                type: 'volume'
                            },
                            priceScaleId: '',
                            scaleMargins: {
                                top: 0.7,
                                bottom: 0
                            }
                        });
                        volumeSeriesRef.current = volumeSeries;
                        // Initialize playback engine
                        const engine = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$chart$2d$playback$2f$playback$2d$engine$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartPlaybackEngine"]();
                        playbackEngineRef.current = engine;
                        // Set up event listeners
                        engine.on('state_change', {
                            "PlaybackChart.useEffect.initializeChart": (state)=>{
                                setPlaybackState(state);
                            }
                        }["PlaybackChart.useEffect.initializeChart"]);
                        engine.on('candle_update', {
                            "PlaybackChart.useEffect.initializeChart": ({ candle, visibleData })=>{
                                updateChart(visibleData);
                            }
                        }["PlaybackChart.useEffect.initializeChart"]);
                        engine.on('pattern_detected', {
                            "PlaybackChart.useEffect.initializeChart": (pattern)=>{
                                setDetectedPatterns({
                                    "PlaybackChart.useEffect.initializeChart": (prev)=>[
                                            ...prev,
                                            pattern
                                        ]
                                }["PlaybackChart.useEffect.initializeChart"]);
                                onPatternDetected?.(pattern);
                                addPatternMarker(pattern);
                            }
                        }["PlaybackChart.useEffect.initializeChart"]);
                        engine.on('market_event', {
                            "PlaybackChart.useEffect.initializeChart": (event)=>{
                                setMarketEvents({
                                    "PlaybackChart.useEffect.initializeChart": (prev)=>[
                                            ...prev,
                                            event
                                        ]
                                }["PlaybackChart.useEffect.initializeChart"]);
                                onMarketEvent?.(event);
                                addEventMarker(event);
                            }
                        }["PlaybackChart.useEffect.initializeChart"]);
                        engine.on('prediction_point', {
                            "PlaybackChart.useEffect.initializeChart": (prediction)=>{
                                setCurrentPrediction(prediction);
                                onPredictionChallenge?.(prediction);
                            }
                        }["PlaybackChart.useEffect.initializeChart"]);
                        // Initialize with data
                        await engine.initialize(symbol, timeframe, startDate, endDate);
                        setIsLoading(false);
                    } catch (error) {
                        console.error('Failed to initialize playback chart:', error);
                        setIsLoading(false);
                    }
                }
            }["PlaybackChart.useEffect.initializeChart"];
            initializeChart();
            // Cleanup
            return ({
                "PlaybackChart.useEffect": ()=>{
                    if (chartRef.current) {
                        chartRef.current.remove();
                    }
                    if (playbackEngineRef.current) {
                        playbackEngineRef.current.stop();
                    }
                }
            })["PlaybackChart.useEffect"];
        }
    }["PlaybackChart.useEffect"], [
        symbol,
        timeframe,
        startDate,
        endDate
    ]);
    // Update chart with new data
    const updateChart = (data)=>{
        if (!candlestickSeriesRef.current || !volumeSeriesRef.current) return;
        const chartData = data.map((candle)=>({
                time: Math.floor(candle.timestamp / 1000),
                open: candle.open,
                high: candle.high,
                low: candle.low,
                close: candle.close
            }));
        const volumeData = data.map((candle)=>({
                time: Math.floor(candle.timestamp / 1000),
                value: candle.volume,
                color: candle.close >= candle.open ? colors.bullish + '50' : colors.bearish + '50'
            }));
        candlestickSeriesRef.current.setData(chartData);
        volumeSeriesRef.current.setData(volumeData);
        // Auto-scroll to latest data
        if (chartRef.current && data.length > 0) {
            chartRef.current.timeScale().scrollToRealTime();
        }
    };
    // Add pattern detection markers
    const addPatternMarker = (pattern)=>{
        if (!candlestickSeriesRef.current || !pattern.timestamp) return;
        const markers = candlestickSeriesRef.current.markers() || [];
        markers.push({
            time: Math.floor(pattern.timestamp / 1000),
            position: 'belowBar',
            color: colors.warning,
            shape: 'circle',
            text: pattern.pattern.name
        });
        candlestickSeriesRef.current.setMarkers(markers);
    };
    // Add market event markers
    const addEventMarker = (event)=>{
        if (!candlestickSeriesRef.current || !event.timestamp) return;
        const markers = candlestickSeriesRef.current.markers() || [];
        const impactColors = {
            low: colors.info,
            medium: colors.warning,
            high: colors.error
        };
        markers.push({
            time: Math.floor(event.timestamp / 1000),
            position: 'aboveBar',
            color: impactColors[event.impact] || colors.info,
            shape: 'arrowDown',
            text: event.title.substring(0, 10) + '...'
        });
        candlestickSeriesRef.current.setMarkers(markers);
    };
    // Playback controls
    const handlePlay = ()=>playbackEngineRef.current?.play();
    const handlePause = ()=>playbackEngineRef.current?.pause();
    const handleStop = ()=>playbackEngineRef.current?.stop();
    const handleSpeedChange = (speed)=>playbackEngineRef.current?.setSpeed(speed);
    const handleJumpToIndex = (index)=>playbackEngineRef.current?.jumpToIndex(index);
    // Handle prediction response
    const handlePredictionResponse = (answerIndex)=>{
        if (!currentPrediction) return;
        const isCorrect = answerIndex === currentPrediction.correctAnswer;
        // Show result (you could emit an event or show a modal here)
        console.log(`Prediction ${isCorrect ? 'correct' : 'incorrect'}: ${currentPrediction.explanation}`);
        setCurrentPrediction(null);
        // Resume playback
        setTimeout(()=>{
            playbackEngineRef.current?.play();
        }, 2000);
    };
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${className} flex items-center justify-center h-96`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-12 w-12 border-b-2 border-current mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                        lineNumber: 246,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        style: {
                            color: colors.textSecondary
                        },
                        children: isAdolescentMode ? '🔄 Loading historical data...' : 'LOADING_HISTORICAL_DATA...'
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                        lineNumber: 247,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/playback-chart.tsx",
                lineNumber: 245,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/charts/playback-chart.tsx",
            lineNumber: 244,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${className} space-y-4`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: chartContainerRef,
                className: "rounded-lg border",
                style: {
                    borderColor: colors.border
                }
            }, void 0, false, {
                fileName: "[project]/src/components/charts/playback-chart.tsx",
                lineNumber: 258,
                columnNumber: 7
            }, this),
            playbackState && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4 rounded-lg",
                style: {
                    backgroundColor: colors.backgroundSecondary,
                    borderColor: colors.border
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handlePlay,
                                        disabled: playbackState.isPlaying,
                                        className: "px-3 py-1 rounded text-sm font-bold transition-colors",
                                        style: {
                                            backgroundColor: playbackState.isPlaying ? colors.disabled : colors.success,
                                            color: colors.background
                                        },
                                        children: isAdolescentMode ? '▶️ Play' : 'PLAY'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 272,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handlePause,
                                        disabled: !playbackState.isPlaying,
                                        className: "px-3 py-1 rounded text-sm font-bold transition-colors",
                                        style: {
                                            backgroundColor: !playbackState.isPlaying ? colors.disabled : colors.warning,
                                            color: colors.background
                                        },
                                        children: isAdolescentMode ? '⏸️ Pause' : 'PAUSE'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 284,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleStop,
                                        className: "px-3 py-1 rounded text-sm font-bold transition-colors",
                                        style: {
                                            backgroundColor: colors.error,
                                            color: colors.background
                                        },
                                        children: isAdolescentMode ? '⏹️ Stop' : 'STOP'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 296,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/charts/playback-chart.tsx",
                                lineNumber: 271,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            color: colors.textSecondary
                                        },
                                        className: "text-sm",
                                        children: isAdolescentMode ? 'Speed:' : 'SPEED:'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 307,
                                        columnNumber: 15
                                    }, this),
                                    [
                                        0.25,
                                        0.5,
                                        1.0,
                                        2.0,
                                        4.0
                                    ].map((speed)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleSpeedChange(speed),
                                            className: `px-2 py-1 rounded text-xs transition-colors ${playbackState.speed === speed ? 'font-bold' : ''}`,
                                            style: {
                                                backgroundColor: playbackState.speed === speed ? colors.primary : colors.backgroundTertiary,
                                                color: playbackState.speed === speed ? colors.background : colors.textSecondary
                                            },
                                            children: [
                                                speed,
                                                "x"
                                            ]
                                        }, speed, true, {
                                            fileName: "[project]/src/components/charts/playback-chart.tsx",
                                            lineNumber: 311,
                                            columnNumber: 17
                                        }, this))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/charts/playback-chart.tsx",
                                lineNumber: 306,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                        lineNumber: 270,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between text-sm",
                                style: {
                                    color: colors.textSecondary
                                },
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            isAdolescentMode ? 'Progress:' : 'PROGRESS:',
                                            " ",
                                            playbackState.currentIndex + 1,
                                            " / ",
                                            playbackState.totalCandles
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 331,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            isAdolescentMode ? 'Speed:' : 'SPEED:',
                                            " ",
                                            playbackState.speed,
                                            "x"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 334,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/charts/playback-chart.tsx",
                                lineNumber: 330,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full h-2 rounded-full",
                                style: {
                                    backgroundColor: colors.backgroundTertiary
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "h-2 rounded-full transition-all duration-300",
                                    style: {
                                        backgroundColor: colors.primary,
                                        width: `${playbackState.currentIndex / Math.max(playbackState.totalCandles - 1, 1) * 100}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/charts/playback-chart.tsx",
                                    lineNumber: 343,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/charts/playback-chart.tsx",
                                lineNumber: 339,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                type: "range",
                                min: "0",
                                max: playbackState.totalCandles - 1,
                                value: playbackState.currentIndex,
                                onChange: (e)=>handleJumpToIndex(parseInt(e.target.value)),
                                className: "w-full"
                            }, void 0, false, {
                                fileName: "[project]/src/components/charts/playback-chart.tsx",
                                lineNumber: 352,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                        lineNumber: 329,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/playback-chart.tsx",
                lineNumber: 266,
                columnNumber: 9
            }, this),
            currentPrediction && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-md w-full p-6 rounded-lg",
                    style: {
                        backgroundColor: colors.background,
                        borderColor: colors.border
                    },
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-bold mb-4",
                            style: {
                                color: colors.textPrimary
                            },
                            children: isAdolescentMode ? '🔮 Prediction Challenge!' : '📊 MARKET_PREDICTION_CHALLENGE'
                        }, void 0, false, {
                            fileName: "[project]/src/components/charts/playback-chart.tsx",
                            lineNumber: 371,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-4",
                            style: {
                                color: colors.textSecondary
                            },
                            children: currentPrediction.question
                        }, void 0, false, {
                            fileName: "[project]/src/components/charts/playback-chart.tsx",
                            lineNumber: 375,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-2 mb-4",
                            children: currentPrediction.options.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>handlePredictionResponse(index),
                                    className: "w-full p-3 rounded text-left transition-colors",
                                    style: {
                                        backgroundColor: colors.backgroundSecondary,
                                        borderColor: colors.border,
                                        color: colors.textPrimary
                                    },
                                    children: option
                                }, index, false, {
                                    fileName: "[project]/src/components/charts/playback-chart.tsx",
                                    lineNumber: 381,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/components/charts/playback-chart.tsx",
                            lineNumber: 379,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm",
                            style: {
                                color: colors.textMuted
                            },
                            children: isAdolescentMode ? `💎 Points: ${currentPrediction.points}` : `POINTS: ${currentPrediction.points}`
                        }, void 0, false, {
                            fileName: "[project]/src/components/charts/playback-chart.tsx",
                            lineNumber: 396,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/charts/playback-chart.tsx",
                    lineNumber: 367,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/charts/playback-chart.tsx",
                lineNumber: 366,
                columnNumber: 9
            }, this),
            detectedPatterns.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-4 rounded-lg",
                style: {
                    backgroundColor: colors.backgroundSecondary,
                    borderColor: colors.border
                },
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "font-bold mb-2",
                        style: {
                            color: colors.textPrimary
                        },
                        children: isAdolescentMode ? '🔍 Detected Patterns' : '📊 PATTERN_DETECTION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                        lineNumber: 409,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-2",
                        children: detectedPatterns.slice(-3).map((pattern, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        style: {
                                            color: colors.warning
                                        },
                                        children: pattern.pattern.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 415,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        style: {
                                            color: colors.textSecondary
                                        },
                                        className: "ml-2",
                                        children: [
                                            "- ",
                                            pattern.pattern.description
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                                        lineNumber: 418,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/components/charts/playback-chart.tsx",
                                lineNumber: 414,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/playback-chart.tsx",
                        lineNumber: 412,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/playback-chart.tsx",
                lineNumber: 405,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/charts/playback-chart.tsx",
        lineNumber: 256,
        columnNumber: 5
    }, this);
}
_s(PlaybackChart, "RVOKKDPdLGPrYRCD4t9ZjzMS7+M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$theme$2f$theme$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useThemeColors"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"]
    ];
});
_c = PlaybackChart;
var _c;
__turbopack_context__.k.register(_c, "PlaybackChart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/games/candle-strike-game.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CandleStrikeGameComponent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$candle$2d$strike$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/games/candle-strike.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/charts/candlestick-chart.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$playback$2d$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/charts/playback-chart.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function CandleStrikeGameComponent({ difficulty, onGameEnd, usePlayback = false, className = '' }) {
    _s();
    const { interfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"])();
    const [game, setGame] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentChallenge, setCurrentChallenge] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameData, setGameData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedAnswer, setSelectedAnswer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showResult, setShowResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastAnswerCorrect, setLastAnswerCorrect] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const isAdolescentMode = interfaceMode === 'adolescent';
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandleStrikeGameComponent.useEffect": ()=>{
            initializeGame();
        }
    }["CandleStrikeGameComponent.useEffect"], [
        difficulty
    ]);
    const initializeGame = async ()=>{
        setIsLoading(true);
        const newGame = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$candle$2d$strike$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CandleStrikeGame"](difficulty);
        setGame(newGame);
        await newGame.start();
        // Update game state every second
        const interval = setInterval(()=>{
            if (newGame.isGameActive()) {
                setGameState(newGame.getState());
                setCurrentChallenge(newGame.getCurrentChallenge());
                setGameData(newGame.getGameSpecificData());
            } else {
                clearInterval(interval);
                const finalScore = newGame.calculateScore();
                onGameEnd(finalScore);
            }
        }, 1000);
        setIsLoading(false);
    };
    const handleAnswerSubmit = async (answerIndex)=>{
        if (!game || selectedAnswer !== null || showResult) return;
        setSelectedAnswer(answerIndex);
        const correct = await game.submitAnswer(answerIndex);
        setLastAnswerCorrect(correct);
        setShowResult(true);
        // Update game state
        setGameState(game.getState());
        setGameData(game.getGameSpecificData());
        // Auto-advance to next challenge after 2 seconds
        setTimeout(()=>{
            setSelectedAnswer(null);
            setShowResult(false);
            setLastAnswerCorrect(null);
            setCurrentChallenge(game.getCurrentChallenge());
        }, 2000);
    };
    const getPatternHighlight = ()=>{
        if (!currentChallenge) return undefined;
        return {
            startIndex: currentChallenge.patternStartIndex,
            endIndex: currentChallenge.patternEndIndex,
            color: isAdolescentMode ? '#fbbf24' : '#10b981'
        };
    };
    const getAnswerButtonStyle = (index)=>{
        const baseStyle = `p-3 rounded-lg font-bold transition-all duration-300 ${isAdolescentMode ? 'text-white border-2' : 'text-gray-900 border-2'}`;
        if (showResult && selectedAnswer !== null) {
            if (index === currentChallenge?.correctAnswer) {
                // Correct answer
                return `${baseStyle} ${isAdolescentMode ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50' : 'bg-green-400 border-green-500 shadow-lg'}`;
            } else if (index === selectedAnswer) {
                // Wrong selected answer
                return `${baseStyle} ${isAdolescentMode ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50' : 'bg-red-400 border-red-500 shadow-lg'}`;
            } else {
                // Other options
                return `${baseStyle} ${isAdolescentMode ? 'bg-gray-600 border-gray-500 opacity-50' : 'bg-gray-300 border-gray-400 opacity-50'}`;
            }
        } else {
            // Normal state
            return `${baseStyle} ${isAdolescentMode ? 'bg-purple-500 hover:bg-purple-600 border-purple-400 hover:shadow-lg hover:shadow-purple-500/30' : 'bg-purple-400 hover:bg-purple-300 border-purple-500 hover:shadow-lg'}`;
        }
    };
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${className} space-y-6`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-xl font-bold mb-2",
                        children: isAdolescentMode ? '🕯️ Loading CandleStrike...' : '📊 INITIALIZING_PATTERN_RECOGNITION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 137,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 136,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChartSkeleton"], {
                    width: 800,
                    height: 400,
                    theme: isAdolescentMode ? 'dark' : 'dark'
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/games/candle-strike-game.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this);
    }
    if (!game || !gameState || !currentChallenge) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${className} text-center`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: isAdolescentMode ? 'text-white' : 'text-green-400',
                children: isAdolescentMode ? '🎮 Game not ready...' : 'SYSTEM_NOT_READY'
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 153,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/games/candle-strike-game.tsx",
            lineNumber: 152,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${className} space-y-6`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-xl font-bold mb-2",
                        children: isAdolescentMode ? '🕯️ CandleStrike Challenge' : '📊 PATTERN_RECOGNITION_MODULE'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: `text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                        children: isAdolescentMode ? 'Identify the candlestick pattern in the highlighted area!' : 'IDENTIFY_CANDLESTICK_PATTERN_IN_HIGHLIGHTED_REGION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 163,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `grid grid-cols-4 gap-4 p-4 rounded-lg ${isAdolescentMode ? 'bg-white/10' : 'bg-gray-800 border border-green-400'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Score' : 'SCORE'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 180,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                children: gameState.score
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 183,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 179,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Accuracy' : 'ACCURACY'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 188,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: [
                                    gameData?.accuracy_percentage?.toFixed(1) || 0,
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 191,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 187,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Streak' : 'STREAK'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 196,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-orange-300' : 'text-orange-400'}`,
                                children: gameData?.streak_count || 0
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 199,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 195,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 204,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`,
                                children: [
                                    gameState.time_remaining,
                                    "s"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 207,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 203,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 176,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center",
                children: usePlayback ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$playback$2d$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    symbol: "BTCUSD",
                    timeframe: "1h",
                    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                    endDate: new Date(),
                    onPatternDetected: (pattern)=>{
                        console.log('Pattern detected:', pattern);
                    },
                    onPredictionChallenge: (challenge)=>{
                        console.log('Prediction challenge:', challenge);
                    },
                    className: "w-full max-w-4xl"
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 216,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    data: currentChallenge.candleData,
                    width: 800,
                    height: 400,
                    theme: "dark",
                    patternHighlight: getPatternHighlight(),
                    title: isAdolescentMode ? '📈 Trading Chart' : '📊 MARKET_DATA_VISUALIZATION',
                    className: "border rounded-lg"
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 230,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 214,
                columnNumber: 7
            }, this),
            currentChallenge.pattern && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PatternAnnotation"], {
                    pattern: {
                        name: "Pattern to Identify",
                        description: isAdolescentMode ? "Look at the highlighted candles and identify the pattern!" : "ANALYZE_HIGHLIGHTED_CANDLESTICKS_AND_IDENTIFY_PATTERN",
                        bullish: true
                    },
                    theme: "dark"
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 245,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 244,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-2 gap-4",
                children: currentChallenge.options.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handleAnswerSubmit(index),
                        disabled: selectedAnswer !== null || showResult,
                        className: getAnswerButtonStyle(index),
                        children: option
                    }, index, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 261,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 259,
                columnNumber: 7
            }, this),
            showResult && lastAnswerCorrect !== null && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center p-4 rounded-lg ${lastAnswerCorrect ? isAdolescentMode ? 'bg-green-500/20 border border-green-400 text-green-100' : 'bg-green-900/50 border border-green-400 text-green-300' : isAdolescentMode ? 'bg-red-500/20 border border-red-400 text-red-100' : 'bg-red-900/50 border border-red-400 text-red-300'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-2xl mb-2",
                        children: lastAnswerCorrect ? isAdolescentMode ? '🎉' : '✅' : isAdolescentMode ? '😅' : '❌'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 285,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "font-bold",
                        children: lastAnswerCorrect ? isAdolescentMode ? 'Excellent! Correct pattern identified!' : 'CORRECT_PATTERN_IDENTIFICATION' : isAdolescentMode ? 'Not quite! The correct answer was highlighted.' : 'INCORRECT_PATTERN_IDENTIFICATION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 291,
                        columnNumber: 11
                    }, this),
                    !lastAnswerCorrect && currentChallenge.pattern && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm mt-2",
                        children: isAdolescentMode ? `The correct pattern was: ${currentChallenge.options[currentChallenge.correctAnswer]}` : `CORRECT_PATTERN: ${currentChallenge.options[currentChallenge.correctAnswer]}`
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 298,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 274,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                children: isAdolescentMode ? `🎯 Patterns Identified: ${gameData?.patterns_identified || 0} | Correct: ${gameData?.correct_identifications || 0}` : `PATTERNS_IDENTIFIED: ${gameData?.patterns_identified || 0} | CORRECT: ${gameData?.correct_identifications || 0}`
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 309,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/games/candle-strike-game.tsx",
        lineNumber: 161,
        columnNumber: 5
    }, this);
}
_s(CandleStrikeGameComponent, "0bGmRNvYWiy7v1YQZMt3OjFLSv8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"]
    ];
});
_c = CandleStrikeGameComponent;
var _c;
__turbopack_context__.k.register(_c, "CandleStrikeGameComponent");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/demo/candle-strike/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CandleStrikeDemoPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$games$2f$candle$2d$strike$2d$game$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/games/candle-strike-game.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function CandleStrikeDemoPage() {
    _s();
    const { interfaceMode, switchInterfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"])();
    const [difficulty, setDifficulty] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('beginner');
    const [gameActive, setGameActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastScore, setLastScore] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const isAdolescentMode = interfaceMode === 'adolescent';
    const handleGameEnd = (score)=>{
        setGameActive(false);
        setLastScore(score);
    };
    const startNewGame = ()=>{
        setGameActive(true);
        setLastScore(null);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen ${isAdolescentMode ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' : 'bg-gray-900'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: `p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/",
                                    className: `text-sm hover:underline ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                    children: [
                                        "← ",
                                        isAdolescentMode ? 'Back to Quest Hub' : 'RETURN_TO_MAIN'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 35,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: `text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                    children: isAdolescentMode ? '🕯️ CandleStrike: Pattern Master' : '📊 CANDLESTICK_PATTERN_RECOGNITION'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 41,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                            lineNumber: 34,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent'),
                                className: `px-4 py-2 rounded-lg transition-colors ${isAdolescentMode ? 'bg-white/20 hover:bg-white/30 text-white' : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'}`,
                                children: isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'
                            }, void 0, false, {
                                fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                lineNumber: 47,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                            lineNumber: 46,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-7xl mx-auto p-6",
                children: !gameActive ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: `p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                    className: `text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                    children: isAdolescentMode ? '🎯 Master the Art of Pattern Recognition!' : '📈 TECHNICAL_ANALYSIS_TRAINING_MODULE'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 71,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: `text-lg mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                    children: isAdolescentMode ? 'Learn to identify candlestick patterns using real market data and professional trading charts. Each pattern tells a story about market sentiment and potential price movements!' : 'Advanced pattern recognition training using real historical market data with professional-grade charting tools. Develop skills in technical analysis and market sentiment interpretation.'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 74,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid md:grid-cols-3 gap-4 mb-6",
                                    children: [
                                        {
                                            icon: isAdolescentMode ? '📊' : '📈',
                                            title: isAdolescentMode ? 'Real Market Data' : 'REAL_MARKET_DATA',
                                            description: isAdolescentMode ? 'Practice with actual historical price data from major cryptocurrencies and stocks' : 'Historical price data from major financial instruments'
                                        },
                                        {
                                            icon: isAdolescentMode ? '🎯' : '🔍',
                                            title: isAdolescentMode ? 'Pattern Recognition' : 'PATTERN_ANALYSIS',
                                            description: isAdolescentMode ? 'Learn to spot hammer, doji, engulfing, and star patterns like a pro trader' : 'Comprehensive candlestick pattern identification training'
                                        },
                                        {
                                            icon: isAdolescentMode ? '⚡' : '📊',
                                            title: isAdolescentMode ? 'Interactive Charts' : 'INTERACTIVE_VISUALIZATION',
                                            description: isAdolescentMode ? 'Professional trading charts with zoom, pan, and pattern highlighting' : 'Professional-grade charting with advanced visualization tools'
                                        }
                                    ].map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `p-4 rounded-lg ${isAdolescentMode ? 'bg-white/5 border border-white/20' : 'bg-gray-700 border border-green-400/50'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-2xl mb-2",
                                                    children: feature.icon
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                    lineNumber: 114,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                    className: `font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                    children: feature.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                    lineNumber: 115,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: `text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                                    children: feature.description
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                    lineNumber: 118,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, index, true, {
                                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                            lineNumber: 106,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 82,
                                    columnNumber: 15
                                }, this),
                                lastScore !== null && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `p-4 rounded-lg mb-6 ${isAdolescentMode ? 'bg-yellow-500/20 border border-yellow-400 text-yellow-100' : 'bg-yellow-900/50 border border-yellow-400 text-yellow-300'}`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "font-bold mb-2",
                                            children: isAdolescentMode ? '🏆 Last Game Results' : '📊 PREVIOUS_SESSION_RESULTS'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                            lineNumber: 131,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: isAdolescentMode ? `Final Score: ${lastScore} points!` : `FINAL_SCORE: ${lastScore}`
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                            lineNumber: 134,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 126,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                            lineNumber: 66,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                            className: `p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm' : 'bg-gray-800 border border-green-400'}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: `text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                    children: isAdolescentMode ? '⚔️ Choose Your Challenge Level' : '🎯 SELECT_DIFFICULTY_LEVEL'
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 147,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid md:grid-cols-3 gap-4 mb-6",
                                    children: [
                                        {
                                            level: 'beginner',
                                            title: isAdolescentMode ? '🌟 Apprentice Trader' : '📚 BEGINNER',
                                            description: isAdolescentMode ? 'Learn basic patterns: Hammer and Doji' : 'Basic patterns: Hammer, Doji',
                                            patterns: [
                                                'Hammer',
                                                'Doji'
                                            ]
                                        },
                                        {
                                            level: 'intermediate',
                                            title: isAdolescentMode ? '⚡ Skilled Trader' : '📈 INTERMEDIATE',
                                            description: isAdolescentMode ? 'Master engulfing patterns and reversals' : 'Engulfing patterns and reversals',
                                            patterns: [
                                                'Bullish Engulfing',
                                                'Bearish Engulfing'
                                            ]
                                        },
                                        {
                                            level: 'advanced',
                                            title: isAdolescentMode ? '🏆 Master Trader' : '🎯 ADVANCED',
                                            description: isAdolescentMode ? 'Complex three-candle star formations' : 'Complex multi-candle patterns',
                                            patterns: [
                                                'Morning Star',
                                                'Evening Star'
                                            ]
                                        }
                                    ].map((difficultyOption)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>setDifficulty(difficultyOption.level),
                                            className: `p-4 rounded-lg border-2 transition-all ${difficulty === difficultyOption.level ? isAdolescentMode ? 'bg-purple-500/30 border-purple-400 shadow-lg shadow-purple-500/30' : 'bg-green-400/20 border-green-400 shadow-lg' : isAdolescentMode ? 'bg-white/5 border-white/20 hover:bg-white/10' : 'bg-gray-700 border-gray-600 hover:border-green-400/50'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                    className: `font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                    children: difficultyOption.title
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                    lineNumber: 193,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: `text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                                    children: difficultyOption.description
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex flex-wrap gap-1",
                                                    children: difficultyOption.patterns.map((pattern, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: `text-xs px-2 py-1 rounded ${isAdolescentMode ? 'bg-white/20 text-white' : 'bg-gray-600 text-green-300'}`,
                                                            children: pattern
                                                        }, i, false, {
                                                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                            lineNumber: 201,
                                                            columnNumber: 25
                                                        }, this))
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                                    lineNumber: 199,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, difficultyOption.level, true, {
                                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                            lineNumber: 178,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 151,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: startNewGame,
                                        className: `px-8 py-4 rounded-lg font-bold text-lg transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600 shadow-lg' : 'bg-green-400 text-gray-900 hover:bg-green-300 shadow-lg'}`,
                                        children: isAdolescentMode ? '🚀 Start Pattern Quest!' : '▶ INITIALIZE_TRAINING_SESSION'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                        lineNumber: 218,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                                    lineNumber: 217,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                            lineNumber: 142,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                    lineNumber: 64,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$games$2f$candle$2d$strike$2d$game$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    difficulty: difficulty,
                    onGameEnd: handleGameEnd,
                    className: "w-full"
                }, void 0, false, {
                    fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                    lineNumber: 232,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/demo/candle-strike/page.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/demo/candle-strike/page.tsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
}
_s(CandleStrikeDemoPage, "UB8W2F6B1Y0JeZheZoLfABYrrxQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useUserStore"]
    ];
});
_c = CandleStrikeDemoPage;
var _c;
__turbopack_context__.k.register(_c, "CandleStrikeDemoPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_4292f28c._.js.map