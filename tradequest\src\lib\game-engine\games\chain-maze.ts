import { BaseGame } from '../base-game'
import { GameType } from '@/types'

interface BlockchainNode {
  id: string
  type: 'validator' | 'miner' | 'user' | 'contract'
  position: { x: number; y: number }
  connections: string[]
  gasPrice: number
  congestion: number
  reward: number
  active: boolean
}

interface Transaction {
  id: string
  from: string
  to: string
  value: number
  gasLimit: number
  gasPrice: number
  status: 'pending' | 'confirmed' | 'failed'
  blockNumber?: number
}

interface Block {
  number: number
  hash: string
  transactions: Transaction[]
  gasUsed: number
  gasLimit: number
  miner: string
  timestamp: number
  difficulty: number
}

interface Puzzle {
  id: string
  type: 'gas_optimization' | 'consensus_mechanism' | 'network_routing'
  title: string
  description: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  objective: string
  initialState: any
  solution: any
  hints: string[]
}

interface ChainMazeData {
  puzzles_solved: number
  gas_saved: number
  blocks_mined: number
  transactions_processed: number
  consensus_score: number
  efficiency_rating: number
  current_puzzle: Puzzle | null
  network_health: number
  total_gas_used: number
}

export class ChainMazeGame extends BaseGame {
  private gameData: ChainMazeData
  private blockchain: Block[] = []
  private network: BlockchainNode[] = []
  private pendingTransactions: Transaction[] = []
  private currentPuzzle: Puzzle | null = null
  private puzzleStartTime: number = 0
  private availablePuzzles: Puzzle[]

  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {
    super('chain_maze', difficulty)
    
    this.gameData = {
      puzzles_solved: 0,
      gas_saved: 0,
      blocks_mined: 0,
      transactions_processed: 0,
      consensus_score: 0,
      efficiency_rating: 100,
      current_puzzle: null,
      network_health: 100,
      total_gas_used: 0,
    }

    this.availablePuzzles = this.generatePuzzlesByDifficulty(difficulty)
    this.config.starting_balance = 1000 // Gas budget
  }

  async initialize(): Promise<void> {
    // Initialize blockchain network
    this.initializeNetwork()
    this.generateGenesisBlock()
    
    // Start first puzzle
    await this.loadNextPuzzle()
    
    // Start network simulation
    this.startNetworkSimulation()
  }

  update(): void {
    // Update network state
    this.updateNetworkState()
    
    // Process pending transactions
    this.processPendingTransactions()
    
    // Update game metrics
    this.updateGameMetrics()
  }

  calculateScore(): number {
    let score = 0
    
    // Base score from puzzles solved
    score += this.gameData.puzzles_solved * 200
    
    // Gas efficiency bonus
    const gasEfficiency = Math.max(0, 1000 - this.gameData.total_gas_used)
    score += gasEfficiency
    
    // Consensus participation bonus
    score += this.gameData.consensus_score * 10
    
    // Network health bonus
    score += this.gameData.network_health * 2
    
    // Efficiency rating bonus
    score += this.gameData.efficiency_rating * 5
    
    // Difficulty multiplier
    score *= this.state.multiplier
    
    return Math.round(Math.max(0, score))
  }

  getGameSpecificData(): ChainMazeData {
    return { ...this.gameData }
  }

  getCurrentPuzzle(): Puzzle | null {
    return this.currentPuzzle
  }

  getNetworkState(): {
    nodes: BlockchainNode[]
    blockchain: Block[]
    pendingTransactions: Transaction[]
  } {
    return {
      nodes: this.network,
      blockchain: this.blockchain,
      pendingTransactions: this.pendingTransactions,
    }
  }

  async submitPuzzleSolution(solution: any): Promise<boolean> {
    if (!this.currentPuzzle || !this.isActive) return false

    const correct = this.validateSolution(this.currentPuzzle, solution)
    const timeToSolve = Date.now() - this.puzzleStartTime

    if (correct) {
      this.gameData.puzzles_solved++
      
      // Apply puzzle effects
      this.applyPuzzleEffects(this.currentPuzzle, solution)
      
      // Time bonus for quick solutions
      if (timeToSolve < 30000) { // Under 30 seconds
        const timeBonus = Math.max(0, 100 - Math.floor(timeToSolve / 300))
        this.gameData.efficiency_rating += timeBonus
      }
      
      // Load next puzzle
      await this.loadNextPuzzle()
    } else {
      // Penalty for wrong solution
      this.gameData.efficiency_rating = Math.max(0, this.gameData.efficiency_rating - 10)
    }

    return correct
  }

  async executeTransaction(tx: Transaction): Promise<boolean> {
    if (this.state.current_balance < tx.gasPrice * tx.gasLimit) {
      return false // Insufficient gas budget
    }

    // Deduct gas cost
    this.state.current_balance -= tx.gasPrice * tx.gasLimit
    this.gameData.total_gas_used += tx.gasPrice * tx.gasLimit

    // Add to pending transactions
    this.pendingTransactions.push({
      ...tx,
      status: 'pending',
    })

    return true
  }

  async mineBlock(): Promise<boolean> {
    if (this.pendingTransactions.length === 0) return false

    const block = this.createNewBlock()
    this.blockchain.push(block)
    this.gameData.blocks_mined++
    
    // Reward for mining
    const miningReward = 50
    this.state.current_balance += miningReward
    
    // Clear processed transactions
    this.pendingTransactions = this.pendingTransactions.filter(
      tx => !block.transactions.includes(tx)
    )

    return true
  }

  private initializeNetwork(): void {
    // Create network nodes
    this.network = [
      {
        id: 'validator_1',
        type: 'validator',
        position: { x: 100, y: 100 },
        connections: ['validator_2', 'miner_1'],
        gasPrice: 20,
        congestion: 0.3,
        reward: 10,
        active: true,
      },
      {
        id: 'validator_2',
        type: 'validator',
        position: { x: 300, y: 100 },
        connections: ['validator_1', 'miner_2'],
        gasPrice: 25,
        congestion: 0.5,
        reward: 15,
        active: true,
      },
      {
        id: 'miner_1',
        type: 'miner',
        position: { x: 200, y: 200 },
        connections: ['validator_1', 'contract_1'],
        gasPrice: 30,
        congestion: 0.7,
        reward: 20,
        active: true,
      },
      {
        id: 'miner_2',
        type: 'miner',
        position: { x: 400, y: 200 },
        connections: ['validator_2', 'contract_1'],
        gasPrice: 35,
        congestion: 0.4,
        reward: 25,
        active: true,
      },
      {
        id: 'contract_1',
        type: 'contract',
        position: { x: 300, y: 300 },
        connections: ['miner_1', 'miner_2'],
        gasPrice: 40,
        congestion: 0.6,
        reward: 30,
        active: true,
      },
    ]
  }

  private generateGenesisBlock(): void {
    this.blockchain = [{
      number: 0,
      hash: '0x0000000000000000000000000000000000000000000000000000000000000000',
      transactions: [],
      gasUsed: 0,
      gasLimit: 8000000,
      miner: 'genesis',
      timestamp: Date.now(),
      difficulty: 1,
    }]
  }

  private async loadNextPuzzle(): Promise<void> {
    if (this.availablePuzzles.length === 0) {
      // Generate more puzzles or end game
      this.availablePuzzles = this.generatePuzzlesByDifficulty(this.config.difficulty)
    }

    this.currentPuzzle = this.availablePuzzles.shift() || null
    this.gameData.current_puzzle = this.currentPuzzle
    this.puzzleStartTime = Date.now()
  }

  private generatePuzzlesByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): Puzzle[] {
    const puzzles: Puzzle[] = []

    if (difficulty === 'beginner' || difficulty === 'intermediate' || difficulty === 'advanced') {
      puzzles.push({
        id: 'gas_opt_1',
        type: 'gas_optimization',
        title: 'Optimize Transaction Gas',
        description: 'Find the most cost-effective path to execute this transaction',
        difficulty: 'beginner',
        objective: 'Minimize gas cost while ensuring transaction success',
        initialState: {
          transaction: { value: 100, gasLimit: 21000 },
          availableNodes: ['validator_1', 'validator_2'],
        },
        solution: { selectedNode: 'validator_1', gasPrice: 20 },
        hints: ['Lower gas price nodes are more cost-effective', 'Check node congestion levels'],
      })
    }

    if (difficulty === 'intermediate' || difficulty === 'advanced') {
      puzzles.push({
        id: 'consensus_1',
        type: 'consensus_mechanism',
        title: 'Achieve Network Consensus',
        description: 'Coordinate validators to reach consensus on the next block',
        difficulty: 'intermediate',
        objective: 'Get 2/3 majority agreement from validators',
        initialState: {
          validators: ['validator_1', 'validator_2'],
          proposedBlock: { number: 1, transactions: 3 },
        },
        solution: { votes: ['validator_1', 'validator_2'], consensus: true },
        hints: ['Validators need to agree on block validity', 'Majority consensus is required'],
      })
    }

    if (difficulty === 'advanced') {
      puzzles.push({
        id: 'routing_1',
        type: 'network_routing',
        title: 'Optimize Network Routing',
        description: 'Find the optimal path through the network for maximum efficiency',
        difficulty: 'advanced',
        objective: 'Route transactions through the network with minimal latency and cost',
        initialState: {
          source: 'validator_1',
          destination: 'contract_1',
          constraints: { maxGas: 100, maxHops: 3 },
        },
        solution: { path: ['validator_1', 'miner_1', 'contract_1'], totalCost: 70 },
        hints: ['Consider both gas cost and network congestion', 'Shorter paths are not always optimal'],
      })
    }

    return puzzles
  }

  private validateSolution(puzzle: Puzzle, solution: any): boolean {
    switch (puzzle.type) {
      case 'gas_optimization':
        return this.validateGasOptimization(puzzle, solution)
      case 'consensus_mechanism':
        return this.validateConsensus(puzzle, solution)
      case 'network_routing':
        return this.validateRouting(puzzle, solution)
      default:
        return false
    }
  }

  private validateGasOptimization(puzzle: Puzzle, solution: any): boolean {
    const expectedSolution = puzzle.solution
    const selectedNode = this.network.find(n => n.id === solution.selectedNode)
    
    if (!selectedNode) return false
    
    // Check if solution is optimal (lowest gas price with acceptable congestion)
    return solution.selectedNode === expectedSolution.selectedNode &&
           solution.gasPrice <= expectedSolution.gasPrice * 1.1 // 10% tolerance
  }

  private validateConsensus(puzzle: Puzzle, solution: any): boolean {
    const requiredVotes = Math.ceil(puzzle.initialState.validators.length * 2 / 3)
    return solution.votes && solution.votes.length >= requiredVotes && solution.consensus === true
  }

  private validateRouting(puzzle: Puzzle, solution: any): boolean {
    const expectedSolution = puzzle.solution
    
    // Validate path exists and meets constraints
    if (!solution.path || !Array.isArray(solution.path)) return false
    
    // Check path validity
    const pathValid = this.validateNetworkPath(solution.path)
    const costAcceptable = solution.totalCost <= expectedSolution.totalCost * 1.2 // 20% tolerance
    
    return pathValid && costAcceptable
  }

  private validateNetworkPath(path: string[]): boolean {
    for (let i = 0; i < path.length - 1; i++) {
      const currentNode = this.network.find(n => n.id === path[i])
      const nextNode = path[i + 1]
      
      if (!currentNode || !currentNode.connections.includes(nextNode)) {
        return false
      }
    }
    return true
  }

  private applyPuzzleEffects(puzzle: Puzzle, solution: any): void {
    switch (puzzle.type) {
      case 'gas_optimization':
        const gasSaved = 50 - (solution.gasPrice || 30)
        this.gameData.gas_saved += Math.max(0, gasSaved)
        break
      case 'consensus_mechanism':
        this.gameData.consensus_score += 10
        this.gameData.network_health += 5
        break
      case 'network_routing':
        this.gameData.efficiency_rating += 10
        break
    }
  }

  private startNetworkSimulation(): void {
    // Simulate network activity
    const simulationInterval = setInterval(() => {
      if (!this.isActive) {
        clearInterval(simulationInterval)
        return
      }
      
      // Generate random transactions
      if (Math.random() < 0.3) {
        this.generateRandomTransaction()
      }
      
      // Update node states
      this.updateNodeStates()
      
    }, 2000)
  }

  private generateRandomTransaction(): void {
    const nodes = this.network.filter(n => n.active)
    const fromNode = nodes[Math.floor(Math.random() * nodes.length)]
    const toNode = nodes[Math.floor(Math.random() * nodes.length)]
    
    if (fromNode.id !== toNode.id) {
      const tx: Transaction = {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        from: fromNode.id,
        to: toNode.id,
        value: Math.floor(Math.random() * 100) + 1,
        gasLimit: 21000,
        gasPrice: fromNode.gasPrice,
        status: 'pending',
      }
      
      this.pendingTransactions.push(tx)
    }
  }

  private updateNodeStates(): void {
    this.network.forEach(node => {
      // Update congestion based on pending transactions
      const nodeTxCount = this.pendingTransactions.filter(
        tx => tx.from === node.id || tx.to === node.id
      ).length
      
      node.congestion = Math.min(1, nodeTxCount / 10)
      
      // Adjust gas price based on congestion
      const basePrices = { validator: 20, miner: 30, user: 15, contract: 40 }
      node.gasPrice = basePrices[node.type] * (1 + node.congestion)
    })
  }

  private processPendingTransactions(): void {
    // Process a few transactions each update
    const txToProcess = this.pendingTransactions.slice(0, 3)
    
    txToProcess.forEach(tx => {
      if (Math.random() < 0.7) { // 70% success rate
        tx.status = 'confirmed'
        this.gameData.transactions_processed++
      } else {
        tx.status = 'failed'
      }
    })
    
    // Remove processed transactions
    this.pendingTransactions = this.pendingTransactions.filter(
      tx => !txToProcess.includes(tx)
    )
  }

  private createNewBlock(): Block {
    const txToInclude = this.pendingTransactions.slice(0, 5) // Max 5 tx per block
    const gasUsed = txToInclude.reduce((sum, tx) => sum + (tx.gasLimit * tx.gasPrice), 0)
    
    return {
      number: this.blockchain.length,
      hash: `0x${Math.random().toString(16).substr(2, 64)}`,
      transactions: txToInclude,
      gasUsed,
      gasLimit: 8000000,
      miner: 'player',
      timestamp: Date.now(),
      difficulty: Math.floor(Math.random() * 1000) + 1000,
    }
  }

  private updateNetworkState(): void {
    // Update network health based on various factors
    const avgCongestion = this.network.reduce((sum, node) => sum + node.congestion, 0) / this.network.length
    const pendingTxRatio = Math.min(1, this.pendingTransactions.length / 20)
    
    this.gameData.network_health = Math.max(0, 100 - (avgCongestion * 30) - (pendingTxRatio * 40))
  }

  private updateGameMetrics(): void {
    // Update efficiency rating based on performance
    const gasEfficiency = Math.max(0, 100 - (this.gameData.total_gas_used / 10))
    const consensusEfficiency = Math.min(100, this.gameData.consensus_score * 2)
    
    this.gameData.efficiency_rating = (gasEfficiency + consensusEfficiency + this.gameData.network_health) / 3
  }
}
