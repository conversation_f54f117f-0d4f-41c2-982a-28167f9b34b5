'use client'

import { useEffect, useRef } from 'react'

interface TradingViewWidgetProps {
  symbol?: string
  width?: number
  height?: number
  interval?: string
  theme?: 'light' | 'dark'
  style?: string
  locale?: string
  toolbar_bg?: string
  enable_publishing?: boolean
  allow_symbol_change?: boolean
  container_id?: string
}

export default function TradingViewWidget({
  symbol = 'BTCUSD',
  width = 800,
  height = 400,
  interval = '1H',
  theme = 'dark',
  style = '1',
  locale = 'en',
  toolbar_bg = '#f1f3f6',
  enable_publishing = false,
  allow_symbol_change = true,
  container_id = 'tradingview_widget',
}: TradingViewWidgetProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    // Clear any existing content
    containerRef.current.innerHTML = ''

    // Create script element
    const script = document.createElement('script')
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js'
    script.type = 'text/javascript'
    script.async = true

    // Widget configuration
    const config = {
      autosize: false,
      width,
      height,
      symbol,
      interval,
      timezone: 'Etc/UTC',
      theme,
      style,
      locale,
      toolbar_bg,
      enable_publishing,
      allow_symbol_change,
      calendar: false,
      support_host: 'https://www.tradingview.com',
    }

    script.innerHTML = JSON.stringify(config)

    // Create container div for the widget
    const widgetContainer = document.createElement('div')
    widgetContainer.className = 'tradingview-widget-container'
    widgetContainer.style.height = `${height}px`
    widgetContainer.style.width = `${width}px`

    const widgetDiv = document.createElement('div')
    widgetDiv.className = 'tradingview-widget-container__widget'
    widgetDiv.style.height = 'calc(100% - 32px)'
    widgetDiv.style.width = '100%'

    const copyrightDiv = document.createElement('div')
    copyrightDiv.className = 'tradingview-widget-copyright'
    copyrightDiv.innerHTML = `
      <a href="https://www.tradingview.com/" rel="noopener nofollow" target="_blank">
        <span className="blue-text">Track all markets on TradingView</span>
      </a>
    `

    widgetContainer.appendChild(widgetDiv)
    widgetContainer.appendChild(copyrightDiv)
    widgetContainer.appendChild(script)

    containerRef.current.appendChild(widgetContainer)

    // Cleanup function
    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [symbol, width, height, interval, theme, style, locale, toolbar_bg, enable_publishing, allow_symbol_change])

  return (
    <div 
      ref={containerRef}
      id={container_id}
      className="tradingview-widget-wrapper"
      style={{ width: `${width}px`, height: `${height}px` }}
    />
  )
}

// Simplified TradingView Mini Widget
export function TradingViewMiniWidget({
  symbol = 'BTCUSD',
  width = 350,
  height = 220,
  theme = 'dark',
}: {
  symbol?: string
  width?: number
  height?: number
  theme?: 'light' | 'dark'
}) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    containerRef.current.innerHTML = ''

    const script = document.createElement('script')
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js'
    script.type = 'text/javascript'
    script.async = true

    const config = {
      symbol,
      width,
      height,
      locale: 'en',
      dateRange: '12M',
      colorTheme: theme,
      trendLineColor: 'rgba(41, 98, 255, 1)',
      underLineColor: 'rgba(41, 98, 255, 0.3)',
      underLineBottomColor: 'rgba(41, 98, 255, 0)',
      isTransparent: false,
      autosize: false,
      largeChartUrl: '',
    }

    script.innerHTML = JSON.stringify(config)

    const widgetContainer = document.createElement('div')
    widgetContainer.className = 'tradingview-widget-container'
    widgetContainer.appendChild(script)

    containerRef.current.appendChild(widgetContainer)

    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [symbol, width, height, theme])

  return (
    <div 
      ref={containerRef}
      className="tradingview-mini-widget-wrapper"
      style={{ width: `${width}px`, height: `${height}px` }}
    />
  )
}

// Market Overview Widget
export function TradingViewMarketOverview({
  colorTheme = 'dark',
  dateRange = '12M',
  showChart = true,
  locale = 'en',
  width = 400,
  height = 400,
}: {
  colorTheme?: 'light' | 'dark'
  dateRange?: string
  showChart?: boolean
  locale?: string
  width?: number
  height?: number
}) {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!containerRef.current) return

    containerRef.current.innerHTML = ''

    const script = document.createElement('script')
    script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js'
    script.type = 'text/javascript'
    script.async = true

    const config = {
      colorTheme,
      dateRange,
      showChart,
      locale,
      width,
      height,
      largeChartUrl: '',
      isTransparent: false,
      showSymbolLogo: true,
      showFloatingTooltip: false,
      plotLineColorGrowing: 'rgba(41, 98, 255, 1)',
      plotLineColorFalling: 'rgba(41, 98, 255, 1)',
      gridLineColor: 'rgba(240, 243, 250, 0)',
      scaleFontColor: 'rgba(106, 109, 120, 1)',
      belowLineFillColorGrowing: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorFalling: 'rgba(41, 98, 255, 0.12)',
      belowLineFillColorGrowingBottom: 'rgba(41, 98, 255, 0)',
      belowLineFillColorFallingBottom: 'rgba(41, 98, 255, 0)',
      symbolActiveColor: 'rgba(41, 98, 255, 0.12)',
      tabs: [
        {
          title: 'Crypto',
          symbols: [
            { s: 'BINANCE:BTCUSDT', d: 'Bitcoin' },
            { s: 'BINANCE:ETHUSDT', d: 'Ethereum' },
            { s: 'BINANCE:ADAUSDT', d: 'Cardano' },
            { s: 'BINANCE:SOLUSDT', d: 'Solana' },
          ],
        },
        {
          title: 'Stocks',
          symbols: [
            { s: 'NASDAQ:AAPL', d: 'Apple' },
            { s: 'NASDAQ:GOOGL', d: 'Google' },
            { s: 'NASDAQ:TSLA', d: 'Tesla' },
            { s: 'NASDAQ:MSFT', d: 'Microsoft' },
          ],
        },
      ],
    }

    script.innerHTML = JSON.stringify(config)

    const widgetContainer = document.createElement('div')
    widgetContainer.className = 'tradingview-widget-container'
    widgetContainer.appendChild(script)

    containerRef.current.appendChild(widgetContainer)

    return () => {
      if (containerRef.current) {
        containerRef.current.innerHTML = ''
      }
    }
  }, [colorTheme, dateRange, showChart, locale, width, height])

  return (
    <div 
      ref={containerRef}
      className="tradingview-market-overview-wrapper"
      style={{ width: `${width}px`, height: `${height}px` }}
    />
  )
}
