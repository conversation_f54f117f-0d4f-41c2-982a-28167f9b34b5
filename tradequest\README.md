# TradeQuest: Master Markets Through Play

A comprehensive gamified trading education platform that makes learning about financial markets engaging and accessible for all ages. TradeQuest features dual-audience interfaces, interactive mini-games, and real-time market data integration.

## 🎮 Features

### Core Platform
- **Dual-Mode Interface**: Seamless switching between adolescent (fantasy-themed) and adult (professional) modes
- **Gamified Learning**: Six unique mini-games teaching different trading concepts
- **Real-Time Data**: Integration with CoinGecko and Alpha Vantage APIs
- **Progress Tracking**: Comprehensive achievement system and experience points
- **Social Features**: Guilds, leaderboards, and multiplayer competitions

### Mini-Games
1. **Scalper Sprint** (Beginner): 60-second rapid trading challenges
2. **CandleStrike** (Beginner): Pattern recognition with candlestick charts
3. **ChainMaze** (Intermediate): Blockchain puzzle navigation
4. **Swing Trader's Odyssey** (Intermediate): Multi-day position management
5. **Day Trader Arena** (Advanced): Real-time multiplayer competitions
6. **Portfolio Survivor** (Advanced): Crisis management scenarios

### Educational Content
- Structured learning modules for crypto, stocks, and forex
- Interactive tutorials and quizzes
- AI-driven scenario generation
- Progress tracking and personalized recommendations

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Supabase account (for database and authentication)
- API keys for market data (optional for development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tradequest
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Edit `.env.local` with your configuration:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # Market Data APIs (optional for development)
   NEXT_PUBLIC_COINGECKO_API_KEY=your_coingecko_api_key
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key

   # Application Configuration
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=http://localhost:3000
   ```

4. **Set up Supabase database**
   - Create a new Supabase project
   - Run the SQL schema from `supabase/schema.sql` in your Supabase SQL editor
   - Enable Row Level Security (RLS) policies

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open the application**
   Navigate to [http://localhost:3000](http://localhost:3000) in your browser

## 🏗️ Project Structure

```
tradequest/
├── src/
│   ├── app/                    # Next.js app directory
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── lib/
│   │   ├── game-engine/       # Game logic and implementations
│   │   │   ├── base-game.ts   # Base game class
│   │   │   └── games/         # Individual game implementations
│   │   ├── services/          # External API integrations
│   │   ├── stores/            # Zustand state management
│   │   ├── supabase/          # Supabase client configuration
│   │   ├── constants.ts       # Application constants
│   │   └── utils.ts           # Utility functions
│   └── types/                 # TypeScript type definitions
├── supabase/
│   └── schema.sql             # Database schema
├── public/                    # Static assets
└── package.json
```

## 🎯 Game Engine Architecture

The platform uses a modular game engine built around a `BaseGame` class that provides:

- **Common Game Lifecycle**: Start, pause, resume, end functionality
- **Trading Operations**: Execute trades, manage positions, calculate P&L
- **Market Data Integration**: Real-time price updates and simulation
- **Score Calculation**: Standardized scoring across all games
- **State Management**: Consistent game state tracking

Each mini-game extends `BaseGame` and implements specific game logic while inheriting core trading functionality.

## 🔧 API Integration

### Market Data Sources
- **CoinGecko API**: Cryptocurrency prices and historical data
- **Alpha Vantage API**: Stock and forex data
- **Fallback System**: Mock data generation for development

### Database (Supabase)
- **Authentication**: User registration and login
- **User Profiles**: Progress tracking and preferences
- **Game Sessions**: Score and achievement storage
- **Social Features**: Guilds and leaderboards

## 🎨 UI/UX Design

### Dual-Mode Interface
- **Adolescent Mode**: Fantasy-themed with bright colors, emojis, and quest-like navigation
- **Adult Mode**: Professional Bloomberg Terminal-style interface with monospace fonts and terminal aesthetics

### Responsive Design
- Mobile-first approach with Tailwind CSS
- Adaptive layouts for different screen sizes
- Touch-friendly controls for mobile gaming

## 🔒 Security & Compliance

### Age Verification
- COPPA-compliant data handling for users under 13
- Separate data policies for minor users
- Parental consent mechanisms

### Data Protection
- GDPR compliance for European users
- Row Level Security (RLS) in Supabase
- Secure API key management

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## 📦 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Next.js team for the excellent framework
- Supabase for backend infrastructure
- CoinGecko and Alpha Vantage for market data APIs
- Tailwind CSS for styling utilities

## 📞 Support

For support, email <EMAIL> or join our Discord community.

---

**TradeQuest** - Making financial education accessible and engaging for everyone! 🚀📈
