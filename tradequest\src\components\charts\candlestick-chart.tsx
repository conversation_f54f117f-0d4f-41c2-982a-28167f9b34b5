'use client'

import { useEffect, useRef, useState } from 'react'
import { create<PERSON>hart, IChartApi, ISeriesApi, ColorType } from 'lightweight-charts'
import { CandlestickData } from '@/types'

interface CandlestickChartProps {
  data: CandlestickData[]
  width?: number
  height?: number
  theme?: 'light' | 'dark'
  patternHighlight?: {
    startIndex: number
    endIndex: number
    color: string
  }
  onPatternClick?: (startIndex: number, endIndex: number) => void
  showVolume?: boolean
  title?: string
  className?: string
}

export default function CandlestickChart({
  data,
  width = 800,
  height = 400,
  theme = 'dark',
  patternHighlight,
  onPatternClick,
  showVolume = true,
  title,
  className = '',
}: CandlestickChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!chartContainerRef.current || data.length === 0) return

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width,
      height,
      layout: {
        background: { type: ColorType.Solid, color: theme === 'dark' ? '#1a1a1a' : '#ffffff' },
        textColor: theme === 'dark' ? '#d1d5db' : '#374151',
      },
      grid: {
        vertLines: { color: theme === 'dark' ? '#374151' : '#e5e7eb' },
        horzLines: { color: theme === 'dark' ? '#374151' : '#e5e7eb' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
      },
      timeScale: {
        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
        timeVisible: true,
        secondsVisible: false,
      },
    })

    chartRef.current = chart

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#10b981',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#10b981',
      wickDownColor: '#ef4444',
      wickUpColor: '#10b981',
    })

    candlestickSeriesRef.current = candlestickSeries

    // Add volume series if enabled
    if (showVolume) {
      const volumeSeries = chart.addHistogramSeries({
        color: theme === 'dark' ? '#6b7280' : '#9ca3af',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: '',
        scaleMargins: {
          top: 0.7,
          bottom: 0,
        },
      })
      volumeSeriesRef.current = volumeSeries
    }

    // Convert data format
    const chartData: LWCandlestickData[] = data.map(candle => ({
      time: Math.floor(candle.timestamp / 1000) as any,
      open: candle.open,
      high: candle.high,
      low: candle.low,
      close: candle.close,
    }))

    const volumeData = data.map(candle => ({
      time: Math.floor(candle.timestamp / 1000) as any,
      value: candle.volume,
      color: candle.close >= candle.open ? '#10b98150' : '#ef444450',
    }))

    // Set data
    candlestickSeries.setData(chartData)
    if (showVolume && volumeSeriesRef.current) {
      volumeSeriesRef.current.setData(volumeData)
    }

    // Fit content
    chart.timeScale().fitContent()

    setIsLoading(false)

    // Cleanup
    return () => {
      chart.remove()
    }
  }, [data, width, height, theme, showVolume])

  // Handle pattern highlighting
  useEffect(() => {
    if (!chartRef.current || !candlestickSeriesRef.current || !patternHighlight) return

    // Add pattern highlight markers
    const markers = []
    
    // Start marker
    markers.push({
      time: Math.floor(data[patternHighlight.startIndex]?.timestamp / 1000) as any,
      position: 'belowBar' as const,
      color: patternHighlight.color,
      shape: 'arrowUp' as const,
      text: 'Pattern Start',
    })

    // End marker
    markers.push({
      time: Math.floor(data[patternHighlight.endIndex]?.timestamp / 1000) as any,
      position: 'belowBar' as const,
      color: patternHighlight.color,
      shape: 'arrowUp' as const,
      text: 'Pattern End',
    })

    candlestickSeriesRef.current.setMarkers(markers)
  }, [patternHighlight, data])

  // Handle click events
  useEffect(() => {
    if (!chartRef.current || !onPatternClick) return

    const handleClick = (param: any) => {
      if (param.time) {
        const clickedIndex = data.findIndex(
          candle => Math.floor(candle.timestamp / 1000) === param.time
        )
        if (clickedIndex !== -1) {
          // For simplicity, assume pattern is 3 candles around clicked point
          const startIndex = Math.max(0, clickedIndex - 1)
          const endIndex = Math.min(data.length - 1, clickedIndex + 1)
          onPatternClick(startIndex, endIndex)
        }
      }
    }

    chartRef.current.subscribeClick(handleClick)

    return () => {
      if (chartRef.current) {
        chartRef.current.unsubscribeClick(handleClick)
      }
    }
  }, [data, onPatternClick])

  return (
    <div className={`relative ${className}`}>
      {title && (
        <div className={`text-center mb-2 font-bold ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          {title}
        </div>
      )}
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
        </div>
      )}
      
      <div 
        ref={chartContainerRef} 
        className="rounded border"
        style={{ 
          width: `${width}px`, 
          height: `${height}px`,
          borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'
        }}
      />
      
      {/* Chart controls */}
      <div className={`mt-2 flex justify-between text-xs ${
        theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
      }`}>
        <span>📊 Candlestick Chart</span>
        <span>{data.length} candles</span>
      </div>
    </div>
  )
}

// Pattern annotation component
export function PatternAnnotation({ 
  pattern, 
  theme = 'dark' 
}: { 
  pattern: { name: string; description: string; bullish: boolean }
  theme?: 'light' | 'dark'
}) {
  return (
    <div className={`p-3 rounded-lg border ${
      theme === 'dark' 
        ? 'bg-gray-800 border-gray-600 text-white' 
        : 'bg-white border-gray-300 text-gray-900'
    }`}>
      <div className="flex items-center gap-2 mb-1">
        <span className={`text-lg ${pattern.bullish ? 'text-green-400' : 'text-red-400'}`}>
          {pattern.bullish ? '📈' : '📉'}
        </span>
        <span className="font-bold">{pattern.name}</span>
      </div>
      <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
        {pattern.description}
      </p>
    </div>
  )
}

// Chart loading skeleton
export function ChartSkeleton({ 
  width = 800, 
  height = 400, 
  theme = 'dark' 
}: { 
  width?: number
  height?: number
  theme?: 'light' | 'dark'
}) {
  return (
    <div 
      className={`animate-pulse rounded border ${
        theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-gray-200 border-gray-300'
      }`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      <div className="flex items-center justify-center h-full">
        <div className={`text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-current mx-auto mb-2"></div>
          <p>Loading chart data...</p>
        </div>
      </div>
    </div>
  )
}
