import { BaseGame } from '../base-game'
import { GameType, TradingPair } from '@/types'
import { TRADING_PAIRS } from '@/lib/constants'

interface ScalperSprintData {
  trades_executed: number
  successful_trades: number
  largest_gain: number
  largest_loss: number
  average_hold_time: number
  speed_bonus: number
}

export class ScalperSprintGame extends BaseGame {
  private gameData: ScalperSprintData
  private tradeHistory: Array<{
    timestamp: number
    symbol: string
    side: 'buy' | 'sell'
    entry_price: number
    exit_price?: number
    hold_time?: number
    pnl?: number
  }> = []

  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {
    super('scalper_sprint', difficulty)
    
    this.gameData = {
      trades_executed: 0,
      successful_trades: 0,
      largest_gain: 0,
      largest_loss: 0,
      average_hold_time: 0,
      speed_bonus: 0,
    }

    // Set available trading pairs for scalping (high volatility pairs)
    this.config.available_pairs = this.getScalpingPairs(difficulty)
  }

  async initialize(): Promise<void> {
    // Initialize market data with realistic scalping prices
    const initialPrices = this.generateInitialPrices()
    initialPrices.forEach((price, symbol) => {
      this.marketData.set(symbol, price)
    })

    // Start market data updates more frequently for scalping
    this.startMarketDataUpdates()
  }

  update(): void {
    // Update market data with high frequency for scalping simulation
    this.simulateScalpingMarketMovement()
    this.updatePositionPnL()
    
    // Check for auto-close conditions (stop loss, take profit)
    this.checkAutoCloseConditions()
    
    // Update game-specific metrics
    this.updateGameMetrics()
  }

  calculateScore(): number {
    const totalPnL = this.getTotalPnL()
    const balanceChange = this.state.current_balance - this.config.starting_balance + totalPnL
    const balanceChangePercentage = (balanceChange / this.config.starting_balance) * 100

    // Base score from P&L percentage
    let score = Math.max(0, balanceChangePercentage * 10)

    // Bonus for number of successful trades
    const successRate = this.gameData.trades_executed > 0 
      ? this.gameData.successful_trades / this.gameData.trades_executed 
      : 0
    score += successRate * 50

    // Speed bonus for quick decision making
    score += this.gameData.speed_bonus

    // Penalty for holding positions too long (this is scalping!)
    const avgHoldTimePenalty = Math.max(0, (this.gameData.average_hold_time - 10) * 2)
    score -= avgHoldTimePenalty

    // Difficulty multiplier
    score *= this.state.multiplier

    return Math.round(Math.max(0, score))
  }

  getGameSpecificData(): ScalperSprintData {
    return { ...this.gameData }
  }

  // Override trade execution to add scalping-specific logic
  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {
    const success = await super.executeTrade(symbol, side, quantity)
    
    if (success) {
      this.gameData.trades_executed++
      
      // Record trade for analytics
      this.tradeHistory.push({
        timestamp: Date.now(),
        symbol,
        side,
        entry_price: this.marketData.get(symbol)!,
      })

      // Speed bonus for quick trades
      const timeSinceStart = (Date.now() - this.startTime) / 1000
      if (timeSinceStart < 10) {
        this.gameData.speed_bonus += 5
      }
    }

    return success
  }

  // Override position closing to track scalping metrics
  async closePosition(positionId: string): Promise<boolean> {
    const position = this.state.positions.find(p => p.id === positionId)
    if (!position) return false

    const success = await super.closePosition(positionId)
    
    if (success && position) {
      const tradeRecord = this.tradeHistory.find(t => 
        t.symbol === position.symbol && 
        t.entry_price === position.entry_price &&
        !t.exit_price
      )

      if (tradeRecord) {
        const holdTime = (Date.now() - tradeRecord.timestamp) / 1000
        const exitPrice = this.marketData.get(position.symbol)!
        const pnl = position.pnl

        // Update trade record
        tradeRecord.exit_price = exitPrice
        tradeRecord.hold_time = holdTime
        tradeRecord.pnl = pnl

        // Update game metrics
        if (pnl > 0) {
          this.gameData.successful_trades++
          this.gameData.largest_gain = Math.max(this.gameData.largest_gain, pnl)
        } else {
          this.gameData.largest_loss = Math.min(this.gameData.largest_loss, pnl)
        }

        this.updateAverageHoldTime()
      }
    }

    return success
  }

  private getScalpingPairs(difficulty: 'beginner' | 'intermediate' | 'advanced'): TradingPair[] {
    const allPairs = TRADING_PAIRS
    
    switch (difficulty) {
      case 'beginner':
        // Major crypto pairs with high liquidity
        return allPairs.filter(pair => 
          ['BTCUSD', 'ETHUSD'].includes(pair.symbol)
        )
      case 'intermediate':
        // Add some altcoins and major stocks
        return allPairs.filter(pair => 
          ['BTCUSD', 'ETHUSD', 'ADAUSD', 'AAPL', 'GOOGL'].includes(pair.symbol)
        )
      case 'advanced':
        // All available pairs including forex
        return allPairs
      default:
        return allPairs.slice(0, 3)
    }
  }

  private generateInitialPrices(): Map<string, number> {
    const prices = new Map<string, number>()
    
    // Realistic starting prices for scalping simulation
    const basePrices: Record<string, number> = {
      'BTCUSD': 45000 + (Math.random() - 0.5) * 2000,
      'ETHUSD': 3000 + (Math.random() - 0.5) * 200,
      'ADAUSD': 0.5 + (Math.random() - 0.5) * 0.1,
      'SOLUSD': 100 + (Math.random() - 0.5) * 20,
      'AAPL': 150 + (Math.random() - 0.5) * 10,
      'GOOGL': 2500 + (Math.random() - 0.5) * 100,
      'TSLA': 800 + (Math.random() - 0.5) * 50,
      'EURUSD': 1.1 + (Math.random() - 0.5) * 0.02,
      'GBPUSD': 1.3 + (Math.random() - 0.5) * 0.02,
      'JPYUSD': 0.009 + (Math.random() - 0.5) * 0.0002,
    }

    this.config.available_pairs.forEach(pair => {
      prices.set(pair.symbol, basePrices[pair.symbol] || 100)
    })

    return prices
  }

  private startMarketDataUpdates(): void {
    // Update market data every 2 seconds for realistic scalping
    const updateInterval = setInterval(() => {
      if (!this.isActive) {
        clearInterval(updateInterval)
        return
      }
      this.simulateScalpingMarketMovement()
    }, 2000)
  }

  private simulateScalpingMarketMovement(): void {
    // Simulate high-frequency price movements typical in scalping
    this.marketData.forEach((price, symbol) => {
      // Higher volatility and more frequent small movements
      const volatility = this.getScalpingVolatility(symbol)
      const direction = Math.random() > 0.5 ? 1 : -1
      const change = direction * Math.random() * volatility * price
      
      // Add some momentum (trending behavior)
      const momentum = this.calculateMomentum(symbol)
      const newPrice = price + change + momentum
      
      this.marketData.set(symbol, Math.max(0.01, newPrice))
    })
  }

  private getScalpingVolatility(symbol: string): number {
    // Higher volatility for scalping simulation
    if (this.isCryptoSymbol(symbol)) return 0.008 // 0.8% per update
    if (this.isStockSymbol(symbol)) return 0.003 // 0.3% per update
    if (this.isForexSymbol(symbol)) return 0.001 // 0.1% per update
    return 0.005
  }

  private calculateMomentum(symbol: string): number {
    // Simple momentum calculation based on recent price history
    // In a real implementation, this would use actual price history
    return (Math.random() - 0.5) * 0.001 * (this.marketData.get(symbol) || 0)
  }

  private checkAutoCloseConditions(): void {
    // Auto-close positions that hit stop loss or take profit levels
    this.state.positions.forEach(position => {
      const currentPrice = position.current_price
      const entryPrice = position.entry_price
      const pnlPercentage = (position.pnl / (entryPrice * position.quantity)) * 100

      // Auto-close on 5% loss (stop loss) or 3% gain (take profit) for scalping
      if (pnlPercentage <= -5 || pnlPercentage >= 3) {
        this.closePosition(position.id)
      }
    })
  }

  private updateGameMetrics(): void {
    // Update average hold time
    this.updateAverageHoldTime()
    
    // Update speed bonus based on quick decision making
    const recentTrades = this.tradeHistory.filter(t => 
      Date.now() - t.timestamp < 5000 // Last 5 seconds
    )
    
    if (recentTrades.length >= 2) {
      this.gameData.speed_bonus += 2 // Bonus for rapid trading
    }
  }

  private updateAverageHoldTime(): void {
    const completedTrades = this.tradeHistory.filter(t => t.hold_time !== undefined)
    if (completedTrades.length > 0) {
      const totalHoldTime = completedTrades.reduce((sum, trade) => sum + (trade.hold_time || 0), 0)
      this.gameData.average_hold_time = totalHoldTime / completedTrades.length
    }
  }
}
