/**
 * Interactive Historical Chart Playback Engine
 * Provides time-travel functionality for educational trading experiences
 */

import { CandlestickData } from '@/types'
import { enhancedMarketDataService, type HistoricalDataRequest, type MarketEvent } from '@/lib/services/enhanced-market-data'

export interface PlaybackState {
  isPlaying: boolean
  isPaused: boolean
  currentIndex: number
  totalCandles: number
  speed: number // 0.5x to 4x
  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'
  symbol: string
  startDate: Date
  endDate: Date
}

export interface PlaybackControls {
  play: () => void
  pause: () => void
  stop: () => void
  setSpeed: (speed: number) => void
  jumpToIndex: (index: number) => void
  jumpToDate: (date: Date) => void
  nextCandle: () => void
  previousCandle: () => void
  setTimeframe: (timeframe: string) => void
}

export interface PlaybackEvent {
  type: 'candle_update' | 'pattern_detected' | 'market_event' | 'prediction_point'
  timestamp: number
  data: any
  description?: string
}

export interface PredictionChallenge {
  id: string
  timestamp: number
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
  difficulty: 'easy' | 'medium' | 'hard'
  points: number
}

export class ChartPlaybackEngine {
  private data: CandlestickData[] = []
  private events: MarketEvent[] = []
  private predictions: PredictionChallenge[] = []
  private state: PlaybackState
  private intervalId: number | null = null
  private eventCallbacks: Map<string, Function[]> = new Map()
  private patternDetector: PatternDetector

  constructor() {
    this.state = {
      isPlaying: false,
      isPaused: false,
      currentIndex: 0,
      totalCandles: 0,
      speed: 1.0,
      timeframe: '1h',
      symbol: 'BTCUSD',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate: new Date(),
    }
    
    this.patternDetector = new PatternDetector()
    this.initializeEventTypes()
  }

  private initializeEventTypes() {
    this.eventCallbacks.set('candle_update', [])
    this.eventCallbacks.set('pattern_detected', [])
    this.eventCallbacks.set('market_event', [])
    this.eventCallbacks.set('prediction_point', [])
    this.eventCallbacks.set('state_change', [])
  }

  // Initialize playback with historical data
  async initialize(symbol: string, timeframe: string, startDate: Date, endDate: Date): Promise<void> {
    this.state.symbol = symbol
    this.state.timeframe = timeframe as any
    this.state.startDate = startDate
    this.state.endDate = endDate
    this.state.currentIndex = 0

    // Fetch historical data
    const request: HistoricalDataRequest = {
      symbol,
      timeframe: timeframe as any,
      startDate,
      endDate,
      limit: 2000,
    }

    try {
      this.data = await enhancedMarketDataService.getHistoricalData(request)
      this.state.totalCandles = this.data.length
      
      // Generate educational events and prediction points
      this.generateEducationalEvents()
      this.generatePredictionChallenges()
      
      this.emitEvent('state_change', this.state)
    } catch (error) {
      console.error('Failed to initialize playback:', error)
      throw error
    }
  }

  // Playback controls
  play(): void {
    if (this.state.isPlaying) return
    
    this.state.isPlaying = true
    this.state.isPaused = false
    
    const baseInterval = 1000 // 1 second base interval
    const interval = baseInterval / this.state.speed
    
    this.intervalId = window.setInterval(() => {
      this.nextCandle()
    }, interval)
    
    this.emitEvent('state_change', this.state)
  }

  pause(): void {
    if (!this.state.isPlaying) return
    
    this.state.isPlaying = false
    this.state.isPaused = true
    
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    
    this.emitEvent('state_change', this.state)
  }

  stop(): void {
    this.state.isPlaying = false
    this.state.isPaused = false
    this.state.currentIndex = 0
    
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    
    this.emitEvent('state_change', this.state)
  }

  setSpeed(speed: number): void {
    const validSpeeds = [0.25, 0.5, 1.0, 2.0, 4.0]
    this.state.speed = validSpeeds.includes(speed) ? speed : 1.0
    
    // Restart playback with new speed if currently playing
    if (this.state.isPlaying) {
      this.pause()
      this.play()
    }
    
    this.emitEvent('state_change', this.state)
  }

  jumpToIndex(index: number): void {
    const newIndex = Math.max(0, Math.min(index, this.data.length - 1))
    this.state.currentIndex = newIndex
    
    this.processCurrentCandle()
    this.emitEvent('state_change', this.state)
  }

  jumpToDate(date: Date): void {
    const targetTimestamp = date.getTime()
    const closestIndex = this.data.findIndex(candle => candle.timestamp >= targetTimestamp)
    
    if (closestIndex !== -1) {
      this.jumpToIndex(closestIndex)
    }
  }

  nextCandle(): void {
    if (this.state.currentIndex >= this.data.length - 1) {
      this.stop()
      return
    }
    
    this.state.currentIndex++
    this.processCurrentCandle()
    this.emitEvent('state_change', this.state)
  }

  previousCandle(): void {
    if (this.state.currentIndex <= 0) return
    
    this.state.currentIndex--
    this.processCurrentCandle()
    this.emitEvent('state_change', this.state)
  }

  async setTimeframe(timeframe: string): Promise<void> {
    this.pause()
    await this.initialize(this.state.symbol, timeframe, this.state.startDate, this.state.endDate)
  }

  // Event system
  on(eventType: string, callback: Function): void {
    if (!this.eventCallbacks.has(eventType)) {
      this.eventCallbacks.set(eventType, [])
    }
    this.eventCallbacks.get(eventType)!.push(callback)
  }

  off(eventType: string, callback: Function): void {
    const callbacks = this.eventCallbacks.get(eventType)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  private emitEvent(eventType: string, data: any): void {
    const callbacks = this.eventCallbacks.get(eventType) || []
    callbacks.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('Error in playback event callback:', error)
      }
    })
  }

  // Process current candle and detect events
  private processCurrentCandle(): void {
    const currentCandle = this.getCurrentCandle()
    if (!currentCandle) return

    // Emit candle update
    this.emitEvent('candle_update', {
      candle: currentCandle,
      index: this.state.currentIndex,
      visibleData: this.getVisibleData(),
    })

    // Check for patterns
    this.detectPatterns()
    
    // Check for market events
    this.checkMarketEvents()
    
    // Check for prediction points
    this.checkPredictionPoints()
  }

  private detectPatterns(): void {
    const recentCandles = this.getRecentCandles(20) // Last 20 candles for pattern detection
    const patterns = this.patternDetector.detectPatterns(recentCandles)
    
    patterns.forEach(pattern => {
      this.emitEvent('pattern_detected', {
        pattern,
        timestamp: this.getCurrentCandle()?.timestamp,
        candles: recentCandles,
      })
    })
  }

  private checkMarketEvents(): void {
    const currentTimestamp = this.getCurrentCandle()?.timestamp
    if (!currentTimestamp) return

    const relevantEvents = this.events.filter(event => 
      Math.abs(event.timestamp - currentTimestamp) < 60 * 60 * 1000 // Within 1 hour
    )

    relevantEvents.forEach(event => {
      this.emitEvent('market_event', event)
    })
  }

  private checkPredictionPoints(): void {
    const prediction = this.predictions.find(p => p.timestamp === this.getCurrentCandle()?.timestamp)
    if (prediction) {
      this.pause() // Pause for prediction challenge
      this.emitEvent('prediction_point', prediction)
    }
  }

  // Data access methods
  getCurrentCandle(): CandlestickData | null {
    return this.data[this.state.currentIndex] || null
  }

  getVisibleData(): CandlestickData[] {
    return this.data.slice(0, this.state.currentIndex + 1)
  }

  getRecentCandles(count: number): CandlestickData[] {
    const start = Math.max(0, this.state.currentIndex - count + 1)
    return this.data.slice(start, this.state.currentIndex + 1)
  }

  getState(): PlaybackState {
    return { ...this.state }
  }

  getAllData(): CandlestickData[] {
    return [...this.data]
  }

  getEvents(): MarketEvent[] {
    return [...this.events]
  }

  getPredictions(): PredictionChallenge[] {
    return [...this.predictions]
  }

  // Generate educational content
  private generateEducationalEvents(): void {
    // This would be populated with real market events in production
    this.events = []
    
    // Generate some sample events for demonstration
    const eventCount = Math.floor(this.data.length / 50) // One event per ~50 candles
    
    for (let i = 0; i < eventCount; i++) {
      const randomIndex = Math.floor(Math.random() * this.data.length)
      const candle = this.data[randomIndex]
      
      this.events.push({
        timestamp: candle.timestamp,
        title: `Market Event ${i + 1}`,
        description: `Educational market event for learning purposes`,
        impact: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
        category: ['earnings', 'news', 'economic', 'technical'][Math.floor(Math.random() * 4)] as any,
      })
    }
    
    this.events.sort((a, b) => a.timestamp - b.timestamp)
  }

  private generatePredictionChallenges(): void {
    this.predictions = []
    
    // Generate prediction points at key moments
    const predictionCount = Math.floor(this.data.length / 100) // One prediction per ~100 candles
    
    for (let i = 0; i < predictionCount; i++) {
      const randomIndex = Math.floor(Math.random() * (this.data.length - 10)) + 5 // Ensure we have future data
      const candle = this.data[randomIndex]
      const futureCandle = this.data[randomIndex + 5] // Look 5 candles ahead
      
      const isUpward = futureCandle.close > candle.close
      
      this.predictions.push({
        id: `prediction_${i}`,
        timestamp: candle.timestamp,
        question: `What do you think will happen to the price in the next few periods?`,
        options: ['Price will go up', 'Price will go down', 'Price will stay flat'],
        correctAnswer: isUpward ? 0 : 1,
        explanation: `The price ${isUpward ? 'increased' : 'decreased'} from ${candle.close.toFixed(2)} to ${futureCandle.close.toFixed(2)}`,
        difficulty: 'medium',
        points: 10,
      })
    }
    
    this.predictions.sort((a, b) => a.timestamp - b.timestamp)
  }
}

// Pattern detection helper class
class PatternDetector {
  detectPatterns(candles: CandlestickData[]): Array<{ name: string; confidence: number; description: string }> {
    const patterns = []
    
    if (candles.length < 3) return patterns
    
    // Simple pattern detection (can be expanded)
    if (this.isHammer(candles[candles.length - 1])) {
      patterns.push({
        name: 'Hammer',
        confidence: 0.8,
        description: 'Potential bullish reversal pattern detected'
      })
    }
    
    if (this.isDoji(candles[candles.length - 1])) {
      patterns.push({
        name: 'Doji',
        confidence: 0.7,
        description: 'Market indecision pattern detected'
      })
    }
    
    if (this.isEngulfing(candles.slice(-2))) {
      patterns.push({
        name: 'Engulfing',
        confidence: 0.9,
        description: 'Strong reversal pattern detected'
      })
    }
    
    return patterns
  }

  private isHammer(candle: CandlestickData): boolean {
    const bodySize = Math.abs(candle.close - candle.open)
    const lowerShadow = Math.min(candle.open, candle.close) - candle.low
    const upperShadow = candle.high - Math.max(candle.open, candle.close)
    const totalRange = candle.high - candle.low
    
    return bodySize < totalRange * 0.3 && lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5
  }

  private isDoji(candle: CandlestickData): boolean {
    const bodySize = Math.abs(candle.close - candle.open)
    const totalRange = candle.high - candle.low
    
    return bodySize < totalRange * 0.1 && totalRange > 0
  }

  private isEngulfing(candles: CandlestickData[]): boolean {
    if (candles.length < 2) return false
    
    const [prev, curr] = candles
    
    // Bullish engulfing
    if (prev.close < prev.open && curr.close > curr.open) {
      return curr.open < prev.close && curr.close > prev.open
    }
    
    // Bearish engulfing
    if (prev.close > prev.open && curr.close < curr.open) {
      return curr.open > prev.close && curr.close < prev.open
    }
    
    return false
  }
}

export { ChartPlaybackEngine }
