module.exports = {

"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculatePnL": (()=>calculatePnL),
    "calculatePnLPercentage": (()=>calculatePnLPercentage),
    "cn": (()=>cn),
    "debounce": (()=>debounce),
    "formatCurrency": (()=>formatCurrency),
    "formatLargeNumber": (()=>formatLargeNumber),
    "formatNumber": (()=>formatNumber),
    "formatPercentage": (()=>formatPercentage),
    "generateColor": (()=>generateColor),
    "generateSessionId": (()=>generateSessionId),
    "getRandomElement": (()=>getRandomElement),
    "getTimeRemaining": (()=>getTimeRemaining),
    "isMinor": (()=>isMinor),
    "sanitizeUsername": (()=>sanitizeUsername),
    "shuffleArray": (()=>shuffleArray),
    "sleep": (()=>sleep),
    "throttle": (()=>throttle),
    "validateAge": (()=>validateAge)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}
function formatPercentage(value, decimals = 2) {
    return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`;
}
function formatNumber(value, decimals = 2) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(value);
}
function formatLargeNumber(value) {
    if (value >= 1e9) {
        return `${(value / 1e9).toFixed(1)}B`;
    }
    if (value >= 1e6) {
        return `${(value / 1e6).toFixed(1)}M`;
    }
    if (value >= 1e3) {
        return `${(value / 1e3).toFixed(1)}K`;
    }
    return value.toString();
}
function calculatePnL(entryPrice, currentPrice, quantity, side) {
    const priceDiff = currentPrice - entryPrice;
    return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity;
}
function calculatePnLPercentage(entryPrice, currentPrice, side) {
    const priceDiff = currentPrice - entryPrice;
    const percentage = priceDiff / entryPrice * 100;
    return side === 'buy' ? percentage : -percentage;
}
function generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
function isMinor(age) {
    return age < 18;
}
function validateAge(age) {
    return age >= 13 && age <= 120;
}
function sanitizeUsername(username) {
    return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase();
}
function getTimeRemaining(endTime) {
    const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString());
    const seconds = Math.floor(total / 1000 % 60);
    const minutes = Math.floor(total / 1000 / 60 % 60);
    const hours = Math.floor(total / (1000 * 60 * 60) % 24);
    const days = Math.floor(total / (1000 * 60 * 60 * 24));
    return {
        total,
        days,
        hours,
        minutes,
        seconds
    };
}
function debounce(func, wait) {
    let timeout;
    return (...args)=>{
        clearTimeout(timeout);
        timeout = setTimeout(()=>func(...args), wait);
    };
}
function throttle(func, limit) {
    let inThrottle;
    return (...args)=>{
        if (!inThrottle) {
            func(...args);
            inThrottle = true;
            setTimeout(()=>inThrottle = false, limit);
        }
    };
}
function getRandomElement(array) {
    return array[Math.floor(Math.random() * array.length)];
}
function shuffleArray(array) {
    const shuffled = [
        ...array
    ];
    for(let i = shuffled.length - 1; i > 0; i--){
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [
            shuffled[j],
            shuffled[i]
        ];
    }
    return shuffled;
}
function sleep(ms) {
    return new Promise((resolve)=>setTimeout(resolve, ms));
}
function generateColor(seed) {
    let hash = 0;
    for(let i = 0; i < seed.length; i++){
        hash = seed.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = hash % 360;
    return `hsl(${hue}, 70%, 50%)`;
}
}}),
"[project]/src/lib/constants.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ACHIEVEMENT_CATEGORIES": (()=>ACHIEVEMENT_CATEGORIES),
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "ERROR_MESSAGES": (()=>ERROR_MESSAGES),
    "GAME_CONFIGS": (()=>GAME_CONFIGS),
    "INTERFACE_MODES": (()=>INTERFACE_MODES),
    "LEVEL_THRESHOLDS": (()=>LEVEL_THRESHOLDS),
    "QUEST_COIN_MULTIPLIERS": (()=>QUEST_COIN_MULTIPLIERS),
    "SUCCESS_MESSAGES": (()=>SUCCESS_MESSAGES),
    "TRADING_PAIRS": (()=>TRADING_PAIRS),
    "UPDATE_INTERVALS": (()=>UPDATE_INTERVALS),
    "VALIDATION_RULES": (()=>VALIDATION_RULES)
});
const GAME_CONFIGS = {
    scalper_sprint: {
        name: 'Scalper Sprint',
        description: '60-second trading challenges with rapid-fire decisions',
        difficulty: 'beginner',
        duration_seconds: 60,
        starting_balance: 10000,
        min_trade_size: 100,
        max_positions: 3,
        quest_coins_base: 50
    },
    candle_strike: {
        name: 'CandleStrike',
        description: 'Pattern recognition game with candlestick charts',
        difficulty: 'beginner',
        duration_seconds: 120,
        starting_balance: 0,
        patterns_to_identify: 5,
        quest_coins_base: 75
    },
    chain_maze: {
        name: 'ChainMaze',
        description: 'Navigate blockchain puzzles and learn consensus mechanisms',
        difficulty: 'intermediate',
        duration_seconds: 300,
        starting_balance: 1000,
        puzzles_to_solve: 3,
        quest_coins_base: 100
    },
    swing_trader_odyssey: {
        name: "Swing Trader's Odyssey",
        description: 'Multi-day position management with risk/reward balancing',
        difficulty: 'intermediate',
        duration_seconds: 600,
        starting_balance: 50000,
        max_positions: 5,
        quest_coins_base: 150
    },
    day_trader_arena: {
        name: 'Day Trader Arena',
        description: 'Real-time multiplayer trading competitions',
        difficulty: 'advanced',
        duration_seconds: 900,
        starting_balance: 100000,
        max_positions: 10,
        quest_coins_base: 200
    },
    portfolio_survivor: {
        name: 'Portfolio Survivor',
        description: 'Crisis management with diversification challenges',
        difficulty: 'advanced',
        duration_seconds: 1200,
        starting_balance: 500000,
        max_positions: 20,
        quest_coins_base: 300
    }
};
const TRADING_PAIRS = [
    {
        base: 'BTC',
        quote: 'USD',
        symbol: 'BTCUSD',
        exchange: 'virtual'
    },
    {
        base: 'ETH',
        quote: 'USD',
        symbol: 'ETHUSD',
        exchange: 'virtual'
    },
    {
        base: 'ADA',
        quote: 'USD',
        symbol: 'ADAUSD',
        exchange: 'virtual'
    },
    {
        base: 'SOL',
        quote: 'USD',
        symbol: 'SOLUSD',
        exchange: 'virtual'
    },
    {
        base: 'AAPL',
        quote: 'USD',
        symbol: 'AAPL',
        exchange: 'virtual'
    },
    {
        base: 'GOOGL',
        quote: 'USD',
        symbol: 'GOOGL',
        exchange: 'virtual'
    },
    {
        base: 'TSLA',
        quote: 'USD',
        symbol: 'TSLA',
        exchange: 'virtual'
    },
    {
        base: 'EUR',
        quote: 'USD',
        symbol: 'EURUSD',
        exchange: 'virtual'
    },
    {
        base: 'GBP',
        quote: 'USD',
        symbol: 'GBPUSD',
        exchange: 'virtual'
    },
    {
        base: 'JPY',
        quote: 'USD',
        symbol: 'JPYUSD',
        exchange: 'virtual'
    }
];
const ACHIEVEMENT_CATEGORIES = {
    trading: {
        name: 'Trading Mastery',
        color: '#10B981',
        icon: '📈'
    },
    learning: {
        name: 'Knowledge Seeker',
        color: '#3B82F6',
        icon: '🎓'
    },
    social: {
        name: 'Community Builder',
        color: '#8B5CF6',
        icon: '👥'
    },
    special: {
        name: 'Special Events',
        color: '#F59E0B',
        icon: '⭐'
    }
};
const LEVEL_THRESHOLDS = [
    0,
    100,
    250,
    500,
    1000,
    1750,
    2750,
    4000,
    5500,
    7500,
    10000,
    13000,
    16500,
    20500,
    25000,
    30000,
    35500,
    41500,
    48000,
    55000,
    62500
];
const QUEST_COIN_MULTIPLIERS = {
    beginner: 1.0,
    intermediate: 1.5,
    advanced: 2.0
};
const INTERFACE_MODES = {
    adolescent: {
        name: 'Adventure Mode',
        description: 'Fantasy-themed interface with quests and adventures',
        primaryColor: '#8B5CF6',
        secondaryColor: '#EC4899',
        fontFamily: 'fantasy'
    },
    adult: {
        name: 'Professional Mode',
        description: 'Bloomberg Terminal-style professional interface',
        primaryColor: '#1F2937',
        secondaryColor: '#3B82F6',
        fontFamily: 'monospace'
    }
};
const UPDATE_INTERVALS = {
    real_time: 1000,
    fast: 5000,
    normal: 15000,
    slow: 60000
};
const API_ENDPOINTS = {
    coingecko: {
        base: 'https://api.coingecko.com/api/v3',
        prices: '/simple/price',
        history: '/coins/{id}/market_chart'
    },
    alpha_vantage: {
        base: 'https://www.alphavantage.co/query',
        intraday: '?function=TIME_SERIES_INTRADAY',
        forex: '?function=FX_INTRADAY'
    }
};
const VALIDATION_RULES = {
    username: {
        minLength: 3,
        maxLength: 20,
        pattern: /^[a-zA-Z0-9_-]+$/
    },
    age: {
        min: 13,
        max: 120
    },
    trade: {
        minAmount: 1,
        maxAmount: 1000000
    }
};
const ERROR_MESSAGES = {
    auth: {
        invalid_credentials: 'Invalid email or password',
        user_not_found: 'User not found',
        email_already_exists: 'Email already registered',
        weak_password: 'Password must be at least 8 characters',
        age_verification_failed: 'Age verification required'
    },
    game: {
        session_expired: 'Game session has expired',
        invalid_trade: 'Invalid trade parameters',
        insufficient_balance: 'Insufficient balance for this trade',
        max_positions_reached: 'Maximum number of positions reached'
    },
    general: {
        network_error: 'Network error, please try again',
        server_error: 'Server error, please try again later',
        validation_error: 'Please check your input and try again'
    }
};
const SUCCESS_MESSAGES = {
    auth: {
        registration_complete: 'Account created successfully!',
        login_success: 'Welcome back!',
        logout_success: 'Logged out successfully'
    },
    game: {
        session_complete: 'Game session completed!',
        achievement_unlocked: 'Achievement unlocked!',
        level_up: 'Level up! Congratulations!'
    },
    general: {
        save_success: 'Changes saved successfully',
        update_success: 'Updated successfully'
    }
};
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[project]/src/lib/services/market-data.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "marketDataService": (()=>marketDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
;
;
class MarketDataService {
    coingeckoClient;
    alphaVantageClient;
    constructor(){
        this.coingeckoClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].coingecko.base,
            timeout: 10000
        });
        this.alphaVantageClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
            baseURL: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].alpha_vantage.base,
            timeout: 10000
        });
    }
    // Cryptocurrency data from CoinGecko
    async getCryptoPrices(symbols) {
        try {
            const ids = symbols.map((symbol)=>this.symbolToCoinGeckoId(symbol)).join(',');
            const response = await this.coingeckoClient.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].coingecko.prices, {
                params: {
                    ids,
                    vs_currencies: 'usd',
                    include_24hr_change: true,
                    include_24hr_vol: true,
                    include_market_cap: true
                }
            });
            return this.formatCoinGeckoResponse(response.data, symbols);
        } catch (error) {
            console.error('Error fetching crypto prices:', error);
            return this.generateMockCryptoData(symbols);
        }
    }
    // Stock data from Alpha Vantage
    async getStockPrices(symbols) {
        try {
            const promises = symbols.map((symbol)=>this.fetchStockPrice(symbol));
            const results = await Promise.all(promises);
            return results.filter(Boolean);
        } catch (error) {
            console.error('Error fetching stock prices:', error);
            return this.generateMockStockData(symbols);
        }
    }
    // Forex data from Alpha Vantage
    async getForexPrices(pairs) {
        try {
            const promises = pairs.map((pair)=>this.fetchForexPrice(pair));
            const results = await Promise.all(promises);
            return results.filter(Boolean);
        } catch (error) {
            console.error('Error fetching forex prices:', error);
            return this.generateMockForexData(pairs);
        }
    }
    // Historical candlestick data with enhanced pattern detection
    async getCandlestickData(symbol, interval = '1h', days = 7) {
        try {
            if (this.isCryptoSymbol(symbol)) {
                return await this.getCryptoCandlestickData(symbol, days);
            } else {
                return await this.getStockCandlestickData(symbol, interval);
            }
        } catch (error) {
            console.error('Error fetching candlestick data:', error);
            return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data
            ;
        }
    }
    // Get historical data with specific patterns for educational purposes
    async getHistoricalDataWithPatterns(symbol, patternType, count = 10) {
        try {
            // For demo purposes, we'll use a combination of real data and pattern-enhanced data
            const baseData = await this.getCandlestickData(symbol, '1h', 30) // 30 days of data
            ;
            // Find or create segments with the requested pattern
            return this.extractPatternSegments(baseData, patternType, count);
        } catch (error) {
            console.error('Error fetching pattern data:', error);
            return this.generatePatternDatasets(symbol, patternType, count);
        }
    }
    // TradingView-style data format
    async getTradingViewData(symbol, resolution = '60', from, to) {
        try {
            const data = await this.getCandlestickData(symbol, '1h', 7);
            return {
                s: 'ok',
                t: data.map((d)=>Math.floor(d.timestamp / 1000)),
                o: data.map((d)=>d.open),
                h: data.map((d)=>d.high),
                l: data.map((d)=>d.low),
                c: data.map((d)=>d.close),
                v: data.map((d)=>d.volume)
            };
        } catch (error) {
            return {
                s: 'error',
                t: [],
                o: [],
                h: [],
                l: [],
                c: [],
                v: []
            };
        }
    }
    // Private helper methods
    async fetchStockPrice(symbol) {
        try {
            const response = await this.alphaVantageClient.get('', {
                params: {
                    function: 'GLOBAL_QUOTE',
                    symbol,
                    apikey: process.env.ALPHA_VANTAGE_API_KEY
                }
            });
            const quote = response.data['Global Quote'];
            if (!quote) return null;
            return {
                symbol,
                price: parseFloat(quote['05. price']),
                change_24h: parseFloat(quote['09. change']),
                change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),
                volume_24h: parseFloat(quote['06. volume']),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return null;
        }
    }
    async fetchForexPrice(pair) {
        try {
            const [from, to] = pair.split('/');
            const response = await this.alphaVantageClient.get('', {
                params: {
                    function: 'CURRENCY_EXCHANGE_RATE',
                    from_currency: from,
                    to_currency: to,
                    apikey: process.env.ALPHA_VANTAGE_API_KEY
                }
            });
            const rate = response.data['Realtime Currency Exchange Rate'];
            if (!rate) return null;
            return {
                symbol: pair,
                price: parseFloat(rate['5. Exchange Rate']),
                change_24h: 0,
                change_percentage_24h: 0,
                volume_24h: 0,
                timestamp: rate['6. Last Refreshed']
            };
        } catch (error) {
            return null;
        }
    }
    async getCryptoCandlestickData(symbol, days) {
        const id = this.symbolToCoinGeckoId(symbol);
        const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {
            params: {
                vs_currency: 'usd',
                days,
                interval: 'hourly'
            }
        });
        const prices = response.data.prices;
        const volumes = response.data.total_volumes;
        return prices.map((price, index)=>({
                timestamp: price[0],
                open: index > 0 ? prices[index - 1][1] : price[1],
                high: price[1] * (1 + Math.random() * 0.02),
                low: price[1] * (1 - Math.random() * 0.02),
                close: price[1],
                volume: volumes[index] ? volumes[index][1] : 0
            }));
    }
    async getStockCandlestickData(symbol, interval) {
        const response = await this.alphaVantageClient.get('', {
            params: {
                function: 'TIME_SERIES_INTRADAY',
                symbol,
                interval,
                apikey: process.env.ALPHA_VANTAGE_API_KEY
            }
        });
        const timeSeries = response.data[`Time Series (${interval})`];
        if (!timeSeries) return [];
        return Object.entries(timeSeries).map(([timestamp, data])=>({
                timestamp: new Date(timestamp).getTime(),
                open: parseFloat(data['1. open']),
                high: parseFloat(data['2. high']),
                low: parseFloat(data['3. low']),
                close: parseFloat(data['4. close']),
                volume: parseFloat(data['5. volume'])
            }));
    }
    symbolToCoinGeckoId(symbol) {
        const mapping = {
            BTC: 'bitcoin',
            ETH: 'ethereum',
            ADA: 'cardano',
            SOL: 'solana',
            DOT: 'polkadot',
            LINK: 'chainlink',
            UNI: 'uniswap',
            MATIC: 'polygon'
        };
        return mapping[symbol.toUpperCase()] || symbol.toLowerCase();
    }
    isCryptoSymbol(symbol) {
        const cryptoSymbols = [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'LINK',
            'UNI',
            'MATIC'
        ];
        return cryptoSymbols.includes(symbol.toUpperCase());
    }
    formatCoinGeckoResponse(data, symbols) {
        return symbols.map((symbol)=>{
            const id = this.symbolToCoinGeckoId(symbol);
            const coinData = data[id];
            if (!coinData) return this.generateMockCryptoData([
                symbol
            ])[0];
            return {
                symbol,
                price: coinData.usd,
                change_24h: coinData.usd_24h_change || 0,
                change_percentage_24h: coinData.usd_24h_change || 0,
                volume_24h: coinData.usd_24h_vol || 0,
                market_cap: coinData.usd_market_cap,
                timestamp: new Date().toISOString()
            };
        });
    }
    // Mock data generators for development and fallback
    generateMockCryptoData(symbols) {
        const basePrices = {
            BTC: 45000,
            ETH: 3000,
            ADA: 0.5,
            SOL: 100
        };
        return symbols.map((symbol)=>({
                symbol,
                price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
                change_24h: (Math.random() - 0.5) * 1000,
                change_percentage_24h: (Math.random() - 0.5) * 10,
                volume_24h: Math.random() * 1000000000,
                market_cap: Math.random() * 100000000000,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockStockData(symbols) {
        const basePrices = {
            AAPL: 150,
            GOOGL: 2500,
            TSLA: 800,
            MSFT: 300
        };
        return symbols.map((symbol)=>({
                symbol,
                price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),
                change_24h: (Math.random() - 0.5) * 20,
                change_percentage_24h: (Math.random() - 0.5) * 5,
                volume_24h: Math.random() * 100000000,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockForexData(pairs) {
        const basePrices = {
            'EUR/USD': 1.1,
            'GBP/USD': 1.3,
            'USD/JPY': 110,
            'USD/CHF': 0.9
        };
        return pairs.map((pair)=>({
                symbol: pair,
                price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),
                change_24h: (Math.random() - 0.5) * 0.01,
                change_percentage_24h: (Math.random() - 0.5) * 1,
                volume_24h: 0,
                timestamp: new Date().toISOString()
            }));
    }
    generateMockCandlestickData(symbol, count) {
        const data = [];
        let price = 100 + Math.random() * 900;
        const now = Date.now();
        for(let i = 0; i < count; i++){
            const timestamp = now - (count - i) * 3600000 // Hourly intervals
            ;
            const change = (Math.random() - 0.5) * 10;
            const open = price;
            const close = price + change;
            const high = Math.max(open, close) + Math.random() * 5;
            const low = Math.min(open, close) - Math.random() * 5;
            const volume = Math.random() * 1000000;
            data.push({
                timestamp,
                open,
                high,
                low,
                close,
                volume
            });
            price = close;
        }
        return data;
    }
    // Extract segments containing specific patterns from real data
    extractPatternSegments(data, patternType, count) {
        const segments = [];
        const segmentLength = 20 // 20 candles per segment
        ;
        // Scan through data looking for patterns
        for(let i = 0; i <= data.length - segmentLength && segments.length < count; i++){
            const segment = data.slice(i, i + segmentLength);
            if (this.containsPattern(segment, patternType)) {
                segments.push(segment);
                i += segmentLength - 1 // Skip ahead to avoid overlapping segments
                ;
            }
        }
        // If we don't have enough real patterns, generate some
        while(segments.length < count){
            segments.push(this.generateSegmentWithPattern(patternType, segmentLength));
        }
        return segments;
    }
    // Check if a segment contains a specific pattern
    containsPattern(segment, patternType) {
        switch(patternType){
            case 'hammer':
                return this.detectHammer(segment);
            case 'doji':
                return this.detectDoji(segment);
            case 'engulfing_bullish':
                return this.detectBullishEngulfing(segment);
            case 'engulfing_bearish':
                return this.detectBearishEngulfing(segment);
            case 'morning_star':
                return this.detectMorningStar(segment);
            case 'evening_star':
                return this.detectEveningStar(segment);
            default:
                return false;
        }
    }
    // Pattern detection algorithms
    detectHammer(segment) {
        for(let i = 1; i < segment.length - 1; i++){
            const candle = segment[i];
            const bodySize = Math.abs(candle.close - candle.open);
            const lowerShadow = Math.min(candle.open, candle.close) - candle.low;
            const upperShadow = candle.high - Math.max(candle.open, candle.close);
            const totalRange = candle.high - candle.low;
            // Hammer criteria: small body, long lower shadow, small upper shadow
            if (bodySize < totalRange * 0.3 && lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5) {
                return true;
            }
        }
        return false;
    }
    detectDoji(segment) {
        for(let i = 0; i < segment.length; i++){
            const candle = segment[i];
            const bodySize = Math.abs(candle.close - candle.open);
            const totalRange = candle.high - candle.low;
            // Doji criteria: very small body relative to total range
            if (bodySize < totalRange * 0.1 && totalRange > 0) {
                return true;
            }
        }
        return false;
    }
    detectBullishEngulfing(segment) {
        for(let i = 1; i < segment.length; i++){
            const prev = segment[i - 1];
            const curr = segment[i];
            // Previous candle is bearish, current is bullish and engulfs previous
            if (prev.close < prev.open && // Previous bearish
            curr.close > curr.open && // Current bullish
            curr.open < prev.close && // Current opens below previous close
            curr.close > prev.open) {
                return true;
            }
        }
        return false;
    }
    detectBearishEngulfing(segment) {
        for(let i = 1; i < segment.length; i++){
            const prev = segment[i - 1];
            const curr = segment[i];
            // Previous candle is bearish, current is bullish and engulfs previous
            if (prev.close > prev.open && // Previous bullish
            curr.close < curr.open && // Current bearish
            curr.open > prev.close && // Current opens above previous close
            curr.close < prev.open) {
                return true;
            }
        }
        return false;
    }
    detectMorningStar(segment) {
        for(let i = 2; i < segment.length; i++){
            const first = segment[i - 2];
            const second = segment[i - 1];
            const third = segment[i];
            // Three candle pattern: bearish, small body, bullish
            if (first.close < first.open && // First bearish
            Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small
            third.close > third.open && // Third bullish
            third.close > (first.open + first.close) / 2) {
                return true;
            }
        }
        return false;
    }
    detectEveningStar(segment) {
        for(let i = 2; i < segment.length; i++){
            const first = segment[i - 2];
            const second = segment[i - 1];
            const third = segment[i];
            // Three candle pattern: bullish, small body, bearish
            if (first.close > first.open && // First bullish
            Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small
            third.close < third.open && // Third bearish
            third.close < (first.open + first.close) / 2) {
                return true;
            }
        }
        return false;
    }
    // Generate a segment with a specific pattern
    generateSegmentWithPattern(patternType, length) {
        const segment = [];
        let currentPrice = 100 + Math.random() * 50;
        const now = Date.now();
        // Generate leading candles
        const patternPosition = Math.floor(length * 0.4) + Math.floor(Math.random() * Math.floor(length * 0.3));
        for(let i = 0; i < patternPosition; i++){
            const candle = this.generateRandomCandle(currentPrice, i, now);
            segment.push(candle);
            currentPrice = candle.close;
        }
        // Generate pattern candles
        const patternCandles = this.generateSpecificPattern(patternType, currentPrice, patternPosition, now);
        segment.push(...patternCandles);
        currentPrice = patternCandles[patternCandles.length - 1].close;
        // Generate trailing candles
        for(let i = patternPosition + patternCandles.length; i < length; i++){
            const candle = this.generateRandomCandle(currentPrice, i, now);
            segment.push(candle);
            currentPrice = candle.close;
        }
        return segment;
    }
    generateSpecificPattern(patternType, startPrice, startIndex, baseTime) {
        switch(patternType){
            case 'hammer':
                return this.generateHammerCandle(startPrice, startIndex, baseTime);
            case 'doji':
                return this.generateDojiCandle(startPrice, startIndex, baseTime);
            case 'engulfing_bullish':
                return this.generateBullishEngulfingPattern(startPrice, startIndex, baseTime);
            case 'engulfing_bearish':
                return this.generateBearishEngulfingPattern(startPrice, startIndex, baseTime);
            case 'morning_star':
                return this.generateMorningStarPattern(startPrice, startIndex, baseTime);
            case 'evening_star':
                return this.generateEveningStarPattern(startPrice, startIndex, baseTime);
            default:
                return this.generateHammerCandle(startPrice, startIndex, baseTime);
        }
    }
}
const marketDataService = new MarketDataService();
}}),
"[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "BaseGame": (()=>BaseGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/market-data.ts [app-ssr] (ecmascript)");
;
;
;
class BaseGame {
    config;
    state;
    startTime;
    endTime;
    isActive = false;
    marketData = new Map();
    constructor(gameType, difficulty){
        const gameConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GAME_CONFIGS"][gameType];
        this.config = {
            type: gameType,
            difficulty,
            duration_seconds: gameConfig.duration_seconds,
            starting_balance: gameConfig.starting_balance,
            available_pairs: [],
            special_rules: {}
        };
        this.state = {
            session_id: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateSessionId"])(),
            current_balance: this.config.starting_balance,
            positions: [],
            time_remaining: this.config.duration_seconds,
            score: 0,
            multiplier: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QUEST_COIN_MULTIPLIERS"][difficulty]
        };
        this.startTime = Date.now();
        this.endTime = this.startTime + this.config.duration_seconds * 1000;
    }
    // Methods that can be overridden by specific games
    async initialize() {
    // Default implementation - can be overridden
    }
    update() {
    // Default implementation - can be overridden
    }
    calculateScore() {
        // Default implementation - can be overridden
        return 0;
    }
    getGameSpecificData() {
        // Default implementation - can be overridden
        return {};
    }
    // Common game lifecycle methods
    async start() {
        await this.initialize();
        this.isActive = true;
        this.startGameLoop();
    }
    pause() {
        this.isActive = false;
    }
    resume() {
        this.isActive = true;
        this.startGameLoop();
    }
    end() {
        this.isActive = false;
        this.state.score = this.calculateScore();
        this.state.time_remaining = 0;
        return this.state;
    }
    // Trading operations
    async executeTrade(symbol, side, quantity) {
        if (!this.isActive) return false;
        const currentPrice = this.marketData.get(symbol);
        if (!currentPrice) return false;
        const tradeValue = currentPrice * quantity;
        const requiredBalance = side === 'buy' ? tradeValue : 0;
        if (this.state.current_balance < requiredBalance) {
            return false // Insufficient balance
            ;
        }
        // Check position limits
        const maxPositions = this.getMaxPositions();
        if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {
            return false // Too many positions
            ;
        }
        // Execute the trade
        const position = {
            id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            symbol,
            side,
            quantity,
            entry_price: currentPrice,
            current_price: currentPrice,
            pnl: 0,
            timestamp: new Date().toISOString()
        };
        // Update balance
        if (side === 'buy') {
            this.state.current_balance -= tradeValue;
        } else {
            this.state.current_balance += tradeValue;
        }
        // Add or update position
        const existingPositionIndex = this.state.positions.findIndex((p)=>p.symbol === symbol);
        if (existingPositionIndex >= 0) {
            // Update existing position (average price calculation would go here)
            this.state.positions[existingPositionIndex] = position;
        } else {
            this.state.positions.push(position);
        }
        return true;
    }
    async closePosition(positionId) {
        if (!this.isActive) return false;
        const positionIndex = this.state.positions.findIndex((p)=>p.id === positionId);
        if (positionIndex === -1) return false;
        const position = this.state.positions[positionIndex];
        const currentPrice = this.marketData.get(position.symbol);
        if (!currentPrice) return false;
        // Calculate final P&L
        const pnl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculatePnL"])(position.entry_price, currentPrice, position.quantity, position.side);
        // Update balance with P&L
        this.state.current_balance += pnl;
        if (position.side === 'sell') {
            // Return the initial trade value for short positions
            this.state.current_balance += position.entry_price * position.quantity;
        }
        // Remove position
        this.state.positions.splice(positionIndex, 1);
        return true;
    }
    // Market data updates
    async updateMarketData() {
        try {
            const symbols = this.config.available_pairs.map((pair)=>pair.symbol);
            // In a real implementation, you'd fetch from different services based on asset type
            const cryptoSymbols = symbols.filter((s)=>this.isCryptoSymbol(s));
            const stockSymbols = symbols.filter((s)=>this.isStockSymbol(s));
            const forexSymbols = symbols.filter((s)=>this.isForexSymbol(s));
            const [cryptoData, stockData, forexData] = await Promise.all([
                cryptoSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["marketDataService"].getCryptoPrices(cryptoSymbols) : [],
                stockSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["marketDataService"].getStockPrices(stockSymbols) : [],
                forexSymbols.length > 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$market$2d$data$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["marketDataService"].getForexPrices(forexSymbols) : []
            ]);
            // Update market data map
            const allData = cryptoData.concat(stockData).concat(forexData);
            allData.forEach((data)=>{
                this.marketData.set(data.symbol, data.price);
            });
            // Update position P&L
            this.updatePositionPnL();
        } catch (error) {
            console.error('Error updating market data:', error);
        }
    }
    // Game state getters
    getState() {
        return {
            ...this.state
        };
    }
    getConfig() {
        return {
            ...this.config
        };
    }
    isGameActive() {
        return this.isActive && this.state.time_remaining > 0;
    }
    getTimeRemaining() {
        if (!this.isActive) return 0;
        const remaining = Math.max(0, this.endTime - Date.now());
        this.state.time_remaining = Math.floor(remaining / 1000);
        return this.state.time_remaining;
    }
    // Protected helper methods
    startGameLoop() {
        if (!this.isActive) return;
        const gameLoop = ()=>{
            if (!this.isActive) return;
            this.update();
            this.getTimeRemaining();
            if (this.state.time_remaining <= 0) {
                this.end();
                return;
            }
            setTimeout(gameLoop, 1000) // Update every second
            ;
        };
        gameLoop();
    }
    updatePositionPnL() {
        this.state.positions.forEach((position)=>{
            const currentPrice = this.marketData.get(position.symbol);
            if (currentPrice) {
                position.current_price = currentPrice;
                position.pnl = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculatePnL"])(position.entry_price, currentPrice, position.quantity, position.side);
            }
        });
    }
    getTotalPnL() {
        return this.state.positions.reduce((total, position)=>total + position.pnl, 0);
    }
    getMaxPositions() {
        const gameConfig = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GAME_CONFIGS"][this.config.type];
        return gameConfig.max_positions || 5;
    }
    hasExistingPosition(symbol) {
        return this.state.positions.some((p)=>p.symbol === symbol);
    }
    isCryptoSymbol(symbol) {
        const cryptoSymbols = [
            'BTC',
            'ETH',
            'ADA',
            'SOL',
            'DOT',
            'LINK',
            'UNI',
            'MATIC'
        ];
        return cryptoSymbols.some((crypto)=>symbol.includes(crypto));
    }
    isStockSymbol(symbol) {
        const stockSymbols = [
            'AAPL',
            'GOOGL',
            'TSLA',
            'MSFT',
            'AMZN',
            'META',
            'NVDA'
        ];
        return stockSymbols.includes(symbol);
    }
    isForexSymbol(symbol) {
        return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY');
    }
    generateRandomPrice(basePrice, volatility = 0.02) {
        const change = (Math.random() - 0.5) * 2 * volatility;
        return basePrice * (1 + change);
    }
    simulateMarketMovement() {
        // Simulate realistic market movements for game purposes
        this.marketData.forEach((price, symbol)=>{
            const volatility = this.getSymbolVolatility(symbol);
            const newPrice = this.generateRandomPrice(price, volatility);
            this.marketData.set(symbol, newPrice);
        });
    }
    getSymbolVolatility(symbol) {
        if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto
        ;
        if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks
        ;
        if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex
        ;
        return 0.02 // Default 2%
        ;
    }
}
}}),
"[project]/src/lib/game-engine/games/scalper-sprint.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ScalperSprintGame": (()=>ScalperSprintGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
;
;
class ScalperSprintGame extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseGame"] {
    gameData;
    tradeHistory = [];
    constructor(difficulty){
        super('scalper_sprint', difficulty);
        this.gameData = {
            trades_executed: 0,
            successful_trades: 0,
            largest_gain: 0,
            largest_loss: 0,
            average_hold_time: 0,
            speed_bonus: 0
        };
        // Set available trading pairs for scalping (high volatility pairs)
        this.config.available_pairs = this.getScalpingPairs(difficulty);
    }
    async initialize() {
        // Initialize market data with realistic scalping prices
        const initialPrices = this.generateInitialPrices();
        initialPrices.forEach((price, symbol)=>{
            this.marketData.set(symbol, price);
        });
        // Start market data updates more frequently for scalping
        this.startMarketDataUpdates();
    }
    update() {
        // Update market data with high frequency for scalping simulation
        this.simulateScalpingMarketMovement();
        this.updatePositionPnL();
        // Check for auto-close conditions (stop loss, take profit)
        this.checkAutoCloseConditions();
        // Update game-specific metrics
        this.updateGameMetrics();
    }
    calculateScore() {
        const totalPnL = this.getTotalPnL();
        const balanceChange = this.state.current_balance - this.config.starting_balance + totalPnL;
        const balanceChangePercentage = balanceChange / this.config.starting_balance * 100;
        // Base score from P&L percentage
        let score = Math.max(0, balanceChangePercentage * 10);
        // Bonus for number of successful trades
        const successRate = this.gameData.trades_executed > 0 ? this.gameData.successful_trades / this.gameData.trades_executed : 0;
        score += successRate * 50;
        // Speed bonus for quick decision making
        score += this.gameData.speed_bonus;
        // Penalty for holding positions too long (this is scalping!)
        const avgHoldTimePenalty = Math.max(0, (this.gameData.average_hold_time - 10) * 2);
        score -= avgHoldTimePenalty;
        // Difficulty multiplier
        score *= this.state.multiplier;
        return Math.round(Math.max(0, score));
    }
    getGameSpecificData() {
        return {
            ...this.gameData
        };
    }
    // Override trade execution to add scalping-specific logic
    async executeTrade(symbol, side, quantity) {
        const success = await super.executeTrade(symbol, side, quantity);
        if (success) {
            this.gameData.trades_executed++;
            // Record trade for analytics
            this.tradeHistory.push({
                timestamp: Date.now(),
                symbol,
                side,
                entry_price: this.marketData.get(symbol)
            });
            // Speed bonus for quick trades
            const timeSinceStart = (Date.now() - this.startTime) / 1000;
            if (timeSinceStart < 10) {
                this.gameData.speed_bonus += 5;
            }
        }
        return success;
    }
    // Override position closing to track scalping metrics
    async closePosition(positionId) {
        const position = this.state.positions.find((p)=>p.id === positionId);
        if (!position) return false;
        const success = await super.closePosition(positionId);
        if (success && position) {
            const tradeRecord = this.tradeHistory.find((t)=>t.symbol === position.symbol && t.entry_price === position.entry_price && !t.exit_price);
            if (tradeRecord) {
                const holdTime = (Date.now() - tradeRecord.timestamp) / 1000;
                const exitPrice = this.marketData.get(position.symbol);
                const pnl = position.pnl;
                // Update trade record
                tradeRecord.exit_price = exitPrice;
                tradeRecord.hold_time = holdTime;
                tradeRecord.pnl = pnl;
                // Update game metrics
                if (pnl > 0) {
                    this.gameData.successful_trades++;
                    this.gameData.largest_gain = Math.max(this.gameData.largest_gain, pnl);
                } else {
                    this.gameData.largest_loss = Math.min(this.gameData.largest_loss, pnl);
                }
                this.updateAverageHoldTime();
            }
        }
        return success;
    }
    getScalpingPairs(difficulty) {
        const allPairs = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRADING_PAIRS"];
        switch(difficulty){
            case 'beginner':
                // Major crypto pairs with high liquidity
                return allPairs.filter((pair)=>[
                        'BTCUSD',
                        'ETHUSD'
                    ].includes(pair.symbol));
            case 'intermediate':
                // Add some altcoins and major stocks
                return allPairs.filter((pair)=>[
                        'BTCUSD',
                        'ETHUSD',
                        'ADAUSD',
                        'AAPL',
                        'GOOGL'
                    ].includes(pair.symbol));
            case 'advanced':
                // All available pairs including forex
                return allPairs;
            default:
                return allPairs.slice(0, 3);
        }
    }
    generateInitialPrices() {
        const prices = new Map();
        // Realistic starting prices for scalping simulation
        const basePrices = {
            'BTCUSD': 45000 + (Math.random() - 0.5) * 2000,
            'ETHUSD': 3000 + (Math.random() - 0.5) * 200,
            'ADAUSD': 0.5 + (Math.random() - 0.5) * 0.1,
            'SOLUSD': 100 + (Math.random() - 0.5) * 20,
            'AAPL': 150 + (Math.random() - 0.5) * 10,
            'GOOGL': 2500 + (Math.random() - 0.5) * 100,
            'TSLA': 800 + (Math.random() - 0.5) * 50,
            'EURUSD': 1.1 + (Math.random() - 0.5) * 0.02,
            'GBPUSD': 1.3 + (Math.random() - 0.5) * 0.02,
            'JPYUSD': 0.009 + (Math.random() - 0.5) * 0.0002
        };
        this.config.available_pairs.forEach((pair)=>{
            prices.set(pair.symbol, basePrices[pair.symbol] || 100);
        });
        return prices;
    }
    startMarketDataUpdates() {
        // Update market data every 2 seconds for realistic scalping
        const updateInterval = setInterval(()=>{
            if (!this.isActive) {
                clearInterval(updateInterval);
                return;
            }
            this.simulateScalpingMarketMovement();
        }, 2000);
    }
    simulateScalpingMarketMovement() {
        // Simulate high-frequency price movements typical in scalping
        this.marketData.forEach((price, symbol)=>{
            // Higher volatility and more frequent small movements
            const volatility = this.getScalpingVolatility(symbol);
            const direction = Math.random() > 0.5 ? 1 : -1;
            const change = direction * Math.random() * volatility * price;
            // Add some momentum (trending behavior)
            const momentum = this.calculateMomentum(symbol);
            const newPrice = price + change + momentum;
            this.marketData.set(symbol, Math.max(0.01, newPrice));
        });
    }
    getScalpingVolatility(symbol) {
        // Higher volatility for scalping simulation
        if (this.isCryptoSymbol(symbol)) return 0.008 // 0.8% per update
        ;
        if (this.isStockSymbol(symbol)) return 0.003 // 0.3% per update
        ;
        if (this.isForexSymbol(symbol)) return 0.001 // 0.1% per update
        ;
        return 0.005;
    }
    calculateMomentum(symbol) {
        // Simple momentum calculation based on recent price history
        // In a real implementation, this would use actual price history
        return (Math.random() - 0.5) * 0.001 * (this.marketData.get(symbol) || 0);
    }
    checkAutoCloseConditions() {
        // Auto-close positions that hit stop loss or take profit levels
        this.state.positions.forEach((position)=>{
            const currentPrice = position.current_price;
            const entryPrice = position.entry_price;
            const pnlPercentage = position.pnl / (entryPrice * position.quantity) * 100;
            // Auto-close on 5% loss (stop loss) or 3% gain (take profit) for scalping
            if (pnlPercentage <= -5 || pnlPercentage >= 3) {
                this.closePosition(position.id);
            }
        });
    }
    updateGameMetrics() {
        // Update average hold time
        this.updateAverageHoldTime();
        // Update speed bonus based on quick decision making
        const recentTrades = this.tradeHistory.filter((t)=>Date.now() - t.timestamp < 5000 // Last 5 seconds
        );
        if (recentTrades.length >= 2) {
            this.gameData.speed_bonus += 2 // Bonus for rapid trading
            ;
        }
    }
    updateAverageHoldTime() {
        const completedTrades = this.tradeHistory.filter((t)=>t.hold_time !== undefined);
        if (completedTrades.length > 0) {
            const totalHoldTime = completedTrades.reduce((sum, trade)=>sum + (trade.hold_time || 0), 0);
            this.gameData.average_hold_time = totalHoldTime / completedTrades.length;
        }
    }
}
}}),
"[project]/src/lib/game-engine/games/chain-maze.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChainMazeGame": (()=>ChainMazeGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)");
;
class ChainMazeGame extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseGame"] {
    gameData;
    blockchain = [];
    network = [];
    pendingTransactions = [];
    currentPuzzle = null;
    puzzleStartTime = 0;
    availablePuzzles;
    constructor(difficulty){
        super('chain_maze', difficulty);
        this.gameData = {
            puzzles_solved: 0,
            gas_saved: 0,
            blocks_mined: 0,
            transactions_processed: 0,
            consensus_score: 0,
            efficiency_rating: 100,
            current_puzzle: null,
            network_health: 100,
            total_gas_used: 0
        };
        this.availablePuzzles = this.generatePuzzlesByDifficulty(difficulty);
        this.config.starting_balance = 1000 // Gas budget
        ;
    }
    async initialize() {
        // Initialize blockchain network
        this.initializeNetwork();
        this.generateGenesisBlock();
        // Start first puzzle
        await this.loadNextPuzzle();
        // Start network simulation
        this.startNetworkSimulation();
    }
    update() {
        // Update network state
        this.updateNetworkState();
        // Process pending transactions
        this.processPendingTransactions();
        // Update game metrics
        this.updateGameMetrics();
    }
    calculateScore() {
        let score = 0;
        // Base score from puzzles solved
        score += this.gameData.puzzles_solved * 200;
        // Gas efficiency bonus
        const gasEfficiency = Math.max(0, 1000 - this.gameData.total_gas_used);
        score += gasEfficiency;
        // Consensus participation bonus
        score += this.gameData.consensus_score * 10;
        // Network health bonus
        score += this.gameData.network_health * 2;
        // Efficiency rating bonus
        score += this.gameData.efficiency_rating * 5;
        // Difficulty multiplier
        score *= this.state.multiplier;
        return Math.round(Math.max(0, score));
    }
    getGameSpecificData() {
        return {
            ...this.gameData
        };
    }
    getCurrentPuzzle() {
        return this.currentPuzzle;
    }
    getNetworkState() {
        return {
            nodes: this.network,
            blockchain: this.blockchain,
            pendingTransactions: this.pendingTransactions
        };
    }
    async submitPuzzleSolution(solution) {
        if (!this.currentPuzzle || !this.isActive) return false;
        const correct = this.validateSolution(this.currentPuzzle, solution);
        const timeToSolve = Date.now() - this.puzzleStartTime;
        if (correct) {
            this.gameData.puzzles_solved++;
            // Apply puzzle effects
            this.applyPuzzleEffects(this.currentPuzzle, solution);
            // Time bonus for quick solutions
            if (timeToSolve < 30000) {
                const timeBonus = Math.max(0, 100 - Math.floor(timeToSolve / 300));
                this.gameData.efficiency_rating += timeBonus;
            }
            // Load next puzzle
            await this.loadNextPuzzle();
        } else {
            // Penalty for wrong solution
            this.gameData.efficiency_rating = Math.max(0, this.gameData.efficiency_rating - 10);
        }
        return correct;
    }
    async executeTransaction(tx) {
        if (this.state.current_balance < tx.gasPrice * tx.gasLimit) {
            return false // Insufficient gas budget
            ;
        }
        // Deduct gas cost
        this.state.current_balance -= tx.gasPrice * tx.gasLimit;
        this.gameData.total_gas_used += tx.gasPrice * tx.gasLimit;
        // Add to pending transactions
        this.pendingTransactions.push({
            ...tx,
            status: 'pending'
        });
        return true;
    }
    async mineBlock() {
        if (this.pendingTransactions.length === 0) return false;
        const block = this.createNewBlock();
        this.blockchain.push(block);
        this.gameData.blocks_mined++;
        // Reward for mining
        const miningReward = 50;
        this.state.current_balance += miningReward;
        // Clear processed transactions
        this.pendingTransactions = this.pendingTransactions.filter((tx)=>!block.transactions.includes(tx));
        return true;
    }
    initializeNetwork() {
        // Create network nodes
        this.network = [
            {
                id: 'validator_1',
                type: 'validator',
                position: {
                    x: 100,
                    y: 100
                },
                connections: [
                    'validator_2',
                    'miner_1'
                ],
                gasPrice: 20,
                congestion: 0.3,
                reward: 10,
                active: true
            },
            {
                id: 'validator_2',
                type: 'validator',
                position: {
                    x: 300,
                    y: 100
                },
                connections: [
                    'validator_1',
                    'miner_2'
                ],
                gasPrice: 25,
                congestion: 0.5,
                reward: 15,
                active: true
            },
            {
                id: 'miner_1',
                type: 'miner',
                position: {
                    x: 200,
                    y: 200
                },
                connections: [
                    'validator_1',
                    'contract_1'
                ],
                gasPrice: 30,
                congestion: 0.7,
                reward: 20,
                active: true
            },
            {
                id: 'miner_2',
                type: 'miner',
                position: {
                    x: 400,
                    y: 200
                },
                connections: [
                    'validator_2',
                    'contract_1'
                ],
                gasPrice: 35,
                congestion: 0.4,
                reward: 25,
                active: true
            },
            {
                id: 'contract_1',
                type: 'contract',
                position: {
                    x: 300,
                    y: 300
                },
                connections: [
                    'miner_1',
                    'miner_2'
                ],
                gasPrice: 40,
                congestion: 0.6,
                reward: 30,
                active: true
            }
        ];
    }
    generateGenesisBlock() {
        this.blockchain = [
            {
                number: 0,
                hash: '0x0000000000000000000000000000000000000000000000000000000000000000',
                transactions: [],
                gasUsed: 0,
                gasLimit: 8000000,
                miner: 'genesis',
                timestamp: Date.now(),
                difficulty: 1
            }
        ];
    }
    async loadNextPuzzle() {
        if (this.availablePuzzles.length === 0) {
            // Generate more puzzles or end game
            this.availablePuzzles = this.generatePuzzlesByDifficulty(this.config.difficulty);
        }
        this.currentPuzzle = this.availablePuzzles.shift() || null;
        this.gameData.current_puzzle = this.currentPuzzle;
        this.puzzleStartTime = Date.now();
    }
    generatePuzzlesByDifficulty(difficulty) {
        const puzzles = [];
        if (difficulty === 'beginner' || difficulty === 'intermediate' || difficulty === 'advanced') {
            puzzles.push({
                id: 'gas_opt_1',
                type: 'gas_optimization',
                title: 'Optimize Transaction Gas',
                description: 'Find the most cost-effective path to execute this transaction',
                difficulty: 'beginner',
                objective: 'Minimize gas cost while ensuring transaction success',
                initialState: {
                    transaction: {
                        value: 100,
                        gasLimit: 21000
                    },
                    availableNodes: [
                        'validator_1',
                        'validator_2'
                    ]
                },
                solution: {
                    selectedNode: 'validator_1',
                    gasPrice: 20
                },
                hints: [
                    'Lower gas price nodes are more cost-effective',
                    'Check node congestion levels'
                ]
            });
        }
        if (difficulty === 'intermediate' || difficulty === 'advanced') {
            puzzles.push({
                id: 'consensus_1',
                type: 'consensus_mechanism',
                title: 'Achieve Network Consensus',
                description: 'Coordinate validators to reach consensus on the next block',
                difficulty: 'intermediate',
                objective: 'Get 2/3 majority agreement from validators',
                initialState: {
                    validators: [
                        'validator_1',
                        'validator_2'
                    ],
                    proposedBlock: {
                        number: 1,
                        transactions: 3
                    }
                },
                solution: {
                    votes: [
                        'validator_1',
                        'validator_2'
                    ],
                    consensus: true
                },
                hints: [
                    'Validators need to agree on block validity',
                    'Majority consensus is required'
                ]
            });
        }
        if (difficulty === 'advanced') {
            puzzles.push({
                id: 'routing_1',
                type: 'network_routing',
                title: 'Optimize Network Routing',
                description: 'Find the optimal path through the network for maximum efficiency',
                difficulty: 'advanced',
                objective: 'Route transactions through the network with minimal latency and cost',
                initialState: {
                    source: 'validator_1',
                    destination: 'contract_1',
                    constraints: {
                        maxGas: 100,
                        maxHops: 3
                    }
                },
                solution: {
                    path: [
                        'validator_1',
                        'miner_1',
                        'contract_1'
                    ],
                    totalCost: 70
                },
                hints: [
                    'Consider both gas cost and network congestion',
                    'Shorter paths are not always optimal'
                ]
            });
        }
        return puzzles;
    }
    validateSolution(puzzle, solution) {
        switch(puzzle.type){
            case 'gas_optimization':
                return this.validateGasOptimization(puzzle, solution);
            case 'consensus_mechanism':
                return this.validateConsensus(puzzle, solution);
            case 'network_routing':
                return this.validateRouting(puzzle, solution);
            default:
                return false;
        }
    }
    validateGasOptimization(puzzle, solution) {
        const expectedSolution = puzzle.solution;
        const selectedNode = this.network.find((n)=>n.id === solution.selectedNode);
        if (!selectedNode) return false;
        // Check if solution is optimal (lowest gas price with acceptable congestion)
        return solution.selectedNode === expectedSolution.selectedNode && solution.gasPrice <= expectedSolution.gasPrice * 1.1 // 10% tolerance
        ;
    }
    validateConsensus(puzzle, solution) {
        const requiredVotes = Math.ceil(puzzle.initialState.validators.length * 2 / 3);
        return solution.votes && solution.votes.length >= requiredVotes && solution.consensus === true;
    }
    validateRouting(puzzle, solution) {
        const expectedSolution = puzzle.solution;
        // Validate path exists and meets constraints
        if (!solution.path || !Array.isArray(solution.path)) return false;
        // Check path validity
        const pathValid = this.validateNetworkPath(solution.path);
        const costAcceptable = solution.totalCost <= expectedSolution.totalCost * 1.2 // 20% tolerance
        ;
        return pathValid && costAcceptable;
    }
    validateNetworkPath(path) {
        for(let i = 0; i < path.length - 1; i++){
            const currentNode = this.network.find((n)=>n.id === path[i]);
            const nextNode = path[i + 1];
            if (!currentNode || !currentNode.connections.includes(nextNode)) {
                return false;
            }
        }
        return true;
    }
    applyPuzzleEffects(puzzle, solution) {
        switch(puzzle.type){
            case 'gas_optimization':
                const gasSaved = 50 - (solution.gasPrice || 30);
                this.gameData.gas_saved += Math.max(0, gasSaved);
                break;
            case 'consensus_mechanism':
                this.gameData.consensus_score += 10;
                this.gameData.network_health += 5;
                break;
            case 'network_routing':
                this.gameData.efficiency_rating += 10;
                break;
        }
    }
    startNetworkSimulation() {
        // Simulate network activity
        const simulationInterval = setInterval(()=>{
            if (!this.isActive) {
                clearInterval(simulationInterval);
                return;
            }
            // Generate random transactions
            if (Math.random() < 0.3) {
                this.generateRandomTransaction();
            }
            // Update node states
            this.updateNodeStates();
        }, 2000);
    }
    generateRandomTransaction() {
        const nodes = this.network.filter((n)=>n.active);
        const fromNode = nodes[Math.floor(Math.random() * nodes.length)];
        const toNode = nodes[Math.floor(Math.random() * nodes.length)];
        if (fromNode.id !== toNode.id) {
            const tx = {
                id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                from: fromNode.id,
                to: toNode.id,
                value: Math.floor(Math.random() * 100) + 1,
                gasLimit: 21000,
                gasPrice: fromNode.gasPrice,
                status: 'pending'
            };
            this.pendingTransactions.push(tx);
        }
    }
    updateNodeStates() {
        this.network.forEach((node)=>{
            // Update congestion based on pending transactions
            const nodeTxCount = this.pendingTransactions.filter((tx)=>tx.from === node.id || tx.to === node.id).length;
            node.congestion = Math.min(1, nodeTxCount / 10);
            // Adjust gas price based on congestion
            const basePrices = {
                validator: 20,
                miner: 30,
                user: 15,
                contract: 40
            };
            node.gasPrice = basePrices[node.type] * (1 + node.congestion);
        });
    }
    processPendingTransactions() {
        // Process a few transactions each update
        const txToProcess = this.pendingTransactions.slice(0, 3);
        txToProcess.forEach((tx)=>{
            if (Math.random() < 0.7) {
                tx.status = 'confirmed';
                this.gameData.transactions_processed++;
            } else {
                tx.status = 'failed';
            }
        });
        // Remove processed transactions
        this.pendingTransactions = this.pendingTransactions.filter((tx)=>!txToProcess.includes(tx));
    }
    createNewBlock() {
        const txToInclude = this.pendingTransactions.slice(0, 5) // Max 5 tx per block
        ;
        const gasUsed = txToInclude.reduce((sum, tx)=>sum + tx.gasLimit * tx.gasPrice, 0);
        return {
            number: this.blockchain.length,
            hash: `0x${Math.random().toString(16).substr(2, 64)}`,
            transactions: txToInclude,
            gasUsed,
            gasLimit: 8000000,
            miner: 'player',
            timestamp: Date.now(),
            difficulty: Math.floor(Math.random() * 1000) + 1000
        };
    }
    updateNetworkState() {
        // Update network health based on various factors
        const avgCongestion = this.network.reduce((sum, node)=>sum + node.congestion, 0) / this.network.length;
        const pendingTxRatio = Math.min(1, this.pendingTransactions.length / 20);
        this.gameData.network_health = Math.max(0, 100 - avgCongestion * 30 - pendingTxRatio * 40);
    }
    updateGameMetrics() {
        // Update efficiency rating based on performance
        const gasEfficiency = Math.max(0, 100 - this.gameData.total_gas_used / 10);
        const consensusEfficiency = Math.min(100, this.gameData.consensus_score * 2);
        this.gameData.efficiency_rating = (gasEfficiency + consensusEfficiency + this.gameData.network_health) / 3;
    }
}
}}),
"[project]/src/lib/game-engine/games/candle-strike.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "CandleStrikeGame": (()=>CandleStrikeGame)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/base-game.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.ts [app-ssr] (ecmascript)");
;
;
class CandleStrikeGame extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$base$2d$game$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseGame"] {
    gameData;
    currentChallenge = null;
    challengeHistory = [];
    challengeStartTime = 0;
    availablePatterns;
    constructor(difficulty){
        super('candle_strike', difficulty);
        this.gameData = {
            patterns_identified: 0,
            correct_identifications: 0,
            wrong_identifications: 0,
            current_pattern: null,
            patterns_completed: [],
            accuracy_percentage: 0,
            speed_bonus: 0,
            streak_count: 0,
            max_streak: 0
        };
        this.availablePatterns = this.getPatternsByDifficulty(difficulty);
        this.config.available_pairs = [
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TRADING_PAIRS"][0]
        ] // Use BTC for pattern recognition
        ;
    }
    async initialize() {
        // Generate first challenge
        await this.generateNewChallenge();
    }
    update() {
        // Update game metrics
        this.updateGameMetrics();
    }
    calculateScore() {
        const baseScore = this.gameData.correct_identifications * 100;
        const accuracyBonus = this.gameData.accuracy_percentage * 2;
        const speedBonus = this.gameData.speed_bonus;
        const streakBonus = this.gameData.max_streak * 50;
        let totalScore = baseScore + accuracyBonus + speedBonus + streakBonus;
        // Difficulty multiplier
        totalScore *= this.state.multiplier;
        return Math.round(Math.max(0, totalScore));
    }
    getGameSpecificData() {
        return {
            ...this.gameData
        };
    }
    getCurrentChallenge() {
        return this.currentChallenge;
    }
    async submitAnswer(answerIndex) {
        if (!this.currentChallenge || !this.isActive) return false;
        const timeToAnswer = Date.now() - this.challengeStartTime;
        const correct = answerIndex === this.currentChallenge.correctAnswer;
        // Record the attempt
        this.challengeHistory.push({
            challenge: this.currentChallenge,
            userAnswer: answerIndex,
            correct,
            timeToAnswer,
            timestamp: Date.now()
        });
        // Update game data
        this.gameData.patterns_identified++;
        if (correct) {
            this.gameData.correct_identifications++;
            this.gameData.streak_count++;
            this.gameData.max_streak = Math.max(this.gameData.max_streak, this.gameData.streak_count);
            // Speed bonus for quick correct answers (under 10 seconds)
            if (timeToAnswer < 10000) {
                const speedBonus = Math.max(0, 50 - Math.floor(timeToAnswer / 200));
                this.gameData.speed_bonus += speedBonus;
            }
            // Add pattern to completed list if not already there
            if (!this.gameData.patterns_completed.find((p)=>p.id === this.currentChallenge.pattern.id)) {
                this.gameData.patterns_completed.push(this.currentChallenge.pattern);
            }
        } else {
            this.gameData.wrong_identifications++;
            this.gameData.streak_count = 0;
        }
        // Update accuracy
        this.gameData.accuracy_percentage = this.gameData.correct_identifications / this.gameData.patterns_identified * 100;
        // Generate next challenge if game is still active
        if (this.isActive && this.state.time_remaining > 0) {
            await this.generateNewChallenge();
        }
        return correct;
    }
    async generateNewChallenge() {
        // Select a random pattern based on difficulty and progress
        const pattern = this.selectNextPattern();
        // Generate candlestick data with the pattern
        const candleData = this.generateCandlestickDataWithPattern(pattern);
        // Find where the pattern occurs in the data
        const patternLocation = this.findPatternInData(candleData, pattern);
        // Generate multiple choice options
        const options = this.generatePatternOptions(pattern);
        this.currentChallenge = {
            pattern,
            candleData,
            patternStartIndex: patternLocation.start,
            patternEndIndex: patternLocation.end,
            options,
            correctAnswer: 0
        };
        // Shuffle options and update correct answer index
        this.shuffleOptions();
        this.gameData.current_pattern = pattern;
        this.challengeStartTime = Date.now();
    }
    selectNextPattern() {
        // Prioritize patterns not yet completed
        const uncompletedPatterns = this.availablePatterns.filter((p)=>!this.gameData.patterns_completed.find((completed)=>completed.id === p.id));
        const patternsToChooseFrom = uncompletedPatterns.length > 0 ? uncompletedPatterns : this.availablePatterns;
        return patternsToChooseFrom[Math.floor(Math.random() * patternsToChooseFrom.length)];
    }
    generateCandlestickDataWithPattern(pattern) {
        const totalCandles = 50;
        const patternPosition = Math.floor(Math.random() * (totalCandles - pattern.maxCandles - 10)) + 10;
        const data = [];
        let currentPrice = 100 + Math.random() * 50;
        // Generate candles before pattern
        for(let i = 0; i < patternPosition; i++){
            const candle = this.generateRandomCandle(currentPrice, i);
            data.push(candle);
            currentPrice = candle.close;
        }
        // Generate pattern candles
        const patternCandles = this.generatePatternCandles(pattern, currentPrice, patternPosition);
        data.push(...patternCandles);
        currentPrice = patternCandles[patternCandles.length - 1].close;
        // Generate candles after pattern
        for(let i = patternPosition + patternCandles.length; i < totalCandles; i++){
            const candle = this.generateRandomCandle(currentPrice, i);
            data.push(candle);
            currentPrice = candle.close;
        }
        return data;
    }
    generateRandomCandle(basePrice, index) {
        const volatility = 0.02;
        const change = (Math.random() - 0.5) * volatility * basePrice;
        const open = basePrice;
        const close = basePrice + change;
        const high = Math.max(open, close) + Math.random() * 0.01 * basePrice;
        const low = Math.min(open, close) - Math.random() * 0.01 * basePrice;
        return {
            timestamp: Date.now() - (50 - index) * 3600000,
            open,
            high,
            low,
            close,
            volume: Math.random() * 1000000
        };
    }
    generatePatternCandles(pattern, startPrice, startIndex) {
        // This is a simplified pattern generation - in a real implementation,
        // you'd have specific algorithms for each pattern type
        const candles = [];
        let currentPrice = startPrice;
        switch(pattern.id){
            case 'hammer':
                return this.generateHammerPattern(startPrice, startIndex);
            case 'doji':
                return this.generateDojiPattern(startPrice, startIndex);
            case 'engulfing_bullish':
                return this.generateEngulfingPattern(startPrice, startIndex, true);
            case 'engulfing_bearish':
                return this.generateEngulfingPattern(startPrice, startIndex, false);
            case 'morning_star':
                return this.generateMorningStarPattern(startPrice, startIndex);
            case 'evening_star':
                return this.generateEveningStarPattern(startPrice, startIndex);
            default:
                return this.generateHammerPattern(startPrice, startIndex);
        }
    }
    generateHammerPattern(startPrice, startIndex) {
        const open = startPrice;
        const close = startPrice + Math.random() * 0.01 * startPrice // Small body
        ;
        const high = Math.max(open, close) + Math.random() * 0.005 * startPrice // Small upper shadow
        ;
        const low = Math.min(open, close) - (0.02 + Math.random() * 0.01) * startPrice // Long lower shadow
        ;
        return [
            {
                timestamp: Date.now() - (50 - startIndex) * 3600000,
                open,
                high,
                low,
                close,
                volume: Math.random() * 1000000
            }
        ];
    }
    generateDojiPattern(startPrice, startIndex) {
        const open = startPrice;
        const close = startPrice + (Math.random() - 0.5) * 0.002 * startPrice // Very small body
        ;
        const high = Math.max(open, close) + (0.01 + Math.random() * 0.01) * startPrice;
        const low = Math.min(open, close) - (0.01 + Math.random() * 0.01) * startPrice;
        return [
            {
                timestamp: Date.now() - (50 - startIndex) * 3600000,
                open,
                high,
                low,
                close,
                volume: Math.random() * 1000000
            }
        ];
    }
    generateEngulfingPattern(startPrice, startIndex, bullish) {
        const candles = [];
        // First candle (small)
        const firstOpen = startPrice;
        const firstClose = bullish ? startPrice - 0.01 * startPrice : startPrice + 0.01 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex) * 3600000,
            open: firstOpen,
            high: Math.max(firstOpen, firstClose) + 0.002 * startPrice,
            low: Math.min(firstOpen, firstClose) - 0.002 * startPrice,
            close: firstClose,
            volume: Math.random() * 1000000
        });
        // Second candle (engulfing)
        const secondOpen = bullish ? firstClose - 0.005 * startPrice : firstClose + 0.005 * startPrice;
        const secondClose = bullish ? firstOpen + 0.015 * startPrice : firstOpen - 0.015 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
            open: secondOpen,
            high: Math.max(secondOpen, secondClose) + 0.002 * startPrice,
            low: Math.min(secondOpen, secondClose) - 0.002 * startPrice,
            close: secondClose,
            volume: Math.random() * 1000000
        });
        return candles;
    }
    generateMorningStarPattern(startPrice, startIndex) {
        const candles = [];
        // First candle (bearish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex) * 3600000,
            open: startPrice,
            high: startPrice + 0.002 * startPrice,
            low: startPrice - 0.015 * startPrice,
            close: startPrice - 0.012 * startPrice,
            volume: Math.random() * 1000000
        });
        // Second candle (small body/doji)
        const secondPrice = startPrice - 0.015 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
            open: secondPrice,
            high: secondPrice + 0.005 * startPrice,
            low: secondPrice - 0.005 * startPrice,
            close: secondPrice + 0.001 * startPrice,
            volume: Math.random() * 1000000
        });
        // Third candle (bullish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 2) * 3600000,
            open: secondPrice + 0.002 * startPrice,
            high: startPrice - 0.002 * startPrice,
            low: secondPrice,
            close: startPrice - 0.003 * startPrice,
            volume: Math.random() * 1000000
        });
        return candles;
    }
    generateEveningStarPattern(startPrice, startIndex) {
        // Similar to morning star but inverted
        const candles = [];
        // First candle (bullish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex) * 3600000,
            open: startPrice,
            high: startPrice + 0.015 * startPrice,
            low: startPrice - 0.002 * startPrice,
            close: startPrice + 0.012 * startPrice,
            volume: Math.random() * 1000000
        });
        // Second candle (small body/doji)
        const secondPrice = startPrice + 0.015 * startPrice;
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 1) * 3600000,
            open: secondPrice,
            high: secondPrice + 0.005 * startPrice,
            low: secondPrice - 0.005 * startPrice,
            close: secondPrice - 0.001 * startPrice,
            volume: Math.random() * 1000000
        });
        // Third candle (bearish)
        candles.push({
            timestamp: Date.now() - (50 - startIndex - 2) * 3600000,
            open: secondPrice - 0.002 * startPrice,
            high: secondPrice,
            low: startPrice + 0.002 * startPrice,
            close: startPrice + 0.003 * startPrice,
            volume: Math.random() * 1000000
        });
        return candles;
    }
    findPatternInData(data, pattern) {
        // For simplicity, we know where we placed the pattern
        // In a real implementation, you'd search for the pattern in the data
        const totalCandles = data.length;
        const patternPosition = Math.floor(totalCandles * 0.4) // Roughly where we placed it
        ;
        return {
            start: patternPosition,
            end: patternPosition + pattern.maxCandles - 1
        };
    }
    generatePatternOptions(correctPattern) {
        const allPatterns = this.getAllPatterns();
        const wrongPatterns = allPatterns.filter((p)=>p.id !== correctPattern.id).sort(()=>Math.random() - 0.5).slice(0, 3);
        return [
            correctPattern.name,
            ...wrongPatterns.map((p)=>p.name)
        ];
    }
    shuffleOptions() {
        if (!this.currentChallenge) return;
        const options = [
            ...this.currentChallenge.options
        ];
        const correctAnswer = options[0];
        // Fisher-Yates shuffle
        for(let i = options.length - 1; i > 0; i--){
            const j = Math.floor(Math.random() * (i + 1));
            [options[i], options[j]] = [
                options[j],
                options[i]
            ];
        }
        this.currentChallenge.options = options;
        this.currentChallenge.correctAnswer = options.indexOf(correctAnswer);
    }
    updateGameMetrics() {
        // Update accuracy percentage
        if (this.gameData.patterns_identified > 0) {
            this.gameData.accuracy_percentage = this.gameData.correct_identifications / this.gameData.patterns_identified * 100;
        }
    }
    getPatternsByDifficulty(difficulty) {
        const allPatterns = this.getAllPatterns();
        return allPatterns.filter((p)=>p.difficulty === difficulty || difficulty === 'advanced' && p.difficulty !== 'advanced');
    }
    getAllPatterns() {
        return [
            {
                id: 'hammer',
                name: 'Hammer',
                description: 'Bullish reversal pattern with long lower shadow',
                bullish: true,
                difficulty: 'beginner',
                minCandles: 1,
                maxCandles: 1
            },
            {
                id: 'doji',
                name: 'Doji',
                description: 'Indecision pattern with very small body',
                bullish: false,
                difficulty: 'beginner',
                minCandles: 1,
                maxCandles: 1
            },
            {
                id: 'engulfing_bullish',
                name: 'Bullish Engulfing',
                description: 'Two-candle bullish reversal pattern',
                bullish: true,
                difficulty: 'intermediate',
                minCandles: 2,
                maxCandles: 2
            },
            {
                id: 'engulfing_bearish',
                name: 'Bearish Engulfing',
                description: 'Two-candle bearish reversal pattern',
                bullish: false,
                difficulty: 'intermediate',
                minCandles: 2,
                maxCandles: 2
            },
            {
                id: 'morning_star',
                name: 'Morning Star',
                description: 'Three-candle bullish reversal pattern',
                bullish: true,
                difficulty: 'advanced',
                minCandles: 3,
                maxCandles: 3
            },
            {
                id: 'evening_star',
                name: 'Evening Star',
                description: 'Three-candle bearish reversal pattern',
                bullish: false,
                difficulty: 'advanced',
                minCandles: 3,
                maxCandles: 3
            }
        ];
    }
}
}}),
"[project]/src/components/charts/candlestick-chart.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChartSkeleton": (()=>ChartSkeleton),
    "PatternAnnotation": (()=>PatternAnnotation),
    "default": (()=>CandlestickChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lightweight-charts/dist/lightweight-charts.development.mjs [app-ssr] (ecmascript)");
'use client';
;
;
;
function CandlestickChart({ data, width = 800, height = 400, theme = 'dark', patternHighlight, onPatternClick, showVolume = true, title, className = '' }) {
    const chartContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const chartRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const candlestickSeriesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const volumeSeriesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!chartContainerRef.current || data.length === 0) return;
        // Create chart
        const chart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createChart"])(chartContainerRef.current, {
            width,
            height,
            layout: {
                background: {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lightweight$2d$charts$2f$dist$2f$lightweight$2d$charts$2e$development$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ColorType"].Solid,
                    color: theme === 'dark' ? '#1a1a1a' : '#ffffff'
                },
                textColor: theme === 'dark' ? '#d1d5db' : '#374151'
            },
            grid: {
                vertLines: {
                    color: theme === 'dark' ? '#374151' : '#e5e7eb'
                },
                horzLines: {
                    color: theme === 'dark' ? '#374151' : '#e5e7eb'
                }
            },
            crosshair: {
                mode: 1
            },
            rightPriceScale: {
                borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'
            },
            timeScale: {
                borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',
                timeVisible: true,
                secondsVisible: false
            }
        });
        chartRef.current = chart;
        // Add candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#10b981',
            downColor: '#ef4444',
            borderDownColor: '#ef4444',
            borderUpColor: '#10b981',
            wickDownColor: '#ef4444',
            wickUpColor: '#10b981'
        });
        candlestickSeriesRef.current = candlestickSeries;
        // Add volume series if enabled
        if (showVolume) {
            const volumeSeries = chart.addHistogramSeries({
                color: theme === 'dark' ? '#6b7280' : '#9ca3af',
                priceFormat: {
                    type: 'volume'
                },
                priceScaleId: '',
                scaleMargins: {
                    top: 0.7,
                    bottom: 0
                }
            });
            volumeSeriesRef.current = volumeSeries;
        }
        // Convert data format
        const chartData = data.map((candle)=>({
                time: Math.floor(candle.timestamp / 1000),
                open: candle.open,
                high: candle.high,
                low: candle.low,
                close: candle.close
            }));
        const volumeData = data.map((candle)=>({
                time: Math.floor(candle.timestamp / 1000),
                value: candle.volume,
                color: candle.close >= candle.open ? '#10b98150' : '#ef444450'
            }));
        // Set data
        candlestickSeries.setData(chartData);
        if (showVolume && volumeSeriesRef.current) {
            volumeSeriesRef.current.setData(volumeData);
        }
        // Fit content
        chart.timeScale().fitContent();
        setIsLoading(false);
        // Cleanup
        return ()=>{
            chart.remove();
        };
    }, [
        data,
        width,
        height,
        theme,
        showVolume
    ]);
    // Handle pattern highlighting
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!chartRef.current || !candlestickSeriesRef.current || !patternHighlight) return;
        // Add pattern highlight markers
        const markers = [];
        // Start marker
        markers.push({
            time: Math.floor(data[patternHighlight.startIndex]?.timestamp / 1000),
            position: 'belowBar',
            color: patternHighlight.color,
            shape: 'arrowUp',
            text: 'Pattern Start'
        });
        // End marker
        markers.push({
            time: Math.floor(data[patternHighlight.endIndex]?.timestamp / 1000),
            position: 'belowBar',
            color: patternHighlight.color,
            shape: 'arrowUp',
            text: 'Pattern End'
        });
        candlestickSeriesRef.current.setMarkers(markers);
    }, [
        patternHighlight,
        data
    ]);
    // Handle click events
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!chartRef.current || !onPatternClick) return;
        const handleClick = (param)=>{
            if (param.time) {
                const clickedIndex = data.findIndex((candle)=>Math.floor(candle.timestamp / 1000) === param.time);
                if (clickedIndex !== -1) {
                    // For simplicity, assume pattern is 3 candles around clicked point
                    const startIndex = Math.max(0, clickedIndex - 1);
                    const endIndex = Math.min(data.length - 1, clickedIndex + 1);
                    onPatternClick(startIndex, endIndex);
                }
            }
        };
        chartRef.current.subscribeClick(handleClick);
        return ()=>{
            if (chartRef.current) {
                chartRef.current.unsubscribeClick(handleClick);
            }
        };
    }, [
        data,
        onPatternClick
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `relative ${className}`,
        children: [
            title && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center mb-2 font-bold ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`,
                children: title
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 188,
                columnNumber: 9
            }, this),
            isLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 flex items-center justify-center bg-black/20 rounded",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"
                }, void 0, false, {
                    fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                    lineNumber: 197,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 196,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: chartContainerRef,
                className: "rounded border",
                style: {
                    width: `${width}px`,
                    height: `${height}px`,
                    borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'
                }
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 201,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `mt-2 flex justify-between text-xs ${theme === 'dark' ? 'text-gray-400' : 'text-gray-600'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: "📊 Candlestick Chart"
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 215,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        children: [
                            data.length,
                            " candles"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 216,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
        lineNumber: 186,
        columnNumber: 5
    }, this);
}
function PatternAnnotation({ pattern, theme = 'dark' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `p-3 rounded-lg border ${theme === 'dark' ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-2 mb-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: `text-lg ${pattern.bullish ? 'text-green-400' : 'text-red-400'}`,
                        children: pattern.bullish ? '📈' : '📉'
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 237,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-bold",
                        children: pattern.name
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 236,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: `text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`,
                children: pattern.description
            }, void 0, false, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 242,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
        lineNumber: 231,
        columnNumber: 5
    }, this);
}
function ChartSkeleton({ width = 800, height = 400, theme = 'dark' }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `animate-pulse rounded border ${theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-gray-200 border-gray-300'}`,
        style: {
            width: `${width}px`,
            height: `${height}px`
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center h-full",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "animate-spin rounded-full h-8 w-8 border-b-2 border-current mx-auto mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 268,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        children: "Loading chart data..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                        lineNumber: 269,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/charts/candlestick-chart.tsx",
                lineNumber: 267,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/charts/candlestick-chart.tsx",
            lineNumber: 266,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/charts/candlestick-chart.tsx",
        lineNumber: 260,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/games/candle-strike-game.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CandleStrikeGameComponent)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$candle$2d$strike$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/games/candle-strike.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/charts/candlestick-chart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
function CandleStrikeGameComponent({ difficulty, onGameEnd, className = '' }) {
    const { interfaceMode } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserStore"])();
    const [game, setGame] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentChallenge, setCurrentChallenge] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameData, setGameData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [selectedAnswer, setSelectedAnswer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showResult, setShowResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastAnswerCorrect, setLastAnswerCorrect] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const isAdolescentMode = interfaceMode === 'adolescent';
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        initializeGame();
    }, [
        difficulty
    ]);
    const initializeGame = async ()=>{
        setIsLoading(true);
        const newGame = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$candle$2d$strike$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CandleStrikeGame"](difficulty);
        setGame(newGame);
        await newGame.start();
        // Update game state every second
        const interval = setInterval(()=>{
            if (newGame.isGameActive()) {
                setGameState(newGame.getState());
                setCurrentChallenge(newGame.getCurrentChallenge());
                setGameData(newGame.getGameSpecificData());
            } else {
                clearInterval(interval);
                const finalScore = newGame.calculateScore();
                onGameEnd(finalScore);
            }
        }, 1000);
        setIsLoading(false);
    };
    const handleAnswerSubmit = async (answerIndex)=>{
        if (!game || selectedAnswer !== null || showResult) return;
        setSelectedAnswer(answerIndex);
        const correct = await game.submitAnswer(answerIndex);
        setLastAnswerCorrect(correct);
        setShowResult(true);
        // Update game state
        setGameState(game.getState());
        setGameData(game.getGameSpecificData());
        // Auto-advance to next challenge after 2 seconds
        setTimeout(()=>{
            setSelectedAnswer(null);
            setShowResult(false);
            setLastAnswerCorrect(null);
            setCurrentChallenge(game.getCurrentChallenge());
        }, 2000);
    };
    const getPatternHighlight = ()=>{
        if (!currentChallenge) return undefined;
        return {
            startIndex: currentChallenge.patternStartIndex,
            endIndex: currentChallenge.patternEndIndex,
            color: isAdolescentMode ? '#fbbf24' : '#10b981'
        };
    };
    const getAnswerButtonStyle = (index)=>{
        const baseStyle = `p-3 rounded-lg font-bold transition-all duration-300 ${isAdolescentMode ? 'text-white border-2' : 'text-gray-900 border-2'}`;
        if (showResult && selectedAnswer !== null) {
            if (index === currentChallenge?.correctAnswer) {
                // Correct answer
                return `${baseStyle} ${isAdolescentMode ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50' : 'bg-green-400 border-green-500 shadow-lg'}`;
            } else if (index === selectedAnswer) {
                // Wrong selected answer
                return `${baseStyle} ${isAdolescentMode ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50' : 'bg-red-400 border-red-500 shadow-lg'}`;
            } else {
                // Other options
                return `${baseStyle} ${isAdolescentMode ? 'bg-gray-600 border-gray-500 opacity-50' : 'bg-gray-300 border-gray-400 opacity-50'}`;
            }
        } else {
            // Normal state
            return `${baseStyle} ${isAdolescentMode ? 'bg-purple-500 hover:bg-purple-600 border-purple-400 hover:shadow-lg hover:shadow-purple-500/30' : 'bg-purple-400 hover:bg-purple-300 border-purple-500 hover:shadow-lg'}`;
        }
    };
    if (isLoading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${className} space-y-6`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-xl font-bold mb-2",
                        children: isAdolescentMode ? '🕯️ Loading CandleStrike...' : '📊 INITIALIZING_PATTERN_RECOGNITION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 134,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 133,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChartSkeleton"], {
                    width: 800,
                    height: 400,
                    theme: isAdolescentMode ? 'dark' : 'dark'
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 138,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/games/candle-strike-game.tsx",
            lineNumber: 132,
            columnNumber: 7
        }, this);
    }
    if (!game || !gameState || !currentChallenge) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `${className} text-center`,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: isAdolescentMode ? 'text-white' : 'text-green-400',
                children: isAdolescentMode ? '🎮 Game not ready...' : 'SYSTEM_NOT_READY'
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 150,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/games/candle-strike-game.tsx",
            lineNumber: 149,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${className} space-y-6`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-xl font-bold mb-2",
                        children: isAdolescentMode ? '🕯️ CandleStrike Challenge' : '📊 PATTERN_RECOGNITION_MODULE'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: `text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                        children: isAdolescentMode ? 'Identify the candlestick pattern in the highlighted area!' : 'IDENTIFY_CANDLESTICK_PATTERN_IN_HIGHLIGHTED_REGION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 164,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `grid grid-cols-4 gap-4 p-4 rounded-lg ${isAdolescentMode ? 'bg-white/10' : 'bg-gray-800 border border-green-400'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Score' : 'SCORE'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 177,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                children: gameState.score
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 180,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 176,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Accuracy' : 'ACCURACY'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 185,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: [
                                    gameData?.accuracy_percentage?.toFixed(1) || 0,
                                    "%"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 188,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 184,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Streak' : 'STREAK'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 193,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-orange-300' : 'text-orange-400'}`,
                                children: gameData?.streak_count || 0
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 196,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 192,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'
                            }, void 0, false, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 201,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`,
                                children: [
                                    gameState.time_remaining,
                                    "s"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                                lineNumber: 204,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 200,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 173,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    data: currentChallenge.candleData,
                    width: 800,
                    height: 400,
                    theme: "dark",
                    patternHighlight: getPatternHighlight(),
                    title: isAdolescentMode ? '📈 Trading Chart' : '📊 MARKET_DATA_VISUALIZATION',
                    className: "border rounded-lg"
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 212,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 211,
                columnNumber: 7
            }, this),
            currentChallenge.pattern && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$charts$2f$candlestick$2d$chart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PatternAnnotation"], {
                    pattern: {
                        name: "Pattern to Identify",
                        description: isAdolescentMode ? "Look at the highlighted candles and identify the pattern!" : "ANALYZE_HIGHLIGHTED_CANDLESTICKS_AND_IDENTIFY_PATTERN",
                        bullish: true
                    },
                    theme: "dark"
                }, void 0, false, {
                    fileName: "[project]/src/components/games/candle-strike-game.tsx",
                    lineNumber: 226,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 225,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-2 gap-4",
                children: currentChallenge.options.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>handleAnswerSubmit(index),
                        disabled: selectedAnswer !== null || showResult,
                        className: getAnswerButtonStyle(index),
                        children: option
                    }, index, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 242,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 240,
                columnNumber: 7
            }, this),
            showResult && lastAnswerCorrect !== null && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center p-4 rounded-lg ${lastAnswerCorrect ? isAdolescentMode ? 'bg-green-500/20 border border-green-400 text-green-100' : 'bg-green-900/50 border border-green-400 text-green-300' : isAdolescentMode ? 'bg-red-500/20 border border-red-400 text-red-100' : 'bg-red-900/50 border border-red-400 text-red-300'}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-2xl mb-2",
                        children: lastAnswerCorrect ? isAdolescentMode ? '🎉' : '✅' : isAdolescentMode ? '😅' : '❌'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 266,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "font-bold",
                        children: lastAnswerCorrect ? isAdolescentMode ? 'Excellent! Correct pattern identified!' : 'CORRECT_PATTERN_IDENTIFICATION' : isAdolescentMode ? 'Not quite! The correct answer was highlighted.' : 'INCORRECT_PATTERN_IDENTIFICATION'
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 272,
                        columnNumber: 11
                    }, this),
                    !lastAnswerCorrect && currentChallenge.pattern && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm mt-2",
                        children: isAdolescentMode ? `The correct pattern was: ${currentChallenge.options[currentChallenge.correctAnswer]}` : `CORRECT_PATTERN: ${currentChallenge.options[currentChallenge.correctAnswer]}`
                    }, void 0, false, {
                        fileName: "[project]/src/components/games/candle-strike-game.tsx",
                        lineNumber: 279,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 255,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `text-center text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                children: isAdolescentMode ? `🎯 Patterns Identified: ${gameData?.patterns_identified || 0} | Correct: ${gameData?.correct_identifications || 0}` : `PATTERNS_IDENTIFIED: ${gameData?.patterns_identified || 0} | CORRECT: ${gameData?.correct_identifications || 0}`
            }, void 0, false, {
                fileName: "[project]/src/components/games/candle-strike-game.tsx",
                lineNumber: 290,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/games/candle-strike-game.tsx",
        lineNumber: 158,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Home)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/stores/user-store.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$scalper$2d$sprint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/games/scalper-sprint.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$chain$2d$maze$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/game-engine/games/chain-maze.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$games$2f$candle$2d$strike$2d$game$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/games/candle-strike-game.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
function Home() {
    const { user, isAuthenticated, interfaceMode, switchInterfaceMode, signOut } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$stores$2f$user$2d$store$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useUserStore"])();
    const [currentGame, setCurrentGame] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [gameType, setGameType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showCandleStrikeComponent, setShowCandleStrikeComponent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const startGame = async (type)=>{
        if (type === 'candle') {
            setShowCandleStrikeComponent(true);
            setGameType(type);
            return;
        }
        let game;
        switch(type){
            case 'scalper':
                game = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$scalper$2d$sprint$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ScalperSprintGame"]('beginner');
                break;
            case 'chain':
                game = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$game$2d$engine$2f$games$2f$chain$2d$maze$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChainMazeGame"]('beginner');
                break;
            default:
                return;
        }
        setCurrentGame(game);
        setGameType(type);
        await game.start();
        // Update game state every second
        const interval = setInterval(()=>{
            if (game.isGameActive()) {
                setGameState(game.getState());
            } else {
                clearInterval(interval);
                setCurrentGame(null);
                setGameState(null);
                setGameType(null);
            }
        }, 1000);
    };
    const handleCandleStrikeEnd = (score)=>{
        setShowCandleStrikeComponent(false);
        setGameType(null);
        // Could add score handling here
        console.log('CandleStrike game ended with score:', score);
    };
    const executeTrade = async (symbol, side)=>{
        if (currentGame && gameType === 'scalper') {
            await currentGame.executeTrade(symbol, side, 1);
            setGameState(currentGame.getState());
        }
    };
    const submitCandleAnswer = async (answerIndex)=>{
        if (currentGame && gameType === 'candle') {
            const correct = await currentGame.submitAnswer(answerIndex);
            setGameState(currentGame.getState());
            return correct;
        }
        return false;
    };
    const submitChainSolution = async (solution)=>{
        if (currentGame && gameType === 'chain') {
            const correct = await currentGame.submitPuzzleSolution(solution);
            setGameState(currentGame.getState());
            return correct;
        }
        return false;
    };
    const isAdolescentMode = interfaceMode === 'adolescent';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `min-h-screen ${isAdolescentMode ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' : 'bg-gray-900 text-green-400 font-mono'}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: `p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto flex justify-between items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: `text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                            children: isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 97,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-4",
                            children: [
                                isAuthenticated && user && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                            children: [
                                                isAdolescentMode ? `👋 ${user.username}` : `USER: ${user.username.toUpperCase()}`,
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `text-xs ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                    children: [
                                                        isAdolescentMode ? `⭐ Level ${user.level}` : `LVL_${user.level}`,
                                                        " |",
                                                        isAdolescentMode ? ` 🪙 ${user.total_quest_coins}` : ` COINS_${user.total_quest_coins}`
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 106,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 104,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent'),
                                            className: `px-3 py-1 text-sm rounded transition-colors ${isAdolescentMode ? 'bg-white/20 hover:bg-white/30 text-white' : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'}`,
                                            children: isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 112,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: signOut,
                                            className: `px-3 py-1 text-sm rounded transition-colors ${isAdolescentMode ? 'bg-red-500/20 hover:bg-red-500/30 text-red-200 border border-red-400' : 'bg-red-400/20 hover:bg-red-400/30 text-red-400 border border-red-400'}`,
                                            children: isAdolescentMode ? '🚪 Logout' : 'LOGOUT'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 123,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true),
                                !isAuthenticated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "/auth/login",
                                            className: `px-4 py-2 rounded-lg transition-colors ${isAdolescentMode ? 'bg-white/20 hover:bg-white/30 text-white' : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'}`,
                                            children: isAdolescentMode ? '🔑 Login' : 'LOGIN'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 138,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: "/auth/register",
                                            className: `px-4 py-2 rounded-lg transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600' : 'bg-green-400 text-gray-900 hover:bg-green-300'}`,
                                            children: isAdolescentMode ? '🚀 Join Quest' : 'REGISTER'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 148,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 137,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 101,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 96,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 95,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                className: "max-w-7xl mx-auto p-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: `mb-8 p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: `text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 172,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: `text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                children: isAdolescentMode ? 'Embark on epic trading adventures and master the markets through exciting mini-games!' : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 175,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: `mb-8 p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm' : 'bg-gray-800 border border-green-400'}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: `text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                children: isAdolescentMode ? '🎮 Mini-Game Demos' : '📊 Trading Simulation Modules'
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 189,
                                columnNumber: 11
                            }, this),
                            !currentGame && !showCandleStrikeComponent ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-6",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                        children: isAdolescentMode ? 'Choose your adventure! Each game teaches different trading skills.' : 'Select a trading simulation module to begin training.'
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 195,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid md:grid-cols-3 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `p-4 rounded-lg border ${isAdolescentMode ? 'bg-white/5 border-white/20' : 'bg-gray-700 border-green-400/50'}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: `font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: isAdolescentMode ? '⚡ Scalper Sprint' : '📈 SCALPER_MODULE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 209,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: `text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? '60-second rapid trading challenge' : 'High-frequency trading simulation'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 212,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>startGame('scalper'),
                                                        className: `w-full py-2 px-3 rounded text-sm font-bold transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600' : 'bg-green-400 text-gray-900 hover:bg-green-300'}`,
                                                        children: isAdolescentMode ? '🚀 Start' : 'INITIALIZE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 218,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 204,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `p-4 rounded-lg border ${isAdolescentMode ? 'bg-white/5 border-white/20' : 'bg-gray-700 border-green-400/50'}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: `font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: isAdolescentMode ? '🕯️ CandleStrike' : '📊 PATTERN_RECOGNITION'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: `text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Identify candlestick patterns' : 'Technical analysis training'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 239,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "space-y-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                onClick: ()=>startGame('candle'),
                                                                className: `w-full py-2 px-3 rounded text-sm font-bold transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-purple-400 to-pink-500 text-white hover:from-purple-500 hover:to-pink-600' : 'bg-purple-400 text-gray-900 hover:bg-purple-300'}`,
                                                                children: isAdolescentMode ? '🎯 Quick Start' : 'QUICK_START'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 246,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                href: "/demo/candle-strike",
                                                                className: `block w-full py-2 px-3 rounded text-sm font-bold text-center transition-colors ${isAdolescentMode ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30' : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'}`,
                                                                children: isAdolescentMode ? '📊 Full Demo' : 'FULL_DEMO'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 256,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 245,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 231,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `p-4 rounded-lg border ${isAdolescentMode ? 'bg-white/5 border-white/20' : 'bg-gray-700 border-green-400/50'}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: `font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                        children: isAdolescentMode ? '🔗 ChainMaze' : '⛓️ BLOCKCHAIN_SIM'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 275,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: `text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`,
                                                        children: isAdolescentMode ? 'Navigate blockchain puzzles' : 'Consensus mechanism training'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 278,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>startGame('chain'),
                                                        className: `w-full py-2 px-3 rounded text-sm font-bold transition-colors ${isAdolescentMode ? 'bg-gradient-to-r from-blue-400 to-cyan-500 text-white hover:from-blue-500 hover:to-cyan-600' : 'bg-blue-400 text-gray-900 hover:bg-blue-300'}`,
                                                        children: isAdolescentMode ? '🧩 Start' : 'INITIALIZE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 284,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 270,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 202,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 194,
                                columnNumber: 13
                            }, this) : showCandleStrikeComponent ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$games$2f$candle$2d$strike$2d$game$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                difficulty: "beginner",
                                onGameEnd: handleCandleStrikeEnd,
                                className: "w-full"
                            }, void 0, false, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 298,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: [
                                    gameState && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `p-4 rounded ${isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                                children: gameType === 'chain' ? isAdolescentMode ? 'Gas Budget' : 'GAS_BUDGET' : isAdolescentMode ? 'Balance' : 'BALANCE'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 312,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                                children: gameType === 'chain' ? gameState.current_balance : `$${gameState.current_balance.toFixed(2)}`
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 318,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 311,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                                children: isAdolescentMode ? 'Score' : 'SCORE'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 323,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                                children: gameState.score
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 326,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 322,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                                children: isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 331,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`,
                                                                children: [
                                                                    gameState.time_remaining,
                                                                    "s"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 334,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 330,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`,
                                                                children: gameType === 'scalper' ? isAdolescentMode ? 'Positions' : 'POSITIONS' : gameType === 'candle' ? isAdolescentMode ? 'Patterns' : 'PATTERNS' : isAdolescentMode ? 'Puzzles' : 'PUZZLES'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 339,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: `text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                                                children: gameType === 'scalper' ? gameState.positions?.length || 0 : gameType === 'candle' ? currentGame?.getGameSpecificData()?.patterns_identified || 0 : currentGame?.getGameSpecificData()?.puzzles_solved || 0
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 347,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 338,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 310,
                                                columnNumber: 19
                                            }, this),
                                            gameType === 'candle' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `text-center mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm",
                                                        children: isAdolescentMode ? '🎯 Identify the candlestick pattern!' : 'IDENTIFY_PATTERN_OBJECTIVE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 361,
                                                        columnNumber: 23
                                                    }, this),
                                                    currentGame?.getCurrentChallenge() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "font-bold",
                                                            children: currentGame.getCurrentChallenge()?.pattern.description
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 366,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 365,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 360,
                                                columnNumber: 21
                                            }, this),
                                            gameType === 'chain' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `text-center mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm",
                                                        children: isAdolescentMode ? '🧩 Solve blockchain puzzles!' : 'BLOCKCHAIN_PUZZLE_OBJECTIVE'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 376,
                                                        columnNumber: 23
                                                    }, this),
                                                    currentGame?.getCurrentPuzzle() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "mt-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "font-bold",
                                                                children: currentGame.getCurrentPuzzle()?.title
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 381,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-xs",
                                                                children: currentGame.getCurrentPuzzle()?.description
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/page.tsx",
                                                                lineNumber: 384,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 380,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 375,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 307,
                                        columnNumber: 17
                                    }, this),
                                    gameType === 'scalper' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>executeTrade('BTCUSD', 'buy'),
                                                className: `p-4 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-green-400 hover:bg-green-300 text-gray-900'}`,
                                                children: isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 397,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>executeTrade('BTCUSD', 'sell'),
                                                className: `p-4 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-red-500 hover:bg-red-600 text-white' : 'bg-red-400 hover:bg-red-300 text-gray-900'}`,
                                                children: isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 407,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 396,
                                        columnNumber: 17
                                    }, this),
                                    gameType === 'candle' && currentGame?.getCurrentChallenge() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 gap-2",
                                        children: currentGame.getCurrentChallenge()?.options.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                onClick: ()=>submitCandleAnswer(index),
                                                className: `p-3 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-purple-500 hover:bg-purple-600 text-white' : 'bg-purple-400 hover:bg-purple-300 text-gray-900'}`,
                                                children: option
                                            }, index, false, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 423,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 421,
                                        columnNumber: 17
                                    }, this),
                                    gameType === 'chain' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>submitChainSolution({
                                                    selectedNode: 'validator_1',
                                                    gasPrice: 20
                                                }),
                                            className: `px-6 py-3 rounded-lg font-bold transition-colors ${isAdolescentMode ? 'bg-blue-500 hover:bg-blue-600 text-white' : 'bg-blue-400 hover:bg-blue-300 text-gray-900'}`,
                                            children: isAdolescentMode ? '🧩 Submit Solution' : 'SUBMIT_SOLUTION'
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 440,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 439,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 304,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 184,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
                        className: "grid md:grid-cols-3 gap-6",
                        children: [
                            {
                                title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',
                                description: isAdolescentMode ? 'Six exciting games to master different trading skills' : 'Comprehensive trading simulation modules',
                                features: [
                                    'Scalper Sprint',
                                    'CandleStrike',
                                    'ChainMaze'
                                ]
                            },
                            {
                                title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',
                                description: isAdolescentMode ? 'Unlock badges and level up your trading hero' : 'Advanced performance tracking and analytics',
                                features: [
                                    'Progress Tracking',
                                    'Leaderboards',
                                    'Statistics'
                                ]
                            },
                            {
                                title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',
                                description: isAdolescentMode ? 'Join guilds and compete with friends' : 'Professional networking and strategy sharing',
                                features: [
                                    'Team Challenges',
                                    'Social Features',
                                    'Competitions'
                                ]
                            }
                        ].map((feature, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `p-6 rounded-lg ${isAdolescentMode ? 'bg-white/10 backdrop-blur-sm text-white' : 'bg-gray-800 border border-green-400'}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: `text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`,
                                        children: feature.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 489,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: `mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`,
                                        children: feature.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 492,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                                        className: `space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`,
                                        children: feature.features.map((item, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: `mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`,
                                                        children: isAdolescentMode ? '✨' : '▶'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/page.tsx",
                                                        lineNumber: 498,
                                                        columnNumber: 21
                                                    }, this),
                                                    item
                                                ]
                                            }, i, true, {
                                                fileName: "[project]/src/app/page.tsx",
                                                lineNumber: 497,
                                                columnNumber: 19
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/page.tsx",
                                        lineNumber: 495,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/app/page.tsx",
                                lineNumber: 481,
                                columnNumber: 13
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 457,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 165,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 90,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__51b8a95b._.js.map