'use client'

import { useState } from 'react'
import CandleStrikeGameComponent from '@/components/games/candle-strike-game'
import { useUserStore } from '@/lib/stores/user-store'
import Link from 'next/link'

export default function CandleStrikeDemoPage() {
  const { interfaceMode, switchInterfaceMode } = useUserStore()
  const [difficulty, setDifficulty] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner')
  const [gameActive, setGameActive] = useState(false)
  const [lastScore, setLastScore] = useState<number | null>(null)

  const isAdolescentMode = interfaceMode === 'adolescent'

  const handleGameEnd = (score: number) => {
    setGameActive(false)
    setLastScore(score)
  }

  const startNewGame = () => {
    setGameActive(true)
    setLastScore(null)
  }

  return (
    <div className={`min-h-screen ${isAdolescentMode 
      ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' 
      : 'bg-gray-900'
    }`}>
      {/* Header */}
      <header className={`p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`}>
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div>
            <Link 
              href="/"
              className={`text-sm hover:underline ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}
            >
              ← {isAdolescentMode ? 'Back to Quest Hub' : 'RETURN_TO_MAIN'}
            </Link>
            <h1 className={`text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
              {isAdolescentMode ? '🕯️ CandleStrike: Pattern Master' : '📊 CANDLESTICK_PATTERN_RECOGNITION'}
            </h1>
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                isAdolescentMode 
                  ? 'bg-white/20 hover:bg-white/30 text-white' 
                  : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'
              }`}
            >
              {isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'}
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {!gameActive ? (
          <div className="space-y-8">
            {/* Game Introduction */}
            <section className={`p-6 rounded-lg ${
              isAdolescentMode 
                ? 'bg-white/10 backdrop-blur-sm text-white' 
                : 'bg-gray-800 border border-green-400'
            }`}>
              <h2 className={`text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                {isAdolescentMode ? '🎯 Master the Art of Pattern Recognition!' : '📈 TECHNICAL_ANALYSIS_TRAINING_MODULE'}
              </h2>
              <p className={`text-lg mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                {isAdolescentMode 
                  ? 'Learn to identify candlestick patterns using real market data and professional trading charts. Each pattern tells a story about market sentiment and potential price movements!'
                  : 'Advanced pattern recognition training using real historical market data with professional-grade charting tools. Develop skills in technical analysis and market sentiment interpretation.'
                }
              </p>
              
              {/* Features */}
              <div className="grid md:grid-cols-3 gap-4 mb-6">
                {[
                  {
                    icon: isAdolescentMode ? '📊' : '📈',
                    title: isAdolescentMode ? 'Real Market Data' : 'REAL_MARKET_DATA',
                    description: isAdolescentMode 
                      ? 'Practice with actual historical price data from major cryptocurrencies and stocks'
                      : 'Historical price data from major financial instruments'
                  },
                  {
                    icon: isAdolescentMode ? '🎯' : '🔍',
                    title: isAdolescentMode ? 'Pattern Recognition' : 'PATTERN_ANALYSIS',
                    description: isAdolescentMode 
                      ? 'Learn to spot hammer, doji, engulfing, and star patterns like a pro trader'
                      : 'Comprehensive candlestick pattern identification training'
                  },
                  {
                    icon: isAdolescentMode ? '⚡' : '📊',
                    title: isAdolescentMode ? 'Interactive Charts' : 'INTERACTIVE_VISUALIZATION',
                    description: isAdolescentMode 
                      ? 'Professional trading charts with zoom, pan, and pattern highlighting'
                      : 'Professional-grade charting with advanced visualization tools'
                  }
                ].map((feature, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg ${
                      isAdolescentMode 
                        ? 'bg-white/5 border border-white/20' 
                        : 'bg-gray-700 border border-green-400/50'
                    }`}
                  >
                    <div className="text-2xl mb-2">{feature.icon}</div>
                    <h3 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                      {feature.title}
                    </h3>
                    <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>

              {lastScore !== null && (
                <div className={`p-4 rounded-lg mb-6 ${
                  isAdolescentMode 
                    ? 'bg-yellow-500/20 border border-yellow-400 text-yellow-100' 
                    : 'bg-yellow-900/50 border border-yellow-400 text-yellow-300'
                }`}>
                  <h3 className="font-bold mb-2">
                    {isAdolescentMode ? '🏆 Last Game Results' : '📊 PREVIOUS_SESSION_RESULTS'}
                  </h3>
                  <p>
                    {isAdolescentMode ? `Final Score: ${lastScore} points!` : `FINAL_SCORE: ${lastScore}`}
                  </p>
                </div>
              )}
            </section>

            {/* Difficulty Selection */}
            <section className={`p-6 rounded-lg ${
              isAdolescentMode 
                ? 'bg-white/10 backdrop-blur-sm' 
                : 'bg-gray-800 border border-green-400'
            }`}>
              <h3 className={`text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                {isAdolescentMode ? '⚔️ Choose Your Challenge Level' : '🎯 SELECT_DIFFICULTY_LEVEL'}
              </h3>
              
              <div className="grid md:grid-cols-3 gap-4 mb-6">
                {[
                  {
                    level: 'beginner' as const,
                    title: isAdolescentMode ? '🌟 Apprentice Trader' : '📚 BEGINNER',
                    description: isAdolescentMode 
                      ? 'Learn basic patterns: Hammer and Doji'
                      : 'Basic patterns: Hammer, Doji',
                    patterns: ['Hammer', 'Doji']
                  },
                  {
                    level: 'intermediate' as const,
                    title: isAdolescentMode ? '⚡ Skilled Trader' : '📈 INTERMEDIATE',
                    description: isAdolescentMode 
                      ? 'Master engulfing patterns and reversals'
                      : 'Engulfing patterns and reversals',
                    patterns: ['Bullish Engulfing', 'Bearish Engulfing']
                  },
                  {
                    level: 'advanced' as const,
                    title: isAdolescentMode ? '🏆 Master Trader' : '🎯 ADVANCED',
                    description: isAdolescentMode 
                      ? 'Complex three-candle star formations'
                      : 'Complex multi-candle patterns',
                    patterns: ['Morning Star', 'Evening Star']
                  }
                ].map((difficultyOption) => (
                  <button
                    key={difficultyOption.level}
                    onClick={() => setDifficulty(difficultyOption.level)}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      difficulty === difficultyOption.level
                        ? (isAdolescentMode 
                            ? 'bg-purple-500/30 border-purple-400 shadow-lg shadow-purple-500/30'
                            : 'bg-green-400/20 border-green-400 shadow-lg'
                          )
                        : (isAdolescentMode 
                            ? 'bg-white/5 border-white/20 hover:bg-white/10'
                            : 'bg-gray-700 border-gray-600 hover:border-green-400/50'
                          )
                    }`}
                  >
                    <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                      {difficultyOption.title}
                    </h4>
                    <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                      {difficultyOption.description}
                    </p>
                    <div className="flex flex-wrap gap-1">
                      {difficultyOption.patterns.map((pattern, i) => (
                        <span
                          key={i}
                          className={`text-xs px-2 py-1 rounded ${
                            isAdolescentMode 
                              ? 'bg-white/20 text-white' 
                              : 'bg-gray-600 text-green-300'
                          }`}
                        >
                          {pattern}
                        </span>
                      ))}
                    </div>
                  </button>
                ))}
              </div>

              <div className="text-center">
                <button
                  onClick={startNewGame}
                  className={`px-8 py-4 rounded-lg font-bold text-lg transition-colors ${
                    isAdolescentMode
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600 shadow-lg'
                      : 'bg-green-400 text-gray-900 hover:bg-green-300 shadow-lg'
                  }`}
                >
                  {isAdolescentMode ? '🚀 Start Pattern Quest!' : '▶ INITIALIZE_TRAINING_SESSION'}
                </button>
              </div>
            </section>
          </div>
        ) : (
          <CandleStrikeGameComponent
            difficulty={difficulty}
            onGameEnd={handleGameEnd}
            className="w-full"
          />
        )}
      </main>
    </div>
  )
}
