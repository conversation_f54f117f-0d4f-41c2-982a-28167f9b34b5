{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\n// Mock client for development when Supabase is not configured\nconst createMockClient = () => ({\n  auth: {\n    signInWithPassword: async () => ({ data: null, error: { message: 'Demo mode - authentication disabled' } }),\n    signUp: async () => ({ data: null, error: { message: 'Demo mode - registration disabled' } }),\n    signOut: async () => ({ error: null }),\n    getSession: async () => ({ data: { session: null }, error: null }),\n    onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),\n    signInWithOAuth: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),\n    exchangeCodeForSession: async () => ({ data: null, error: { message: 'Demo mode - OAuth disabled' } }),\n  },\n  from: () => ({\n    select: () => ({\n      eq: () => ({\n        single: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n      })\n    }),\n    insert: () => ({\n      select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n    }),\n    update: () => ({\n      eq: () => ({\n        select: async () => ({ data: null, error: { message: 'Demo mode - database disabled' } })\n      })\n    })\n  })\n})\n\nexport function createClient() {\n  // Check if Supabase environment variables are properly configured\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!supabaseUrl || !supabaseKey || supabaseUrl.includes('demo') || supabaseKey.includes('demo')) {\n    console.warn('⚠️  Supabase not configured - running in demo mode. Authentication and database features will be disabled.')\n    return createMockClient() as any\n  }\n\n  try {\n    return createBrowserClient(supabaseUrl, supabaseKey)\n  } catch (error) {\n    console.error('Failed to create Supabase client:', error)\n    console.warn('Falling back to demo mode')\n    return createMockClient() as any\n  }\n}\n"], "names": [], "mappings": ";;;AAgCsB;AAhCtB;AAAA;;AAEA,8DAA8D;AAC9D,MAAM,mBAAmB,IAAM,CAAC;QAC9B,MAAM;YACJ,oBAAoB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAAsC;gBAAE,CAAC;YAC1G,QAAQ,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAAoC;gBAAE,CAAC;YAC5F,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;YACrC,YAAY,UAAY,CAAC;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YACjE,mBAAmB,IAAM,CAAC;oBAAE,MAAM;wBAAE,cAAc;4BAAE,aAAa,KAAO;wBAAE;oBAAE;gBAAE,CAAC;YAC/E,iBAAiB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAA6B;gBAAE,CAAC;YAC9F,wBAAwB,UAAY,CAAC;oBAAE,MAAM;oBAAM,OAAO;wBAAE,SAAS;oBAA6B;gBAAE,CAAC;QACvG;QACA,MAAM,IAAM,CAAC;gBACX,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,UAAY,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAAgC;oCAAE,CAAC;4BAC1F,CAAC;oBACH,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,QAAQ,UAAY,CAAC;gCAAE,MAAM;gCAAM,OAAO;oCAAE,SAAS;gCAAgC;4BAAE,CAAC;oBAC1F,CAAC;gBACD,QAAQ,IAAM,CAAC;wBACb,IAAI,IAAM,CAAC;gCACT,QAAQ,UAAY,CAAC;wCAAE,MAAM;wCAAM,OAAO;4CAAE,SAAS;wCAAgC;oCAAE,CAAC;4BAC1F,CAAC;oBACH,CAAC;YACH,CAAC;IACH,CAAC;AAEM,SAAS;IACd,kEAAkE;IAClE,MAAM;IACN,MAAM;IAEN,IAAI,CAAC,eAAe,CAAC,eAAe,YAAY,QAAQ,CAAC,WAAW,YAAY,QAAQ,CAAC,SAAS;QAChG,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/stores/user-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { UserProfile, Achievement, GameSession, ThemeConfig } from '@/types'\nimport { createClient } from '@/lib/supabase/client'\n\ninterface UserState {\n  // User data\n  user: UserProfile | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  \n  // Theme and UI\n  interfaceMode: 'adolescent' | 'adult'\n  themeConfig: ThemeConfig\n  \n  // Game state\n  currentGameSession: GameSession | null\n  recentSessions: GameSession[]\n  \n  // Actions\n  setUser: (user: UserProfile | null) => void\n  setAuthenticated: (authenticated: boolean) => void\n  setLoading: (loading: boolean) => void\n  switchInterfaceMode: (mode: 'adolescent' | 'adult') => void\n  syncWithThemeStore: () => void\n  updateThemeConfig: (config: Partial<ThemeConfig>) => void\n  addQuestCoins: (amount: number, source: string) => void\n  spendQuestCoins: (amount: number, purpose: string) => boolean\n  addExperience: (points: number) => void\n  unlockAchievement: (achievement: Achievement) => void\n  startGameSession: (session: GameSession) => void\n  endGameSession: (session: GameSession) => void\n  updateUserProfile: (updates: Partial<UserProfile>) => void\n  clearUserData: () => void\n  signOut: () => Promise<void>\n  initializeAuth: () => Promise<void>\n}\n\nconst defaultThemeConfig: ThemeConfig = {\n  mode: 'adolescent',\n  primary_color: '#8B5CF6',\n  secondary_color: '#EC4899',\n  background_style: 'fantasy',\n  font_family: 'fantasy',\n}\n\nexport const useUserStore = create<UserState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      interfaceMode: 'adolescent',\n      themeConfig: defaultThemeConfig,\n      currentGameSession: null,\n      recentSessions: [],\n\n      // User management actions\n      setUser: (user) => {\n        set({ user, isAuthenticated: !!user })\n        \n        // Update interface mode based on user preference or age\n        if (user) {\n          const mode = user.interface_mode || (user.is_minor ? 'adolescent' : 'adult')\n          get().switchInterfaceMode(mode)\n        }\n      },\n\n      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),\n\n      setLoading: (loading) => set({ isLoading: loading }),\n\n      // Theme and UI actions\n      switchInterfaceMode: (mode) => {\n        const newThemeConfig: ThemeConfig = mode === 'adolescent' \n          ? {\n              mode: 'adolescent',\n              primary_color: '#8B5CF6',\n              secondary_color: '#EC4899',\n              background_style: 'fantasy',\n              font_family: 'fantasy',\n            }\n          : {\n              mode: 'adult',\n              primary_color: '#1F2937',\n              secondary_color: '#3B82F6',\n              background_style: 'professional',\n              font_family: 'monospace',\n            }\n\n        set({\n          interfaceMode: mode,\n          themeConfig: newThemeConfig\n        })\n\n        // Sync with theme store\n        get().syncWithThemeStore()\n\n        // Update user preference in database\n        const { user } = get()\n        if (user) {\n          get().updateUserProfile({ interface_mode: mode })\n        }\n      },\n\n      syncWithThemeStore: () => {\n        // Import theme store dynamically to avoid circular dependency\n        import('@/lib/stores/theme-store').then(({ useThemeStore }) => {\n          const { setInterfaceMode } = useThemeStore.getState()\n          setInterfaceMode(get().interfaceMode)\n        }).catch(() => {\n          // Theme store not available, ignore\n        })\n      },\n\n      updateThemeConfig: (config) => {\n        set((state) => ({\n          themeConfig: { ...state.themeConfig, ...config }\n        }))\n      },\n\n      // Quest coins management\n      addQuestCoins: (amount, source) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins + amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Added ${amount} QuestCoins from ${source}`)\n      },\n\n      spendQuestCoins: (amount, purpose) => {\n        const { user } = get()\n        if (!user || user.total_quest_coins < amount) {\n          return false\n        }\n\n        const updatedUser = {\n          ...user,\n          total_quest_coins: user.total_quest_coins - amount\n        }\n\n        set({ user: updatedUser })\n\n        // In a real app, you'd also update the database and create a transaction record\n        console.log(`Spent ${amount} QuestCoins on ${purpose}`)\n        return true\n      },\n\n      // Experience and leveling\n      addExperience: (points) => {\n        const { user } = get()\n        if (!user) return\n\n        const newExperience = user.experience_points + points\n        const newLevel = calculateLevel(newExperience)\n        const leveledUp = newLevel > user.level\n\n        const updatedUser = {\n          ...user,\n          experience_points: newExperience,\n          level: newLevel\n        }\n\n        set({ user: updatedUser })\n\n        if (leveledUp) {\n          console.log(`Level up! Now level ${newLevel}`)\n          // In a real app, you'd trigger level up effects, notifications, etc.\n        }\n      },\n\n      // Achievement system\n      unlockAchievement: (achievement) => {\n        const { user } = get()\n        if (!user) return\n\n        // Check if achievement is already unlocked\n        const alreadyUnlocked = user.achievements.some(a => a.id === achievement.id)\n        if (alreadyUnlocked) return\n\n        const updatedUser = {\n          ...user,\n          achievements: [...user.achievements, { ...achievement, unlocked_at: new Date().toISOString() }]\n        }\n\n        set({ user: updatedUser })\n\n        // Award quest coins for achievement\n        get().addQuestCoins(achievement.points, `Achievement: ${achievement.name}`)\n\n        console.log(`Achievement unlocked: ${achievement.name}`)\n        // In a real app, you'd show a notification, play sound effects, etc.\n      },\n\n      // Game session management\n      startGameSession: (session) => {\n        set({ currentGameSession: session })\n      },\n\n      endGameSession: (session) => {\n        const { recentSessions } = get()\n        \n        // Add to recent sessions (keep last 10)\n        const updatedSessions = [session, ...recentSessions].slice(0, 10)\n        \n        set({ \n          currentGameSession: null,\n          recentSessions: updatedSessions\n        })\n\n        // Award quest coins and experience\n        get().addQuestCoins(session.quest_coins_earned, `Game: ${session.game_type}`)\n        get().addExperience(Math.floor(session.score / 10))\n\n        // Check for achievements\n        checkGameAchievements(session, get())\n      },\n\n      // Profile updates\n      updateUserProfile: (updates) => {\n        const { user } = get()\n        if (!user) return\n\n        const updatedUser = { ...user, ...updates }\n        set({ user: updatedUser })\n\n        // In a real app, you'd sync with the database\n        console.log('User profile updated:', updates)\n      },\n\n      // Cleanup\n      clearUserData: () => {\n        set({\n          user: null,\n          isAuthenticated: false,\n          currentGameSession: null,\n          recentSessions: [],\n          interfaceMode: 'adolescent',\n          themeConfig: defaultThemeConfig,\n        })\n      },\n\n      // Authentication methods\n      signOut: async () => {\n        const supabase = createClient()\n        await supabase.auth.signOut()\n        get().clearUserData()\n      },\n\n      initializeAuth: async () => {\n        try {\n          const supabase = createClient()\n\n          // Get initial session\n          const { data: { session }, error: sessionError } = await supabase.auth.getSession()\n\n          if (sessionError) {\n            console.warn('Auth session error (demo mode):', sessionError.message)\n            // Set demo user for development\n            get().setUser({\n              id: 'demo-user-id',\n              email: '<EMAIL>',\n              username: 'DemoTrader',\n              age: 25,\n              is_minor: false,\n              interface_mode: 'adolescent',\n              avatar_url: null,\n              total_quest_coins: 1000,\n              level: 1,\n              experience_points: 0,\n              achievements: [],\n              guild_id: null,\n              preferred_language: 'en',\n              created_at: new Date().toISOString(),\n              updated_at: new Date().toISOString(),\n            })\n            return\n          }\n\n          if (session?.user) {\n            // Fetch user profile\n            const { data: profile, error: profileError } = await supabase\n              .from('user_profiles')\n              .select('*')\n              .eq('id', session.user.id)\n              .single()\n\n            if (profileError) {\n              console.warn('Profile fetch error (demo mode):', profileError.message)\n              return\n            }\n\n            if (profile) {\n              // Fetch achievements\n              const { data: achievements } = await supabase\n                .from('user_achievements')\n                .select(`\n                  achievement_id,\n                  unlocked_at,\n                  achievements (\n                    id,\n                    name,\n                    description,\n                    icon,\n                    category,\n                    points\n                  )\n                `)\n                .eq('user_id', session.user.id)\n\n              const userAchievements = achievements?.map(ua => ({\n                ...ua.achievements,\n                unlocked_at: ua.unlocked_at\n              })) || []\n\n              get().setUser({\n                id: profile.id,\n                email: session.user.email!,\n                username: profile.username,\n                age: profile.age,\n                is_minor: profile.is_minor,\n                interface_mode: profile.interface_mode,\n                avatar_url: profile.avatar_url,\n                total_quest_coins: profile.total_quest_coins,\n                level: profile.level,\n                experience_points: profile.experience_points,\n                achievements: userAchievements,\n                guild_id: profile.guild_id,\n                preferred_language: profile.preferred_language,\n                created_at: profile.created_at,\n                updated_at: profile.updated_at,\n              })\n            }\n          }\n\n          // Listen for auth changes\n          supabase.auth.onAuthStateChange(async (event, session) => {\n            if (event === 'SIGNED_OUT' || !session) {\n              get().clearUserData()\n            } else if (event === 'SIGNED_IN' && session?.user) {\n              // Refresh user data when signed in\n              get().initializeAuth()\n            }\n          })\n        } catch (error) {\n          console.error('Auth initialization error:', error)\n          console.warn('Running in demo mode without authentication')\n\n          // Set demo user for development\n          get().setUser({\n            id: 'demo-user-id',\n            email: '<EMAIL>',\n            username: 'DemoTrader',\n            age: 25,\n            is_minor: false,\n            interface_mode: 'adolescent',\n            avatar_url: null,\n            total_quest_coins: 1000,\n            level: 1,\n            experience_points: 0,\n            achievements: [],\n            guild_id: null,\n            preferred_language: 'en',\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n        }\n      },\n    }),\n    {\n      name: 'tradequest-user-storage',\n      partialize: (state) => ({\n        user: state.user,\n        interfaceMode: state.interfaceMode,\n        themeConfig: state.themeConfig,\n        recentSessions: state.recentSessions,\n      }),\n    }\n  )\n)\n\n// Helper functions\nfunction calculateLevel(experience: number): number {\n  const LEVEL_THRESHOLDS = [\n    0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n    13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n  ]\n\n  for (let i = LEVEL_THRESHOLDS.length - 1; i >= 0; i--) {\n    if (experience >= LEVEL_THRESHOLDS[i]) {\n      return i + 1\n    }\n  }\n  return 1\n}\n\nfunction checkGameAchievements(session: GameSession, store: any) {\n  const achievements: Achievement[] = []\n\n  // First game achievement\n  if (store.recentSessions.length === 0) {\n    achievements.push({\n      id: 'first_game',\n      name: 'First Steps',\n      description: 'Complete your first trading game',\n      icon: '🎮',\n      category: 'trading',\n      points: 50,\n    })\n  }\n\n  // High score achievements\n  if (session.score >= 1000) {\n    achievements.push({\n      id: 'high_score_1000',\n      name: 'Rising Trader',\n      description: 'Score 1000+ points in a single game',\n      icon: '📈',\n      category: 'trading',\n      points: 100,\n    })\n  }\n\n  if (session.score >= 5000) {\n    achievements.push({\n      id: 'high_score_5000',\n      name: 'Expert Trader',\n      description: 'Score 5000+ points in a single game',\n      icon: '🏆',\n      category: 'trading',\n      points: 250,\n    })\n  }\n\n  // Game-specific achievements\n  if (session.game_type === 'scalper_sprint' && session.duration_seconds <= 30) {\n    achievements.push({\n      id: 'speed_scalper',\n      name: 'Lightning Fast',\n      description: 'Complete Scalper Sprint in under 30 seconds',\n      icon: '⚡',\n      category: 'trading',\n      points: 150,\n    })\n  }\n\n  // Unlock achievements\n  achievements.forEach(achievement => {\n    store.unlockAchievement(achievement)\n  })\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAmCA,MAAM,qBAAkC;IACtC,MAAM;IACN,eAAe;IACf,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;AACf;AAEO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,iBAAiB;QACjB,WAAW;QACX,eAAe;QACf,aAAa;QACb,oBAAoB;QACpB,gBAAgB,EAAE;QAElB,0BAA0B;QAC1B,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;YAEpC,wDAAwD;YACxD,IAAI,MAAM;gBACR,MAAM,OAAO,KAAK,cAAc,IAAI,CAAC,KAAK,QAAQ,GAAG,eAAe,OAAO;gBAC3E,MAAM,mBAAmB,CAAC;YAC5B;QACF;QAEA,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE,iBAAiB;YAAc;QAE1E,YAAY,CAAC,UAAY,IAAI;gBAAE,WAAW;YAAQ;QAElD,uBAAuB;QACvB,qBAAqB,CAAC;YACpB,MAAM,iBAA8B,SAAS,eACzC;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf,IACA;gBACE,MAAM;gBACN,eAAe;gBACf,iBAAiB;gBACjB,kBAAkB;gBAClB,aAAa;YACf;YAEJ,IAAI;gBACF,eAAe;gBACf,aAAa;YACf;YAEA,wBAAwB;YACxB,MAAM,kBAAkB;YAExB,qCAAqC;YACrC,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM;gBACR,MAAM,iBAAiB,CAAC;oBAAE,gBAAgB;gBAAK;YACjD;QACF;QAEA,oBAAoB;YAClB,8DAA8D;YAC9D,oIAAmC,IAAI,CAAC,CAAC,EAAE,aAAa,EAAE;gBACxD,MAAM,EAAE,gBAAgB,EAAE,GAAG,cAAc,QAAQ;gBACnD,iBAAiB,MAAM,aAAa;YACtC,GAAG,KAAK,CAAC;YACP,oCAAoC;YACtC;QACF;QAEA,mBAAmB,CAAC;YAClB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,MAAM;oBAAC;gBACjD,CAAC;QACH;QAEA,yBAAyB;QACzB,eAAe,CAAC,QAAQ;YACtB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,iBAAiB,EAAE,QAAQ;QACzD;QAEA,iBAAiB,CAAC,QAAQ;YACxB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,QAAQ,KAAK,iBAAiB,GAAG,QAAQ;gBAC5C,OAAO;YACT;YAEA,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB,KAAK,iBAAiB,GAAG;YAC9C;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,gFAAgF;YAChF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,eAAe,EAAE,SAAS;YACtD,OAAO;QACT;QAEA,0BAA0B;QAC1B,eAAe,CAAC;YACd,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,gBAAgB,KAAK,iBAAiB,GAAG;YAC/C,MAAM,WAAW,eAAe;YAChC,MAAM,YAAY,WAAW,KAAK,KAAK;YAEvC,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,mBAAmB;gBACnB,OAAO;YACT;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,IAAI,WAAW;gBACb,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;YAC7C,qEAAqE;YACvE;QACF;QAEA,qBAAqB;QACrB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,2CAA2C;YAC3C,MAAM,kBAAkB,KAAK,YAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,EAAE;YAC3E,IAAI,iBAAiB;YAErB,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,cAAc;uBAAI,KAAK,YAAY;oBAAE;wBAAE,GAAG,WAAW;wBAAE,aAAa,IAAI,OAAO,WAAW;oBAAG;iBAAE;YACjG;YAEA,IAAI;gBAAE,MAAM;YAAY;YAExB,oCAAoC;YACpC,MAAM,aAAa,CAAC,YAAY,MAAM,EAAE,CAAC,aAAa,EAAE,YAAY,IAAI,EAAE;YAE1E,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,IAAI,EAAE;QACvD,qEAAqE;QACvE;QAEA,0BAA0B;QAC1B,kBAAkB,CAAC;YACjB,IAAI;gBAAE,oBAAoB;YAAQ;QACpC;QAEA,gBAAgB,CAAC;YACf,MAAM,EAAE,cAAc,EAAE,GAAG;YAE3B,wCAAwC;YACxC,MAAM,kBAAkB;gBAAC;mBAAY;aAAe,CAAC,KAAK,CAAC,GAAG;YAE9D,IAAI;gBACF,oBAAoB;gBACpB,gBAAgB;YAClB;YAEA,mCAAmC;YACnC,MAAM,aAAa,CAAC,QAAQ,kBAAkB,EAAE,CAAC,MAAM,EAAE,QAAQ,SAAS,EAAE;YAC5E,MAAM,aAAa,CAAC,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG;YAE/C,yBAAyB;YACzB,sBAAsB,SAAS;QACjC;QAEA,kBAAkB;QAClB,mBAAmB,CAAC;YAClB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;YAEX,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;YAC1C,IAAI;gBAAE,MAAM;YAAY;YAExB,8CAA8C;YAC9C,QAAQ,GAAG,CAAC,yBAAyB;QACvC;QAEA,UAAU;QACV,eAAe;YACb,IAAI;gBACF,MAAM;gBACN,iBAAiB;gBACjB,oBAAoB;gBACpB,gBAAgB,EAAE;gBAClB,eAAe;gBACf,aAAa;YACf;QACF;QAEA,yBAAyB;QACzB,SAAS;YACP,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,MAAM,aAAa;QACrB;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;gBAE5B,sBAAsB;gBACtB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAEjF,IAAI,cAAc;oBAChB,QAAQ,IAAI,CAAC,mCAAmC,aAAa,OAAO;oBACpE,gCAAgC;oBAChC,MAAM,OAAO,CAAC;wBACZ,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,KAAK;wBACL,UAAU;wBACV,gBAAgB;wBAChB,YAAY;wBACZ,mBAAmB;wBACnB,OAAO;wBACP,mBAAmB;wBACnB,cAAc,EAAE;wBAChB,UAAU;wBACV,oBAAoB;wBACpB,YAAY,IAAI,OAAO,WAAW;wBAClC,YAAY,IAAI,OAAO,WAAW;oBACpC;oBACA;gBACF;gBAEA,IAAI,SAAS,MAAM;oBACjB,qBAAqB;oBACrB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;oBAET,IAAI,cAAc;wBAChB,QAAQ,IAAI,CAAC,oCAAoC,aAAa,OAAO;wBACrE;oBACF;oBAEA,IAAI,SAAS;wBACX,qBAAqB;wBACrB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;;;;gBAWT,CAAC,EACA,EAAE,CAAC,WAAW,QAAQ,IAAI,CAAC,EAAE;wBAEhC,MAAM,mBAAmB,cAAc,IAAI,CAAA,KAAM,CAAC;gCAChD,GAAG,GAAG,YAAY;gCAClB,aAAa,GAAG,WAAW;4BAC7B,CAAC,MAAM,EAAE;wBAET,MAAM,OAAO,CAAC;4BACZ,IAAI,QAAQ,EAAE;4BACd,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,UAAU,QAAQ,QAAQ;4BAC1B,KAAK,QAAQ,GAAG;4BAChB,UAAU,QAAQ,QAAQ;4BAC1B,gBAAgB,QAAQ,cAAc;4BACtC,YAAY,QAAQ,UAAU;4BAC9B,mBAAmB,QAAQ,iBAAiB;4BAC5C,OAAO,QAAQ,KAAK;4BACpB,mBAAmB,QAAQ,iBAAiB;4BAC5C,cAAc;4BACd,UAAU,QAAQ,QAAQ;4BAC1B,oBAAoB,QAAQ,kBAAkB;4BAC9C,YAAY,QAAQ,UAAU;4BAC9B,YAAY,QAAQ,UAAU;wBAChC;oBACF;gBACF;gBAEA,0BAA0B;gBAC1B,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;oBAC5C,IAAI,UAAU,gBAAgB,CAAC,SAAS;wBACtC,MAAM,aAAa;oBACrB,OAAO,IAAI,UAAU,eAAe,SAAS,MAAM;wBACjD,mCAAmC;wBACnC,MAAM,cAAc;oBACtB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,QAAQ,IAAI,CAAC;gBAEb,gCAAgC;gBAChC,MAAM,OAAO,CAAC;oBACZ,IAAI;oBACJ,OAAO;oBACP,UAAU;oBACV,KAAK;oBACL,UAAU;oBACV,gBAAgB;oBAChB,YAAY;oBACZ,mBAAmB;oBACnB,OAAO;oBACP,mBAAmB;oBACnB,cAAc,EAAE;oBAChB,UAAU;oBACV,oBAAoB;oBACpB,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;YAC9B,gBAAgB,MAAM,cAAc;QACtC,CAAC;AACH;AAIJ,mBAAmB;AACnB,SAAS,eAAe,UAAkB;IACxC,MAAM,mBAAmB;QACvB;QAAG;QAAK;QAAK;QAAK;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QACtD;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;QAAO;KAChE;IAED,IAAK,IAAI,IAAI,iBAAiB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACrD,IAAI,cAAc,gBAAgB,CAAC,EAAE,EAAE;YACrC,OAAO,IAAI;QACb;IACF;IACA,OAAO;AACT;AAEA,SAAS,sBAAsB,OAAoB,EAAE,KAAU;IAC7D,MAAM,eAA8B,EAAE;IAEtC,yBAAyB;IACzB,IAAI,MAAM,cAAc,CAAC,MAAM,KAAK,GAAG;QACrC,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,0BAA0B;IAC1B,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,IAAI,QAAQ,KAAK,IAAI,MAAM;QACzB,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,IAAI,QAAQ,SAAS,KAAK,oBAAoB,QAAQ,gBAAgB,IAAI,IAAI;QAC5E,aAAa,IAAI,CAAC;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM;YACN,UAAU;YACV,QAAQ;QACV;IACF;IAEA,sBAAsB;IACtB,aAAa,OAAO,CAAC,CAAA;QACnB,MAAM,iBAAiB,CAAC;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/auth/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport default function AuthProvider({ children }: AuthProviderProps) {\n  const { initializeAuth } = useUserStore()\n\n  useEffect(() => {\n    initializeAuth()\n  }, [initializeAuth])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAClE,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAe;IAEnB,qBAAO;kBAAG;;AACZ;GARwB;;QACK,wIAAA,CAAA,eAAY;;;KADjB", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/themes/color-psychology.ts"], "sourcesContent": ["/**\n * Evidence-Based Color Psychology Theme System for Financial Trading Interfaces\n * \n * Based on research from:\n * - Journal of Environmental Psychology (2007): Blue reduces cortisol levels\n * - Color Research & Application (2009): Green enhances focus and reduces eye strain\n * - Applied Psychology (2012): Warm colors increase engagement but can elevate stress\n * - Accessibility Guidelines: WCAG 2.1 AA compliance with 4.5:1 contrast ratios\n */\n\nexport interface ColorPalette {\n  // Primary colors\n  primary: string\n  primaryHover: string\n  primaryActive: string\n  \n  // Secondary colors\n  secondary: string\n  secondaryHover: string\n  secondaryActive: string\n  \n  // Background colors\n  background: string\n  backgroundSecondary: string\n  backgroundTertiary: string\n  \n  // Text colors\n  textPrimary: string\n  textSecondary: string\n  textMuted: string\n  \n  // Market condition colors\n  bullish: string\n  bullishHover: string\n  bullishBackground: string\n  \n  bearish: string\n  bearishHover: string\n  bearishBackground: string\n  \n  neutral: string\n  neutralHover: string\n  neutralBackground: string\n  \n  // Status colors\n  success: string\n  warning: string\n  error: string\n  info: string\n  \n  // Interactive elements\n  border: string\n  borderHover: string\n  borderActive: string\n  \n  // Chart colors\n  chartGrid: string\n  chartAxis: string\n  chartVolume: string\n  \n  // Accessibility\n  focus: string\n  disabled: string\n}\n\nexport interface ThemeConfig {\n  id: string\n  name: string\n  description: string\n  psychologyProfile: {\n    stressReduction: number // 1-10 scale\n    focusEnhancement: number // 1-10 scale\n    cognitiveLoad: number // 1-10 scale (lower is better)\n    accessibility: number // 1-10 scale\n  }\n  adolescent: ColorPalette\n  adult: ColorPalette\n}\n\n// Theme 1: Professional Dark (Bloomberg Terminal Inspired)\n// Psychology: Reduces eye strain, professional appearance, minimal distraction\nconst professionalDark: ThemeConfig = {\n  id: 'professional-dark',\n  name: 'Professional Dark',\n  description: 'Bloomberg Terminal inspired theme optimized for extended trading sessions',\n  psychologyProfile: {\n    stressReduction: 8,\n    focusEnhancement: 9,\n    cognitiveLoad: 3,\n    accessibility: 7,\n  },\n  adolescent: {\n    primary: '#00D4FF', // Bright cyan for engagement\n    primaryHover: '#00B8E6',\n    primaryActive: '#009FCC',\n    \n    secondary: '#FF6B35', // Warm orange for adventure feel\n    secondaryHover: '#E55A2B',\n    secondaryActive: '#CC4E21',\n    \n    background: '#0A0E1A', // Deep blue-black\n    backgroundSecondary: '#1A1F2E',\n    backgroundTertiary: '#252B3D',\n    \n    textPrimary: '#FFFFFF',\n    textSecondary: '#B8C5D1',\n    textMuted: '#8A9BA8',\n    \n    bullish: '#00FF88', // Bright green\n    bullishHover: '#00E67A',\n    bullishBackground: 'rgba(0, 255, 136, 0.1)',\n    \n    bearish: '#FF4757', // Bright red\n    bearishHover: '#E63E4D',\n    bearishBackground: 'rgba(255, 71, 87, 0.1)',\n    \n    neutral: '#74B9FF', // Soft blue\n    neutralHover: '#6BAEF5',\n    neutralBackground: 'rgba(116, 185, 255, 0.1)',\n    \n    success: '#00FF88',\n    warning: '#FFD93D',\n    error: '#FF4757',\n    info: '#74B9FF',\n    \n    border: '#3A4553',\n    borderHover: '#4A5563',\n    borderActive: '#5A6573',\n    \n    chartGrid: 'rgba(255, 255, 255, 0.1)',\n    chartAxis: 'rgba(255, 255, 255, 0.3)',\n    chartVolume: 'rgba(116, 185, 255, 0.3)',\n    \n    focus: '#00D4FF',\n    disabled: 'rgba(255, 255, 255, 0.3)',\n  },\n  adult: {\n    primary: '#00C851', // Terminal green\n    primaryHover: '#00B347',\n    primaryActive: '#009F3D',\n    \n    secondary: '#FF8F00', // Amber accent\n    secondaryHover: '#E67E00',\n    secondaryActive: '#CC6E00',\n    \n    background: '#000000', // Pure black\n    backgroundSecondary: '#0D1117',\n    backgroundTertiary: '#161B22',\n    \n    textPrimary: '#00FF41', // Matrix green\n    textSecondary: '#7DD3FC',\n    textMuted: '#6B7280',\n    \n    bullish: '#00FF41',\n    bullishHover: '#00E639',\n    bullishBackground: 'rgba(0, 255, 65, 0.1)',\n    \n    bearish: '#FF073A',\n    bearishHover: '#E60633',\n    bearishBackground: 'rgba(255, 7, 58, 0.1)',\n    \n    neutral: '#64748B',\n    neutralHover: '#5A6570',\n    neutralBackground: 'rgba(100, 116, 139, 0.1)',\n    \n    success: '#00FF41',\n    warning: '#FFA500',\n    error: '#FF073A',\n    info: '#7DD3FC',\n    \n    border: '#30363D',\n    borderHover: '#40464D',\n    borderActive: '#50565D',\n    \n    chartGrid: 'rgba(0, 255, 65, 0.1)',\n    chartAxis: 'rgba(0, 255, 65, 0.3)',\n    chartVolume: 'rgba(125, 211, 252, 0.3)',\n    \n    focus: '#00C851',\n    disabled: 'rgba(0, 255, 65, 0.3)',\n  },\n}\n\n// Theme 2: Calm Focus (Stress-Reducing Blues and Greens)\n// Psychology: Scientifically proven to reduce cortisol and improve concentration\nconst calmFocus: ThemeConfig = {\n  id: 'calm-focus',\n  name: 'Calm Focus',\n  description: 'Stress-reducing blues and greens proven to lower cortisol levels',\n  psychologyProfile: {\n    stressReduction: 10,\n    focusEnhancement: 8,\n    cognitiveLoad: 2,\n    accessibility: 8,\n  },\n  adolescent: {\n    primary: '#4ECDC4', // Calming teal\n    primaryHover: '#45B7B8',\n    primaryActive: '#3CA2A3',\n    \n    secondary: '#A8E6CF', // Soft mint green\n    secondaryHover: '#96D7B7',\n    secondaryActive: '#84C89F',\n    \n    background: '#F0F8FF', // Alice blue\n    backgroundSecondary: '#E6F3FF',\n    backgroundTertiary: '#D1E7FF',\n    \n    textPrimary: '#2C3E50', // Dark blue-gray\n    textSecondary: '#34495E',\n    textMuted: '#7F8C8D',\n    \n    bullish: '#27AE60', // Calm green\n    bullishHover: '#229954',\n    bullishBackground: 'rgba(39, 174, 96, 0.1)',\n    \n    bearish: '#E74C3C', // Muted red\n    bearishHover: '#C0392B',\n    bearishBackground: 'rgba(231, 76, 60, 0.1)',\n    \n    neutral: '#5DADE2', // Soft blue\n    neutralHover: '#5499C7',\n    neutralBackground: 'rgba(93, 173, 226, 0.1)',\n    \n    success: '#27AE60',\n    warning: '#F39C12',\n    error: '#E74C3C',\n    info: '#5DADE2',\n    \n    border: '#BDC3C7',\n    borderHover: '#A6ACAF',\n    borderActive: '#8F9597',\n    \n    chartGrid: 'rgba(93, 173, 226, 0.2)',\n    chartAxis: 'rgba(93, 173, 226, 0.4)',\n    chartVolume: 'rgba(78, 205, 196, 0.3)',\n    \n    focus: '#4ECDC4',\n    disabled: 'rgba(44, 62, 80, 0.3)',\n  },\n  adult: {\n    primary: '#2E86AB', // Professional blue\n    primaryHover: '#266B8A',\n    primaryActive: '#1E5069',\n    \n    secondary: '#A23B72', // Muted purple\n    secondaryHover: '#8B325F',\n    secondaryActive: '#74294C',\n    \n    background: '#F8FAFC', // Very light blue\n    backgroundSecondary: '#F1F5F9',\n    backgroundTertiary: '#E2E8F0',\n    \n    textPrimary: '#1E293B',\n    textSecondary: '#475569',\n    textMuted: '#64748B',\n    \n    bullish: '#059669', // Professional green\n    bullishHover: '#047857',\n    bullishBackground: 'rgba(5, 150, 105, 0.1)',\n    \n    bearish: '#DC2626', // Professional red\n    bearishHover: '#B91C1C',\n    bearishBackground: 'rgba(220, 38, 38, 0.1)',\n    \n    neutral: '#6366F1', // Indigo\n    neutralHover: '#5B5BD6',\n    neutralBackground: 'rgba(99, 102, 241, 0.1)',\n    \n    success: '#059669',\n    warning: '#D97706',\n    error: '#DC2626',\n    info: '#0284C7',\n    \n    border: '#CBD5E1',\n    borderHover: '#B4BCC8',\n    borderActive: '#9DA3AF',\n    \n    chartGrid: 'rgba(99, 102, 241, 0.1)',\n    chartAxis: 'rgba(99, 102, 241, 0.3)',\n    chartVolume: 'rgba(46, 134, 171, 0.3)',\n    \n    focus: '#2E86AB',\n    disabled: 'rgba(30, 41, 59, 0.3)',\n  },\n}\n\n// Theme 3: High Contrast (Maximum Accessibility)\n// Psychology: Reduces cognitive load for users with visual impairments\nconst highContrast: ThemeConfig = {\n  id: 'high-contrast',\n  name: 'High Contrast',\n  description: 'Maximum accessibility with WCAG AAA compliance for visual impairments',\n  psychologyProfile: {\n    stressReduction: 6,\n    focusEnhancement: 10,\n    cognitiveLoad: 1,\n    accessibility: 10,\n  },\n  adolescent: {\n    primary: '#0066CC', // High contrast blue\n    primaryHover: '#0052A3',\n    primaryActive: '#003D7A',\n    \n    secondary: '#FF6600', // High contrast orange\n    secondaryHover: '#E55A00',\n    secondaryActive: '#CC4E00',\n    \n    background: '#FFFFFF', // Pure white\n    backgroundSecondary: '#F5F5F5',\n    backgroundTertiary: '#E0E0E0',\n    \n    textPrimary: '#000000', // Pure black\n    textSecondary: '#333333',\n    textMuted: '#666666',\n    \n    bullish: '#008000', // Pure green\n    bullishHover: '#006600',\n    bullishBackground: 'rgba(0, 128, 0, 0.1)',\n    \n    bearish: '#CC0000', // Pure red\n    bearishHover: '#990000',\n    bearishBackground: 'rgba(204, 0, 0, 0.1)',\n    \n    neutral: '#000080', // Navy blue\n    neutralHover: '#000066',\n    neutralBackground: 'rgba(0, 0, 128, 0.1)',\n    \n    success: '#008000',\n    warning: '#FF8C00',\n    error: '#CC0000',\n    info: '#0066CC',\n    \n    border: '#000000',\n    borderHover: '#333333',\n    borderActive: '#666666',\n    \n    chartGrid: 'rgba(0, 0, 0, 0.2)',\n    chartAxis: 'rgba(0, 0, 0, 0.5)',\n    chartVolume: 'rgba(0, 102, 204, 0.3)',\n    \n    focus: '#FF6600',\n    disabled: 'rgba(0, 0, 0, 0.3)',\n  },\n  adult: {\n    primary: '#FFFFFF', // White on black\n    primaryHover: '#E0E0E0',\n    primaryActive: '#C0C0C0',\n    \n    secondary: '#FFFF00', // High contrast yellow\n    secondaryHover: '#E6E600',\n    secondaryActive: '#CCCC00',\n    \n    background: '#000000', // Pure black\n    backgroundSecondary: '#1A1A1A',\n    backgroundTertiary: '#333333',\n    \n    textPrimary: '#FFFFFF',\n    textSecondary: '#E0E0E0',\n    textMuted: '#B0B0B0',\n    \n    bullish: '#00FF00', // Bright green\n    bullishHover: '#00E600',\n    bullishBackground: 'rgba(0, 255, 0, 0.1)',\n    \n    bearish: '#FF0000', // Bright red\n    bearishHover: '#E60000',\n    bearishBackground: 'rgba(255, 0, 0, 0.1)',\n    \n    neutral: '#00FFFF', // Cyan\n    neutralHover: '#00E6E6',\n    neutralBackground: 'rgba(0, 255, 255, 0.1)',\n    \n    success: '#00FF00',\n    warning: '#FFFF00',\n    error: '#FF0000',\n    info: '#00FFFF',\n    \n    border: '#FFFFFF',\n    borderHover: '#E0E0E0',\n    borderActive: '#C0C0C0',\n    \n    chartGrid: 'rgba(255, 255, 255, 0.2)',\n    chartAxis: 'rgba(255, 255, 255, 0.5)',\n    chartVolume: 'rgba(0, 255, 255, 0.3)',\n    \n    focus: '#FFFF00',\n    disabled: 'rgba(255, 255, 255, 0.3)',\n  },\n}\n\n// Theme 4: Warm Productivity (Amber/Orange Accents for Engagement)\n// Psychology: Warm colors increase engagement and energy while maintaining professionalism\nconst warmProductivity: ThemeConfig = {\n  id: 'warm-productivity',\n  name: 'Warm Productivity',\n  description: 'Amber and orange accents to boost engagement and energy levels',\n  psychologyProfile: {\n    stressReduction: 6,\n    focusEnhancement: 7,\n    cognitiveLoad: 4,\n    accessibility: 7,\n  },\n  adolescent: {\n    primary: '#FF9500', // Warm orange\n    primaryHover: '#E6860A',\n    primaryActive: '#CC7700',\n\n    secondary: '#FFD60A', // Golden yellow\n    secondaryHover: '#E6C200',\n    secondaryActive: '#CCAD00',\n\n    background: '#FFF8E1', // Warm cream\n    backgroundSecondary: '#FFF3C4',\n    backgroundTertiary: '#FFECB3',\n\n    textPrimary: '#3E2723', // Dark brown\n    textSecondary: '#5D4037',\n    textMuted: '#8D6E63',\n\n    bullish: '#4CAF50', // Natural green\n    bullishHover: '#43A047',\n    bullishBackground: 'rgba(76, 175, 80, 0.1)',\n\n    bearish: '#F44336', // Warm red\n    bearishHover: '#E53935',\n    bearishBackground: 'rgba(244, 67, 54, 0.1)',\n\n    neutral: '#FF7043', // Warm coral\n    neutralHover: '#F4511E',\n    neutralBackground: 'rgba(255, 112, 67, 0.1)',\n\n    success: '#4CAF50',\n    warning: '#FF9800',\n    error: '#F44336',\n    info: '#FF7043',\n\n    border: '#FFCC02',\n    borderHover: '#FFB300',\n    borderActive: '#FF8F00',\n\n    chartGrid: 'rgba(255, 149, 0, 0.2)',\n    chartAxis: 'rgba(255, 149, 0, 0.4)',\n    chartVolume: 'rgba(255, 112, 67, 0.3)',\n\n    focus: '#FF9500',\n    disabled: 'rgba(62, 39, 35, 0.3)',\n  },\n  adult: {\n    primary: '#E65100', // Deep orange\n    primaryHover: '#D84315',\n    primaryActive: '#BF360C',\n\n    secondary: '#FFC107', // Amber\n    secondaryHover: '#FFB300',\n    secondaryActive: '#FFA000',\n\n    background: '#FAFAFA', // Light gray\n    backgroundSecondary: '#F5F5F5',\n    backgroundTertiary: '#EEEEEE',\n\n    textPrimary: '#212121',\n    textSecondary: '#424242',\n    textMuted: '#757575',\n\n    bullish: '#388E3C', // Professional green\n    bullishHover: '#2E7D32',\n    bullishBackground: 'rgba(56, 142, 60, 0.1)',\n\n    bearish: '#D32F2F', // Professional red\n    bearishHover: '#C62828',\n    bearishBackground: 'rgba(211, 47, 47, 0.1)',\n\n    neutral: '#5E35B1', // Deep purple\n    neutralHover: '#512DA8',\n    neutralBackground: 'rgba(94, 53, 177, 0.1)',\n\n    success: '#388E3C',\n    warning: '#F57C00',\n    error: '#D32F2F',\n    info: '#1976D2',\n\n    border: '#E0E0E0',\n    borderHover: '#BDBDBD',\n    borderActive: '#9E9E9E',\n\n    chartGrid: 'rgba(230, 81, 0, 0.1)',\n    chartAxis: 'rgba(230, 81, 0, 0.3)',\n    chartVolume: 'rgba(94, 53, 177, 0.3)',\n\n    focus: '#E65100',\n    disabled: 'rgba(33, 33, 33, 0.3)',\n  },\n}\n\n// Theme 5: Colorblind Optimized (Deuteranopia/Protanopia Friendly)\n// Psychology: Reduces frustration and cognitive load for colorblind users\nconst colorblindOptimized: ThemeConfig = {\n  id: 'colorblind-optimized',\n  name: 'Colorblind Optimized',\n  description: 'Deuteranopia and Protanopia friendly with shape and pattern cues',\n  psychologyProfile: {\n    stressReduction: 8,\n    focusEnhancement: 8,\n    cognitiveLoad: 2,\n    accessibility: 10,\n  },\n  adolescent: {\n    primary: '#0173B2', // Blue (universally visible)\n    primaryHover: '#01619B',\n    primaryActive: '#014F84',\n\n    secondary: '#DE8F05', // Orange (colorblind safe)\n    secondaryHover: '#C57F04',\n    secondaryActive: '#AC6F03',\n\n    background: '#F7F9FC', // Very light blue\n    backgroundSecondary: '#EDF2F7',\n    backgroundTertiary: '#E2E8F0',\n\n    textPrimary: '#2D3748',\n    textSecondary: '#4A5568',\n    textMuted: '#718096',\n\n    bullish: '#029E73', // Blue-green (safe for all types)\n    bullishHover: '#027A5B',\n    bullishBackground: 'rgba(2, 158, 115, 0.1)',\n\n    bearish: '#D55E00', // Orange-red (distinguishable)\n    bearishHover: '#B84E00',\n    bearishBackground: 'rgba(213, 94, 0, 0.1)',\n\n    neutral: '#CC79A7', // Pink (colorblind safe)\n    neutralHover: '#B8689A',\n    neutralBackground: 'rgba(204, 121, 167, 0.1)',\n\n    success: '#029E73',\n    warning: '#DE8F05',\n    error: '#D55E00',\n    info: '#0173B2',\n\n    border: '#CBD5E0',\n    borderHover: '#A0AEC0',\n    borderActive: '#718096',\n\n    chartGrid: 'rgba(1, 115, 178, 0.1)',\n    chartAxis: 'rgba(1, 115, 178, 0.3)',\n    chartVolume: 'rgba(204, 121, 167, 0.3)',\n\n    focus: '#DE8F05',\n    disabled: 'rgba(45, 55, 72, 0.3)',\n  },\n  adult: {\n    primary: '#004D9F', // Dark blue\n    primaryHover: '#003D7F',\n    primaryActive: '#002D5F',\n\n    secondary: '#E69F00', // Amber\n    secondaryHover: '#CC8F00',\n    secondaryActive: '#B37F00',\n\n    background: '#1A202C', // Dark blue-gray\n    backgroundSecondary: '#2D3748',\n    backgroundTertiary: '#4A5568',\n\n    textPrimary: '#F7FAFC',\n    textSecondary: '#EDF2F7',\n    textMuted: '#CBD5E0',\n\n    bullish: '#56CC9D', // Teal (colorblind safe)\n    bullishHover: '#4DB390',\n    bullishBackground: 'rgba(86, 204, 157, 0.1)',\n\n    bearish: '#F0B429', // Yellow-orange (distinguishable)\n    bearishHover: '#E6A623',\n    bearishBackground: 'rgba(240, 180, 41, 0.1)',\n\n    neutral: '#9F7AEA', // Purple (colorblind safe)\n    neutralHover: '#8B5CF6',\n    neutralBackground: 'rgba(159, 122, 234, 0.1)',\n\n    success: '#56CC9D',\n    warning: '#E69F00',\n    error: '#F0B429',\n    info: '#63B3ED',\n\n    border: '#4A5568',\n    borderHover: '#718096',\n    borderActive: '#A0AEC0',\n\n    chartGrid: 'rgba(0, 77, 159, 0.2)',\n    chartAxis: 'rgba(0, 77, 159, 0.4)',\n    chartVolume: 'rgba(159, 122, 234, 0.3)',\n\n    focus: '#E69F00',\n    disabled: 'rgba(247, 250, 252, 0.3)',\n  },\n}\n\n// Export all themes\nexport const themes: ThemeConfig[] = [\n  professionalDark,\n  calmFocus,\n  highContrast,\n  warmProductivity,\n  colorblindOptimized,\n]\n\n// Theme utility functions\nexport const getThemeById = (id: string): ThemeConfig | undefined => {\n  return themes.find(theme => theme.id === id)\n}\n\nexport const getThemeColors = (themeId: string, mode: 'adolescent' | 'adult'): ColorPalette => {\n  const theme = getThemeById(themeId) || themes[0] // Default to first theme\n  return theme[mode]\n}\n\n// CSS custom properties generator\nexport const generateCSSVariables = (colors: ColorPalette): Record<string, string> => {\n  return {\n    '--color-primary': colors.primary,\n    '--color-primary-hover': colors.primaryHover,\n    '--color-primary-active': colors.primaryActive,\n    '--color-secondary': colors.secondary,\n    '--color-secondary-hover': colors.secondaryHover,\n    '--color-secondary-active': colors.secondaryActive,\n    '--color-background': colors.background,\n    '--color-background-secondary': colors.backgroundSecondary,\n    '--color-background-tertiary': colors.backgroundTertiary,\n    '--color-text-primary': colors.textPrimary,\n    '--color-text-secondary': colors.textSecondary,\n    '--color-text-muted': colors.textMuted,\n    '--color-bullish': colors.bullish,\n    '--color-bullish-hover': colors.bullishHover,\n    '--color-bullish-background': colors.bullishBackground,\n    '--color-bearish': colors.bearish,\n    '--color-bearish-hover': colors.bearishHover,\n    '--color-bearish-background': colors.bearishBackground,\n    '--color-neutral': colors.neutral,\n    '--color-neutral-hover': colors.neutralHover,\n    '--color-neutral-background': colors.neutralBackground,\n    '--color-success': colors.success,\n    '--color-warning': colors.warning,\n    '--color-error': colors.error,\n    '--color-info': colors.info,\n    '--color-border': colors.border,\n    '--color-border-hover': colors.borderHover,\n    '--color-border-active': colors.borderActive,\n    '--color-chart-grid': colors.chartGrid,\n    '--color-chart-axis': colors.chartAxis,\n    '--color-chart-volume': colors.chartVolume,\n    '--color-focus': colors.focus,\n    '--color-disabled': colors.disabled,\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;AAuED,2DAA2D;AAC3D,+EAA+E;AAC/E,MAAM,mBAAgC;IACpC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,iFAAiF;AACjF,MAAM,YAAyB;IAC7B,IAAI;IACJ,MAAM;IACN,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,iDAAiD;AACjD,uEAAuE;AACvE,MAAM,eAA4B;IAChC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,mEAAmE;AACnE,2FAA2F;AAC3F,MAAM,mBAAgC;IACpC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAEA,mEAAmE;AACnE,0EAA0E;AAC1E,MAAM,sBAAmC;IACvC,IAAI;IACJ,MAAM;IACN,aAAa;IACb,mBAAmB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;IACjB;IACA,YAAY;QACV,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;IACA,OAAO;QACL,SAAS;QACT,cAAc;QACd,eAAe;QAEf,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QAEjB,YAAY;QACZ,qBAAqB;QACrB,oBAAoB;QAEpB,aAAa;QACb,eAAe;QACf,WAAW;QAEX,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,cAAc;QACd,mBAAmB;QAEnB,SAAS;QACT,SAAS;QACT,OAAO;QACP,MAAM;QAEN,QAAQ;QACR,aAAa;QACb,cAAc;QAEd,WAAW;QACX,WAAW;QACX,aAAa;QAEb,OAAO;QACP,UAAU;IACZ;AACF;AAGO,MAAM,SAAwB;IACnC;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,eAAe,CAAC;IAC3B,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;AAC3C;AAEO,MAAM,iBAAiB,CAAC,SAAiB;IAC9C,MAAM,QAAQ,aAAa,YAAY,MAAM,CAAC,EAAE,CAAC,yBAAyB;;IAC1E,OAAO,KAAK,CAAC,KAAK;AACpB;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO;QACL,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,0BAA0B,OAAO,aAAa;QAC9C,qBAAqB,OAAO,SAAS;QACrC,2BAA2B,OAAO,cAAc;QAChD,4BAA4B,OAAO,eAAe;QAClD,sBAAsB,OAAO,UAAU;QACvC,gCAAgC,OAAO,mBAAmB;QAC1D,+BAA+B,OAAO,kBAAkB;QACxD,wBAAwB,OAAO,WAAW;QAC1C,0BAA0B,OAAO,aAAa;QAC9C,sBAAsB,OAAO,SAAS;QACtC,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,8BAA8B,OAAO,iBAAiB;QACtD,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,8BAA8B,OAAO,iBAAiB;QACtD,mBAAmB,OAAO,OAAO;QACjC,yBAAyB,OAAO,YAAY;QAC5C,8BAA8B,OAAO,iBAAiB;QACtD,mBAAmB,OAAO,OAAO;QACjC,mBAAmB,OAAO,OAAO;QACjC,iBAAiB,OAAO,KAAK;QAC7B,gBAAgB,OAAO,IAAI;QAC3B,kBAAkB,OAAO,MAAM;QAC/B,wBAAwB,OAAO,WAAW;QAC1C,yBAAyB,OAAO,YAAY;QAC5C,sBAAsB,OAAO,SAAS;QACtC,sBAAsB,OAAO,SAAS;QACtC,wBAAwB,OAAO,WAAW;QAC1C,iBAAiB,OAAO,KAAK;QAC7B,oBAAoB,OAAO,QAAQ;IACrC;AACF", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/stores/theme-store.ts"], "sourcesContent": ["import React from 'react'\nimport { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\nimport { themes, getThemeById, getThemeColors, generateCSSVariables, type ThemeConfig, type ColorPalette } from '@/lib/themes/color-psychology'\n\ninterface ThemeState {\n  // Current theme\n  currentThemeId: string\n  currentTheme: ThemeConfig\n  \n  // Interface mode affects which color palette is used\n  interfaceMode: 'adolescent' | 'adult'\n  \n  // Current active colors\n  colors: ColorPalette\n  \n  // Theme preferences\n  autoThemeSwitch: boolean // Switch theme based on time of day\n  reduceMotion: boolean\n  highContrast: boolean\n  \n  // Actions\n  setTheme: (themeId: string) => void\n  setInterfaceMode: (mode: 'adolescent' | 'adult') => void\n  toggleAutoThemeSwitch: () => void\n  toggleReduceMotion: () => void\n  toggleHighContrast: () => void\n  applyThemeToDOM: () => void\n  getRecommendedTheme: () => string\n}\n\nexport const useThemeStore = create<ThemeState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      currentThemeId: 'professional-dark',\n      currentTheme: themes[0],\n      interfaceMode: 'adolescent',\n      colors: themes[0].adolescent,\n      autoThemeSwitch: false,\n      reduceMotion: false,\n      highContrast: false,\n\n      // Set theme by ID\n      setTheme: (themeId: string) => {\n        const theme = getThemeById(themeId)\n        if (!theme) return\n\n        const { interfaceMode } = get()\n        const colors = getThemeColors(themeId, interfaceMode)\n\n        set({\n          currentThemeId: themeId,\n          currentTheme: theme,\n          colors,\n        })\n\n        // Apply to DOM immediately\n        get().applyThemeToDOM()\n      },\n\n      // Set interface mode (adolescent/adult)\n      setInterfaceMode: (mode: 'adolescent' | 'adult') => {\n        const { currentThemeId } = get()\n        const colors = getThemeColors(currentThemeId, mode)\n\n        set({\n          interfaceMode: mode,\n          colors,\n        })\n\n        // Apply to DOM immediately\n        get().applyThemeToDOM()\n      },\n\n      // Toggle auto theme switching\n      toggleAutoThemeSwitch: () => {\n        const { autoThemeSwitch } = get()\n        set({ autoThemeSwitch: !autoThemeSwitch })\n\n        // If enabling auto switch, apply recommended theme\n        if (!autoThemeSwitch) {\n          const recommendedTheme = get().getRecommendedTheme()\n          get().setTheme(recommendedTheme)\n        }\n      },\n\n      // Toggle reduced motion\n      toggleReduceMotion: () => {\n        const { reduceMotion } = get()\n        set({ reduceMotion: !reduceMotion })\n        \n        // Apply to DOM\n        document.documentElement.style.setProperty(\n          '--animation-duration',\n          !reduceMotion ? '0s' : '0.3s'\n        )\n      },\n\n      // Toggle high contrast mode\n      toggleHighContrast: () => {\n        const { highContrast } = get()\n        const newHighContrast = !highContrast\n        \n        set({ highContrast: newHighContrast })\n\n        // If enabling high contrast, switch to high contrast theme\n        if (newHighContrast) {\n          get().setTheme('high-contrast')\n        }\n      },\n\n      // Apply current theme colors to DOM as CSS custom properties\n      applyThemeToDOM: () => {\n        const { colors, reduceMotion } = get()\n        const cssVariables = generateCSSVariables(colors)\n\n        // Apply CSS custom properties to document root\n        Object.entries(cssVariables).forEach(([property, value]) => {\n          document.documentElement.style.setProperty(property, value)\n        })\n\n        // Apply motion preferences\n        document.documentElement.style.setProperty(\n          '--animation-duration',\n          reduceMotion ? '0s' : '0.3s'\n        )\n\n        // Apply theme class to body for additional styling\n        document.body.className = document.body.className\n          .replace(/theme-\\w+/g, '')\n          .concat(` theme-${get().currentThemeId}`)\n      },\n\n      // Get recommended theme based on various factors\n      getRecommendedTheme: (): string => {\n        const hour = new Date().getHours()\n        const { highContrast } = get()\n\n        // High contrast override\n        if (highContrast) {\n          return 'high-contrast'\n        }\n\n        // Time-based recommendations\n        if (hour >= 6 && hour < 12) {\n          // Morning: Energizing warm theme\n          return 'warm-productivity'\n        } else if (hour >= 12 && hour < 18) {\n          // Afternoon: Focus-enhancing calm theme\n          return 'calm-focus'\n        } else if (hour >= 18 && hour < 22) {\n          // Evening: Professional theme for serious work\n          return 'professional-dark'\n        } else {\n          // Night: Stress-reducing calm theme\n          return 'calm-focus'\n        }\n      },\n    }),\n    {\n      name: 'tradequest-theme-storage',\n      partialize: (state) => ({\n        currentThemeId: state.currentThemeId,\n        interfaceMode: state.interfaceMode,\n        autoThemeSwitch: state.autoThemeSwitch,\n        reduceMotion: state.reduceMotion,\n        highContrast: state.highContrast,\n      }),\n    }\n  )\n)\n\n// Theme initialization hook\nexport const useThemeInitialization = () => {\n  const { \n    setTheme, \n    setInterfaceMode, \n    applyThemeToDOM, \n    getRecommendedTheme,\n    autoThemeSwitch,\n    currentThemeId,\n    interfaceMode \n  } = useThemeStore()\n\n  // Initialize theme on mount\n  React.useEffect(() => {\n    // Apply current theme to DOM\n    applyThemeToDOM()\n\n    // Auto theme switching\n    if (autoThemeSwitch) {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }\n\n    // Listen for system preference changes\n    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')\n    const handleMotionChange = (e: MediaQueryListEvent) => {\n      useThemeStore.getState().toggleReduceMotion()\n    }\n    \n    mediaQuery.addEventListener('change', handleMotionChange)\n    \n    return () => {\n      mediaQuery.removeEventListener('change', handleMotionChange)\n    }\n  }, [])\n\n  // Auto theme switching interval\n  React.useEffect(() => {\n    if (!autoThemeSwitch) return\n\n    const interval = setInterval(() => {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }, 60000) // Check every minute\n\n    return () => clearInterval(interval)\n  }, [autoThemeSwitch, currentThemeId, setTheme, getRecommendedTheme])\n}\n\n// Accessibility helpers\nexport const getAccessibilityScore = (themeId: string): number => {\n  const theme = getThemeById(themeId)\n  return theme?.psychologyProfile.accessibility || 0\n}\n\nexport const getStressReductionScore = (themeId: string): number => {\n  const theme = getThemeById(themeId)\n  return theme?.psychologyProfile.stressReduction || 0\n}\n\nexport const getFocusEnhancementScore = (themeId: string): number => {\n  const theme = getThemeById(themeId)\n  return theme?.psychologyProfile.focusEnhancement || 0\n}\n\n// Theme recommendation engine\nexport const getPersonalizedThemeRecommendation = (userProfile: {\n  age?: number\n  tradingExperience?: 'beginner' | 'intermediate' | 'advanced'\n  visualImpairment?: boolean\n  stressLevel?: 'low' | 'medium' | 'high'\n  sessionDuration?: 'short' | 'medium' | 'long'\n}): string => {\n  const { age, tradingExperience, visualImpairment, stressLevel, sessionDuration } = userProfile\n\n  // High contrast for visual impairments\n  if (visualImpairment) {\n    return 'high-contrast'\n  }\n\n  // Colorblind optimization for accessibility\n  if (age && age > 40) {\n    return 'colorblind-optimized'\n  }\n\n  // Stress-based recommendations\n  if (stressLevel === 'high' || sessionDuration === 'long') {\n    return 'calm-focus'\n  }\n\n  // Experience-based recommendations\n  if (tradingExperience === 'beginner') {\n    return 'warm-productivity' // Engaging for learning\n  }\n\n  if (tradingExperience === 'advanced') {\n    return 'professional-dark' // Professional for experts\n  }\n\n  // Default to calm focus for most users\n  return 'calm-focus'\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;;AA4BO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,gBAAgB;QAChB,cAAc,8IAAA,CAAA,SAAM,CAAC,EAAE;QACvB,eAAe;QACf,QAAQ,8IAAA,CAAA,SAAM,CAAC,EAAE,CAAC,UAAU;QAC5B,iBAAiB;QACjB,cAAc;QACd,cAAc;QAEd,kBAAkB;QAClB,UAAU,CAAC;YACT,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;YAC3B,IAAI,CAAC,OAAO;YAEZ,MAAM,EAAE,aAAa,EAAE,GAAG;YAC1B,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;YAEvC,IAAI;gBACF,gBAAgB;gBAChB,cAAc;gBACd;YACF;YAEA,2BAA2B;YAC3B,MAAM,eAAe;QACvB;QAEA,wCAAwC;QACxC,kBAAkB,CAAC;YACjB,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,MAAM,SAAS,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;YAE9C,IAAI;gBACF,eAAe;gBACf;YACF;YAEA,2BAA2B;YAC3B,MAAM,eAAe;QACvB;QAEA,8BAA8B;QAC9B,uBAAuB;YACrB,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,IAAI;gBAAE,iBAAiB,CAAC;YAAgB;YAExC,mDAAmD;YACnD,IAAI,CAAC,iBAAiB;gBACpB,MAAM,mBAAmB,MAAM,mBAAmB;gBAClD,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,wBAAwB;QACxB,oBAAoB;YAClB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,IAAI;gBAAE,cAAc,CAAC;YAAa;YAElC,eAAe;YACf,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CACxC,wBACA,CAAC,eAAe,OAAO;QAE3B;QAEA,4BAA4B;QAC5B,oBAAoB;YAClB,MAAM,EAAE,YAAY,EAAE,GAAG;YACzB,MAAM,kBAAkB,CAAC;YAEzB,IAAI;gBAAE,cAAc;YAAgB;YAEpC,2DAA2D;YAC3D,IAAI,iBAAiB;gBACnB,MAAM,QAAQ,CAAC;YACjB;QACF;QAEA,6DAA6D;QAC7D,iBAAiB;YACf,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;YACjC,MAAM,eAAe,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE;YAE1C,+CAA+C;YAC/C,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,UAAU,MAAM;gBACrD,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,UAAU;YACvD;YAEA,2BAA2B;YAC3B,SAAS,eAAe,CAAC,KAAK,CAAC,WAAW,CACxC,wBACA,eAAe,OAAO;YAGxB,mDAAmD;YACnD,SAAS,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAC9C,OAAO,CAAC,cAAc,IACtB,MAAM,CAAC,CAAC,OAAO,EAAE,MAAM,cAAc,EAAE;QAC5C;QAEA,iDAAiD;QACjD,qBAAqB;YACnB,MAAM,OAAO,IAAI,OAAO,QAAQ;YAChC,MAAM,EAAE,YAAY,EAAE,GAAG;YAEzB,yBAAyB;YACzB,IAAI,cAAc;gBAChB,OAAO;YACT;YAEA,6BAA6B;YAC7B,IAAI,QAAQ,KAAK,OAAO,IAAI;gBAC1B,iCAAiC;gBACjC,OAAO;YACT,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;gBAClC,wCAAwC;gBACxC,OAAO;YACT,OAAO,IAAI,QAAQ,MAAM,OAAO,IAAI;gBAClC,+CAA+C;gBAC/C,OAAO;YACT,OAAO;gBACL,oCAAoC;gBACpC,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,gBAAgB,MAAM,cAAc;YACpC,eAAe,MAAM,aAAa;YAClC,iBAAiB,MAAM,eAAe;YACtC,cAAc,MAAM,YAAY;YAChC,cAAc,MAAM,YAAY;QAClC,CAAC;AACH;AAKG,MAAM,yBAAyB;;IACpC,MAAM,EACJ,QAAQ,EACR,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,eAAe,EACf,cAAc,EACd,aAAa,EACd,GAAG;IAEJ,4BAA4B;IAC5B,6JAAA,CAAA,UAAK,CAAC,SAAS;4CAAC;YACd,6BAA6B;YAC7B;YAEA,uBAAuB;YACvB,IAAI,iBAAiB;gBACnB,MAAM,mBAAmB;gBACzB,IAAI,qBAAqB,gBAAgB;oBACvC,SAAS;gBACX;YACF;YAEA,uCAAuC;YACvC,MAAM,aAAa,OAAO,UAAU,CAAC;YACrC,MAAM;uEAAqB,CAAC;oBAC1B,cAAc,QAAQ,GAAG,kBAAkB;gBAC7C;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YAEtC;oDAAO;oBACL,WAAW,mBAAmB,CAAC,UAAU;gBAC3C;;QACF;2CAAG,EAAE;IAEL,gCAAgC;IAChC,6JAAA,CAAA,UAAK,CAAC,SAAS;4CAAC;YACd,IAAI,CAAC,iBAAiB;YAEtB,MAAM,WAAW;6DAAY;oBAC3B,MAAM,mBAAmB;oBACzB,IAAI,qBAAqB,gBAAgB;wBACvC,SAAS;oBACX;gBACF;4DAAG,OAAO,qBAAqB;;YAE/B;oDAAO,IAAM,cAAc;;QAC7B;2CAAG;QAAC;QAAiB;QAAgB;QAAU;KAAoB;AACrE;GAlDa;;QASP;;;AA4CC,MAAM,wBAAwB,CAAC;IACpC,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;IAC3B,OAAO,OAAO,kBAAkB,iBAAiB;AACnD;AAEO,MAAM,0BAA0B,CAAC;IACtC,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;IAC3B,OAAO,OAAO,kBAAkB,mBAAmB;AACrD;AAEO,MAAM,2BAA2B,CAAC;IACvC,MAAM,QAAQ,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;IAC3B,OAAO,OAAO,kBAAkB,oBAAoB;AACtD;AAGO,MAAM,qCAAqC,CAAC;IAOjD,MAAM,EAAE,GAAG,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,WAAW,EAAE,eAAe,EAAE,GAAG;IAEnF,uCAAuC;IACvC,IAAI,kBAAkB;QACpB,OAAO;IACT;IAEA,4CAA4C;IAC5C,IAAI,OAAO,MAAM,IAAI;QACnB,OAAO;IACT;IAEA,+BAA+B;IAC/B,IAAI,gBAAgB,UAAU,oBAAoB,QAAQ;QACxD,OAAO;IACT;IAEA,mCAAmC;IACnC,IAAI,sBAAsB,YAAY;QACpC,OAAO,oBAAoB,wBAAwB;;IACrD;IAEA,IAAI,sBAAsB,YAAY;QACpC,OAAO,oBAAoB,2BAA2B;;IACxD;IAEA,uCAAuC;IACvC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/theme/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useThemeStore } from '@/lib/stores/theme-store'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface ThemeProviderProps {\n  children: React.ReactNode\n}\n\nexport default function ThemeProvider({ children }: ThemeProviderProps) {\n  const { \n    applyThemeToDOM, \n    setInterfaceMode,\n    autoThemeSwitch,\n    getRecommendedTheme,\n    setTheme,\n    currentThemeId\n  } = useThemeStore()\n  \n  const { interfaceMode, user } = useUserStore()\n\n  // Initialize theme system\n  useEffect(() => {\n    // Apply current theme to DOM\n    applyThemeToDOM()\n\n    // Sync interface mode with user store\n    setInterfaceMode(interfaceMode)\n\n    // Auto theme switching based on time\n    if (autoThemeSwitch) {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }\n\n    // Listen for system preference changes\n    const mediaQueries = [\n      window.matchMedia('(prefers-reduced-motion: reduce)'),\n      window.matchMedia('(prefers-contrast: high)'),\n      window.matchMedia('(prefers-color-scheme: dark)'),\n    ]\n\n    const handleSystemPreferenceChange = () => {\n      // Re-apply theme to respect system preferences\n      applyThemeToDOM()\n    }\n\n    mediaQueries.forEach(mq => {\n      mq.addEventListener('change', handleSystemPreferenceChange)\n    })\n\n    return () => {\n      mediaQueries.forEach(mq => {\n        mq.removeEventListener('change', handleSystemPreferenceChange)\n      })\n    }\n  }, [])\n\n  // Auto theme switching interval\n  useEffect(() => {\n    if (!autoThemeSwitch) return\n\n    const interval = setInterval(() => {\n      const recommendedTheme = getRecommendedTheme()\n      if (recommendedTheme !== currentThemeId) {\n        setTheme(recommendedTheme)\n      }\n    }, 60000) // Check every minute\n\n    return () => clearInterval(interval)\n  }, [autoThemeSwitch, currentThemeId, setTheme, getRecommendedTheme])\n\n  // Sync interface mode changes\n  useEffect(() => {\n    setInterfaceMode(interfaceMode)\n  }, [interfaceMode, setInterfaceMode])\n\n  // Apply theme changes to DOM\n  useEffect(() => {\n    applyThemeToDOM()\n  }, [currentThemeId, interfaceMode, applyThemeToDOM])\n\n  return <>{children}</>\n}\n\n// Hook for accessing theme colors in components\nexport function useThemeColors() {\n  const { colors } = useThemeStore()\n  return colors\n}\n\n// Hook for theme-aware styling\nexport function useThemeClasses() {\n  const { currentThemeId, interfaceMode } = useThemeStore()\n  \n  const getButtonClass = (variant: 'primary' | 'secondary' | 'success' | 'warning' | 'error' = 'primary') => {\n    const baseClass = 'px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2'\n    \n    const variantClasses = {\n      primary: 'bg-[var(--color-primary)] hover:bg-[var(--color-primary-hover)] active:bg-[var(--color-primary-active)] text-white focus:ring-[var(--color-focus)]',\n      secondary: 'bg-[var(--color-secondary)] hover:bg-[var(--color-secondary-hover)] active:bg-[var(--color-secondary-active)] text-white focus:ring-[var(--color-focus)]',\n      success: 'bg-[var(--color-success)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',\n      warning: 'bg-[var(--color-warning)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',\n      error: 'bg-[var(--color-error)] hover:opacity-90 text-white focus:ring-[var(--color-focus)]',\n    }\n    \n    return `${baseClass} ${variantClasses[variant]}`\n  }\n  \n  const getCardClass = () => {\n    return 'bg-[var(--color-background-secondary)] border border-[var(--color-border)] rounded-lg p-4'\n  }\n  \n  const getTextClass = (variant: 'primary' | 'secondary' | 'muted' = 'primary') => {\n    const variantClasses = {\n      primary: 'text-[var(--color-text-primary)]',\n      secondary: 'text-[var(--color-text-secondary)]',\n      muted: 'text-[var(--color-text-muted)]',\n    }\n    \n    return variantClasses[variant]\n  }\n  \n  const getBullishClass = () => {\n    return 'text-[var(--color-bullish)] bg-[var(--color-bullish-background)]'\n  }\n  \n  const getBearishClass = () => {\n    return 'text-[var(--color-bearish)] bg-[var(--color-bearish-background)]'\n  }\n  \n  const getNeutralClass = () => {\n    return 'text-[var(--color-neutral)] bg-[var(--color-neutral-background)]'\n  }\n  \n  return {\n    getButtonClass,\n    getCardClass,\n    getTextClass,\n    getBullishClass,\n    getBearishClass,\n    getNeutralClass,\n    currentThemeId,\n    interfaceMode,\n  }\n}\n\n// Component for theme-aware market condition indicators\nexport function MarketConditionBadge({ \n  condition, \n  children, \n  className = '' \n}: { \n  condition: 'bullish' | 'bearish' | 'neutral'\n  children: React.ReactNode\n  className?: string \n}) {\n  const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses()\n  \n  const conditionClasses = {\n    bullish: getBullishClass(),\n    bearish: getBearishClass(),\n    neutral: getNeutralClass(),\n  }\n  \n  return (\n    <span className={`px-2 py-1 rounded text-sm font-medium ${conditionClasses[condition]} ${className}`}>\n      {children}\n    </span>\n  )\n}\n\n// Component for theme-aware trading buttons\nexport function TradingButton({ \n  action, \n  onClick, \n  children, \n  disabled = false,\n  className = '' \n}: { \n  action: 'buy' | 'sell' | 'neutral'\n  onClick: () => void\n  children: React.ReactNode\n  disabled?: boolean\n  className?: string \n}) {\n  const { getBullishClass, getBearishClass, getNeutralClass } = useThemeClasses()\n  \n  const actionClasses = {\n    buy: getBullishClass(),\n    sell: getBearishClass(),\n    neutral: getNeutralClass(),\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      className={`px-4 py-2 rounded-lg font-bold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[var(--color-focus)] disabled:opacity-50 disabled:cursor-not-allowed ${actionClasses[action]} ${className}`}\n    >\n      {children}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,cAAc,EAAE,QAAQ,EAAsB;;IACpE,MAAM,EACJ,eAAe,EACf,gBAAgB,EAChB,eAAe,EACf,mBAAmB,EACnB,QAAQ,EACR,cAAc,EACf,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE3C,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,6BAA6B;YAC7B;YAEA,sCAAsC;YACtC,iBAAiB;YAEjB,qCAAqC;YACrC,IAAI,iBAAiB;gBACnB,MAAM,mBAAmB;gBACzB,IAAI,qBAAqB,gBAAgB;oBACvC,SAAS;gBACX;YACF;YAEA,uCAAuC;YACvC,MAAM,eAAe;gBACnB,OAAO,UAAU,CAAC;gBAClB,OAAO,UAAU,CAAC;gBAClB,OAAO,UAAU,CAAC;aACnB;YAED,MAAM;wEAA+B;oBACnC,+CAA+C;oBAC/C;gBACF;;YAEA,aAAa,OAAO;2CAAC,CAAA;oBACnB,GAAG,gBAAgB,CAAC,UAAU;gBAChC;;YAEA;2CAAO;oBACL,aAAa,OAAO;mDAAC,CAAA;4BACnB,GAAG,mBAAmB,CAAC,UAAU;wBACnC;;gBACF;;QACF;kCAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,iBAAiB;YAEtB,MAAM,WAAW;oDAAY;oBAC3B,MAAM,mBAAmB;oBACzB,IAAI,qBAAqB,gBAAgB;wBACvC,SAAS;oBACX;gBACF;mDAAG,OAAO,qBAAqB;;YAE/B;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;QAAiB;QAAgB;QAAU;KAAoB;IAEnE,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,iBAAiB;QACnB;kCAAG;QAAC;QAAe;KAAiB;IAEpC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG;QAAC;QAAgB;QAAe;KAAgB;IAEnD,qBAAO;kBAAG;;AACZ;GA5EwB;;QAQlB,yIAAA,CAAA,gBAAa;QAEe,wIAAA,CAAA,eAAY;;;KAVtB;AA+EjB,SAAS;;IACd,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAC/B,OAAO;AACT;IAHgB;;QACK,yIAAA,CAAA,gBAAa;;;AAK3B,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAEtD,MAAM,iBAAiB,CAAC,UAAqE,SAAS;QACpG,MAAM,YAAY;QAElB,MAAM,iBAAiB;YACrB,SAAS;YACT,WAAW;YACX,SAAS;YACT,SAAS;YACT,OAAO;QACT;QAEA,OAAO,GAAG,UAAU,CAAC,EAAE,cAAc,CAAC,QAAQ,EAAE;IAClD;IAEA,MAAM,eAAe;QACnB,OAAO;IACT;IAEA,MAAM,eAAe,CAAC,UAA6C,SAAS;QAC1E,MAAM,iBAAiB;YACrB,SAAS;YACT,WAAW;YACX,OAAO;QACT;QAEA,OAAO,cAAc,CAAC,QAAQ;IAChC;IAEA,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAO;IACT;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IArDgB;;QAC4B,yIAAA,CAAA,gBAAa;;;AAuDlD,SAAS,qBAAqB,EACnC,SAAS,EACT,QAAQ,EACR,YAAY,EAAE,EAKf;;IACC,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG;IAE9D,MAAM,mBAAmB;QACvB,SAAS;QACT,SAAS;QACT,SAAS;IACX;IAEA,qBACE,6LAAC;QAAK,WAAW,CAAC,sCAAsC,EAAE,gBAAgB,CAAC,UAAU,CAAC,CAAC,EAAE,WAAW;kBACjG;;;;;;AAGP;IAtBgB;;QASgD;;;MAThD;AAyBT,SAAS,cAAc,EAC5B,MAAM,EACN,OAAO,EACP,QAAQ,EACR,WAAW,KAAK,EAChB,YAAY,EAAE,EAOf;;IACC,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG;IAE9D,MAAM,gBAAgB;QACpB,KAAK;QACL,MAAM;QACN,SAAS;IACX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW,CAAC,2KAA2K,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,EAAE,WAAW;kBAE5N;;;;;;AAGP;IA9BgB;;QAagD;;;MAbhD", "debugId": null}}]}