'use client'

import { useState } from 'react'
import { useUserStore } from '@/lib/stores/user-store'
import { ScalperSprintGame } from '@/lib/game-engine/games/scalper-sprint'
import { CandleStrikeGame } from '@/lib/game-engine/games/candle-strike'
import { ChainMazeGame } from '@/lib/game-engine/games/chain-maze'
import CandleStrikeGameComponent from '@/components/games/candle-strike-game'
import ThemeSelector from '@/components/theme/theme-selector'

export default function Home() {
  const { user, isAuthenticated, interfaceMode, switchInterfaceMode, signOut } = useUserStore()
  const [currentGame, setCurrentGame] = useState<ScalperSprintGame | CandleStrikeGame | ChainMazeGame | null>(null)
  const [gameState, setGameState] = useState<any>(null)
  const [gameType, setGameType] = useState<'scalper' | 'candle' | 'chain' | null>(null)
  const [showCandleStrikeComponent, setShowCandleStrikeComponent] = useState(false)
  const [showThemeSelector, setShowThemeSelector] = useState(false)

  const startGame = async (type: 'scalper' | 'candle' | 'chain') => {
    if (type === 'candle') {
      setShowCandleStrikeComponent(true)
      setGameType(type)
      return
    }

    let game: ScalperSprintGame | ChainMazeGame

    switch (type) {
      case 'scalper':
        game = new ScalperSprintGame('beginner')
        break
      case 'chain':
        game = new ChainMazeGame('beginner')
        break
      default:
        return
    }

    setCurrentGame(game)
    setGameType(type)

    await game.start()

    // Update game state every second
    const interval = setInterval(() => {
      if (game.isGameActive()) {
        setGameState(game.getState())
      } else {
        clearInterval(interval)
        setCurrentGame(null)
        setGameState(null)
        setGameType(null)
      }
    }, 1000)
  }

  const handleCandleStrikeEnd = (score: number) => {
    setShowCandleStrikeComponent(false)
    setGameType(null)
    // Could add score handling here
    console.log('CandleStrike game ended with score:', score)
  }

  const executeTrade = async (symbol: string, side: 'buy' | 'sell') => {
    if (currentGame && gameType === 'scalper') {
      await (currentGame as ScalperSprintGame).executeTrade(symbol, side, 1)
      setGameState(currentGame.getState())
    }
  }

  const submitCandleAnswer = async (answerIndex: number) => {
    if (currentGame && gameType === 'candle') {
      const correct = await (currentGame as CandleStrikeGame).submitAnswer(answerIndex)
      setGameState(currentGame.getState())
      return correct
    }
    return false
  }

  const submitChainSolution = async (solution: any) => {
    if (currentGame && gameType === 'chain') {
      const correct = await (currentGame as ChainMazeGame).submitPuzzleSolution(solution)
      setGameState(currentGame.getState())
      return correct
    }
    return false
  }

  const isAdolescentMode = interfaceMode === 'adolescent'

  return (
    <div className={`min-h-screen ${isAdolescentMode
      ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500'
      : 'bg-gray-900 text-green-400 font-mono'
    }`}>
      {/* Header */}
      <header className={`p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`}>
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className={`text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'}
          </h1>

          <div className="flex items-center gap-4">
            {isAuthenticated && user && (
              <>
                <div className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                  {isAdolescentMode ? `👋 ${user.username}` : `USER: ${user.username.toUpperCase()}`}
                  <div className={`text-xs ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>
                    {isAdolescentMode ? `⭐ Level ${user.level}` : `LVL_${user.level}`} |
                    {isAdolescentMode ? ` 🪙 ${user.total_quest_coins}` : ` COINS_${user.total_quest_coins}`}
                  </div>
                </div>

                <button
                  onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    isAdolescentMode
                      ? 'bg-white/20 hover:bg-white/30 text-white'
                      : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'
                  }`}
                >
                  {isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'}
                </button>

                <button
                  onClick={() => setShowThemeSelector(!showThemeSelector)}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    isAdolescentMode
                      ? 'bg-purple-500/20 hover:bg-purple-500/30 text-purple-200 border border-purple-400'
                      : 'bg-purple-400/20 hover:bg-purple-400/30 text-purple-400 border border-purple-400'
                  }`}
                >
                  {isAdolescentMode ? '🎨 Themes' : 'THEMES'}
                </button>

                <button
                  onClick={signOut}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    isAdolescentMode
                      ? 'bg-red-500/20 hover:bg-red-500/30 text-red-200 border border-red-400'
                      : 'bg-red-400/20 hover:bg-red-400/30 text-red-400 border border-red-400'
                  }`}
                >
                  {isAdolescentMode ? '🚪 Logout' : 'LOGOUT'}
                </button>
              </>
            )}

            {!isAuthenticated && (
              <div className="flex gap-2">
                <a
                  href="/auth/login"
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    isAdolescentMode
                      ? 'bg-white/20 hover:bg-white/30 text-white'
                      : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'
                  }`}
                >
                  {isAdolescentMode ? '🔑 Login' : 'LOGIN'}
                </a>
                <a
                  href="/auth/register"
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    isAdolescentMode
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'
                      : 'bg-green-400 text-gray-900 hover:bg-green-300'
                  }`}
                >
                  {isAdolescentMode ? '🚀 Join Quest' : 'REGISTER'}
                </a>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {/* Welcome Section */}
        <section className={`mb-8 p-6 rounded-lg ${
          isAdolescentMode
            ? 'bg-white/10 backdrop-blur-sm text-white'
            : 'bg-gray-800 border border-green-400'
        }`}>
          <h2 className={`text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'}
          </h2>
          <p className={`text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
            {isAdolescentMode
              ? 'Embark on epic trading adventures and master the markets through exciting mini-games!'
              : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'
            }
          </p>
        </section>

        {/* Advanced Features Banner */}
        <section className={`mb-8 p-6 rounded-lg ${
          isAdolescentMode
            ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm border border-purple-400'
            : 'bg-gray-800 border border-green-400'
        }`}>
          <div className="text-center">
            <h3 className={`text-2xl font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
              {isAdolescentMode ? '🚀 NEW: Advanced Trading Features!' : '📊 ADVANCED_TRADING_SYSTEMS'}
            </h3>
            <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
              {isAdolescentMode
                ? 'Experience real-time chart playback, pattern recognition, and evidence-based color psychology themes!'
                : 'Interactive historical chart playback, real-time pattern detection, and scientifically-optimized interface themes.'
              }
            </p>
            <a
              href="/demo/playback"
              className={`inline-block px-8 py-4 rounded-lg font-bold text-lg transition-all shadow-lg ${
                isAdolescentMode
                  ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600 hover:shadow-xl'
                  : 'bg-green-400 text-gray-900 hover:bg-green-300 hover:shadow-xl'
              }`}
            >
              {isAdolescentMode ? '🎯 Explore Advanced Features' : '▶ ACCESS_ADVANCED_SYSTEMS'}
            </a>
          </div>
        </section>

        {/* Game Demo Section */}
        <section className={`mb-8 p-6 rounded-lg ${
          isAdolescentMode
            ? 'bg-white/10 backdrop-blur-sm'
            : 'bg-gray-800 border border-green-400'
        }`}>
          <h3 className={`text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {isAdolescentMode ? '🎮 Mini-Game Demos' : '📊 Trading Simulation Modules'}
          </h3>

          {!currentGame && !showCandleStrikeComponent ? (
            <div className="space-y-6">
              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                {isAdolescentMode
                  ? 'Choose your adventure! Each game teaches different trading skills.'
                  : 'Select a trading simulation module to begin training.'
                }
              </p>

              <div className="grid md:grid-cols-3 gap-4">
                {/* Scalper Sprint */}
                <div className={`p-4 rounded-lg border ${
                  isAdolescentMode
                    ? 'bg-white/5 border-white/20'
                    : 'bg-gray-700 border-green-400/50'
                }`}>
                  <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                    {isAdolescentMode ? '⚡ Scalper Sprint' : '📈 SCALPER_MODULE'}
                  </h4>
                  <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                    {isAdolescentMode
                      ? '60-second rapid trading challenge'
                      : 'High-frequency trading simulation'
                    }
                  </p>
                  <button
                    onClick={() => startGame('scalper')}
                    className={`w-full py-2 px-3 rounded text-sm font-bold transition-colors ${
                      isAdolescentMode
                        ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'
                        : 'bg-green-400 text-gray-900 hover:bg-green-300'
                    }`}
                  >
                    {isAdolescentMode ? '🚀 Start' : 'INITIALIZE'}
                  </button>
                </div>

                {/* CandleStrike */}
                <div className={`p-4 rounded-lg border ${
                  isAdolescentMode
                    ? 'bg-white/5 border-white/20'
                    : 'bg-gray-700 border-green-400/50'
                }`}>
                  <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                    {isAdolescentMode ? '🕯️ CandleStrike' : '📊 PATTERN_RECOGNITION'}
                  </h4>
                  <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                    {isAdolescentMode
                      ? 'Identify candlestick patterns'
                      : 'Technical analysis training'
                    }
                  </p>
                  <div className="space-y-2">
                    <button
                      onClick={() => startGame('candle')}
                      className={`w-full py-2 px-3 rounded text-sm font-bold transition-colors ${
                        isAdolescentMode
                          ? 'bg-gradient-to-r from-purple-400 to-pink-500 text-white hover:from-purple-500 hover:to-pink-600'
                          : 'bg-purple-400 text-gray-900 hover:bg-purple-300'
                      }`}
                    >
                      {isAdolescentMode ? '🎯 Quick Start' : 'QUICK_START'}
                    </button>
                    <a
                      href="/demo/candle-strike"
                      className={`block w-full py-2 px-3 rounded text-sm font-bold text-center transition-colors ${
                        isAdolescentMode
                          ? 'bg-white/20 hover:bg-white/30 text-white border border-white/30'
                          : 'bg-gray-700 hover:bg-gray-600 text-green-300 border border-green-400'
                      }`}
                    >
                      {isAdolescentMode ? '📊 Full Demo' : 'FULL_DEMO'}
                    </a>
                  </div>
                </div>

                {/* ChainMaze */}
                <div className={`p-4 rounded-lg border ${
                  isAdolescentMode
                    ? 'bg-white/5 border-white/20'
                    : 'bg-gray-700 border-green-400/50'
                }`}>
                  <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                    {isAdolescentMode ? '🔗 ChainMaze' : '⛓️ BLOCKCHAIN_SIM'}
                  </h4>
                  <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>
                    {isAdolescentMode
                      ? 'Navigate blockchain puzzles'
                      : 'Consensus mechanism training'
                    }
                  </p>
                  <button
                    onClick={() => startGame('chain')}
                    className={`w-full py-2 px-3 rounded text-sm font-bold transition-colors ${
                      isAdolescentMode
                        ? 'bg-gradient-to-r from-blue-400 to-cyan-500 text-white hover:from-blue-500 hover:to-cyan-600'
                        : 'bg-blue-400 text-gray-900 hover:bg-blue-300'
                    }`}
                  >
                    {isAdolescentMode ? '🧩 Start' : 'INITIALIZE'}
                  </button>
                </div>
              </div>
            </div>
          ) : showCandleStrikeComponent ? (
            <CandleStrikeGameComponent
              difficulty="beginner"
              onGameEnd={handleCandleStrikeEnd}
              className="w-full"
            />
          ) : (
            <div className="space-y-4">
              {/* Game State Display */}
              {gameState && (
                <div className={`p-4 rounded ${
                  isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'
                }`}>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                        {gameType === 'chain'
                          ? (isAdolescentMode ? 'Gas Budget' : 'GAS_BUDGET')
                          : (isAdolescentMode ? 'Balance' : 'BALANCE')
                        }
                      </div>
                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>
                        {gameType === 'chain' ? gameState.current_balance : `$${gameState.current_balance.toFixed(2)}`}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                        {isAdolescentMode ? 'Score' : 'SCORE'}
                      </div>
                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                        {gameState.score}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                        {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}
                      </div>
                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>
                        {gameState.time_remaining}s
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                        {gameType === 'scalper'
                          ? (isAdolescentMode ? 'Positions' : 'POSITIONS')
                          : gameType === 'candle'
                            ? (isAdolescentMode ? 'Patterns' : 'PATTERNS')
                            : (isAdolescentMode ? 'Puzzles' : 'PUZZLES')
                        }
                      </div>
                      <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                        {gameType === 'scalper'
                          ? gameState.positions?.length || 0
                          : gameType === 'candle'
                            ? (currentGame as CandleStrikeGame)?.getGameSpecificData()?.patterns_identified || 0
                            : (currentGame as ChainMazeGame)?.getGameSpecificData()?.puzzles_solved || 0
                        }
                      </div>
                    </div>
                  </div>

                  {/* Game-specific info */}
                  {gameType === 'candle' && (
                    <div className={`text-center mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                      <p className="text-sm">
                        {isAdolescentMode ? '🎯 Identify the candlestick pattern!' : 'IDENTIFY_PATTERN_OBJECTIVE'}
                      </p>
                      {(currentGame as CandleStrikeGame)?.getCurrentChallenge() && (
                        <div className="mt-2">
                          <p className="font-bold">
                            {(currentGame as CandleStrikeGame).getCurrentChallenge()?.pattern.description}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {gameType === 'chain' && (
                    <div className={`text-center mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                      <p className="text-sm">
                        {isAdolescentMode ? '🧩 Solve blockchain puzzles!' : 'BLOCKCHAIN_PUZZLE_OBJECTIVE'}
                      </p>
                      {(currentGame as ChainMazeGame)?.getCurrentPuzzle() && (
                        <div className="mt-2">
                          <p className="font-bold">
                            {(currentGame as ChainMazeGame).getCurrentPuzzle()?.title}
                          </p>
                          <p className="text-xs">
                            {(currentGame as ChainMazeGame).getCurrentPuzzle()?.description}
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Game Controls */}
              {gameType === 'scalper' && (
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => executeTrade('BTCUSD', 'buy')}
                    className={`p-4 rounded-lg font-bold transition-colors ${
                      isAdolescentMode
                        ? 'bg-green-500 hover:bg-green-600 text-white'
                        : 'bg-green-400 hover:bg-green-300 text-gray-900'
                    }`}
                  >
                    {isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'}
                  </button>
                  <button
                    onClick={() => executeTrade('BTCUSD', 'sell')}
                    className={`p-4 rounded-lg font-bold transition-colors ${
                      isAdolescentMode
                        ? 'bg-red-500 hover:bg-red-600 text-white'
                        : 'bg-red-400 hover:bg-red-300 text-gray-900'
                    }`}
                  >
                    {isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'}
                  </button>
                </div>
              )}

              {gameType === 'candle' && (currentGame as CandleStrikeGame)?.getCurrentChallenge() && (
                <div className="grid grid-cols-2 gap-2">
                  {(currentGame as CandleStrikeGame).getCurrentChallenge()?.options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => submitCandleAnswer(index)}
                      className={`p-3 rounded-lg font-bold transition-colors ${
                        isAdolescentMode
                          ? 'bg-purple-500 hover:bg-purple-600 text-white'
                          : 'bg-purple-400 hover:bg-purple-300 text-gray-900'
                      }`}
                    >
                      {option}
                    </button>
                  ))}
                </div>
              )}

              {gameType === 'chain' && (
                <div className="text-center">
                  <button
                    onClick={() => submitChainSolution({ selectedNode: 'validator_1', gasPrice: 20 })}
                    className={`px-6 py-3 rounded-lg font-bold transition-colors ${
                      isAdolescentMode
                        ? 'bg-blue-500 hover:bg-blue-600 text-white'
                        : 'bg-blue-400 hover:bg-blue-300 text-gray-900'
                    }`}
                  >
                    {isAdolescentMode ? '🧩 Submit Solution' : 'SUBMIT_SOLUTION'}
                  </button>
                </div>
              )}
            </div>
          )}
        </section>

        {/* Features Preview */}
        <section className="grid md:grid-cols-3 gap-6">
          {[
            {
              title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',
              description: isAdolescentMode
                ? 'Six exciting games to master different trading skills'
                : 'Comprehensive trading simulation modules',
              features: ['Scalper Sprint', 'CandleStrike', 'ChainMaze']
            },
            {
              title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',
              description: isAdolescentMode
                ? 'Unlock badges and level up your trading hero'
                : 'Advanced performance tracking and analytics',
              features: ['Progress Tracking', 'Leaderboards', 'Statistics']
            },
            {
              title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',
              description: isAdolescentMode
                ? 'Join guilds and compete with friends'
                : 'Professional networking and strategy sharing',
              features: ['Team Challenges', 'Social Features', 'Competitions']
            }
          ].map((feature, index) => (
            <div
              key={index}
              className={`p-6 rounded-lg ${
                isAdolescentMode
                  ? 'bg-white/10 backdrop-blur-sm text-white'
                  : 'bg-gray-800 border border-green-400'
              }`}
            >
              <h3 className={`text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                {feature.title}
              </h3>
              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                {feature.description}
              </p>
              <ul className={`space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`}>
                {feature.features.map((item, i) => (
                  <li key={i} className="flex items-center">
                    <span className={`mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>
                      {isAdolescentMode ? '✨' : '▶'}
                    </span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </section>

        {/* Theme Selector Modal */}
        {showThemeSelector && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto rounded-lg ${
              isAdolescentMode
                ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500 p-1'
                : 'bg-gray-800 border border-green-400'
            }`}>
              <div className={`rounded-lg p-6 ${
                isAdolescentMode
                  ? 'bg-black/80 backdrop-blur-sm'
                  : 'bg-gray-900'
              }`}>
                <div className="flex justify-between items-center mb-6">
                  <h2 className={`text-xl font-bold ${
                    isAdolescentMode ? 'text-white' : 'text-green-400'
                  }`}>
                    {isAdolescentMode ? '🎨 Theme Customization' : '🎨 INTERFACE_THEME_CONFIGURATION'}
                  </h2>
                  <button
                    onClick={() => setShowThemeSelector(false)}
                    className={`text-2xl hover:opacity-70 ${
                      isAdolescentMode ? 'text-white' : 'text-green-400'
                    }`}
                  >
                    ✕
                  </button>
                </div>

                <ThemeSelector showPsychologyInfo={true} />
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  )
}
