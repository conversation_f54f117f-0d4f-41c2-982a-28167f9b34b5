'use client'

import { useState } from 'react'
import { useUserStore } from '@/lib/stores/user-store'
import { ScalperSprintGame } from '@/lib/game-engine/games/scalper-sprint'

export default function Home() {
  const { interfaceMode, switchInterfaceMode } = useUserStore()
  const [currentGame, setCurrentGame] = useState<ScalperSprintGame | null>(null)
  const [gameState, setGameState] = useState<any>(null)

  const startScalperSprint = async () => {
    const game = new ScalperSprintGame('beginner')
    setCurrentGame(game)

    await game.start()

    // Update game state every second
    const interval = setInterval(() => {
      if (game.isGameActive()) {
        setGameState(game.getState())
      } else {
        clearInterval(interval)
        setCurrentGame(null)
        setGameState(null)
      }
    }, 1000)
  }

  const executeTrade = async (symbol: string, side: 'buy' | 'sell') => {
    if (currentGame) {
      await currentGame.executeTrade(symbol, side, 1)
      setGameState(currentGame.getState())
    }
  }

  const isAdolescentMode = interfaceMode === 'adolescent'

  return (
    <div className={`min-h-screen ${isAdolescentMode
      ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500'
      : 'bg-gray-900 text-green-400 font-mono'
    }`}>
      {/* Header */}
      <header className={`p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`}>
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <h1 className={`text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {isAdolescentMode ? '🏰 TradeQuest: Adventure Mode' : '📊 TradeQuest: Professional Terminal'}
          </h1>

          <div className="flex items-center gap-4">
            <button
              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}
              className={`px-4 py-2 rounded-lg transition-colors ${
                isAdolescentMode
                  ? 'bg-white/20 hover:bg-white/30 text-white'
                  : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'
              }`}
            >
              Switch to {isAdolescentMode ? 'Professional' : 'Adventure'} Mode
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {/* Welcome Section */}
        <section className={`mb-8 p-6 rounded-lg ${
          isAdolescentMode
            ? 'bg-white/10 backdrop-blur-sm text-white'
            : 'bg-gray-800 border border-green-400'
        }`}>
          <h2 className={`text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {isAdolescentMode ? '🎮 Welcome, Young Trader!' : '💼 Trading Terminal Active'}
          </h2>
          <p className={`text-lg ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
            {isAdolescentMode
              ? 'Embark on epic trading adventures and master the markets through exciting mini-games!'
              : 'Professional trading simulation environment. Execute trades with precision and analyze market data.'
            }
          </p>
        </section>

        {/* Game Demo Section */}
        <section className={`mb-8 p-6 rounded-lg ${
          isAdolescentMode
            ? 'bg-white/10 backdrop-blur-sm'
            : 'bg-gray-800 border border-green-400'
        }`}>
          <h3 className={`text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
            {isAdolescentMode ? '⚡ Scalper Sprint Challenge' : '📈 High-Frequency Trading Simulation'}
          </h3>

          {!currentGame ? (
            <div>
              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                {isAdolescentMode
                  ? 'Test your speed and reflexes in this 60-second trading challenge!'
                  : 'Execute rapid trades in a simulated high-frequency environment.'
                }
              </p>
              <button
                onClick={startScalperSprint}
                className={`px-6 py-3 rounded-lg font-bold transition-colors ${
                  isAdolescentMode
                    ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600'
                    : 'bg-green-400 text-gray-900 hover:bg-green-300'
                }`}
              >
                {isAdolescentMode ? '🚀 Start Adventure!' : 'INITIALIZE TRADING SESSION'}
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Game State Display */}
              {gameState && (
                <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 p-4 rounded ${
                  isAdolescentMode ? 'bg-white/20' : 'bg-gray-700 border border-green-400'
                }`}>
                  <div className="text-center">
                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                      {isAdolescentMode ? 'Quest Coins' : 'BALANCE'}
                    </div>
                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>
                      ${gameState.current_balance.toFixed(2)}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                      {isAdolescentMode ? 'Score' : 'SCORE'}
                    </div>
                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                      {gameState.score}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                      {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}
                    </div>
                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>
                      {gameState.time_remaining}s
                    </div>
                  </div>
                  <div className="text-center">
                    <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>
                      {isAdolescentMode ? 'Positions' : 'POSITIONS'}
                    </div>
                    <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                      {gameState.positions.length}
                    </div>
                  </div>
                </div>
              )}

              {/* Trading Controls */}
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={() => executeTrade('BTCUSD', 'buy')}
                  className={`p-4 rounded-lg font-bold transition-colors ${
                    isAdolescentMode
                      ? 'bg-green-500 hover:bg-green-600 text-white'
                      : 'bg-green-400 hover:bg-green-300 text-gray-900'
                  }`}
                >
                  {isAdolescentMode ? '🟢 BUY Bitcoin' : 'BUY BTC/USD'}
                </button>
                <button
                  onClick={() => executeTrade('BTCUSD', 'sell')}
                  className={`p-4 rounded-lg font-bold transition-colors ${
                    isAdolescentMode
                      ? 'bg-red-500 hover:bg-red-600 text-white'
                      : 'bg-red-400 hover:bg-red-300 text-gray-900'
                  }`}
                >
                  {isAdolescentMode ? '🔴 SELL Bitcoin' : 'SELL BTC/USD'}
                </button>
              </div>
            </div>
          )}
        </section>

        {/* Features Preview */}
        <section className="grid md:grid-cols-3 gap-6">
          {[
            {
              title: isAdolescentMode ? '🎯 Mini-Games' : '📊 Trading Modules',
              description: isAdolescentMode
                ? 'Six exciting games to master different trading skills'
                : 'Comprehensive trading simulation modules',
              features: ['Scalper Sprint', 'CandleStrike', 'ChainMaze']
            },
            {
              title: isAdolescentMode ? '🏆 Achievements' : '📈 Performance Analytics',
              description: isAdolescentMode
                ? 'Unlock badges and level up your trading hero'
                : 'Advanced performance tracking and analytics',
              features: ['Progress Tracking', 'Leaderboards', 'Statistics']
            },
            {
              title: isAdolescentMode ? '👥 Guilds' : '🤝 Social Trading',
              description: isAdolescentMode
                ? 'Join guilds and compete with friends'
                : 'Professional networking and strategy sharing',
              features: ['Team Challenges', 'Social Features', 'Competitions']
            }
          ].map((feature, index) => (
            <div
              key={index}
              className={`p-6 rounded-lg ${
                isAdolescentMode
                  ? 'bg-white/10 backdrop-blur-sm text-white'
                  : 'bg-gray-800 border border-green-400'
              }`}
            >
              <h3 className={`text-lg font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>
                {feature.title}
              </h3>
              <p className={`mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>
                {feature.description}
              </p>
              <ul className={`space-y-1 ${isAdolescentMode ? 'text-white/80' : 'text-green-200'}`}>
                {feature.features.map((item, i) => (
                  <li key={i} className="flex items-center">
                    <span className={`mr-2 ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>
                      {isAdolescentMode ? '✨' : '▶'}
                    </span>
                    {item}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </section>
      </main>
    </div>
  )
}
