{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency: string = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatPercentage(value: number, decimals: number = 2): string {\n  return `${value >= 0 ? '+' : ''}${value.toFixed(decimals)}%`\n}\n\nexport function formatNumber(value: number, decimals: number = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(value)\n}\n\nexport function formatLargeNumber(value: number): string {\n  if (value >= 1e9) {\n    return `${(value / 1e9).toFixed(1)}B`\n  }\n  if (value >= 1e6) {\n    return `${(value / 1e6).toFixed(1)}M`\n  }\n  if (value >= 1e3) {\n    return `${(value / 1e3).toFixed(1)}K`\n  }\n  return value.toString()\n}\n\nexport function calculatePnL(entryPrice: number, currentPrice: number, quantity: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  return side === 'buy' ? priceDiff * quantity : -priceDiff * quantity\n}\n\nexport function calculatePnLPercentage(entryPrice: number, currentPrice: number, side: 'buy' | 'sell'): number {\n  const priceDiff = currentPrice - entryPrice\n  const percentage = (priceDiff / entryPrice) * 100\n  return side === 'buy' ? percentage : -percentage\n}\n\nexport function generateSessionId(): string {\n  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n}\n\nexport function isMinor(age: number): boolean {\n  return age < 18\n}\n\nexport function validateAge(age: number): boolean {\n  return age >= 13 && age <= 120\n}\n\nexport function sanitizeUsername(username: string): string {\n  return username.replace(/[^a-zA-Z0-9_-]/g, '').toLowerCase()\n}\n\nexport function getTimeRemaining(endTime: Date): {\n  total: number\n  days: number\n  hours: number\n  minutes: number\n  seconds: number\n} {\n  const total = Date.parse(endTime.toString()) - Date.parse(new Date().toString())\n  const seconds = Math.floor((total / 1000) % 60)\n  const minutes = Math.floor((total / 1000 / 60) % 60)\n  const hours = Math.floor((total / (1000 * 60 * 60)) % 24)\n  const days = Math.floor(total / (1000 * 60 * 60 * 24))\n\n  return {\n    total,\n    days,\n    hours,\n    minutes,\n    seconds,\n  }\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args)\n      inThrottle = true\n      setTimeout(() => (inThrottle = false), limit)\n    }\n  }\n}\n\nexport function getRandomElement<T>(array: T[]): T {\n  return array[Math.floor(Math.random() * array.length)]\n}\n\nexport function shuffleArray<T>(array: T[]): T[] {\n  const shuffled = [...array]\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1))\n    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]\n  }\n  return shuffled\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n\nexport function generateColor(seed: string): string {\n  let hash = 0\n  for (let i = 0; i < seed.length; i++) {\n    hash = seed.charCodeAt(i) + ((hash << 5) - hash)\n  }\n  const hue = hash % 360\n  return `hsl(${hue}, 70%, 50%)`\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAmB,KAAK;IACrE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,iBAAiB,KAAa,EAAE,WAAmB,CAAC;IAClE,OAAO,GAAG,SAAS,IAAI,MAAM,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,CAAC;AAC9D;AAEO,SAAS,aAAa,KAAa,EAAE,WAAmB,CAAC;IAC9D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,kBAAkB,KAAa;IAC7C,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,SAAS,KAAK;QAChB,OAAO,GAAG,CAAC,QAAQ,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IACA,OAAO,MAAM,QAAQ;AACvB;AAEO,SAAS,aAAa,UAAkB,EAAE,YAAoB,EAAE,QAAgB,EAAE,IAAoB;IAC3G,MAAM,YAAY,eAAe;IACjC,OAAO,SAAS,QAAQ,YAAY,WAAW,CAAC,YAAY;AAC9D;AAEO,SAAS,uBAAuB,UAAkB,EAAE,YAAoB,EAAE,IAAoB;IACnG,MAAM,YAAY,eAAe;IACjC,MAAM,aAAa,AAAC,YAAY,aAAc;IAC9C,OAAO,SAAS,QAAQ,aAAa,CAAC;AACxC;AAEO,SAAS;IACd,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAC3E;AAEO,SAAS,QAAQ,GAAW;IACjC,OAAO,MAAM;AACf;AAEO,SAAS,YAAY,GAAW;IACrC,OAAO,OAAO,MAAM,OAAO;AAC7B;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,OAAO,CAAC,mBAAmB,IAAI,WAAW;AAC5D;AAEO,SAAS,iBAAiB,OAAa;IAO5C,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,QAAQ,MAAM,KAAK,KAAK,CAAC,IAAI,OAAO,QAAQ;IAC7E,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAQ;IAC5C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,QAAQ,OAAO,KAAM;IACjD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,QAAQ,CAAC,OAAO,KAAK,EAAE,IAAK;IACtD,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;IAEpD,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,iBAAoB,KAAU;IAC5C,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAEO,SAAS,aAAgB,KAAU;IACxC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC1C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IAC1D;IACA,OAAO;AACT;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,OAAO,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI;IACjD;IACA,MAAM,MAAM,OAAO;IACnB,OAAO,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC;AAChC", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/constants.ts"], "sourcesContent": ["import { GameType, TradingPair } from '@/types'\n\n// Game Configuration\nexport const GAME_CONFIGS = {\n  scalper_sprint: {\n    name: 'Scalper Sprint',\n    description: '60-second trading challenges with rapid-fire decisions',\n    difficulty: 'beginner',\n    duration_seconds: 60,\n    starting_balance: 10000,\n    min_trade_size: 100,\n    max_positions: 3,\n    quest_coins_base: 50,\n  },\n  candle_strike: {\n    name: 'CandleStrike',\n    description: 'Pattern recognition game with candlestick charts',\n    difficulty: 'beginner',\n    duration_seconds: 120,\n    starting_balance: 0, // Pattern recognition, no trading\n    patterns_to_identify: 5,\n    quest_coins_base: 75,\n  },\n  chain_maze: {\n    name: 'ChainMaze',\n    description: 'Navigate blockchain puzzles and learn consensus mechanisms',\n    difficulty: 'intermediate',\n    duration_seconds: 300,\n    starting_balance: 1000, // Gas fees simulation\n    puzzles_to_solve: 3,\n    quest_coins_base: 100,\n  },\n  swing_trader_odyssey: {\n    name: \"Swing Trader's Odyssey\",\n    description: 'Multi-day position management with risk/reward balancing',\n    difficulty: 'intermediate',\n    duration_seconds: 600, // 10 minutes simulating days\n    starting_balance: 50000,\n    max_positions: 5,\n    quest_coins_base: 150,\n  },\n  day_trader_arena: {\n    name: 'Day Trader Arena',\n    description: 'Real-time multiplayer trading competitions',\n    difficulty: 'advanced',\n    duration_seconds: 900, // 15 minutes\n    starting_balance: 100000,\n    max_positions: 10,\n    quest_coins_base: 200,\n  },\n  portfolio_survivor: {\n    name: 'Portfolio Survivor',\n    description: 'Crisis management with diversification challenges',\n    difficulty: 'advanced',\n    duration_seconds: 1200, // 20 minutes\n    starting_balance: 500000,\n    max_positions: 20,\n    quest_coins_base: 300,\n  },\n} as const\n\n// Trading Pairs\nexport const TRADING_PAIRS: TradingPair[] = [\n  { base: 'BTC', quote: 'USD', symbol: 'BTCUSD', exchange: 'virtual' },\n  { base: 'ETH', quote: 'USD', symbol: 'ETHUSD', exchange: 'virtual' },\n  { base: 'ADA', quote: 'USD', symbol: 'ADAUSD', exchange: 'virtual' },\n  { base: 'SOL', quote: 'USD', symbol: 'SOLUSD', exchange: 'virtual' },\n  { base: 'AAPL', quote: 'USD', symbol: 'AAPL', exchange: 'virtual' },\n  { base: 'GOOGL', quote: 'USD', symbol: 'GOOGL', exchange: 'virtual' },\n  { base: 'TSLA', quote: 'USD', symbol: 'TSLA', exchange: 'virtual' },\n  { base: 'EUR', quote: 'USD', symbol: 'EURUSD', exchange: 'virtual' },\n  { base: 'GBP', quote: 'USD', symbol: 'GBPUSD', exchange: 'virtual' },\n  { base: 'JPY', quote: 'USD', symbol: 'JPYUSD', exchange: 'virtual' },\n]\n\n// Achievement Categories and Points\nexport const ACHIEVEMENT_CATEGORIES = {\n  trading: {\n    name: 'Trading Mastery',\n    color: '#10B981',\n    icon: '📈',\n  },\n  learning: {\n    name: 'Knowledge Seeker',\n    color: '#3B82F6',\n    icon: '🎓',\n  },\n  social: {\n    name: 'Community Builder',\n    color: '#8B5CF6',\n    icon: '👥',\n  },\n  special: {\n    name: 'Special Events',\n    color: '#F59E0B',\n    icon: '⭐',\n  },\n} as const\n\n// Level System\nexport const LEVEL_THRESHOLDS = [\n  0, 100, 250, 500, 1000, 1750, 2750, 4000, 5500, 7500, 10000,\n  13000, 16500, 20500, 25000, 30000, 35500, 41500, 48000, 55000, 62500,\n]\n\nexport const QUEST_COIN_MULTIPLIERS = {\n  beginner: 1.0,\n  intermediate: 1.5,\n  advanced: 2.0,\n} as const\n\n// UI Constants\nexport const INTERFACE_MODES = {\n  adolescent: {\n    name: 'Adventure Mode',\n    description: 'Fantasy-themed interface with quests and adventures',\n    primaryColor: '#8B5CF6',\n    secondaryColor: '#EC4899',\n    fontFamily: 'fantasy',\n  },\n  adult: {\n    name: 'Professional Mode',\n    description: 'Bloomberg Terminal-style professional interface',\n    primaryColor: '#1F2937',\n    secondaryColor: '#3B82F6',\n    fontFamily: 'monospace',\n  },\n} as const\n\n// Market Data Update Intervals\nexport const UPDATE_INTERVALS = {\n  real_time: 1000, // 1 second\n  fast: 5000, // 5 seconds\n  normal: 15000, // 15 seconds\n  slow: 60000, // 1 minute\n} as const\n\n// API Endpoints\nexport const API_ENDPOINTS = {\n  coingecko: {\n    base: 'https://api.coingecko.com/api/v3',\n    prices: '/simple/price',\n    history: '/coins/{id}/market_chart',\n  },\n  alpha_vantage: {\n    base: 'https://www.alphavantage.co/query',\n    intraday: '?function=TIME_SERIES_INTRADAY',\n    forex: '?function=FX_INTRADAY',\n  },\n} as const\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  username: {\n    minLength: 3,\n    maxLength: 20,\n    pattern: /^[a-zA-Z0-9_-]+$/,\n  },\n  age: {\n    min: 13,\n    max: 120,\n  },\n  trade: {\n    minAmount: 1,\n    maxAmount: 1000000,\n  },\n} as const\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  auth: {\n    invalid_credentials: 'Invalid email or password',\n    user_not_found: 'User not found',\n    email_already_exists: 'Email already registered',\n    weak_password: 'Password must be at least 8 characters',\n    age_verification_failed: 'Age verification required',\n  },\n  game: {\n    session_expired: 'Game session has expired',\n    invalid_trade: 'Invalid trade parameters',\n    insufficient_balance: 'Insufficient balance for this trade',\n    max_positions_reached: 'Maximum number of positions reached',\n  },\n  general: {\n    network_error: 'Network error, please try again',\n    server_error: 'Server error, please try again later',\n    validation_error: 'Please check your input and try again',\n  },\n} as const\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  auth: {\n    registration_complete: 'Account created successfully!',\n    login_success: 'Welcome back!',\n    logout_success: 'Logged out successfully',\n  },\n  game: {\n    session_complete: 'Game session completed!',\n    achievement_unlocked: 'Achievement unlocked!',\n    level_up: 'Level up! Congratulations!',\n  },\n  general: {\n    save_success: 'Changes saved successfully',\n    update_success: 'Updated successfully',\n  },\n} as const\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,MAAM,eAAe;IAC1B,gBAAgB;QACd,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,kBAAkB;IACpB;IACA,eAAe;QACb,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,sBAAsB;QACtB,kBAAkB;IACpB;IACA,YAAY;QACV,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;IACpB;IACA,sBAAsB;QACpB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,kBAAkB;QAChB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;IACA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;IACpB;AACF;AAGO,MAAM,gBAA+B;IAC1C;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAS,OAAO;QAAO,QAAQ;QAAS,UAAU;IAAU;IACpE;QAAE,MAAM;QAAQ,OAAO;QAAO,QAAQ;QAAQ,UAAU;IAAU;IAClE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;IACnE;QAAE,MAAM;QAAO,OAAO;QAAO,QAAQ;QAAU,UAAU;IAAU;CACpE;AAGM,MAAM,yBAAyB;IACpC,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,OAAO;QACP,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;IACR;AACF;AAGO,MAAM,mBAAmB;IAC9B;IAAG;IAAK;IAAK;IAAK;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACtD;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;IAAO;CAChE;AAEM,MAAM,yBAAyB;IACpC,UAAU;IACV,cAAc;IACd,UAAU;AACZ;AAGO,MAAM,kBAAkB;IAC7B,YAAY;QACV,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;IACA,OAAO;QACL,MAAM;QACN,aAAa;QACb,cAAc;QACd,gBAAgB;QAChB,YAAY;IACd;AACF;AAGO,MAAM,mBAAmB;IAC9B,WAAW;IACX,MAAM;IACN,QAAQ;IACR,MAAM;AACR;AAGO,MAAM,gBAAgB;IAC3B,WAAW;QACT,MAAM;QACN,QAAQ;QACR,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB;IAC9B,UAAU;QACR,WAAW;QACX,WAAW;QACX,SAAS;IACX;IACA,KAAK;QACH,KAAK;QACL,KAAK;IACP;IACA,OAAO;QACL,WAAW;QACX,WAAW;IACb;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM;QACJ,qBAAqB;QACrB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,yBAAyB;IAC3B;IACA,MAAM;QACJ,iBAAiB;QACjB,eAAe;QACf,sBAAsB;QACtB,uBAAuB;IACzB;IACA,SAAS;QACP,eAAe;QACf,cAAc;QACd,kBAAkB;IACpB;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM;QACJ,uBAAuB;QACvB,eAAe;QACf,gBAAgB;IAClB;IACA,MAAM;QACJ,kBAAkB;QAClB,sBAAsB;QACtB,UAAU;IACZ;IACA,SAAS;QACP,cAAc;QACd,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/services/market-data.ts"], "sourcesContent": ["import axios from 'axios'\nimport { MarketData, CandlestickData } from '@/types'\nimport { API_ENDPOINTS } from '@/lib/constants'\n\nclass MarketDataService {\n  private coingeckoClient: any\n  private alphaVantageClient: any\n\n  constructor() {\n    this.coingeckoClient = axios.create({\n      baseURL: API_ENDPOINTS.coingecko.base,\n      timeout: 10000,\n    })\n\n    this.alphaVantageClient = axios.create({\n      baseURL: API_ENDPOINTS.alpha_vantage.base,\n      timeout: 10000,\n    })\n  }\n\n  // Cryptocurrency data from CoinGecko\n  async getCryptoPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const ids = symbols.map(symbol => this.symbolToCoinGeckoId(symbol)).join(',')\n      const response = await this.coingeckoClient.get(API_ENDPOINTS.coingecko.prices, {\n        params: {\n          ids,\n          vs_currencies: 'usd',\n          include_24hr_change: true,\n          include_24hr_vol: true,\n          include_market_cap: true,\n        },\n      })\n\n      return this.formatCoinGeckoResponse(response.data, symbols)\n    } catch (error) {\n      console.error('Error fetching crypto prices:', error)\n      return this.generateMockCryptoData(symbols)\n    }\n  }\n\n  // Stock data from Alpha Vantage\n  async getStockPrices(symbols: string[]): Promise<MarketData[]> {\n    try {\n      const promises = symbols.map(symbol => this.fetchStockPrice(symbol))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching stock prices:', error)\n      return this.generateMockStockData(symbols)\n    }\n  }\n\n  // Forex data from Alpha Vantage\n  async getForexPrices(pairs: string[]): Promise<MarketData[]> {\n    try {\n      const promises = pairs.map(pair => this.fetchForexPrice(pair))\n      const results = await Promise.all(promises)\n      return results.filter(Boolean) as MarketData[]\n    } catch (error) {\n      console.error('Error fetching forex prices:', error)\n      return this.generateMockForexData(pairs)\n    }\n  }\n\n  // Historical candlestick data with enhanced pattern detection\n  async getCandlestickData(symbol: string, interval: string = '1h', days: number = 7): Promise<CandlestickData[]> {\n    try {\n      if (this.isCryptoSymbol(symbol)) {\n        return await this.getCryptoCandlestickData(symbol, days)\n      } else {\n        return await this.getStockCandlestickData(symbol, interval)\n      }\n    } catch (error) {\n      console.error('Error fetching candlestick data:', error)\n      return this.generateMockCandlestickData(symbol, 168) // 7 days of hourly data\n    }\n  }\n\n  // Get historical data with specific patterns for educational purposes\n  async getHistoricalDataWithPatterns(symbol: string, patternType: string, count: number = 10): Promise<CandlestickData[][]> {\n    try {\n      // For demo purposes, we'll use a combination of real data and pattern-enhanced data\n      const baseData = await this.getCandlestickData(symbol, '1h', 30) // 30 days of data\n\n      // Find or create segments with the requested pattern\n      return this.extractPatternSegments(baseData, patternType, count)\n    } catch (error) {\n      console.error('Error fetching pattern data:', error)\n      return this.generatePatternDatasets(symbol, patternType, count)\n    }\n  }\n\n  // TradingView-style data format\n  async getTradingViewData(symbol: string, resolution: string = '60', from: number, to: number): Promise<{\n    s: string\n    t: number[]\n    o: number[]\n    h: number[]\n    l: number[]\n    c: number[]\n    v: number[]\n  }> {\n    try {\n      const data = await this.getCandlestickData(symbol, '1h', 7)\n\n      return {\n        s: 'ok',\n        t: data.map(d => Math.floor(d.timestamp / 1000)),\n        o: data.map(d => d.open),\n        h: data.map(d => d.high),\n        l: data.map(d => d.low),\n        c: data.map(d => d.close),\n        v: data.map(d => d.volume),\n      }\n    } catch (error) {\n      return {\n        s: 'error',\n        t: [],\n        o: [],\n        h: [],\n        l: [],\n        c: [],\n        v: [],\n      }\n    }\n  }\n\n  // Private helper methods\n  private async fetchStockPrice(symbol: string): Promise<MarketData | null> {\n    try {\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'GLOBAL_QUOTE',\n          symbol,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const quote = response.data['Global Quote']\n      if (!quote) return null\n\n      return {\n        symbol,\n        price: parseFloat(quote['05. price']),\n        change_24h: parseFloat(quote['09. change']),\n        change_percentage_24h: parseFloat(quote['10. change percent'].replace('%', '')),\n        volume_24h: parseFloat(quote['06. volume']),\n        timestamp: new Date().toISOString(),\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async fetchForexPrice(pair: string): Promise<MarketData | null> {\n    try {\n      const [from, to] = pair.split('/')\n      const response = await this.alphaVantageClient.get('', {\n        params: {\n          function: 'CURRENCY_EXCHANGE_RATE',\n          from_currency: from,\n          to_currency: to,\n          apikey: process.env.ALPHA_VANTAGE_API_KEY,\n        },\n      })\n\n      const rate = response.data['Realtime Currency Exchange Rate']\n      if (!rate) return null\n\n      return {\n        symbol: pair,\n        price: parseFloat(rate['5. Exchange Rate']),\n        change_24h: 0, // Alpha Vantage doesn't provide 24h change for forex\n        change_percentage_24h: 0,\n        volume_24h: 0,\n        timestamp: rate['6. Last Refreshed'],\n      }\n    } catch (error) {\n      return null\n    }\n  }\n\n  private async getCryptoCandlestickData(symbol: string, days: number): Promise<CandlestickData[]> {\n    const id = this.symbolToCoinGeckoId(symbol)\n    const response = await this.coingeckoClient.get(`/coins/${id}/market_chart`, {\n      params: {\n        vs_currency: 'usd',\n        days,\n        interval: 'hourly',\n      },\n    })\n\n    const prices = response.data.prices\n    const volumes = response.data.total_volumes\n\n    return prices.map((price: [number, number], index: number) => ({\n      timestamp: price[0],\n      open: index > 0 ? prices[index - 1][1] : price[1],\n      high: price[1] * (1 + Math.random() * 0.02), // Simulate high\n      low: price[1] * (1 - Math.random() * 0.02), // Simulate low\n      close: price[1],\n      volume: volumes[index] ? volumes[index][1] : 0,\n    }))\n  }\n\n  private async getStockCandlestickData(symbol: string, interval: string): Promise<CandlestickData[]> {\n    const response = await this.alphaVantageClient.get('', {\n      params: {\n        function: 'TIME_SERIES_INTRADAY',\n        symbol,\n        interval,\n        apikey: process.env.ALPHA_VANTAGE_API_KEY,\n      },\n    })\n\n    const timeSeries = response.data[`Time Series (${interval})`]\n    if (!timeSeries) return []\n\n    return Object.entries(timeSeries).map(([timestamp, data]: [string, any]) => ({\n      timestamp: new Date(timestamp).getTime(),\n      open: parseFloat(data['1. open']),\n      high: parseFloat(data['2. high']),\n      low: parseFloat(data['3. low']),\n      close: parseFloat(data['4. close']),\n      volume: parseFloat(data['5. volume']),\n    }))\n  }\n\n  private symbolToCoinGeckoId(symbol: string): string {\n    const mapping: Record<string, string> = {\n      BTC: 'bitcoin',\n      ETH: 'ethereum',\n      ADA: 'cardano',\n      SOL: 'solana',\n      DOT: 'polkadot',\n      LINK: 'chainlink',\n      UNI: 'uniswap',\n      MATIC: 'polygon',\n    }\n    return mapping[symbol.toUpperCase()] || symbol.toLowerCase()\n  }\n\n  private isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.includes(symbol.toUpperCase())\n  }\n\n  private formatCoinGeckoResponse(data: any, symbols: string[]): MarketData[] {\n    return symbols.map(symbol => {\n      const id = this.symbolToCoinGeckoId(symbol)\n      const coinData = data[id]\n      \n      if (!coinData) return this.generateMockCryptoData([symbol])[0]\n\n      return {\n        symbol,\n        price: coinData.usd,\n        change_24h: coinData.usd_24h_change || 0,\n        change_percentage_24h: coinData.usd_24h_change || 0,\n        volume_24h: coinData.usd_24h_vol || 0,\n        market_cap: coinData.usd_market_cap,\n        timestamp: new Date().toISOString(),\n      }\n    })\n  }\n\n  // Mock data generators for development and fallback\n  private generateMockCryptoData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      BTC: 45000,\n      ETH: 3000,\n      ADA: 0.5,\n      SOL: 100,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 1000,\n      change_percentage_24h: (Math.random() - 0.5) * 10,\n      volume_24h: Math.random() * 1000000000,\n      market_cap: Math.random() * 100000000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockStockData(symbols: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      AAPL: 150,\n      GOOGL: 2500,\n      TSLA: 800,\n      MSFT: 300,\n    }\n\n    return symbols.map(symbol => ({\n      symbol,\n      price: (basePrices[symbol] || 100) * (0.95 + Math.random() * 0.1),\n      change_24h: (Math.random() - 0.5) * 20,\n      change_percentage_24h: (Math.random() - 0.5) * 5,\n      volume_24h: Math.random() * 100000000,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockForexData(pairs: string[]): MarketData[] {\n    const basePrices: Record<string, number> = {\n      'EUR/USD': 1.1,\n      'GBP/USD': 1.3,\n      'USD/JPY': 110,\n      'USD/CHF': 0.9,\n    }\n\n    return pairs.map(pair => ({\n      symbol: pair,\n      price: (basePrices[pair] || 1) * (0.99 + Math.random() * 0.02),\n      change_24h: (Math.random() - 0.5) * 0.01,\n      change_percentage_24h: (Math.random() - 0.5) * 1,\n      volume_24h: 0,\n      timestamp: new Date().toISOString(),\n    }))\n  }\n\n  private generateMockCandlestickData(symbol: string, count: number): CandlestickData[] {\n    const data: CandlestickData[] = []\n    let price = 100 + Math.random() * 900\n    const now = Date.now()\n\n    for (let i = 0; i < count; i++) {\n      const timestamp = now - (count - i) * 3600000 // Hourly intervals\n      const change = (Math.random() - 0.5) * 10\n      const open = price\n      const close = price + change\n      const high = Math.max(open, close) + Math.random() * 5\n      const low = Math.min(open, close) - Math.random() * 5\n      const volume = Math.random() * 1000000\n\n      data.push({\n        timestamp,\n        open,\n        high,\n        low,\n        close,\n        volume,\n      })\n\n      price = close\n    }\n\n    return data\n  }\n\n  // Extract segments containing specific patterns from real data\n  extractPatternSegments(data: CandlestickData[], patternType: string, count: number): CandlestickData[][] {\n    const segments: CandlestickData[][] = []\n    const segmentLength = 20 // 20 candles per segment\n\n    // Scan through data looking for patterns\n    for (let i = 0; i <= data.length - segmentLength && segments.length < count; i++) {\n      const segment = data.slice(i, i + segmentLength)\n\n      if (this.containsPattern(segment, patternType)) {\n        segments.push(segment)\n        i += segmentLength - 1 // Skip ahead to avoid overlapping segments\n      }\n    }\n\n    // If we don't have enough real patterns, generate some\n    while (segments.length < count) {\n      segments.push(this.generateSegmentWithPattern(patternType, segmentLength))\n    }\n\n    return segments\n  }\n\n  // Check if a segment contains a specific pattern\n  private containsPattern(segment: CandlestickData[], patternType: string): boolean {\n    switch (patternType) {\n      case 'hammer':\n        return this.detectHammer(segment)\n      case 'doji':\n        return this.detectDoji(segment)\n      case 'engulfing_bullish':\n        return this.detectBullishEngulfing(segment)\n      case 'engulfing_bearish':\n        return this.detectBearishEngulfing(segment)\n      case 'morning_star':\n        return this.detectMorningStar(segment)\n      case 'evening_star':\n        return this.detectEveningStar(segment)\n      default:\n        return false\n    }\n  }\n\n  // Pattern detection algorithms\n  private detectHammer(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length - 1; i++) {\n      const candle = segment[i]\n      const bodySize = Math.abs(candle.close - candle.open)\n      const lowerShadow = Math.min(candle.open, candle.close) - candle.low\n      const upperShadow = candle.high - Math.max(candle.open, candle.close)\n      const totalRange = candle.high - candle.low\n\n      // Hammer criteria: small body, long lower shadow, small upper shadow\n      if (bodySize < totalRange * 0.3 &&\n          lowerShadow > bodySize * 2 &&\n          upperShadow < bodySize * 0.5) {\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectDoji(segment: CandlestickData[]): boolean {\n    for (let i = 0; i < segment.length; i++) {\n      const candle = segment[i]\n      const bodySize = Math.abs(candle.close - candle.open)\n      const totalRange = candle.high - candle.low\n\n      // Doji criteria: very small body relative to total range\n      if (bodySize < totalRange * 0.1 && totalRange > 0) {\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectBullishEngulfing(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length; i++) {\n      const prev = segment[i - 1]\n      const curr = segment[i]\n\n      // Previous candle is bearish, current is bullish and engulfs previous\n      if (prev.close < prev.open && // Previous bearish\n          curr.close > curr.open && // Current bullish\n          curr.open < prev.close && // Current opens below previous close\n          curr.close > prev.open) { // Current closes above previous open\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectBearishEngulfing(segment: CandlestickData[]): boolean {\n    for (let i = 1; i < segment.length; i++) {\n      const prev = segment[i - 1]\n      const curr = segment[i]\n\n      // Previous candle is bearish, current is bullish and engulfs previous\n      if (prev.close > prev.open && // Previous bullish\n          curr.close < curr.open && // Current bearish\n          curr.open > prev.close && // Current opens above previous close\n          curr.close < prev.open) { // Current closes below previous open\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectMorningStar(segment: CandlestickData[]): boolean {\n    for (let i = 2; i < segment.length; i++) {\n      const first = segment[i - 2]\n      const second = segment[i - 1]\n      const third = segment[i]\n\n      // Three candle pattern: bearish, small body, bullish\n      if (first.close < first.open && // First bearish\n          Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small\n          third.close > third.open && // Third bullish\n          third.close > (first.open + first.close) / 2) { // Third closes above midpoint of first\n        return true\n      }\n    }\n    return false\n  }\n\n  private detectEveningStar(segment: CandlestickData[]): boolean {\n    for (let i = 2; i < segment.length; i++) {\n      const first = segment[i - 2]\n      const second = segment[i - 1]\n      const third = segment[i]\n\n      // Three candle pattern: bullish, small body, bearish\n      if (first.close > first.open && // First bullish\n          Math.abs(second.close - second.open) < Math.abs(first.close - first.open) * 0.5 && // Second small\n          third.close < third.open && // Third bearish\n          third.close < (first.open + first.close) / 2) { // Third closes below midpoint of first\n        return true\n      }\n    }\n    return false\n  }\n\n  // Generate a segment with a specific pattern\n  private generateSegmentWithPattern(patternType: string, length: number): CandlestickData[] {\n    const segment: CandlestickData[] = []\n    let currentPrice = 100 + Math.random() * 50\n    const now = Date.now()\n\n    // Generate leading candles\n    const patternPosition = Math.floor(length * 0.4) + Math.floor(Math.random() * Math.floor(length * 0.3))\n\n    for (let i = 0; i < patternPosition; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i, now)\n      segment.push(candle)\n      currentPrice = candle.close\n    }\n\n    // Generate pattern candles\n    const patternCandles = this.generateSpecificPattern(patternType, currentPrice, patternPosition, now)\n    segment.push(...patternCandles)\n    currentPrice = patternCandles[patternCandles.length - 1].close\n\n    // Generate trailing candles\n    for (let i = patternPosition + patternCandles.length; i < length; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i, now)\n      segment.push(candle)\n      currentPrice = candle.close\n    }\n\n    return segment\n  }\n\n  private generateSpecificPattern(patternType: string, startPrice: number, startIndex: number, baseTime: number): CandlestickData[] {\n    switch (patternType) {\n      case 'hammer':\n        return this.generateHammerCandle(startPrice, startIndex, baseTime)\n      case 'doji':\n        return this.generateDojiCandle(startPrice, startIndex, baseTime)\n      case 'engulfing_bullish':\n        return this.generateBullishEngulfingPattern(startPrice, startIndex, baseTime)\n      case 'engulfing_bearish':\n        return this.generateBearishEngulfingPattern(startPrice, startIndex, baseTime)\n      case 'morning_star':\n        return this.generateMorningStarPattern(startPrice, startIndex, baseTime)\n      case 'evening_star':\n        return this.generateEveningStarPattern(startPrice, startIndex, baseTime)\n      default:\n        return this.generateHammerCandle(startPrice, startIndex, baseTime)\n    }\n  }\n}\n\nexport const marketDataService = new MarketDataService()\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAEA,MAAM;IACI,gBAAoB;IACpB,mBAAuB;IAE/B,aAAc;QACZ,IAAI,CAAC,eAAe,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YAClC,SAAS,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,IAAI;YACrC,SAAS;QACX;QAEA,IAAI,CAAC,kBAAkB,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACrC,SAAS,uHAAA,CAAA,gBAAa,CAAC,aAAa,CAAC,IAAI;YACzC,SAAS;QACX;IACF;IAEA,qCAAqC;IACrC,MAAM,gBAAgB,OAAiB,EAAyB;QAC9D,IAAI;YACF,MAAM,MAAM,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,mBAAmB,CAAC,SAAS,IAAI,CAAC;YACzE,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,uHAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,MAAM,EAAE;gBAC9E,QAAQ;oBACN;oBACA,eAAe;oBACf,qBAAqB;oBACrB,kBAAkB;oBAClB,oBAAoB;gBACtB;YACF;YAEA,OAAO,IAAI,CAAC,uBAAuB,CAAC,SAAS,IAAI,EAAE;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACrC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,OAAiB,EAAyB;QAC7D,IAAI;YACF,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,eAAe,CAAC;YAC5D,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,KAAe,EAAyB;QAC3D,IAAI;YACF,MAAM,WAAW,MAAM,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,eAAe,CAAC;YACxD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;YAClC,OAAO,QAAQ,MAAM,CAAC;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACpC;IACF;IAEA,8DAA8D;IAC9D,MAAM,mBAAmB,MAAc,EAAE,WAAmB,IAAI,EAAE,OAAe,CAAC,EAA8B;QAC9G,IAAI;YACF,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS;gBAC/B,OAAO,MAAM,IAAI,CAAC,wBAAwB,CAAC,QAAQ;YACrD,OAAO;gBACL,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,QAAQ;YACpD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,IAAI,CAAC,2BAA2B,CAAC,QAAQ,KAAK,wBAAwB;;QAC/E;IACF;IAEA,sEAAsE;IACtE,MAAM,8BAA8B,MAAc,EAAE,WAAmB,EAAE,QAAgB,EAAE,EAAgC;QACzH,IAAI;YACF,oFAAoF;YACpF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,MAAM,IAAI,kBAAkB;;YAEnF,qDAAqD;YACrD,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU,aAAa;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ,aAAa;QAC3D;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAmB,MAAc,EAAE,aAAqB,IAAI,EAAE,IAAY,EAAE,EAAU,EAQzF;QACD,IAAI;YACF,MAAM,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,MAAM;YAEzD,OAAO;gBACL,GAAG;gBACH,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,KAAK,KAAK,CAAC,EAAE,SAAS,GAAG;gBAC1C,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACvB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;gBACvB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;gBACtB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;gBACxB,GAAG,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,GAAG;gBACH,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;gBACL,GAAG,EAAE;YACP;QACF;IACF;IAEA,yBAAyB;IACzB,MAAc,gBAAgB,MAAc,EAA8B;QACxE,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV;oBACA,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,QAAQ,SAAS,IAAI,CAAC,eAAe;YAC3C,IAAI,CAAC,OAAO,OAAO;YAEnB,OAAO;gBACL;gBACA,OAAO,WAAW,KAAK,CAAC,YAAY;gBACpC,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,uBAAuB,WAAW,KAAK,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK;gBAC3E,YAAY,WAAW,KAAK,CAAC,aAAa;gBAC1C,WAAW,IAAI,OAAO,WAAW;YACnC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,gBAAgB,IAAY,EAA8B;QACtE,IAAI;YACF,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC;YAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;gBACrD,QAAQ;oBACN,UAAU;oBACV,eAAe;oBACf,aAAa;oBACb,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;gBAC3C;YACF;YAEA,MAAM,OAAO,SAAS,IAAI,CAAC,kCAAkC;YAC7D,IAAI,CAAC,MAAM,OAAO;YAElB,OAAO;gBACL,QAAQ;gBACR,OAAO,WAAW,IAAI,CAAC,mBAAmB;gBAC1C,YAAY;gBACZ,uBAAuB;gBACvB,YAAY;gBACZ,WAAW,IAAI,CAAC,oBAAoB;YACtC;QACF,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAc,yBAAyB,MAAc,EAAE,IAAY,EAA8B;QAC/F,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;QACpC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,aAAa,CAAC,EAAE;YAC3E,QAAQ;gBACN,aAAa;gBACb;gBACA,UAAU;YACZ;QACF;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,MAAM;QACnC,MAAM,UAAU,SAAS,IAAI,CAAC,aAAa;QAE3C,OAAO,OAAO,GAAG,CAAC,CAAC,OAAyB,QAAkB,CAAC;gBAC7D,WAAW,KAAK,CAAC,EAAE;gBACnB,MAAM,QAAQ,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;gBACjD,MAAM,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBAC1C,KAAK,KAAK,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,MAAM,KAAK,IAAI;gBACzC,OAAO,KAAK,CAAC,EAAE;gBACf,QAAQ,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,GAAG;YAC/C,CAAC;IACH;IAEA,MAAc,wBAAwB,MAAc,EAAE,QAAgB,EAA8B;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI;YACrD,QAAQ;gBACN,UAAU;gBACV;gBACA;gBACA,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;YAC3C;QACF;QAEA,MAAM,aAAa,SAAS,IAAI,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,OAAO,EAAE;QAE1B,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,WAAW,KAAoB,GAAK,CAAC;gBAC3E,WAAW,IAAI,KAAK,WAAW,OAAO;gBACtC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,MAAM,WAAW,IAAI,CAAC,UAAU;gBAChC,KAAK,WAAW,IAAI,CAAC,SAAS;gBAC9B,OAAO,WAAW,IAAI,CAAC,WAAW;gBAClC,QAAQ,WAAW,IAAI,CAAC,YAAY;YACtC,CAAC;IACH;IAEQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,UAAkC;YACtC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,MAAM;YACN,KAAK;YACL,OAAO;QACT;QACA,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI,OAAO,WAAW;IAC5D;IAEQ,eAAe,MAAc,EAAW;QAC9C,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,QAAQ,CAAC,OAAO,WAAW;IAClD;IAEQ,wBAAwB,IAAS,EAAE,OAAiB,EAAgB;QAC1E,OAAO,QAAQ,GAAG,CAAC,CAAA;YACjB,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC;YACpC,MAAM,WAAW,IAAI,CAAC,GAAG;YAEzB,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBAAC;aAAO,CAAC,CAAC,EAAE;YAE9D,OAAO;gBACL;gBACA,OAAO,SAAS,GAAG;gBACnB,YAAY,SAAS,cAAc,IAAI;gBACvC,uBAAuB,SAAS,cAAc,IAAI;gBAClD,YAAY,SAAS,WAAW,IAAI;gBACpC,YAAY,SAAS,cAAc;gBACnC,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;IACF;IAEA,oDAAoD;IAC5C,uBAAuB,OAAiB,EAAgB;QAC9D,MAAM,aAAqC;YACzC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,OAAiB,EAAgB;QAC7D,MAAM,aAAqC;YACzC,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;QAEA,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC5B;gBACA,OAAO,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,GAAG;gBAChE,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY,KAAK,MAAM,KAAK;gBAC5B,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,sBAAsB,KAAe,EAAgB;QAC3D,MAAM,aAAqC;YACzC,WAAW;YACX,WAAW;YACX,WAAW;YACX,WAAW;QACb;QAEA,OAAO,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACxB,QAAQ;gBACR,OAAO,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI;gBAC7D,YAAY,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpC,uBAAuB,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC/C,YAAY;gBACZ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;IACH;IAEQ,4BAA4B,MAAc,EAAE,KAAa,EAAqB;QACpF,MAAM,OAA0B,EAAE;QAClC,IAAI,QAAQ,MAAM,KAAK,MAAM,KAAK;QAClC,MAAM,MAAM,KAAK,GAAG;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,YAAY,MAAM,CAAC,QAAQ,CAAC,IAAI,QAAQ,mBAAmB;;YACjE,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;YACvC,MAAM,OAAO;YACb,MAAM,QAAQ,QAAQ;YACtB,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACrD,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK;YACpD,MAAM,SAAS,KAAK,MAAM,KAAK;YAE/B,KAAK,IAAI,CAAC;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,QAAQ;QACV;QAEA,OAAO;IACT;IAEA,+DAA+D;IAC/D,uBAAuB,IAAuB,EAAE,WAAmB,EAAE,KAAa,EAAuB;QACvG,MAAM,WAAgC,EAAE;QACxC,MAAM,gBAAgB,GAAG,yBAAyB;;QAElD,yCAAyC;QACzC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,GAAG,iBAAiB,SAAS,MAAM,GAAG,OAAO,IAAK;YAChF,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI;YAElC,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,cAAc;gBAC9C,SAAS,IAAI,CAAC;gBACd,KAAK,gBAAgB,EAAE,2CAA2C;;YACpE;QACF;QAEA,uDAAuD;QACvD,MAAO,SAAS,MAAM,GAAG,MAAO;YAC9B,SAAS,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,aAAa;QAC7D;QAEA,OAAO;IACT;IAEA,iDAAiD;IACzC,gBAAgB,OAA0B,EAAE,WAAmB,EAAW;QAChF,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,YAAY,CAAC;YAC3B,KAAK;gBACH,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,KAAK;gBACH,OAAO,IAAI,CAAC,sBAAsB,CAAC;YACrC,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC,KAAK;gBACH,OAAO,IAAI,CAAC,iBAAiB,CAAC;YAChC;gBACE,OAAO;QACX;IACF;IAEA,+BAA+B;IACvB,aAAa,OAA0B,EAAW;QACxD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAK;YAC3C,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;YACpD,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,GAAG;YACpE,MAAM,cAAc,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK;YACpE,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;YAE3C,qEAAqE;YACrE,IAAI,WAAW,aAAa,OACxB,cAAc,WAAW,KACzB,cAAc,WAAW,KAAK;gBAChC,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,WAAW,OAA0B,EAAW;QACtD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;YACpD,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;YAE3C,yDAAyD;YACzD,IAAI,WAAW,aAAa,OAAO,aAAa,GAAG;gBACjD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,uBAAuB,OAA0B,EAAW;QAClE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE;YAC3B,MAAM,OAAO,OAAO,CAAC,EAAE;YAEvB,sEAAsE;YACtE,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,mBAAmB;YAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,kBAAkB;YAC5C,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;YAC/D,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,uBAAuB,OAA0B,EAAW;QAClE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,OAAO,OAAO,CAAC,IAAI,EAAE;YAC3B,MAAM,OAAO,OAAO,CAAC,EAAE;YAEvB,sEAAsE;YACtE,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,mBAAmB;YAC7C,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,kBAAkB;YAC5C,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,qCAAqC;YAC/D,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;gBAC1B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,OAA0B,EAAW;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,SAAS,OAAO,CAAC,IAAI,EAAE;YAC7B,MAAM,QAAQ,OAAO,CAAC,EAAE;YAExB,qDAAqD;YACrD,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,OAAO,eAAe;YAClG,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG;gBAChD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,OAA0B,EAAW;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,QAAQ,OAAO,CAAC,IAAI,EAAE;YAC5B,MAAM,SAAS,OAAO,CAAC,IAAI,EAAE;YAC7B,MAAM,QAAQ,OAAO,CAAC,EAAE;YAExB,qDAAqD;YACrD,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,OAAO,eAAe;YAClG,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,gBAAgB;YAC5C,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG;gBAChD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,6CAA6C;IACrC,2BAA2B,WAAmB,EAAE,MAAc,EAAqB;QACzF,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe,MAAM,KAAK,MAAM,KAAK;QACzC,MAAM,MAAM,KAAK,GAAG;QAEpB,2BAA2B;QAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,SAAS,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,CAAC,SAAS;QAElG,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG;YAC1D,QAAQ,IAAI,CAAC;YACb,eAAe,OAAO,KAAK;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC,aAAa,cAAc,iBAAiB;QAChG,QAAQ,IAAI,IAAI;QAChB,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9D,4BAA4B;QAC5B,IAAK,IAAI,IAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,QAAQ,IAAK;YACrE,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc,GAAG;YAC1D,QAAQ,IAAI,CAAC;YACb,eAAe,OAAO,KAAK;QAC7B;QAEA,OAAO;IACT;IAEQ,wBAAwB,WAAmB,EAAE,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAqB;QAChI,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,YAAY;YAC3D,KAAK;gBACH,OAAO,IAAI,CAAC,kBAAkB,CAAC,YAAY,YAAY;YACzD,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,YAAY;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,+BAA+B,CAAC,YAAY,YAAY;YACtE,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;YACjE,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY,YAAY;YACjE;gBACE,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,YAAY;QAC7D;IACF;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/base-game.ts"], "sourcesContent": ["import { GameConfig, GameState, Position, GameType } from '@/types'\nimport { generateSessionId, calculatePnL } from '@/lib/utils'\nimport { GAME_CONFIGS, QUEST_COIN_MULTIPLIERS } from '@/lib/constants'\nimport { marketDataService } from '@/lib/services/market-data'\n\nexport class BaseGame {\n  protected config: GameConfig\n  protected state: GameState\n  protected startTime: number\n  protected endTime: number\n  protected isActive: boolean = false\n  protected marketData: Map<string, number> = new Map()\n\n  constructor(gameType: GameType, difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    const gameConfig = GAME_CONFIGS[gameType]\n\n    this.config = {\n      type: gameType,\n      difficulty,\n      duration_seconds: gameConfig.duration_seconds,\n      starting_balance: gameConfig.starting_balance,\n      available_pairs: [], // Will be set by specific game implementations\n      special_rules: {},\n    }\n\n    this.state = {\n      session_id: generateSessionId(),\n      current_balance: this.config.starting_balance,\n      positions: [],\n      time_remaining: this.config.duration_seconds,\n      score: 0,\n      multiplier: QUEST_COIN_MULTIPLIERS[difficulty],\n    }\n\n    this.startTime = Date.now()\n    this.endTime = this.startTime + (this.config.duration_seconds * 1000)\n  }\n\n  // Methods that can be overridden by specific games\n  async initialize(): Promise<void> {\n    // Default implementation - can be overridden\n  }\n\n  update(): void {\n    // Default implementation - can be overridden\n  }\n\n  calculateScore(): number {\n    // Default implementation - can be overridden\n    return 0\n  }\n\n  getGameSpecificData(): any {\n    // Default implementation - can be overridden\n    return {}\n  }\n\n  // Common game lifecycle methods\n  async start(): Promise<void> {\n    await this.initialize()\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  pause(): void {\n    this.isActive = false\n  }\n\n  resume(): void {\n    this.isActive = true\n    this.startGameLoop()\n  }\n\n  end(): GameState {\n    this.isActive = false\n    this.state.score = this.calculateScore()\n    this.state.time_remaining = 0\n    return this.state\n  }\n\n  // Trading operations\n  async executeTrade(symbol: string, side: 'buy' | 'sell', quantity: number): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const currentPrice = this.marketData.get(symbol)\n    if (!currentPrice) return false\n\n    const tradeValue = currentPrice * quantity\n    const requiredBalance = side === 'buy' ? tradeValue : 0\n\n    if (this.state.current_balance < requiredBalance) {\n      return false // Insufficient balance\n    }\n\n    // Check position limits\n    const maxPositions = this.getMaxPositions()\n    if (this.state.positions.length >= maxPositions && !this.hasExistingPosition(symbol)) {\n      return false // Too many positions\n    }\n\n    // Execute the trade\n    const position: Position = {\n      id: `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n      symbol,\n      side,\n      quantity,\n      entry_price: currentPrice,\n      current_price: currentPrice,\n      pnl: 0,\n      timestamp: new Date().toISOString(),\n    }\n\n    // Update balance\n    if (side === 'buy') {\n      this.state.current_balance -= tradeValue\n    } else {\n      this.state.current_balance += tradeValue\n    }\n\n    // Add or update position\n    const existingPositionIndex = this.state.positions.findIndex(p => p.symbol === symbol)\n    if (existingPositionIndex >= 0) {\n      // Update existing position (average price calculation would go here)\n      this.state.positions[existingPositionIndex] = position\n    } else {\n      this.state.positions.push(position)\n    }\n\n    return true\n  }\n\n  async closePosition(positionId: string): Promise<boolean> {\n    if (!this.isActive) return false\n\n    const positionIndex = this.state.positions.findIndex(p => p.id === positionId)\n    if (positionIndex === -1) return false\n\n    const position = this.state.positions[positionIndex]\n    const currentPrice = this.marketData.get(position.symbol)\n    if (!currentPrice) return false\n\n    // Calculate final P&L\n    const pnl = calculatePnL(position.entry_price, currentPrice, position.quantity, position.side)\n    \n    // Update balance with P&L\n    this.state.current_balance += pnl\n    if (position.side === 'sell') {\n      // Return the initial trade value for short positions\n      this.state.current_balance += position.entry_price * position.quantity\n    }\n\n    // Remove position\n    this.state.positions.splice(positionIndex, 1)\n\n    return true\n  }\n\n  // Market data updates\n  async updateMarketData(): Promise<void> {\n    try {\n      const symbols = this.config.available_pairs.map(pair => pair.symbol)\n      \n      // In a real implementation, you'd fetch from different services based on asset type\n      const cryptoSymbols = symbols.filter(s => this.isCryptoSymbol(s))\n      const stockSymbols = symbols.filter(s => this.isStockSymbol(s))\n      const forexSymbols = symbols.filter(s => this.isForexSymbol(s))\n\n      const [cryptoData, stockData, forexData] = await Promise.all([\n        cryptoSymbols.length > 0 ? marketDataService.getCryptoPrices(cryptoSymbols) : [],\n        stockSymbols.length > 0 ? marketDataService.getStockPrices(stockSymbols) : [],\n        forexSymbols.length > 0 ? marketDataService.getForexPrices(forexSymbols) : [],\n      ])\n\n      // Update market data map\n      const allData = cryptoData.concat(stockData).concat(forexData)\n      allData.forEach(data => {\n        this.marketData.set(data.symbol, data.price)\n      })\n\n      // Update position P&L\n      this.updatePositionPnL()\n    } catch (error) {\n      console.error('Error updating market data:', error)\n    }\n  }\n\n  // Game state getters\n  getState(): GameState {\n    return { ...this.state }\n  }\n\n  getConfig(): GameConfig {\n    return { ...this.config }\n  }\n\n  isGameActive(): boolean {\n    return this.isActive && this.state.time_remaining > 0\n  }\n\n  getTimeRemaining(): number {\n    if (!this.isActive) return 0\n    const remaining = Math.max(0, this.endTime - Date.now())\n    this.state.time_remaining = Math.floor(remaining / 1000)\n    return this.state.time_remaining\n  }\n\n  // Protected helper methods\n  protected startGameLoop(): void {\n    if (!this.isActive) return\n\n    const gameLoop = () => {\n      if (!this.isActive) return\n\n      this.update()\n      this.getTimeRemaining()\n\n      if (this.state.time_remaining <= 0) {\n        this.end()\n        return\n      }\n\n      setTimeout(gameLoop, 1000) // Update every second\n    }\n\n    gameLoop()\n  }\n\n  protected updatePositionPnL(): void {\n    this.state.positions.forEach(position => {\n      const currentPrice = this.marketData.get(position.symbol)\n      if (currentPrice) {\n        position.current_price = currentPrice\n        position.pnl = calculatePnL(\n          position.entry_price,\n          currentPrice,\n          position.quantity,\n          position.side\n        )\n      }\n    })\n  }\n\n  protected getTotalPnL(): number {\n    return this.state.positions.reduce((total, position) => total + position.pnl, 0)\n  }\n\n  protected getMaxPositions(): number {\n    const gameConfig = GAME_CONFIGS[this.config.type]\n    return (gameConfig as any).max_positions || 5\n  }\n\n  protected hasExistingPosition(symbol: string): boolean {\n    return this.state.positions.some(p => p.symbol === symbol)\n  }\n\n  protected isCryptoSymbol(symbol: string): boolean {\n    const cryptoSymbols = ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK', 'UNI', 'MATIC']\n    return cryptoSymbols.some(crypto => symbol.includes(crypto))\n  }\n\n  protected isStockSymbol(symbol: string): boolean {\n    const stockSymbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT', 'AMZN', 'META', 'NVDA']\n    return stockSymbols.includes(symbol)\n  }\n\n  protected isForexSymbol(symbol: string): boolean {\n    return symbol.includes('USD') || symbol.includes('EUR') || symbol.includes('GBP') || symbol.includes('JPY')\n  }\n\n  protected generateRandomPrice(basePrice: number, volatility: number = 0.02): number {\n    const change = (Math.random() - 0.5) * 2 * volatility\n    return basePrice * (1 + change)\n  }\n\n  protected simulateMarketMovement(): void {\n    // Simulate realistic market movements for game purposes\n    this.marketData.forEach((price, symbol) => {\n      const volatility = this.getSymbolVolatility(symbol)\n      const newPrice = this.generateRandomPrice(price, volatility)\n      this.marketData.set(symbol, newPrice)\n    })\n  }\n\n  protected getSymbolVolatility(symbol: string): number {\n    if (this.isCryptoSymbol(symbol)) return 0.05 // 5% volatility for crypto\n    if (this.isStockSymbol(symbol)) return 0.02 // 2% volatility for stocks\n    if (this.isForexSymbol(symbol)) return 0.01 // 1% volatility for forex\n    return 0.02 // Default 2%\n  }\n}\n\nexport { BaseGame }\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;;;;AAEO,MAAM;IACD,OAAkB;IAClB,MAAgB;IAChB,UAAiB;IACjB,QAAe;IACf,WAAoB,MAAK;IACzB,aAAkC,IAAI,MAAK;IAErD,YAAY,QAAkB,EAAE,UAAoD,CAAE;QACpF,MAAM,aAAa,uHAAA,CAAA,eAAY,CAAC,SAAS;QAEzC,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM;YACN;YACA,kBAAkB,WAAW,gBAAgB;YAC7C,kBAAkB,WAAW,gBAAgB;YAC7C,iBAAiB,EAAE;YACnB,eAAe,CAAC;QAClB;QAEA,IAAI,CAAC,KAAK,GAAG;YACX,YAAY,CAAA,GAAA,mHAAA,CAAA,oBAAiB,AAAD;YAC5B,iBAAiB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC7C,WAAW,EAAE;YACb,gBAAgB,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC5C,OAAO;YACP,YAAY,uHAAA,CAAA,yBAAsB,CAAC,WAAW;QAChD;QAEA,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;QACzB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,GAAG;IAClE;IAEA,mDAAmD;IACnD,MAAM,aAA4B;IAChC,6CAA6C;IAC/C;IAEA,SAAe;IACb,6CAA6C;IAC/C;IAEA,iBAAyB;QACvB,6CAA6C;QAC7C,OAAO;IACT;IAEA,sBAA2B;QACzB,6CAA6C;QAC7C,OAAO,CAAC;IACV;IAEA,gCAAgC;IAChC,MAAM,QAAuB;QAC3B,MAAM,IAAI,CAAC,UAAU;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,QAAc;QACZ,IAAI,CAAC,QAAQ,GAAG;IAClB;IAEA,SAAe;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa;IACpB;IAEA,MAAiB;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;QACtC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;QAC5B,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAc,EAAE,IAAoB,EAAE,QAAgB,EAAoB;QAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,cAAc,OAAO;QAE1B,MAAM,aAAa,eAAe;QAClC,MAAM,kBAAkB,SAAS,QAAQ,aAAa;QAEtD,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,iBAAiB;YAChD,OAAO,MAAM,uBAAuB;;QACtC;QAEA,wBAAwB;QACxB,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS;YACpF,OAAO,MAAM,qBAAqB;;QACpC;QAEA,oBAAoB;QACpB,MAAM,WAAqB;YACzB,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAClE;YACA;YACA;YACA,aAAa;YACb,eAAe;YACf,KAAK;YACL,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAChC;QAEA,yBAAyB;QACzB,MAAM,wBAAwB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAC/E,IAAI,yBAAyB,GAAG;YAC9B,qEAAqE;YACrE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,sBAAsB,GAAG;QAChD,OAAO;YACL,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QAC5B;QAEA,OAAO;IACT;IAEA,MAAM,cAAc,UAAkB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAE3B,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACnE,IAAI,kBAAkB,CAAC,GAAG,OAAO;QAEjC,MAAM,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,cAAc;QACpD,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;QACxD,IAAI,CAAC,cAAc,OAAO;QAE1B,sBAAsB;QACtB,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,SAAS,WAAW,EAAE,cAAc,SAAS,QAAQ,EAAE,SAAS,IAAI;QAE7F,0BAA0B;QAC1B,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI;QAC9B,IAAI,SAAS,IAAI,KAAK,QAAQ;YAC5B,qDAAqD;YACrD,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,SAAS,WAAW,GAAG,SAAS,QAAQ;QACxE;QAEA,kBAAkB;QAClB,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,eAAe;QAE3C,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,mBAAkC;QACtC,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;YAEnE,oFAAoF;YACpF,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,cAAc,CAAC;YAC9D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAC5D,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,IAAK,IAAI,CAAC,aAAa,CAAC;YAE5D,MAAM,CAAC,YAAY,WAAW,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3D,cAAc,MAAM,GAAG,IAAI,wIAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,iBAAiB,EAAE;gBAChF,aAAa,MAAM,GAAG,IAAI,wIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;gBAC7E,aAAa,MAAM,GAAG,IAAI,wIAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC,gBAAgB,EAAE;aAC9E;YAED,yBAAyB;YACzB,MAAM,UAAU,WAAW,MAAM,CAAC,WAAW,MAAM,CAAC;YACpD,QAAQ,OAAO,CAAC,CAAA;gBACd,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,MAAM,EAAE,KAAK,KAAK;YAC7C;YAEA,sBAAsB;YACtB,IAAI,CAAC,iBAAiB;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,qBAAqB;IACrB,WAAsB;QACpB,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK;QAAC;IACzB;IAEA,YAAwB;QACtB,OAAO;YAAE,GAAG,IAAI,CAAC,MAAM;QAAC;IAC1B;IAEA,eAAwB;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG;IACtD;IAEA,mBAA2B;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAC3B,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,GAAG,KAAK,GAAG;QACrD,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC,YAAY;QACnD,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc;IAClC;IAEA,2BAA2B;IACjB,gBAAsB;QAC9B,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QAEpB,MAAM,WAAW;YACf,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAEpB,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,gBAAgB;YAErB,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,IAAI,GAAG;gBAClC,IAAI,CAAC,GAAG;gBACR;YACF;YAEA,WAAW,UAAU,MAAM,sBAAsB;;QACnD;QAEA;IACF;IAEU,oBAA0B;QAClC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YAC3B,MAAM,eAAe,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,MAAM;YACxD,IAAI,cAAc;gBAChB,SAAS,aAAa,GAAG;gBACzB,SAAS,GAAG,GAAG,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EACxB,SAAS,WAAW,EACpB,cACA,SAAS,QAAQ,EACjB,SAAS,IAAI;YAEjB;QACF;IACF;IAEU,cAAsB;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,WAAa,QAAQ,SAAS,GAAG,EAAE;IAChF;IAEU,kBAA0B;QAClC,MAAM,aAAa,uHAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjD,OAAO,AAAC,WAAmB,aAAa,IAAI;IAC9C;IAEU,oBAAoB,MAAc,EAAW;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IACrD;IAEU,eAAe,MAAc,EAAW;QAChD,MAAM,gBAAgB;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAQ;YAAO;SAAQ;QACjF,OAAO,cAAc,IAAI,CAAC,CAAA,SAAU,OAAO,QAAQ,CAAC;IACtD;IAEU,cAAc,MAAc,EAAW;QAC/C,MAAM,eAAe;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAQ;YAAQ;SAAO;QAC9E,OAAO,aAAa,QAAQ,CAAC;IAC/B;IAEU,cAAc,MAAc,EAAW;QAC/C,OAAO,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC,UAAU,OAAO,QAAQ,CAAC;IACvG;IAEU,oBAAoB,SAAiB,EAAE,aAAqB,IAAI,EAAU;QAClF,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,IAAI;QAC3C,OAAO,YAAY,CAAC,IAAI,MAAM;IAChC;IAEU,yBAA+B;QACvC,wDAAwD;QACxD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,OAAO;YAC9B,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAC;YAC5C,MAAM,WAAW,IAAI,CAAC,mBAAmB,CAAC,OAAO;YACjD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ;QAC9B;IACF;IAEU,oBAAoB,MAAc,EAAU;QACpD,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACxE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,2BAA2B;;QACvE,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,OAAO,KAAK,0BAA0B;;QACtE,OAAO,KAAK,aAAa;;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/game-engine/games/candle-strike.ts"], "sourcesContent": ["import { BaseGame } from '../base-game'\nimport { GameType, CandlestickData } from '@/types'\nimport { TRADING_PAIRS } from '@/lib/constants'\n\ninterface CandlePattern {\n  id: string\n  name: string\n  description: string\n  bullish: boolean\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  minCandles: number\n  maxCandles: number\n}\n\ninterface CandleStrikeData {\n  patterns_identified: number\n  correct_identifications: number\n  wrong_identifications: number\n  current_pattern: CandlePattern | null\n  patterns_completed: CandlePattern[]\n  accuracy_percentage: number\n  speed_bonus: number\n  streak_count: number\n  max_streak: number\n}\n\ninterface PatternChallenge {\n  pattern: CandlePattern\n  candleData: CandlestickData[]\n  patternStartIndex: number\n  patternEndIndex: number\n  options: string[]\n  correctAnswer: number\n}\n\nexport class CandleStrikeGame extends BaseGame {\n  private gameData: CandleStrikeData\n  private currentChallenge: PatternChallenge | null = null\n  private challengeHistory: Array<{\n    challenge: PatternChallenge\n    userAnswer: number\n    correct: boolean\n    timeToAnswer: number\n    timestamp: number\n  }> = []\n  private challengeStartTime: number = 0\n  private availablePatterns: CandlePattern[]\n\n  constructor(difficulty: 'beginner' | 'intermediate' | 'advanced') {\n    super('candle_strike', difficulty)\n    \n    this.gameData = {\n      patterns_identified: 0,\n      correct_identifications: 0,\n      wrong_identifications: 0,\n      current_pattern: null,\n      patterns_completed: [],\n      accuracy_percentage: 0,\n      speed_bonus: 0,\n      streak_count: 0,\n      max_streak: 0,\n    }\n\n    this.availablePatterns = this.getPatternsByDifficulty(difficulty)\n    this.config.available_pairs = [TRADING_PAIRS[0]] // Use BTC for pattern recognition\n  }\n\n  async initialize(): Promise<void> {\n    // Generate first challenge\n    await this.generateNewChallenge()\n  }\n\n  update(): void {\n    // Update game metrics\n    this.updateGameMetrics()\n  }\n\n  calculateScore(): number {\n    const baseScore = this.gameData.correct_identifications * 100\n    const accuracyBonus = this.gameData.accuracy_percentage * 2\n    const speedBonus = this.gameData.speed_bonus\n    const streakBonus = this.gameData.max_streak * 50\n    \n    let totalScore = baseScore + accuracyBonus + speedBonus + streakBonus\n    \n    // Difficulty multiplier\n    totalScore *= this.state.multiplier\n    \n    return Math.round(Math.max(0, totalScore))\n  }\n\n  getGameSpecificData(): CandleStrikeData {\n    return { ...this.gameData }\n  }\n\n  getCurrentChallenge(): PatternChallenge | null {\n    return this.currentChallenge\n  }\n\n  async submitAnswer(answerIndex: number): Promise<boolean> {\n    if (!this.currentChallenge || !this.isActive) return false\n\n    const timeToAnswer = Date.now() - this.challengeStartTime\n    const correct = answerIndex === this.currentChallenge.correctAnswer\n\n    // Record the attempt\n    this.challengeHistory.push({\n      challenge: this.currentChallenge,\n      userAnswer: answerIndex,\n      correct,\n      timeToAnswer,\n      timestamp: Date.now(),\n    })\n\n    // Update game data\n    this.gameData.patterns_identified++\n    \n    if (correct) {\n      this.gameData.correct_identifications++\n      this.gameData.streak_count++\n      this.gameData.max_streak = Math.max(this.gameData.max_streak, this.gameData.streak_count)\n      \n      // Speed bonus for quick correct answers (under 10 seconds)\n      if (timeToAnswer < 10000) {\n        const speedBonus = Math.max(0, 50 - Math.floor(timeToAnswer / 200))\n        this.gameData.speed_bonus += speedBonus\n      }\n      \n      // Add pattern to completed list if not already there\n      if (!this.gameData.patterns_completed.find(p => p.id === this.currentChallenge!.pattern.id)) {\n        this.gameData.patterns_completed.push(this.currentChallenge.pattern)\n      }\n    } else {\n      this.gameData.wrong_identifications++\n      this.gameData.streak_count = 0\n    }\n\n    // Update accuracy\n    this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100\n\n    // Generate next challenge if game is still active\n    if (this.isActive && this.state.time_remaining > 0) {\n      await this.generateNewChallenge()\n    }\n\n    return correct\n  }\n\n  private async generateNewChallenge(): Promise<void> {\n    // Select a random pattern based on difficulty and progress\n    const pattern = this.selectNextPattern()\n    \n    // Generate candlestick data with the pattern\n    const candleData = this.generateCandlestickDataWithPattern(pattern)\n    \n    // Find where the pattern occurs in the data\n    const patternLocation = this.findPatternInData(candleData, pattern)\n    \n    // Generate multiple choice options\n    const options = this.generatePatternOptions(pattern)\n    \n    this.currentChallenge = {\n      pattern,\n      candleData,\n      patternStartIndex: patternLocation.start,\n      patternEndIndex: patternLocation.end,\n      options,\n      correctAnswer: 0, // Correct answer is always first, then shuffled\n    }\n\n    // Shuffle options and update correct answer index\n    this.shuffleOptions()\n    \n    this.gameData.current_pattern = pattern\n    this.challengeStartTime = Date.now()\n  }\n\n  private selectNextPattern(): CandlePattern {\n    // Prioritize patterns not yet completed\n    const uncompletedPatterns = this.availablePatterns.filter(\n      p => !this.gameData.patterns_completed.find(completed => completed.id === p.id)\n    )\n    \n    const patternsToChooseFrom = uncompletedPatterns.length > 0 ? uncompletedPatterns : this.availablePatterns\n    \n    return patternsToChooseFrom[Math.floor(Math.random() * patternsToChooseFrom.length)]\n  }\n\n  private generateCandlestickDataWithPattern(pattern: CandlePattern): CandlestickData[] {\n    const totalCandles = 50\n    const patternPosition = Math.floor(Math.random() * (totalCandles - pattern.maxCandles - 10)) + 10\n    \n    const data: CandlestickData[] = []\n    let currentPrice = 100 + Math.random() * 50\n    \n    // Generate candles before pattern\n    for (let i = 0; i < patternPosition; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i)\n      data.push(candle)\n      currentPrice = candle.close\n    }\n    \n    // Generate pattern candles\n    const patternCandles = this.generatePatternCandles(pattern, currentPrice, patternPosition)\n    data.push(...patternCandles)\n    currentPrice = patternCandles[patternCandles.length - 1].close\n    \n    // Generate candles after pattern\n    for (let i = patternPosition + patternCandles.length; i < totalCandles; i++) {\n      const candle = this.generateRandomCandle(currentPrice, i)\n      data.push(candle)\n      currentPrice = candle.close\n    }\n    \n    return data\n  }\n\n  private generateRandomCandle(basePrice: number, index: number): CandlestickData {\n    const volatility = 0.02\n    const change = (Math.random() - 0.5) * volatility * basePrice\n    const open = basePrice\n    const close = basePrice + change\n    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice\n    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice\n    \n    return {\n      timestamp: Date.now() - (50 - index) * 3600000, // Hourly intervals\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }\n  }\n\n  private generatePatternCandles(pattern: CandlePattern, startPrice: number, startIndex: number): CandlestickData[] {\n    // This is a simplified pattern generation - in a real implementation,\n    // you'd have specific algorithms for each pattern type\n    const candles: CandlestickData[] = []\n    let currentPrice = startPrice\n    \n    switch (pattern.id) {\n      case 'hammer':\n        return this.generateHammerPattern(startPrice, startIndex)\n      case 'doji':\n        return this.generateDojiPattern(startPrice, startIndex)\n      case 'engulfing_bullish':\n        return this.generateEngulfingPattern(startPrice, startIndex, true)\n      case 'engulfing_bearish':\n        return this.generateEngulfingPattern(startPrice, startIndex, false)\n      case 'morning_star':\n        return this.generateMorningStarPattern(startPrice, startIndex)\n      case 'evening_star':\n        return this.generateEveningStarPattern(startPrice, startIndex)\n      default:\n        return this.generateHammerPattern(startPrice, startIndex)\n    }\n  }\n\n  private generateHammerPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const open = startPrice\n    const close = startPrice + (Math.random() * 0.01 * startPrice) // Small body\n    const high = Math.max(open, close) + (Math.random() * 0.005 * startPrice) // Small upper shadow\n    const low = Math.min(open, close) - (0.02 + Math.random() * 0.01) * startPrice // Long lower shadow\n    \n    return [{\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }]\n  }\n\n  private generateDojiPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const open = startPrice\n    const close = startPrice + (Math.random() - 0.5) * 0.002 * startPrice // Very small body\n    const high = Math.max(open, close) + (0.01 + Math.random() * 0.01) * startPrice\n    const low = Math.min(open, close) - (0.01 + Math.random() * 0.01) * startPrice\n    \n    return [{\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open,\n      high,\n      low,\n      close,\n      volume: Math.random() * 1000000,\n    }]\n  }\n\n  private generateEngulfingPattern(startPrice: number, startIndex: number, bullish: boolean): CandlestickData[] {\n    const candles: CandlestickData[] = []\n    \n    // First candle (small)\n    const firstOpen = startPrice\n    const firstClose = bullish \n      ? startPrice - 0.01 * startPrice \n      : startPrice + 0.01 * startPrice\n    \n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: firstOpen,\n      high: Math.max(firstOpen, firstClose) + 0.002 * startPrice,\n      low: Math.min(firstOpen, firstClose) - 0.002 * startPrice,\n      close: firstClose,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (engulfing)\n    const secondOpen = bullish \n      ? firstClose - 0.005 * startPrice \n      : firstClose + 0.005 * startPrice\n    const secondClose = bullish \n      ? firstOpen + 0.015 * startPrice \n      : firstOpen - 0.015 * startPrice\n    \n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondOpen,\n      high: Math.max(secondOpen, secondClose) + 0.002 * startPrice,\n      low: Math.min(secondOpen, secondClose) - 0.002 * startPrice,\n      close: secondClose,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private generateMorningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    const candles: CandlestickData[] = []\n    \n    // First candle (bearish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: startPrice,\n      high: startPrice + 0.002 * startPrice,\n      low: startPrice - 0.015 * startPrice,\n      close: startPrice - 0.012 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (small body/doji)\n    const secondPrice = startPrice - 0.015 * startPrice\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondPrice,\n      high: secondPrice + 0.005 * startPrice,\n      low: secondPrice - 0.005 * startPrice,\n      close: secondPrice + 0.001 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Third candle (bullish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,\n      open: secondPrice + 0.002 * startPrice,\n      high: startPrice - 0.002 * startPrice,\n      low: secondPrice,\n      close: startPrice - 0.003 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private generateEveningStarPattern(startPrice: number, startIndex: number): CandlestickData[] {\n    // Similar to morning star but inverted\n    const candles: CandlestickData[] = []\n    \n    // First candle (bullish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex) * 3600000,\n      open: startPrice,\n      high: startPrice + 0.015 * startPrice,\n      low: startPrice - 0.002 * startPrice,\n      close: startPrice + 0.012 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Second candle (small body/doji)\n    const secondPrice = startPrice + 0.015 * startPrice\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 1) * 3600000,\n      open: secondPrice,\n      high: secondPrice + 0.005 * startPrice,\n      low: secondPrice - 0.005 * startPrice,\n      close: secondPrice - 0.001 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    // Third candle (bearish)\n    candles.push({\n      timestamp: Date.now() - (50 - startIndex - 2) * 3600000,\n      open: secondPrice - 0.002 * startPrice,\n      high: secondPrice,\n      low: startPrice + 0.002 * startPrice,\n      close: startPrice + 0.003 * startPrice,\n      volume: Math.random() * 1000000,\n    })\n    \n    return candles\n  }\n\n  private findPatternInData(data: CandlestickData[], pattern: CandlePattern): { start: number; end: number } {\n    // For simplicity, we know where we placed the pattern\n    // In a real implementation, you'd search for the pattern in the data\n    const totalCandles = data.length\n    const patternPosition = Math.floor(totalCandles * 0.4) // Roughly where we placed it\n    \n    return {\n      start: patternPosition,\n      end: patternPosition + pattern.maxCandles - 1,\n    }\n  }\n\n  private generatePatternOptions(correctPattern: CandlePattern): string[] {\n    const allPatterns = this.getAllPatterns()\n    const wrongPatterns = allPatterns\n      .filter(p => p.id !== correctPattern.id)\n      .sort(() => Math.random() - 0.5)\n      .slice(0, 3)\n    \n    return [correctPattern.name, ...wrongPatterns.map(p => p.name)]\n  }\n\n  private shuffleOptions(): void {\n    if (!this.currentChallenge) return\n    \n    const options = [...this.currentChallenge.options]\n    const correctAnswer = options[0]\n    \n    // Fisher-Yates shuffle\n    for (let i = options.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1))\n      ;[options[i], options[j]] = [options[j], options[i]]\n    }\n    \n    this.currentChallenge.options = options\n    this.currentChallenge.correctAnswer = options.indexOf(correctAnswer)\n  }\n\n  private updateGameMetrics(): void {\n    // Update accuracy percentage\n    if (this.gameData.patterns_identified > 0) {\n      this.gameData.accuracy_percentage = (this.gameData.correct_identifications / this.gameData.patterns_identified) * 100\n    }\n  }\n\n  private getPatternsByDifficulty(difficulty: 'beginner' | 'intermediate' | 'advanced'): CandlePattern[] {\n    const allPatterns = this.getAllPatterns()\n    return allPatterns.filter(p => p.difficulty === difficulty || (difficulty === 'advanced' && p.difficulty !== 'advanced'))\n  }\n\n  private getAllPatterns(): CandlePattern[] {\n    return [\n      {\n        id: 'hammer',\n        name: 'Hammer',\n        description: 'Bullish reversal pattern with long lower shadow',\n        bullish: true,\n        difficulty: 'beginner',\n        minCandles: 1,\n        maxCandles: 1,\n      },\n      {\n        id: 'doji',\n        name: 'Doji',\n        description: 'Indecision pattern with very small body',\n        bullish: false,\n        difficulty: 'beginner',\n        minCandles: 1,\n        maxCandles: 1,\n      },\n      {\n        id: 'engulfing_bullish',\n        name: 'Bullish Engulfing',\n        description: 'Two-candle bullish reversal pattern',\n        bullish: true,\n        difficulty: 'intermediate',\n        minCandles: 2,\n        maxCandles: 2,\n      },\n      {\n        id: 'engulfing_bearish',\n        name: 'Bearish Engulfing',\n        description: 'Two-candle bearish reversal pattern',\n        bullish: false,\n        difficulty: 'intermediate',\n        minCandles: 2,\n        maxCandles: 2,\n      },\n      {\n        id: 'morning_star',\n        name: 'Morning Star',\n        description: 'Three-candle bullish reversal pattern',\n        bullish: true,\n        difficulty: 'advanced',\n        minCandles: 3,\n        maxCandles: 3,\n      },\n      {\n        id: 'evening_star',\n        name: 'Evening Star',\n        description: 'Three-candle bearish reversal pattern',\n        bullish: false,\n        difficulty: 'advanced',\n        minCandles: 3,\n        maxCandles: 3,\n      },\n    ]\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAiCO,MAAM,yBAAyB,4IAAA,CAAA,WAAQ;IACpC,SAA0B;IAC1B,mBAA4C,KAAI;IAChD,mBAMH,EAAE,CAAA;IACC,qBAA6B,EAAC;IAC9B,kBAAkC;IAE1C,YAAY,UAAoD,CAAE;QAChE,KAAK,CAAC,iBAAiB;QAEvB,IAAI,CAAC,QAAQ,GAAG;YACd,qBAAqB;YACrB,yBAAyB;YACzB,uBAAuB;YACvB,iBAAiB;YACjB,oBAAoB,EAAE;YACtB,qBAAqB;YACrB,aAAa;YACb,cAAc;YACd,YAAY;QACd;QAEA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC;QACtD,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG;YAAC,uHAAA,CAAA,gBAAa,CAAC,EAAE;SAAC,CAAC,kCAAkC;;IACrF;IAEA,MAAM,aAA4B;QAChC,2BAA2B;QAC3B,MAAM,IAAI,CAAC,oBAAoB;IACjC;IAEA,SAAe;QACb,sBAAsB;QACtB,IAAI,CAAC,iBAAiB;IACxB;IAEA,iBAAyB;QACvB,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG;QAC1D,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG;QAC1D,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,WAAW;QAC5C,MAAM,cAAc,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;QAE/C,IAAI,aAAa,YAAY,gBAAgB,aAAa;QAE1D,wBAAwB;QACxB,cAAc,IAAI,CAAC,KAAK,CAAC,UAAU;QAEnC,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG;IAChC;IAEA,sBAAwC;QACtC,OAAO;YAAE,GAAG,IAAI,CAAC,QAAQ;QAAC;IAC5B;IAEA,sBAA+C;QAC7C,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA,MAAM,aAAa,WAAmB,EAAoB;QACxD,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO;QAErD,MAAM,eAAe,KAAK,GAAG,KAAK,IAAI,CAAC,kBAAkB;QACzD,MAAM,UAAU,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,aAAa;QAEnE,qBAAqB;QACrB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACzB,WAAW,IAAI,CAAC,gBAAgB;YAChC,YAAY;YACZ;YACA;YACA,WAAW,KAAK,GAAG;QACrB;QAEA,mBAAmB;QACnB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QAEjC,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,uBAAuB;YACrC,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC1B,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;YAExF,2DAA2D;YAC3D,IAAI,eAAe,OAAO;gBACxB,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,KAAK,CAAC,eAAe;gBAC9D,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI;YAC/B;YAEA,qDAAqD;YACrD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI,CAAC,gBAAgB,CAAE,OAAO,CAAC,EAAE,GAAG;gBAC3F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO;YACrE;QACF,OAAO;YACL,IAAI,CAAC,QAAQ,CAAC,qBAAqB;YACnC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG;QAC/B;QAEA,kBAAkB;QAClB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,AAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAI;QAElH,kDAAkD;QAClD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,GAAG;YAClD,MAAM,IAAI,CAAC,oBAAoB;QACjC;QAEA,OAAO;IACT;IAEA,MAAc,uBAAsC;QAClD,2DAA2D;QAC3D,MAAM,UAAU,IAAI,CAAC,iBAAiB;QAEtC,6CAA6C;QAC7C,MAAM,aAAa,IAAI,CAAC,kCAAkC,CAAC;QAE3D,4CAA4C;QAC5C,MAAM,kBAAkB,IAAI,CAAC,iBAAiB,CAAC,YAAY;QAE3D,mCAAmC;QACnC,MAAM,UAAU,IAAI,CAAC,sBAAsB,CAAC;QAE5C,IAAI,CAAC,gBAAgB,GAAG;YACtB;YACA;YACA,mBAAmB,gBAAgB,KAAK;YACxC,iBAAiB,gBAAgB,GAAG;YACpC;YACA,eAAe;QACjB;QAEA,kDAAkD;QAClD,IAAI,CAAC,cAAc;QAEnB,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG;QAChC,IAAI,CAAC,kBAAkB,GAAG,KAAK,GAAG;IACpC;IAEQ,oBAAmC;QACzC,wCAAwC;QACxC,MAAM,sBAAsB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CACvD,CAAA,IAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK,EAAE,EAAE;QAGhF,MAAM,uBAAuB,oBAAoB,MAAM,GAAG,IAAI,sBAAsB,IAAI,CAAC,iBAAiB;QAE1G,OAAO,oBAAoB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,qBAAqB,MAAM,EAAE;IACtF;IAEQ,mCAAmC,OAAsB,EAAqB;QACpF,MAAM,eAAe;QACrB,MAAM,kBAAkB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,eAAe,QAAQ,UAAU,GAAG,EAAE,KAAK;QAE/F,MAAM,OAA0B,EAAE;QAClC,IAAI,eAAe,MAAM,KAAK,MAAM,KAAK;QAEzC,kCAAkC;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACvD,KAAK,IAAI,CAAC;YACV,eAAe,OAAO,KAAK;QAC7B;QAEA,2BAA2B;QAC3B,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,SAAS,cAAc;QAC1E,KAAK,IAAI,IAAI;QACb,eAAe,cAAc,CAAC,eAAe,MAAM,GAAG,EAAE,CAAC,KAAK;QAE9D,iCAAiC;QACjC,IAAK,IAAI,IAAI,kBAAkB,eAAe,MAAM,EAAE,IAAI,cAAc,IAAK;YAC3E,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,cAAc;YACvD,KAAK,IAAI,CAAC;YACV,eAAe,OAAO,KAAK;QAC7B;QAEA,OAAO;IACT;IAEQ,qBAAqB,SAAiB,EAAE,KAAa,EAAmB;QAC9E,MAAM,aAAa;QACnB,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;QACpD,MAAM,OAAO;QACb,MAAM,QAAQ,YAAY;QAC1B,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAC5D,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAE3D,OAAO;YACL,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI;YACvC;YACA;YACA;YACA;YACA,QAAQ,KAAK,MAAM,KAAK;QAC1B;IACF;IAEQ,uBAAuB,OAAsB,EAAE,UAAkB,EAAE,UAAkB,EAAqB;QAChH,sEAAsE;QACtE,uDAAuD;QACvD,MAAM,UAA6B,EAAE;QACrC,IAAI,eAAe;QAEnB,OAAQ,QAAQ,EAAE;YAChB,KAAK;gBACH,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY;YAChD,KAAK;gBACH,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY;YAC9C,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,YAAY;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,wBAAwB,CAAC,YAAY,YAAY;YAC/D,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY;YACrD,KAAK;gBACH,OAAO,IAAI,CAAC,0BAA0B,CAAC,YAAY;YACrD;gBACE,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY;QAClD;IACF;IAEQ,sBAAsB,UAAkB,EAAE,UAAkB,EAAqB;QACvF,MAAM,OAAO;QACb,MAAM,QAAQ,aAAc,KAAK,MAAM,KAAK,OAAO,WAAY,aAAa;;QAC5E,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAU,KAAK,MAAM,KAAK,QAAQ,WAAY,qBAAqB;;QAC/F,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI,WAAW,oBAAoB;;QAEnG,OAAO;YAAC;gBACN,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;gBAC5C;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,MAAM,KAAK;YAC1B;SAAE;IACJ;IAEQ,oBAAoB,UAAkB,EAAE,UAAkB,EAAqB;QACrF,MAAM,OAAO;QACb,MAAM,QAAQ,aAAa,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,QAAQ,WAAW,kBAAkB;;QACxF,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;QACrE,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,CAAC,OAAO,KAAK,MAAM,KAAK,IAAI,IAAI;QAEpE,OAAO;YAAC;gBACN,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;gBAC5C;gBACA;gBACA;gBACA;gBACA,QAAQ,KAAK,MAAM,KAAK;YAC1B;SAAE;IACJ;IAEQ,yBAAyB,UAAkB,EAAE,UAAkB,EAAE,OAAgB,EAAqB;QAC5G,MAAM,UAA6B,EAAE;QAErC,uBAAuB;QACvB,MAAM,YAAY;QAClB,MAAM,aAAa,UACf,aAAa,OAAO,aACpB,aAAa,OAAO;QAExB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,KAAK,GAAG,CAAC,WAAW,cAAc,QAAQ;YAChD,KAAK,KAAK,GAAG,CAAC,WAAW,cAAc,QAAQ;YAC/C,OAAO;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,4BAA4B;QAC5B,MAAM,aAAa,UACf,aAAa,QAAQ,aACrB,aAAa,QAAQ;QACzB,MAAM,cAAc,UAChB,YAAY,QAAQ,aACpB,YAAY,QAAQ;QAExB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,KAAK,GAAG,CAAC,YAAY,eAAe,QAAQ;YAClD,KAAK,KAAK,GAAG,CAAC,YAAY,eAAe,QAAQ;YACjD,OAAO;YACP,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,2BAA2B,UAAkB,EAAE,UAAkB,EAAqB;QAC5F,MAAM,UAA6B,EAAE;QAErC,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,aAAa,QAAQ;YAC3B,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,kCAAkC;QAClC,MAAM,cAAc,aAAa,QAAQ;QACzC,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,cAAc,QAAQ;YAC5B,KAAK,cAAc,QAAQ;YAC3B,OAAO,cAAc,QAAQ;YAC7B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM,cAAc,QAAQ;YAC5B,MAAM,aAAa,QAAQ;YAC3B,KAAK;YACL,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,2BAA2B,UAAkB,EAAE,UAAkB,EAAqB;QAC5F,uCAAuC;QACvC,MAAM,UAA6B,EAAE;QAErC,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,UAAU,IAAI;YAC5C,MAAM;YACN,MAAM,aAAa,QAAQ;YAC3B,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,kCAAkC;QAClC,MAAM,cAAc,aAAa,QAAQ;QACzC,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM;YACN,MAAM,cAAc,QAAQ;YAC5B,KAAK,cAAc,QAAQ;YAC3B,OAAO,cAAc,QAAQ;YAC7B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,yBAAyB;QACzB,QAAQ,IAAI,CAAC;YACX,WAAW,KAAK,GAAG,KAAK,CAAC,KAAK,aAAa,CAAC,IAAI;YAChD,MAAM,cAAc,QAAQ;YAC5B,MAAM;YACN,KAAK,aAAa,QAAQ;YAC1B,OAAO,aAAa,QAAQ;YAC5B,QAAQ,KAAK,MAAM,KAAK;QAC1B;QAEA,OAAO;IACT;IAEQ,kBAAkB,IAAuB,EAAE,OAAsB,EAAkC;QACzG,sDAAsD;QACtD,qEAAqE;QACrE,MAAM,eAAe,KAAK,MAAM;QAChC,MAAM,kBAAkB,KAAK,KAAK,CAAC,eAAe,KAAK,6BAA6B;;QAEpF,OAAO;YACL,OAAO;YACP,KAAK,kBAAkB,QAAQ,UAAU,GAAG;QAC9C;IACF;IAEQ,uBAAuB,cAA6B,EAAY;QACtE,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,gBAAgB,YACnB,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE,EACtC,IAAI,CAAC,IAAM,KAAK,MAAM,KAAK,KAC3B,KAAK,CAAC,GAAG;QAEZ,OAAO;YAAC,eAAe,IAAI;eAAK,cAAc,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;SAAE;IACjE;IAEQ,iBAAuB;QAC7B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAE5B,MAAM,UAAU;eAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO;SAAC;QAClD,MAAM,gBAAgB,OAAO,CAAC,EAAE;QAEhC,uBAAuB;QACvB,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;YAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;YAC1C,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,GAAG;gBAAC,OAAO,CAAC,EAAE;gBAAE,OAAO,CAAC,EAAE;aAAC;QACtD;QAEA,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;QAChC,IAAI,CAAC,gBAAgB,CAAC,aAAa,GAAG,QAAQ,OAAO,CAAC;IACxD;IAEQ,oBAA0B;QAChC,6BAA6B;QAC7B,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,GAAG;YACzC,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,AAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAI;QACpH;IACF;IAEQ,wBAAwB,UAAoD,EAAmB;QACrG,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK,cAAe,eAAe,cAAc,EAAE,UAAU,KAAK;IAC/G;IAEQ,iBAAkC;QACxC,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,YAAY;gBACZ,YAAY;YACd;SACD;IACH;AACF", "debugId": null}}, {"offset": {"line": 1659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/charts/candlestick-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { create<PERSON><PERSON>, IChartApi, ISeriesApi, CandlestickData as LWCandlestickData, ColorType } from 'lightweight-charts'\nimport { CandlestickData } from '@/types'\n\ninterface CandlestickChartProps {\n  data: CandlestickData[]\n  width?: number\n  height?: number\n  theme?: 'light' | 'dark'\n  patternHighlight?: {\n    startIndex: number\n    endIndex: number\n    color: string\n  }\n  onPatternClick?: (startIndex: number, endIndex: number) => void\n  showVolume?: boolean\n  title?: string\n  className?: string\n}\n\nexport default function CandlestickChart({\n  data,\n  width = 800,\n  height = 400,\n  theme = 'dark',\n  patternHighlight,\n  onPatternClick,\n  showVolume = true,\n  title,\n  className = '',\n}: CandlestickChartProps) {\n  const chartContainerRef = useRef<HTMLDivElement>(null)\n  const chartRef = useRef<IChartApi | null>(null)\n  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)\n  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  useEffect(() => {\n    if (!chartContainerRef.current || data.length === 0) return\n\n    // Create chart\n    const chart = createChart(chartContainerRef.current, {\n      width,\n      height,\n      layout: {\n        background: { type: ColorType.Solid, color: theme === 'dark' ? '#1a1a1a' : '#ffffff' },\n        textColor: theme === 'dark' ? '#d1d5db' : '#374151',\n      },\n      grid: {\n        vertLines: { color: theme === 'dark' ? '#374151' : '#e5e7eb' },\n        horzLines: { color: theme === 'dark' ? '#374151' : '#e5e7eb' },\n      },\n      crosshair: {\n        mode: 1,\n      },\n      rightPriceScale: {\n        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',\n      },\n      timeScale: {\n        borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db',\n        timeVisible: true,\n        secondsVisible: false,\n      },\n    })\n\n    chartRef.current = chart\n\n    // Add candlestick series\n    const candlestickSeries = chart.addCandlestickSeries({\n      upColor: '#10b981',\n      downColor: '#ef4444',\n      borderDownColor: '#ef4444',\n      borderUpColor: '#10b981',\n      wickDownColor: '#ef4444',\n      wickUpColor: '#10b981',\n    })\n\n    candlestickSeriesRef.current = candlestickSeries\n\n    // Add volume series if enabled\n    if (showVolume) {\n      const volumeSeries = chart.addHistogramSeries({\n        color: theme === 'dark' ? '#6b7280' : '#9ca3af',\n        priceFormat: {\n          type: 'volume',\n        },\n        priceScaleId: '',\n        scaleMargins: {\n          top: 0.7,\n          bottom: 0,\n        },\n      })\n      volumeSeriesRef.current = volumeSeries\n    }\n\n    // Convert data format\n    const chartData: LWCandlestickData[] = data.map(candle => ({\n      time: Math.floor(candle.timestamp / 1000) as any,\n      open: candle.open,\n      high: candle.high,\n      low: candle.low,\n      close: candle.close,\n    }))\n\n    const volumeData = data.map(candle => ({\n      time: Math.floor(candle.timestamp / 1000) as any,\n      value: candle.volume,\n      color: candle.close >= candle.open ? '#10b98150' : '#ef444450',\n    }))\n\n    // Set data\n    candlestickSeries.setData(chartData)\n    if (showVolume && volumeSeriesRef.current) {\n      volumeSeriesRef.current.setData(volumeData)\n    }\n\n    // Fit content\n    chart.timeScale().fitContent()\n\n    setIsLoading(false)\n\n    // Cleanup\n    return () => {\n      chart.remove()\n    }\n  }, [data, width, height, theme, showVolume])\n\n  // Handle pattern highlighting\n  useEffect(() => {\n    if (!chartRef.current || !candlestickSeriesRef.current || !patternHighlight) return\n\n    // Add pattern highlight markers\n    const markers = []\n    \n    // Start marker\n    markers.push({\n      time: Math.floor(data[patternHighlight.startIndex]?.timestamp / 1000) as any,\n      position: 'belowBar' as const,\n      color: patternHighlight.color,\n      shape: 'arrowUp' as const,\n      text: 'Pattern Start',\n    })\n\n    // End marker\n    markers.push({\n      time: Math.floor(data[patternHighlight.endIndex]?.timestamp / 1000) as any,\n      position: 'belowBar' as const,\n      color: patternHighlight.color,\n      shape: 'arrowUp' as const,\n      text: 'Pattern End',\n    })\n\n    candlestickSeriesRef.current.setMarkers(markers)\n  }, [patternHighlight, data])\n\n  // Handle click events\n  useEffect(() => {\n    if (!chartRef.current || !onPatternClick) return\n\n    const handleClick = (param: any) => {\n      if (param.time) {\n        const clickedIndex = data.findIndex(\n          candle => Math.floor(candle.timestamp / 1000) === param.time\n        )\n        if (clickedIndex !== -1) {\n          // For simplicity, assume pattern is 3 candles around clicked point\n          const startIndex = Math.max(0, clickedIndex - 1)\n          const endIndex = Math.min(data.length - 1, clickedIndex + 1)\n          onPatternClick(startIndex, endIndex)\n        }\n      }\n    }\n\n    chartRef.current.subscribeClick(handleClick)\n\n    return () => {\n      if (chartRef.current) {\n        chartRef.current.unsubscribeClick(handleClick)\n      }\n    }\n  }, [data, onPatternClick])\n\n  return (\n    <div className={`relative ${className}`}>\n      {title && (\n        <div className={`text-center mb-2 font-bold ${\n          theme === 'dark' ? 'text-white' : 'text-gray-900'\n        }`}>\n          {title}\n        </div>\n      )}\n      \n      {isLoading && (\n        <div className=\"absolute inset-0 flex items-center justify-center bg-black/20 rounded\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400\"></div>\n        </div>\n      )}\n      \n      <div \n        ref={chartContainerRef} \n        className=\"rounded border\"\n        style={{ \n          width: `${width}px`, \n          height: `${height}px`,\n          borderColor: theme === 'dark' ? '#4b5563' : '#d1d5db'\n        }}\n      />\n      \n      {/* Chart controls */}\n      <div className={`mt-2 flex justify-between text-xs ${\n        theme === 'dark' ? 'text-gray-400' : 'text-gray-600'\n      }`}>\n        <span>📊 Candlestick Chart</span>\n        <span>{data.length} candles</span>\n      </div>\n    </div>\n  )\n}\n\n// Pattern annotation component\nexport function PatternAnnotation({ \n  pattern, \n  theme = 'dark' \n}: { \n  pattern: { name: string; description: string; bullish: boolean }\n  theme?: 'light' | 'dark'\n}) {\n  return (\n    <div className={`p-3 rounded-lg border ${\n      theme === 'dark' \n        ? 'bg-gray-800 border-gray-600 text-white' \n        : 'bg-white border-gray-300 text-gray-900'\n    }`}>\n      <div className=\"flex items-center gap-2 mb-1\">\n        <span className={`text-lg ${pattern.bullish ? 'text-green-400' : 'text-red-400'}`}>\n          {pattern.bullish ? '📈' : '📉'}\n        </span>\n        <span className=\"font-bold\">{pattern.name}</span>\n      </div>\n      <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>\n        {pattern.description}\n      </p>\n    </div>\n  )\n}\n\n// Chart loading skeleton\nexport function ChartSkeleton({ \n  width = 800, \n  height = 400, \n  theme = 'dark' \n}: { \n  width?: number\n  height?: number\n  theme?: 'light' | 'dark'\n}) {\n  return (\n    <div \n      className={`animate-pulse rounded border ${\n        theme === 'dark' ? 'bg-gray-800 border-gray-600' : 'bg-gray-200 border-gray-300'\n      }`}\n      style={{ width: `${width}px`, height: `${height}px` }}\n    >\n      <div className=\"flex items-center justify-center h-full\">\n        <div className={`text-center ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-current mx-auto mb-2\"></div>\n          <p>Loading chart data...</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAsBe,SAAS,iBAAiB,EACvC,IAAI,EACJ,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,QAAQ,MAAM,EACd,gBAAgB,EAChB,cAAc,EACd,aAAa,IAAI,EACjB,KAAK,EACL,YAAY,EAAE,EACQ;IACtB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoC;IACtE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkC;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB,OAAO,IAAI,KAAK,MAAM,KAAK,GAAG;QAErD,eAAe;QACf,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,OAAO,EAAE;YACnD;YACA;YACA,QAAQ;gBACN,YAAY;oBAAE,MAAM,sLAAA,CAAA,YAAS,CAAC,KAAK;oBAAE,OAAO,UAAU,SAAS,YAAY;gBAAU;gBACrF,WAAW,UAAU,SAAS,YAAY;YAC5C;YACA,MAAM;gBACJ,WAAW;oBAAE,OAAO,UAAU,SAAS,YAAY;gBAAU;gBAC7D,WAAW;oBAAE,OAAO,UAAU,SAAS,YAAY;gBAAU;YAC/D;YACA,WAAW;gBACT,MAAM;YACR;YACA,iBAAiB;gBACf,aAAa,UAAU,SAAS,YAAY;YAC9C;YACA,WAAW;gBACT,aAAa,UAAU,SAAS,YAAY;gBAC5C,aAAa;gBACb,gBAAgB;YAClB;QACF;QAEA,SAAS,OAAO,GAAG;QAEnB,yBAAyB;QACzB,MAAM,oBAAoB,MAAM,oBAAoB,CAAC;YACnD,SAAS;YACT,WAAW;YACX,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,aAAa;QACf;QAEA,qBAAqB,OAAO,GAAG;QAE/B,+BAA+B;QAC/B,IAAI,YAAY;YACd,MAAM,eAAe,MAAM,kBAAkB,CAAC;gBAC5C,OAAO,UAAU,SAAS,YAAY;gBACtC,aAAa;oBACX,MAAM;gBACR;gBACA,cAAc;gBACd,cAAc;oBACZ,KAAK;oBACL,QAAQ;gBACV;YACF;YACA,gBAAgB,OAAO,GAAG;QAC5B;QAEA,sBAAsB;QACtB,MAAM,YAAiC,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACzD,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG;gBACpC,MAAM,OAAO,IAAI;gBACjB,MAAM,OAAO,IAAI;gBACjB,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;YACrB,CAAC;QAED,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACrC,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG;gBACpC,OAAO,OAAO,MAAM;gBACpB,OAAO,OAAO,KAAK,IAAI,OAAO,IAAI,GAAG,cAAc;YACrD,CAAC;QAED,WAAW;QACX,kBAAkB,OAAO,CAAC;QAC1B,IAAI,cAAc,gBAAgB,OAAO,EAAE;YACzC,gBAAgB,OAAO,CAAC,OAAO,CAAC;QAClC;QAEA,cAAc;QACd,MAAM,SAAS,GAAG,UAAU;QAE5B,aAAa;QAEb,UAAU;QACV,OAAO;YACL,MAAM,MAAM;QACd;IACF,GAAG;QAAC;QAAM;QAAO;QAAQ;QAAO;KAAW;IAE3C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,kBAAkB;QAE7E,gCAAgC;QAChC,MAAM,UAAU,EAAE;QAElB,eAAe;QACf,QAAQ,IAAI,CAAC;YACX,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,iBAAiB,UAAU,CAAC,EAAE,YAAY;YAChE,UAAU;YACV,OAAO,iBAAiB,KAAK;YAC7B,OAAO;YACP,MAAM;QACR;QAEA,aAAa;QACb,QAAQ,IAAI,CAAC;YACX,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,iBAAiB,QAAQ,CAAC,EAAE,YAAY;YAC9D,UAAU;YACV,OAAO,iBAAiB,KAAK;YAC7B,OAAO;YACP,MAAM;QACR;QAEA,qBAAqB,OAAO,CAAC,UAAU,CAAC;IAC1C,GAAG;QAAC;QAAkB;KAAK;IAE3B,sBAAsB;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,gBAAgB;QAE1C,MAAM,cAAc,CAAC;YACnB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,eAAe,KAAK,SAAS,CACjC,CAAA,SAAU,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG,UAAU,MAAM,IAAI;gBAE9D,IAAI,iBAAiB,CAAC,GAAG;oBACvB,mEAAmE;oBACnE,MAAM,aAAa,KAAK,GAAG,CAAC,GAAG,eAAe;oBAC9C,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,MAAM,GAAG,GAAG,eAAe;oBAC1D,eAAe,YAAY;gBAC7B;YACF;QACF;QAEA,SAAS,OAAO,CAAC,cAAc,CAAC;QAEhC,OAAO;YACL,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,gBAAgB,CAAC;YACpC;QACF;IACF,GAAG;QAAC;QAAM;KAAe;IAEzB,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;YACpC,uBACC,8OAAC;gBAAI,WAAW,CAAC,2BAA2B,EAC1C,UAAU,SAAS,eAAe,iBAClC;0BACC;;;;;;YAIJ,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAInB,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,OAAO,GAAG,MAAM,EAAE,CAAC;oBACnB,QAAQ,GAAG,OAAO,EAAE,CAAC;oBACrB,aAAa,UAAU,SAAS,YAAY;gBAC9C;;;;;;0BAIF,8OAAC;gBAAI,WAAW,CAAC,kCAAkC,EACjD,UAAU,SAAS,kBAAkB,iBACrC;;kCACA,8OAAC;kCAAK;;;;;;kCACN,8OAAC;;4BAAM,KAAK,MAAM;4BAAC;;;;;;;;;;;;;;;;;;;AAI3B;AAGO,SAAS,kBAAkB,EAChC,OAAO,EACP,QAAQ,MAAM,EAIf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,sBAAsB,EACrC,UAAU,SACN,2CACA,0CACJ;;0BACA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,OAAO,GAAG,mBAAmB,gBAAgB;kCAC9E,QAAQ,OAAO,GAAG,OAAO;;;;;;kCAE5B,8OAAC;wBAAK,WAAU;kCAAa,QAAQ,IAAI;;;;;;;;;;;;0BAE3C,8OAAC;gBAAE,WAAW,CAAC,QAAQ,EAAE,UAAU,SAAS,kBAAkB,iBAAiB;0BAC5E,QAAQ,WAAW;;;;;;;;;;;;AAI5B;AAGO,SAAS,cAAc,EAC5B,QAAQ,GAAG,EACX,SAAS,GAAG,EACZ,QAAQ,MAAM,EAKf;IACC,qBACE,8OAAC;QACC,WAAW,CAAC,6BAA6B,EACvC,UAAU,SAAS,gCAAgC,+BACnD;QACF,OAAO;YAAE,OAAO,GAAG,MAAM,EAAE,CAAC;YAAE,QAAQ,GAAG,OAAO,EAAE,CAAC;QAAC;kBAEpD,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAC,YAAY,EAAE,UAAU,SAAS,kBAAkB,iBAAiB;;kCACnF,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/services/enhanced-market-data.ts"], "sourcesContent": ["/**\n * Enhanced Market Data Service with Real Historical Data Integration\n * Supports multiple data providers and timeframes for immersive chart playback\n */\n\nimport { CandlestickData } from '@/types'\n\nexport interface MarketDataProvider {\n  name: string\n  baseUrl: string\n  apiKey?: string\n  rateLimit: number // requests per minute\n  supportedSymbols: string[]\n  supportedTimeframes: string[]\n}\n\nexport interface HistoricalDataRequest {\n  symbol: string\n  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'\n  startDate: Date\n  endDate: Date\n  limit?: number\n}\n\nexport interface PlaybackEvent {\n  timestamp: number\n  type: 'price_update' | 'volume_spike' | 'news_event' | 'pattern_detected'\n  data: any\n  description?: string\n}\n\nexport interface MarketEvent {\n  timestamp: number\n  title: string\n  description: string\n  impact: 'low' | 'medium' | 'high'\n  category: 'earnings' | 'news' | 'economic' | 'technical'\n}\n\nclass EnhancedMarketDataService {\n  private providers: Map<string, MarketDataProvider> = new Map()\n  private cache: Map<string, { data: CandlestickData[], timestamp: number }> = new Map()\n  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\n  constructor() {\n    this.initializeProviders()\n  }\n\n  private initializeProviders() {\n    // Alpha Vantage Provider\n    this.providers.set('alphavantage', {\n      name: 'Alpha Vantage',\n      baseUrl: 'https://www.alphavantage.co/query',\n      apiKey: process.env.ALPHA_VANTAGE_API_KEY,\n      rateLimit: 5, // 5 requests per minute for free tier\n      supportedSymbols: ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY', 'QQQ'],\n      supportedTimeframes: ['1m', '5m', '15m', '30m', '60m', '1d'],\n    })\n\n    // Polygon.io Provider\n    this.providers.set('polygon', {\n      name: 'Polygon.io',\n      baseUrl: 'https://api.polygon.io/v2/aggs/ticker',\n      apiKey: process.env.POLYGON_API_KEY,\n      rateLimit: 5, // 5 requests per minute for free tier\n      supportedSymbols: ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY', 'QQQ'],\n      supportedTimeframes: ['1m', '5m', '15m', '1h', '1d'],\n    })\n\n    // CoinGecko for Crypto (Free)\n    this.providers.set('coingecko', {\n      name: 'CoinGecko',\n      baseUrl: 'https://api.coingecko.com/api/v3',\n      rateLimit: 10, // 10-30 requests per minute\n      supportedSymbols: ['bitcoin', 'ethereum', 'cardano', 'solana', 'polkadot'],\n      supportedTimeframes: ['1h', '4h', '1d'],\n    })\n  }\n\n  // Get historical data with automatic provider selection\n  async getHistoricalData(request: HistoricalDataRequest): Promise<CandlestickData[]> {\n    const cacheKey = this.generateCacheKey(request)\n    \n    // Check cache first\n    const cached = this.cache.get(cacheKey)\n    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {\n      return cached.data\n    }\n\n    try {\n      let data: CandlestickData[] = []\n\n      // Try different providers based on symbol type\n      if (this.isCryptoSymbol(request.symbol)) {\n        data = await this.fetchFromCoinGecko(request)\n      } else if (this.isStockSymbol(request.symbol)) {\n        // Try Alpha Vantage first, fallback to Polygon\n        try {\n          data = await this.fetchFromAlphaVantage(request)\n        } catch (error) {\n          console.warn('Alpha Vantage failed, trying Polygon:', error)\n          data = await this.fetchFromPolygon(request)\n        }\n      }\n\n      // If no real data available, generate realistic mock data\n      if (data.length === 0) {\n        console.warn('No real data available, generating mock data for:', request.symbol)\n        data = this.generateRealisticHistoricalData(request)\n      }\n\n      // Cache the result\n      this.cache.set(cacheKey, { data, timestamp: Date.now() })\n      \n      return data\n    } catch (error) {\n      console.error('Error fetching historical data:', error)\n      // Fallback to mock data\n      return this.generateRealisticHistoricalData(request)\n    }\n  }\n\n  // Fetch from Alpha Vantage\n  private async fetchFromAlphaVantage(request: HistoricalDataRequest): Promise<CandlestickData[]> {\n    const provider = this.providers.get('alphavantage')!\n    if (!provider.apiKey) {\n      throw new Error('Alpha Vantage API key not configured')\n    }\n\n    const timeframeMap: Record<string, string> = {\n      '1m': '1min',\n      '5m': '5min',\n      '15m': '15min',\n      '1h': '60min',\n      '1d': 'daily',\n    }\n\n    const interval = timeframeMap[request.timeframe] || 'daily'\n    const functionType = request.timeframe === '1d' ? 'TIME_SERIES_DAILY' : 'TIME_SERIES_INTRADAY'\n    \n    const params = new URLSearchParams({\n      function: functionType,\n      symbol: request.symbol,\n      apikey: provider.apiKey,\n      outputsize: 'full',\n      ...(functionType === 'TIME_SERIES_INTRADAY' && { interval }),\n    })\n\n    const response = await fetch(`${provider.baseUrl}?${params}`)\n    const data = await response.json()\n\n    if (data['Error Message']) {\n      throw new Error(data['Error Message'])\n    }\n\n    // Parse Alpha Vantage response\n    const timeSeriesKey = Object.keys(data).find(key => key.includes('Time Series'))\n    if (!timeSeriesKey) {\n      throw new Error('Invalid Alpha Vantage response format')\n    }\n\n    const timeSeries = data[timeSeriesKey]\n    const candlesticks: CandlestickData[] = []\n\n    for (const [timestamp, values] of Object.entries(timeSeries)) {\n      const candle = values as any\n      candlesticks.push({\n        timestamp: new Date(timestamp).getTime(),\n        open: parseFloat(candle['1. open']),\n        high: parseFloat(candle['2. high']),\n        low: parseFloat(candle['3. low']),\n        close: parseFloat(candle['4. close']),\n        volume: parseFloat(candle['5. volume']),\n      })\n    }\n\n    // Filter by date range and sort\n    return candlesticks\n      .filter(candle => {\n        const date = new Date(candle.timestamp)\n        return date >= request.startDate && date <= request.endDate\n      })\n      .sort((a, b) => a.timestamp - b.timestamp)\n      .slice(0, request.limit || 1000)\n  }\n\n  // Fetch from Polygon.io\n  private async fetchFromPolygon(request: HistoricalDataRequest): Promise<CandlestickData[]> {\n    const provider = this.providers.get('polygon')!\n    if (!provider.apiKey) {\n      throw new Error('Polygon API key not configured')\n    }\n\n    const timeframeMap: Record<string, { multiplier: number, timespan: string }> = {\n      '1m': { multiplier: 1, timespan: 'minute' },\n      '5m': { multiplier: 5, timespan: 'minute' },\n      '15m': { multiplier: 15, timespan: 'minute' },\n      '1h': { multiplier: 1, timespan: 'hour' },\n      '1d': { multiplier: 1, timespan: 'day' },\n    }\n\n    const { multiplier, timespan } = timeframeMap[request.timeframe] || { multiplier: 1, timespan: 'day' }\n    \n    const startDate = request.startDate.toISOString().split('T')[0]\n    const endDate = request.endDate.toISOString().split('T')[0]\n\n    const url = `${provider.baseUrl}/${request.symbol}/range/${multiplier}/${timespan}/${startDate}/${endDate}?apikey=${provider.apiKey}`\n    \n    const response = await fetch(url)\n    const data = await response.json()\n\n    if (data.status !== 'OK') {\n      throw new Error(data.error || 'Polygon API error')\n    }\n\n    // Parse Polygon response\n    const candlesticks: CandlestickData[] = data.results?.map((result: any) => ({\n      timestamp: result.t,\n      open: result.o,\n      high: result.h,\n      low: result.l,\n      close: result.c,\n      volume: result.v,\n    })) || []\n\n    return candlesticks\n      .sort((a, b) => a.timestamp - b.timestamp)\n      .slice(0, request.limit || 1000)\n  }\n\n  // Fetch from CoinGecko\n  private async fetchFromCoinGecko(request: HistoricalDataRequest): Promise<CandlestickData[]> {\n    const provider = this.providers.get('coingecko')!\n    \n    // CoinGecko uses different symbol format\n    const coinId = this.getCoinGeckoId(request.symbol)\n    const days = Math.ceil((request.endDate.getTime() - request.startDate.getTime()) / (1000 * 60 * 60 * 24))\n    \n    const url = `${provider.baseUrl}/coins/${coinId}/ohlc?vs_currency=usd&days=${days}`\n    \n    const response = await fetch(url)\n    const data = await response.json()\n\n    if (!Array.isArray(data)) {\n      throw new Error('Invalid CoinGecko response')\n    }\n\n    // Parse CoinGecko OHLC data\n    const candlesticks: CandlestickData[] = data.map((ohlc: number[]) => ({\n      timestamp: ohlc[0],\n      open: ohlc[1],\n      high: ohlc[2],\n      low: ohlc[3],\n      close: ohlc[4],\n      volume: Math.random() * 1000000, // CoinGecko OHLC doesn't include volume\n    }))\n\n    return candlesticks\n      .filter(candle => {\n        const date = new Date(candle.timestamp)\n        return date >= request.startDate && date <= request.endDate\n      })\n      .sort((a, b) => a.timestamp - b.timestamp)\n      .slice(0, request.limit || 1000)\n  }\n\n  // Generate realistic historical data with patterns and events\n  private generateRealisticHistoricalData(request: HistoricalDataRequest): CandlestickData[] {\n    const { symbol, timeframe, startDate, endDate, limit = 1000 } = request\n    \n    const timeframeMs = this.getTimeframeMs(timeframe)\n    const totalCandles = Math.min(\n      Math.floor((endDate.getTime() - startDate.getTime()) / timeframeMs),\n      limit\n    )\n\n    const candlesticks: CandlestickData[] = []\n    let currentPrice = this.getBasePrice(symbol)\n    let currentTime = startDate.getTime()\n\n    // Add market events and patterns\n    const events = this.generateMarketEvents(startDate, endDate, symbol)\n    \n    for (let i = 0; i < totalCandles; i++) {\n      // Check for events at this timestamp\n      const currentEvent = events.find(event => \n        Math.abs(event.timestamp - currentTime) < timeframeMs\n      )\n\n      // Generate candle with event influence\n      const candle = this.generateCandleWithEvent(\n        currentPrice, \n        currentTime, \n        symbol, \n        timeframe, \n        currentEvent\n      )\n      \n      candlesticks.push(candle)\n      currentPrice = candle.close\n      currentTime += timeframeMs\n    }\n\n    return candlesticks\n  }\n\n  // Generate market events for educational purposes\n  private generateMarketEvents(startDate: Date, endDate: Date, symbol: string): MarketEvent[] {\n    const events: MarketEvent[] = []\n    const duration = endDate.getTime() - startDate.getTime()\n    const eventCount = Math.floor(duration / (1000 * 60 * 60 * 24 * 7)) // Weekly events\n    \n    for (let i = 0; i < eventCount; i++) {\n      const timestamp = startDate.getTime() + (duration * Math.random())\n      \n      events.push({\n        timestamp,\n        title: this.getRandomEventTitle(symbol),\n        description: this.getRandomEventDescription(symbol),\n        impact: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,\n        category: ['earnings', 'news', 'economic', 'technical'][Math.floor(Math.random() * 4)] as any,\n      })\n    }\n    \n    return events.sort((a, b) => a.timestamp - b.timestamp)\n  }\n\n  // Helper methods\n  private generateCacheKey(request: HistoricalDataRequest): string {\n    return `${request.symbol}_${request.timeframe}_${request.startDate.getTime()}_${request.endDate.getTime()}`\n  }\n\n  private isCryptoSymbol(symbol: string): boolean {\n    return ['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'bitcoin', 'ethereum'].includes(symbol.toLowerCase())\n  }\n\n  private isStockSymbol(symbol: string): boolean {\n    return ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'SPY', 'QQQ'].includes(symbol.toUpperCase())\n  }\n\n  private getCoinGeckoId(symbol: string): string {\n    const mapping: Record<string, string> = {\n      'BTC': 'bitcoin',\n      'ETH': 'ethereum',\n      'ADA': 'cardano',\n      'SOL': 'solana',\n      'DOT': 'polkadot',\n    }\n    return mapping[symbol.toUpperCase()] || 'bitcoin'\n  }\n\n  private getTimeframeMs(timeframe: string): number {\n    const mapping: Record<string, number> = {\n      '1m': 60 * 1000,\n      '5m': 5 * 60 * 1000,\n      '15m': 15 * 60 * 1000,\n      '1h': 60 * 60 * 1000,\n      '4h': 4 * 60 * 60 * 1000,\n      '1d': 24 * 60 * 60 * 1000,\n    }\n    return mapping[timeframe] || 60 * 60 * 1000\n  }\n\n  private getBasePrice(symbol: string): number {\n    const prices: Record<string, number> = {\n      'AAPL': 150,\n      'GOOGL': 2500,\n      'MSFT': 300,\n      'TSLA': 800,\n      'SPY': 400,\n      'BTC': 45000,\n      'ETH': 3000,\n    }\n    return prices[symbol.toUpperCase()] || 100\n  }\n\n  private generateCandleWithEvent(\n    basePrice: number, \n    timestamp: number, \n    symbol: string, \n    timeframe: string, \n    event?: MarketEvent\n  ): CandlestickData {\n    let volatility = 0.02 // 2% base volatility\n    \n    // Increase volatility for events\n    if (event) {\n      const eventMultiplier = { low: 1.5, medium: 2.5, high: 4.0 }\n      volatility *= eventMultiplier[event.impact]\n    }\n\n    const change = (Math.random() - 0.5) * volatility * basePrice\n    const open = basePrice\n    const close = basePrice + change\n    const high = Math.max(open, close) + Math.random() * 0.01 * basePrice\n    const low = Math.min(open, close) - Math.random() * 0.01 * basePrice\n\n    return {\n      timestamp,\n      open,\n      high,\n      low,\n      close,\n      volume: (500000 + Math.random() * 1000000) * (event ? 2 : 1),\n    }\n  }\n\n  private getRandomEventTitle(symbol: string): string {\n    const titles = [\n      `${symbol} Earnings Report Released`,\n      `Major ${symbol} Partnership Announced`,\n      `${symbol} Stock Split Declared`,\n      `Analyst Upgrades ${symbol} Rating`,\n      `${symbol} CEO Interview on Market Outlook`,\n      `Regulatory News Affects ${symbol}`,\n      `${symbol} Technical Breakout Detected`,\n    ]\n    return titles[Math.floor(Math.random() * titles.length)]\n  }\n\n  private getRandomEventDescription(symbol: string): string {\n    const descriptions = [\n      `Quarterly earnings exceeded expectations with strong revenue growth.`,\n      `Strategic partnership announcement drives investor confidence.`,\n      `Stock split announcement indicates management confidence in future growth.`,\n      `Analyst upgrade based on improved fundamentals and market position.`,\n      `CEO provides positive outlook on company direction and market opportunities.`,\n      `Regulatory developments create uncertainty in the market.`,\n      `Technical analysis indicates potential breakout from consolidation pattern.`,\n    ]\n    return descriptions[Math.floor(Math.random() * descriptions.length)]\n  }\n}\n\nexport const enhancedMarketDataService = new EnhancedMarketDataService()\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAoCD,MAAM;IACI,YAA6C,IAAI,MAAK;IACtD,QAAqE,IAAI,MAAK;IACrE,iBAAiB,IAAI,KAAK,KAAK,YAAY;KAAb;IAE/C,aAAc;QACZ,IAAI,CAAC,mBAAmB;IAC1B;IAEQ,sBAAsB;QAC5B,yBAAyB;QACzB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,gBAAgB;YACjC,MAAM;YACN,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,qBAAqB;YACzC,WAAW;YACX,kBAAkB;gBAAC;gBAAQ;gBAAS;gBAAQ;gBAAQ;gBAAO;aAAM;YACjE,qBAAqB;gBAAC;gBAAM;gBAAM;gBAAO;gBAAO;gBAAO;aAAK;QAC9D;QAEA,sBAAsB;QACtB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW;YAC5B,MAAM;YACN,SAAS;YACT,QAAQ,QAAQ,GAAG,CAAC,eAAe;YACnC,WAAW;YACX,kBAAkB;gBAAC;gBAAQ;gBAAS;gBAAQ;gBAAQ;gBAAO;aAAM;YACjE,qBAAqB;gBAAC;gBAAM;gBAAM;gBAAO;gBAAM;aAAK;QACtD;QAEA,8BAA8B;QAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa;YAC9B,MAAM;YACN,SAAS;YACT,WAAW;YACX,kBAAkB;gBAAC;gBAAW;gBAAY;gBAAW;gBAAU;aAAW;YAC1E,qBAAqB;gBAAC;gBAAM;gBAAM;aAAK;QACzC;IACF;IAEA,wDAAwD;IACxD,MAAM,kBAAkB,OAA8B,EAA8B;QAClF,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;QAEvC,oBAAoB;QACpB,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE;YACjE,OAAO,OAAO,IAAI;QACpB;QAEA,IAAI;YACF,IAAI,OAA0B,EAAE;YAEhC,+CAA+C;YAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,MAAM,GAAG;gBACvC,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC;YACvC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,MAAM,GAAG;gBAC7C,+CAA+C;gBAC/C,IAAI;oBACF,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC;gBAC1C,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,yCAAyC;oBACtD,OAAO,MAAM,IAAI,CAAC,gBAAgB,CAAC;gBACrC;YACF;YAEA,0DAA0D;YAC1D,IAAI,KAAK,MAAM,KAAK,GAAG;gBACrB,QAAQ,IAAI,CAAC,qDAAqD,QAAQ,MAAM;gBAChF,OAAO,IAAI,CAAC,+BAA+B,CAAC;YAC9C;YAEA,mBAAmB;YACnB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;gBAAE;gBAAM,WAAW,KAAK,GAAG;YAAG;YAEvD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,wBAAwB;YACxB,OAAO,IAAI,CAAC,+BAA+B,CAAC;QAC9C;IACF;IAEA,2BAA2B;IAC3B,MAAc,sBAAsB,OAA8B,EAA8B;QAC9F,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAuC;YAC3C,MAAM;YACN,MAAM;YACN,OAAO;YACP,MAAM;YACN,MAAM;QACR;QAEA,MAAM,WAAW,YAAY,CAAC,QAAQ,SAAS,CAAC,IAAI;QACpD,MAAM,eAAe,QAAQ,SAAS,KAAK,OAAO,sBAAsB;QAExE,MAAM,SAAS,IAAI,gBAAgB;YACjC,UAAU;YACV,QAAQ,QAAQ,MAAM;YACtB,QAAQ,SAAS,MAAM;YACvB,YAAY;YACZ,GAAI,iBAAiB,0BAA0B;gBAAE;YAAS,CAAC;QAC7D;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,OAAO,CAAC,CAAC,EAAE,QAAQ;QAC5D,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,MAAM,IAAI,MAAM,IAAI,CAAC,gBAAgB;QACvC;QAEA,+BAA+B;QAC/B,MAAM,gBAAgB,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA,MAAO,IAAI,QAAQ,CAAC;QACjE,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,aAAa,IAAI,CAAC,cAAc;QACtC,MAAM,eAAkC,EAAE;QAE1C,KAAK,MAAM,CAAC,WAAW,OAAO,IAAI,OAAO,OAAO,CAAC,YAAa;YAC5D,MAAM,SAAS;YACf,aAAa,IAAI,CAAC;gBAChB,WAAW,IAAI,KAAK,WAAW,OAAO;gBACtC,MAAM,WAAW,MAAM,CAAC,UAAU;gBAClC,MAAM,WAAW,MAAM,CAAC,UAAU;gBAClC,KAAK,WAAW,MAAM,CAAC,SAAS;gBAChC,OAAO,WAAW,MAAM,CAAC,WAAW;gBACpC,QAAQ,WAAW,MAAM,CAAC,YAAY;YACxC;QACF;QAEA,gCAAgC;QAChC,OAAO,aACJ,MAAM,CAAC,CAAA;YACN,MAAM,OAAO,IAAI,KAAK,OAAO,SAAS;YACtC,OAAO,QAAQ,QAAQ,SAAS,IAAI,QAAQ,QAAQ,OAAO;QAC7D,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,QAAQ,KAAK,IAAI;IAC/B;IAEA,wBAAwB;IACxB,MAAc,iBAAiB,OAA8B,EAA8B;QACzF,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAyE;YAC7E,MAAM;gBAAE,YAAY;gBAAG,UAAU;YAAS;YAC1C,MAAM;gBAAE,YAAY;gBAAG,UAAU;YAAS;YAC1C,OAAO;gBAAE,YAAY;gBAAI,UAAU;YAAS;YAC5C,MAAM;gBAAE,YAAY;gBAAG,UAAU;YAAO;YACxC,MAAM;gBAAE,YAAY;gBAAG,UAAU;YAAM;QACzC;QAEA,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,YAAY,CAAC,QAAQ,SAAS,CAAC,IAAI;YAAE,YAAY;YAAG,UAAU;QAAM;QAErG,MAAM,YAAY,QAAQ,SAAS,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC/D,MAAM,UAAU,QAAQ,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAE3D,MAAM,MAAM,GAAG,SAAS,OAAO,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ,QAAQ,EAAE,SAAS,MAAM,EAAE;QAErI,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;YACxB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;QAChC;QAEA,yBAAyB;QACzB,MAAM,eAAkC,KAAK,OAAO,EAAE,IAAI,CAAC,SAAgB,CAAC;gBAC1E,WAAW,OAAO,CAAC;gBACnB,MAAM,OAAO,CAAC;gBACd,MAAM,OAAO,CAAC;gBACd,KAAK,OAAO,CAAC;gBACb,OAAO,OAAO,CAAC;gBACf,QAAQ,OAAO,CAAC;YAClB,CAAC,MAAM,EAAE;QAET,OAAO,aACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,QAAQ,KAAK,IAAI;IAC/B;IAEA,uBAAuB;IACvB,MAAc,mBAAmB,OAA8B,EAA8B;QAC3F,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAEpC,yCAAyC;QACzC,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,QAAQ,MAAM;QACjD,MAAM,OAAO,KAAK,IAAI,CAAC,CAAC,QAAQ,OAAO,CAAC,OAAO,KAAK,QAAQ,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;QAEvG,MAAM,MAAM,GAAG,SAAS,OAAO,CAAC,OAAO,EAAE,OAAO,2BAA2B,EAAE,MAAM;QAEnF,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,4BAA4B;QAC5B,MAAM,eAAkC,KAAK,GAAG,CAAC,CAAC,OAAmB,CAAC;gBACpE,WAAW,IAAI,CAAC,EAAE;gBAClB,MAAM,IAAI,CAAC,EAAE;gBACb,MAAM,IAAI,CAAC,EAAE;gBACb,KAAK,IAAI,CAAC,EAAE;gBACZ,OAAO,IAAI,CAAC,EAAE;gBACd,QAAQ,KAAK,MAAM,KAAK;YAC1B,CAAC;QAED,OAAO,aACJ,MAAM,CAAC,CAAA;YACN,MAAM,OAAO,IAAI,KAAK,OAAO,SAAS;YACtC,OAAO,QAAQ,QAAQ,SAAS,IAAI,QAAQ,QAAQ,OAAO;QAC7D,GACC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS,EACxC,KAAK,CAAC,GAAG,QAAQ,KAAK,IAAI;IAC/B;IAEA,8DAA8D;IACtD,gCAAgC,OAA8B,EAAqB;QACzF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,IAAI,EAAE,GAAG;QAEhE,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC;QACxC,MAAM,eAAe,KAAK,GAAG,CAC3B,KAAK,KAAK,CAAC,CAAC,QAAQ,OAAO,KAAK,UAAU,OAAO,EAAE,IAAI,cACvD;QAGF,MAAM,eAAkC,EAAE;QAC1C,IAAI,eAAe,IAAI,CAAC,YAAY,CAAC;QACrC,IAAI,cAAc,UAAU,OAAO;QAEnC,iCAAiC;QACjC,MAAM,SAAS,IAAI,CAAC,oBAAoB,CAAC,WAAW,SAAS;QAE7D,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,qCAAqC;YACrC,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,QAC/B,KAAK,GAAG,CAAC,MAAM,SAAS,GAAG,eAAe;YAG5C,uCAAuC;YACvC,MAAM,SAAS,IAAI,CAAC,uBAAuB,CACzC,cACA,aACA,QACA,WACA;YAGF,aAAa,IAAI,CAAC;YAClB,eAAe,OAAO,KAAK;YAC3B,eAAe;QACjB;QAEA,OAAO;IACT;IAEA,kDAAkD;IAC1C,qBAAqB,SAAe,EAAE,OAAa,EAAE,MAAc,EAAiB;QAC1F,MAAM,SAAwB,EAAE;QAChC,MAAM,WAAW,QAAQ,OAAO,KAAK,UAAU,OAAO;QACtD,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,gBAAgB;;QAEpF,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,YAAY,UAAU,OAAO,KAAM,WAAW,KAAK,MAAM;YAE/D,OAAO,IAAI,CAAC;gBACV;gBACA,OAAO,IAAI,CAAC,mBAAmB,CAAC;gBAChC,aAAa,IAAI,CAAC,yBAAyB,CAAC;gBAC5C,QAAQ;oBAAC;oBAAO;oBAAU;iBAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBAChE,UAAU;oBAAC;oBAAY;oBAAQ;oBAAY;iBAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;YACxF;QACF;QAEA,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACxD;IAEA,iBAAiB;IACT,iBAAiB,OAA8B,EAAU;QAC/D,OAAO,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,OAAO,GAAG,CAAC,EAAE,QAAQ,OAAO,CAAC,OAAO,IAAI;IAC7G;IAEQ,eAAe,MAAc,EAAW;QAC9C,OAAO;YAAC;YAAO;YAAO;YAAO;YAAO;YAAO;YAAW;SAAW,CAAC,QAAQ,CAAC,OAAO,WAAW;IAC/F;IAEQ,cAAc,MAAc,EAAW;QAC7C,OAAO;YAAC;YAAQ;YAAS;YAAQ;YAAQ;YAAO;SAAM,CAAC,QAAQ,CAAC,OAAO,WAAW;IACpF;IAEQ,eAAe,MAAc,EAAU;QAC7C,MAAM,UAAkC;YACtC,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,OAAO,OAAO,CAAC,OAAO,WAAW,GAAG,IAAI;IAC1C;IAEQ,eAAe,SAAiB,EAAU;QAChD,MAAM,UAAkC;YACtC,MAAM,KAAK;YACX,MAAM,IAAI,KAAK;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,KAAK;YAChB,MAAM,IAAI,KAAK,KAAK;YACpB,MAAM,KAAK,KAAK,KAAK;QACvB;QACA,OAAO,OAAO,CAAC,UAAU,IAAI,KAAK,KAAK;IACzC;IAEQ,aAAa,MAAc,EAAU;QAC3C,MAAM,SAAiC;YACrC,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,OAAO,MAAM,CAAC,OAAO,WAAW,GAAG,IAAI;IACzC;IAEQ,wBACN,SAAiB,EACjB,SAAiB,EACjB,MAAc,EACd,SAAiB,EACjB,KAAmB,EACF;QACjB,IAAI,aAAa,KAAK,qBAAqB;;QAE3C,iCAAiC;QACjC,IAAI,OAAO;YACT,MAAM,kBAAkB;gBAAE,KAAK;gBAAK,QAAQ;gBAAK,MAAM;YAAI;YAC3D,cAAc,eAAe,CAAC,MAAM,MAAM,CAAC;QAC7C;QAEA,MAAM,SAAS,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,aAAa;QACpD,MAAM,OAAO;QACb,MAAM,QAAQ,YAAY;QAC1B,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAC5D,MAAM,MAAM,KAAK,GAAG,CAAC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO;QAE3D,OAAO;YACL;YACA;YACA;YACA;YACA;YACA,QAAQ,CAAC,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC;QAC7D;IACF;IAEQ,oBAAoB,MAAc,EAAU;QAClD,MAAM,SAAS;YACb,GAAG,OAAO,yBAAyB,CAAC;YACpC,CAAC,MAAM,EAAE,OAAO,sBAAsB,CAAC;YACvC,GAAG,OAAO,qBAAqB,CAAC;YAChC,CAAC,iBAAiB,EAAE,OAAO,OAAO,CAAC;YACnC,GAAG,OAAO,gCAAgC,CAAC;YAC3C,CAAC,wBAAwB,EAAE,QAAQ;YACnC,GAAG,OAAO,4BAA4B,CAAC;SACxC;QACD,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;IAC1D;IAEQ,0BAA0B,MAAc,EAAU;QACxD,MAAM,eAAe;YACnB,CAAC,oEAAoE,CAAC;YACtE,CAAC,8DAA8D,CAAC;YAChE,CAAC,0EAA0E,CAAC;YAC5E,CAAC,mEAAmE,CAAC;YACrE,CAAC,4EAA4E,CAAC;YAC9E,CAAC,yDAAyD,CAAC;YAC3D,CAAC,2EAA2E,CAAC;SAC9E;QACD,OAAO,YAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM,EAAE;IACtE;AACF;AAEO,MAAM,4BAA4B,IAAI", "debugId": null}}, {"offset": {"line": 2394, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/lib/chart-playback/playback-engine.ts"], "sourcesContent": ["/**\n * Interactive Historical Chart Playback Engine\n * Provides time-travel functionality for educational trading experiences\n */\n\nimport { CandlestickData } from '@/types'\nimport { enhancedMarketDataService, type HistoricalDataRequest, type MarketEvent } from '@/lib/services/enhanced-market-data'\n\nexport interface PlaybackState {\n  isPlaying: boolean\n  isPaused: boolean\n  currentIndex: number\n  totalCandles: number\n  speed: number // 0.5x to 4x\n  timeframe: '1m' | '5m' | '15m' | '1h' | '4h' | '1d'\n  symbol: string\n  startDate: Date\n  endDate: Date\n}\n\nexport interface PlaybackControls {\n  play: () => void\n  pause: () => void\n  stop: () => void\n  setSpeed: (speed: number) => void\n  jumpToIndex: (index: number) => void\n  jumpToDate: (date: Date) => void\n  nextCandle: () => void\n  previousCandle: () => void\n  setTimeframe: (timeframe: string) => void\n}\n\nexport interface PlaybackEvent {\n  type: 'candle_update' | 'pattern_detected' | 'market_event' | 'prediction_point'\n  timestamp: number\n  data: any\n  description?: string\n}\n\nexport interface PredictionChallenge {\n  id: string\n  timestamp: number\n  question: string\n  options: string[]\n  correctAnswer: number\n  explanation: string\n  difficulty: 'easy' | 'medium' | 'hard'\n  points: number\n}\n\nexport class ChartPlaybackEngine {\n  private data: CandlestickData[] = []\n  private events: MarketEvent[] = []\n  private predictions: PredictionChallenge[] = []\n  private state: PlaybackState\n  private intervalId: number | null = null\n  private eventCallbacks: Map<string, Function[]> = new Map()\n  private patternDetector: PatternDetector\n\n  constructor() {\n    this.state = {\n      isPlaying: false,\n      isPaused: false,\n      currentIndex: 0,\n      totalCandles: 0,\n      speed: 1.0,\n      timeframe: '1h',\n      symbol: 'BTCUSD',\n      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago\n      endDate: new Date(),\n    }\n    \n    this.patternDetector = new PatternDetector()\n    this.initializeEventTypes()\n  }\n\n  private initializeEventTypes() {\n    this.eventCallbacks.set('candle_update', [])\n    this.eventCallbacks.set('pattern_detected', [])\n    this.eventCallbacks.set('market_event', [])\n    this.eventCallbacks.set('prediction_point', [])\n    this.eventCallbacks.set('state_change', [])\n  }\n\n  // Initialize playback with historical data\n  async initialize(symbol: string, timeframe: string, startDate: Date, endDate: Date): Promise<void> {\n    this.state.symbol = symbol\n    this.state.timeframe = timeframe as any\n    this.state.startDate = startDate\n    this.state.endDate = endDate\n    this.state.currentIndex = 0\n\n    // Fetch historical data\n    const request: HistoricalDataRequest = {\n      symbol,\n      timeframe: timeframe as any,\n      startDate,\n      endDate,\n      limit: 2000,\n    }\n\n    try {\n      this.data = await enhancedMarketDataService.getHistoricalData(request)\n      this.state.totalCandles = this.data.length\n      \n      // Generate educational events and prediction points\n      this.generateEducationalEvents()\n      this.generatePredictionChallenges()\n      \n      this.emitEvent('state_change', this.state)\n    } catch (error) {\n      console.error('Failed to initialize playback:', error)\n      throw error\n    }\n  }\n\n  // Playback controls\n  play(): void {\n    if (this.state.isPlaying) return\n    \n    this.state.isPlaying = true\n    this.state.isPaused = false\n    \n    const baseInterval = 1000 // 1 second base interval\n    const interval = baseInterval / this.state.speed\n    \n    this.intervalId = window.setInterval(() => {\n      this.nextCandle()\n    }, interval)\n    \n    this.emitEvent('state_change', this.state)\n  }\n\n  pause(): void {\n    if (!this.state.isPlaying) return\n    \n    this.state.isPlaying = false\n    this.state.isPaused = true\n    \n    if (this.intervalId) {\n      clearInterval(this.intervalId)\n      this.intervalId = null\n    }\n    \n    this.emitEvent('state_change', this.state)\n  }\n\n  stop(): void {\n    this.state.isPlaying = false\n    this.state.isPaused = false\n    this.state.currentIndex = 0\n    \n    if (this.intervalId) {\n      clearInterval(this.intervalId)\n      this.intervalId = null\n    }\n    \n    this.emitEvent('state_change', this.state)\n  }\n\n  setSpeed(speed: number): void {\n    const validSpeeds = [0.25, 0.5, 1.0, 2.0, 4.0]\n    this.state.speed = validSpeeds.includes(speed) ? speed : 1.0\n    \n    // Restart playback with new speed if currently playing\n    if (this.state.isPlaying) {\n      this.pause()\n      this.play()\n    }\n    \n    this.emitEvent('state_change', this.state)\n  }\n\n  jumpToIndex(index: number): void {\n    const newIndex = Math.max(0, Math.min(index, this.data.length - 1))\n    this.state.currentIndex = newIndex\n    \n    this.processCurrentCandle()\n    this.emitEvent('state_change', this.state)\n  }\n\n  jumpToDate(date: Date): void {\n    const targetTimestamp = date.getTime()\n    const closestIndex = this.data.findIndex(candle => candle.timestamp >= targetTimestamp)\n    \n    if (closestIndex !== -1) {\n      this.jumpToIndex(closestIndex)\n    }\n  }\n\n  nextCandle(): void {\n    if (this.state.currentIndex >= this.data.length - 1) {\n      this.stop()\n      return\n    }\n    \n    this.state.currentIndex++\n    this.processCurrentCandle()\n    this.emitEvent('state_change', this.state)\n  }\n\n  previousCandle(): void {\n    if (this.state.currentIndex <= 0) return\n    \n    this.state.currentIndex--\n    this.processCurrentCandle()\n    this.emitEvent('state_change', this.state)\n  }\n\n  async setTimeframe(timeframe: string): Promise<void> {\n    this.pause()\n    await this.initialize(this.state.symbol, timeframe, this.state.startDate, this.state.endDate)\n  }\n\n  // Event system\n  on(eventType: string, callback: Function): void {\n    if (!this.eventCallbacks.has(eventType)) {\n      this.eventCallbacks.set(eventType, [])\n    }\n    this.eventCallbacks.get(eventType)!.push(callback)\n  }\n\n  off(eventType: string, callback: Function): void {\n    const callbacks = this.eventCallbacks.get(eventType)\n    if (callbacks) {\n      const index = callbacks.indexOf(callback)\n      if (index > -1) {\n        callbacks.splice(index, 1)\n      }\n    }\n  }\n\n  private emitEvent(eventType: string, data: any): void {\n    const callbacks = this.eventCallbacks.get(eventType) || []\n    callbacks.forEach(callback => {\n      try {\n        callback(data)\n      } catch (error) {\n        console.error('Error in playback event callback:', error)\n      }\n    })\n  }\n\n  // Process current candle and detect events\n  private processCurrentCandle(): void {\n    const currentCandle = this.getCurrentCandle()\n    if (!currentCandle) return\n\n    // Emit candle update\n    this.emitEvent('candle_update', {\n      candle: currentCandle,\n      index: this.state.currentIndex,\n      visibleData: this.getVisibleData(),\n    })\n\n    // Check for patterns\n    this.detectPatterns()\n    \n    // Check for market events\n    this.checkMarketEvents()\n    \n    // Check for prediction points\n    this.checkPredictionPoints()\n  }\n\n  private detectPatterns(): void {\n    const recentCandles = this.getRecentCandles(20) // Last 20 candles for pattern detection\n    const patterns = this.patternDetector.detectPatterns(recentCandles)\n    \n    patterns.forEach(pattern => {\n      this.emitEvent('pattern_detected', {\n        pattern,\n        timestamp: this.getCurrentCandle()?.timestamp,\n        candles: recentCandles,\n      })\n    })\n  }\n\n  private checkMarketEvents(): void {\n    const currentTimestamp = this.getCurrentCandle()?.timestamp\n    if (!currentTimestamp) return\n\n    const relevantEvents = this.events.filter(event => \n      Math.abs(event.timestamp - currentTimestamp) < 60 * 60 * 1000 // Within 1 hour\n    )\n\n    relevantEvents.forEach(event => {\n      this.emitEvent('market_event', event)\n    })\n  }\n\n  private checkPredictionPoints(): void {\n    const prediction = this.predictions.find(p => p.timestamp === this.getCurrentCandle()?.timestamp)\n    if (prediction) {\n      this.pause() // Pause for prediction challenge\n      this.emitEvent('prediction_point', prediction)\n    }\n  }\n\n  // Data access methods\n  getCurrentCandle(): CandlestickData | null {\n    return this.data[this.state.currentIndex] || null\n  }\n\n  getVisibleData(): CandlestickData[] {\n    return this.data.slice(0, this.state.currentIndex + 1)\n  }\n\n  getRecentCandles(count: number): CandlestickData[] {\n    const start = Math.max(0, this.state.currentIndex - count + 1)\n    return this.data.slice(start, this.state.currentIndex + 1)\n  }\n\n  getState(): PlaybackState {\n    return { ...this.state }\n  }\n\n  getAllData(): CandlestickData[] {\n    return [...this.data]\n  }\n\n  getEvents(): MarketEvent[] {\n    return [...this.events]\n  }\n\n  getPredictions(): PredictionChallenge[] {\n    return [...this.predictions]\n  }\n\n  // Generate educational content\n  private generateEducationalEvents(): void {\n    // This would be populated with real market events in production\n    this.events = []\n    \n    // Generate some sample events for demonstration\n    const eventCount = Math.floor(this.data.length / 50) // One event per ~50 candles\n    \n    for (let i = 0; i < eventCount; i++) {\n      const randomIndex = Math.floor(Math.random() * this.data.length)\n      const candle = this.data[randomIndex]\n      \n      this.events.push({\n        timestamp: candle.timestamp,\n        title: `Market Event ${i + 1}`,\n        description: `Educational market event for learning purposes`,\n        impact: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,\n        category: ['earnings', 'news', 'economic', 'technical'][Math.floor(Math.random() * 4)] as any,\n      })\n    }\n    \n    this.events.sort((a, b) => a.timestamp - b.timestamp)\n  }\n\n  private generatePredictionChallenges(): void {\n    this.predictions = []\n    \n    // Generate prediction points at key moments\n    const predictionCount = Math.floor(this.data.length / 100) // One prediction per ~100 candles\n    \n    for (let i = 0; i < predictionCount; i++) {\n      const randomIndex = Math.floor(Math.random() * (this.data.length - 10)) + 5 // Ensure we have future data\n      const candle = this.data[randomIndex]\n      const futureCandle = this.data[randomIndex + 5] // Look 5 candles ahead\n      \n      const isUpward = futureCandle.close > candle.close\n      \n      this.predictions.push({\n        id: `prediction_${i}`,\n        timestamp: candle.timestamp,\n        question: `What do you think will happen to the price in the next few periods?`,\n        options: ['Price will go up', 'Price will go down', 'Price will stay flat'],\n        correctAnswer: isUpward ? 0 : 1,\n        explanation: `The price ${isUpward ? 'increased' : 'decreased'} from ${candle.close.toFixed(2)} to ${futureCandle.close.toFixed(2)}`,\n        difficulty: 'medium',\n        points: 10,\n      })\n    }\n    \n    this.predictions.sort((a, b) => a.timestamp - b.timestamp)\n  }\n}\n\n// Pattern detection helper class\nclass PatternDetector {\n  detectPatterns(candles: CandlestickData[]): Array<{ name: string; confidence: number; description: string }> {\n    const patterns = []\n    \n    if (candles.length < 3) return patterns\n    \n    // Simple pattern detection (can be expanded)\n    if (this.isHammer(candles[candles.length - 1])) {\n      patterns.push({\n        name: 'Hammer',\n        confidence: 0.8,\n        description: 'Potential bullish reversal pattern detected'\n      })\n    }\n    \n    if (this.isDoji(candles[candles.length - 1])) {\n      patterns.push({\n        name: 'Doji',\n        confidence: 0.7,\n        description: 'Market indecision pattern detected'\n      })\n    }\n    \n    if (this.isEngulfing(candles.slice(-2))) {\n      patterns.push({\n        name: 'Engulfing',\n        confidence: 0.9,\n        description: 'Strong reversal pattern detected'\n      })\n    }\n    \n    return patterns\n  }\n\n  private isHammer(candle: CandlestickData): boolean {\n    const bodySize = Math.abs(candle.close - candle.open)\n    const lowerShadow = Math.min(candle.open, candle.close) - candle.low\n    const upperShadow = candle.high - Math.max(candle.open, candle.close)\n    const totalRange = candle.high - candle.low\n    \n    return bodySize < totalRange * 0.3 && lowerShadow > bodySize * 2 && upperShadow < bodySize * 0.5\n  }\n\n  private isDoji(candle: CandlestickData): boolean {\n    const bodySize = Math.abs(candle.close - candle.open)\n    const totalRange = candle.high - candle.low\n    \n    return bodySize < totalRange * 0.1 && totalRange > 0\n  }\n\n  private isEngulfing(candles: CandlestickData[]): boolean {\n    if (candles.length < 2) return false\n    \n    const [prev, curr] = candles\n    \n    // Bullish engulfing\n    if (prev.close < prev.open && curr.close > curr.open) {\n      return curr.open < prev.close && curr.close > prev.open\n    }\n    \n    // Bearish engulfing\n    if (prev.close > prev.open && curr.close < curr.open) {\n      return curr.open > prev.close && curr.close < prev.open\n    }\n    \n    return false\n  }\n}\n\nexport { ChartPlaybackEngine }\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAGD;;AA4CO,MAAM;IACH,OAA0B,EAAE,CAAA;IAC5B,SAAwB,EAAE,CAAA;IAC1B,cAAqC,EAAE,CAAA;IACvC,MAAoB;IACpB,aAA4B,KAAI;IAChC,iBAA0C,IAAI,MAAK;IACnD,gBAAgC;IAExC,aAAc;QACZ,IAAI,CAAC,KAAK,GAAG;YACX,WAAW;YACX,UAAU;YACV,cAAc;YACd,cAAc;YACd,OAAO;YACP,WAAW;YACX,QAAQ;YACR,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;YACrD,SAAS,IAAI;QACf;QAEA,IAAI,CAAC,eAAe,GAAG,IAAI;QAC3B,IAAI,CAAC,oBAAoB;IAC3B;IAEQ,uBAAuB;QAC7B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAC3C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,EAAE;QAC9C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,EAAE;QAC1C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,EAAE;QAC9C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB,EAAE;IAC5C;IAEA,2CAA2C;IAC3C,MAAM,WAAW,MAAc,EAAE,SAAiB,EAAE,SAAe,EAAE,OAAa,EAAiB;QACjG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACpB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;QACvB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;QACrB,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG;QAE1B,wBAAwB;QACxB,MAAM,UAAiC;YACrC;YACA,WAAW;YACX;YACA;YACA,OAAO;QACT;QAEA,IAAI;YACF,IAAI,CAAC,IAAI,GAAG,MAAM,oJAAA,CAAA,4BAAyB,CAAC,iBAAiB,CAAC;YAC9D,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;YAE1C,oDAAoD;YACpD,IAAI,CAAC,yBAAyB;YAC9B,IAAI,CAAC,4BAA4B;YAEjC,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,OAAa;QACX,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;QAE1B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;QACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QAEtB,MAAM,eAAe,KAAK,yBAAyB;;QACnD,MAAM,WAAW,eAAe,IAAI,CAAC,KAAK,CAAC,KAAK;QAEhD,IAAI,CAAC,UAAU,GAAG,OAAO,WAAW,CAAC;YACnC,IAAI,CAAC,UAAU;QACjB,GAAG;QAEH,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,QAAc;QACZ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;QAE3B,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;QACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QAEtB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,cAAc,IAAI,CAAC,UAAU;YAC7B,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,OAAa;QACX,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;QACvB,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACtB,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG;QAE1B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,cAAc,IAAI,CAAC,UAAU;YAC7B,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,SAAS,KAAa,EAAQ;QAC5B,MAAM,cAAc;YAAC;YAAM;YAAK;YAAK;YAAK;SAAI;QAC9C,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,YAAY,QAAQ,CAAC,SAAS,QAAQ;QAEzD,uDAAuD;QACvD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;YACxB,IAAI,CAAC,KAAK;YACV,IAAI,CAAC,IAAI;QACX;QAEA,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,YAAY,KAAa,EAAQ;QAC/B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAChE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG;QAE1B,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,WAAW,IAAU,EAAQ;QAC3B,MAAM,kBAAkB,KAAK,OAAO;QACpC,MAAM,eAAe,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA,SAAU,OAAO,SAAS,IAAI;QAEvE,IAAI,iBAAiB,CAAC,GAAG;YACvB,IAAI,CAAC,WAAW,CAAC;QACnB;IACF;IAEA,aAAmB;QACjB,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG;YACnD,IAAI,CAAC,IAAI;YACT;QACF;QAEA,IAAI,CAAC,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,iBAAuB;QACrB,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,GAAG;QAElC,IAAI,CAAC,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,KAAK;IAC3C;IAEA,MAAM,aAAa,SAAiB,EAAiB;QACnD,IAAI,CAAC,KAAK;QACV,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO;IAC9F;IAEA,eAAe;IACf,GAAG,SAAiB,EAAE,QAAkB,EAAQ;QAC9C,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY;YACvC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,EAAE;QACvC;QACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAY,IAAI,CAAC;IAC3C;IAEA,IAAI,SAAiB,EAAE,QAAkB,EAAQ;QAC/C,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAC1C,IAAI,WAAW;YACb,MAAM,QAAQ,UAAU,OAAO,CAAC;YAChC,IAAI,QAAQ,CAAC,GAAG;gBACd,UAAU,MAAM,CAAC,OAAO;YAC1B;QACF;IACF;IAEQ,UAAU,SAAiB,EAAE,IAAS,EAAQ;QACpD,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE;QAC1D,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI;gBACF,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF;IACF;IAEA,2CAA2C;IACnC,uBAA6B;QACnC,MAAM,gBAAgB,IAAI,CAAC,gBAAgB;QAC3C,IAAI,CAAC,eAAe;QAEpB,qBAAqB;QACrB,IAAI,CAAC,SAAS,CAAC,iBAAiB;YAC9B,QAAQ;YACR,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY;YAC9B,aAAa,IAAI,CAAC,cAAc;QAClC;QAEA,qBAAqB;QACrB,IAAI,CAAC,cAAc;QAEnB,0BAA0B;QAC1B,IAAI,CAAC,iBAAiB;QAEtB,8BAA8B;QAC9B,IAAI,CAAC,qBAAqB;IAC5B;IAEQ,iBAAuB;QAC7B,MAAM,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,IAAI,wCAAwC;;QACxF,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;QAErD,SAAS,OAAO,CAAC,CAAA;YACf,IAAI,CAAC,SAAS,CAAC,oBAAoB;gBACjC;gBACA,WAAW,IAAI,CAAC,gBAAgB,IAAI;gBACpC,SAAS;YACX;QACF;IACF;IAEQ,oBAA0B;QAChC,MAAM,mBAAmB,IAAI,CAAC,gBAAgB,IAAI;QAClD,IAAI,CAAC,kBAAkB;QAEvB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,QACxC,KAAK,GAAG,CAAC,MAAM,SAAS,GAAG,oBAAoB,KAAK,KAAK,KAAK,gBAAgB;;QAGhF,eAAe,OAAO,CAAC,CAAA;YACrB,IAAI,CAAC,SAAS,CAAC,gBAAgB;QACjC;IACF;IAEQ,wBAA8B;QACpC,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,IAAI,CAAC,gBAAgB,IAAI;QACvF,IAAI,YAAY;YACd,IAAI,CAAC,KAAK,GAAG,iCAAiC;;YAC9C,IAAI,CAAC,SAAS,CAAC,oBAAoB;QACrC;IACF;IAEA,sBAAsB;IACtB,mBAA2C;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI;IAC/C;IAEA,iBAAoC;QAClC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG;IACtD;IAEA,iBAAiB,KAAa,EAAqB;QACjD,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ;QAC5D,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG;IAC1D;IAEA,WAA0B;QACxB,OAAO;YAAE,GAAG,IAAI,CAAC,KAAK;QAAC;IACzB;IAEA,aAAgC;QAC9B,OAAO;eAAI,IAAI,CAAC,IAAI;SAAC;IACvB;IAEA,YAA2B;QACzB,OAAO;eAAI,IAAI,CAAC,MAAM;SAAC;IACzB;IAEA,iBAAwC;QACtC,OAAO;eAAI,IAAI,CAAC,WAAW;SAAC;IAC9B;IAEA,+BAA+B;IACvB,4BAAkC;QACxC,gEAAgE;QAChE,IAAI,CAAC,MAAM,GAAG,EAAE;QAEhB,gDAAgD;QAChD,MAAM,aAAa,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,4BAA4B;;QAEjF,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;YACnC,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM;YAC/D,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;YAErC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACf,WAAW,OAAO,SAAS;gBAC3B,OAAO,CAAC,aAAa,EAAE,IAAI,GAAG;gBAC9B,aAAa,CAAC,8CAA8C,CAAC;gBAC7D,QAAQ;oBAAC;oBAAO;oBAAU;iBAAO,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;gBAChE,UAAU;oBAAC;oBAAY;oBAAQ;oBAAY;iBAAY,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;YACxF;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACtD;IAEQ,+BAAqC;QAC3C,IAAI,CAAC,WAAW,GAAG,EAAE;QAErB,4CAA4C;QAC5C,MAAM,kBAAkB,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,kCAAkC;;QAE7F,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,IAAK;YACxC,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,KAAK,EAAE,6BAA6B;;YACzG,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;YACrC,MAAM,eAAe,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,uBAAuB;;YAEvE,MAAM,WAAW,aAAa,KAAK,GAAG,OAAO,KAAK;YAElD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;gBACpB,IAAI,CAAC,WAAW,EAAE,GAAG;gBACrB,WAAW,OAAO,SAAS;gBAC3B,UAAU,CAAC,mEAAmE,CAAC;gBAC/E,SAAS;oBAAC;oBAAoB;oBAAsB;iBAAuB;gBAC3E,eAAe,WAAW,IAAI;gBAC9B,aAAa,CAAC,UAAU,EAAE,WAAW,cAAc,YAAY,MAAM,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,EAAE,aAAa,KAAK,CAAC,OAAO,CAAC,IAAI;gBACpI,YAAY;gBACZ,QAAQ;YACV;QACF;QAEA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IAC3D;AACF;AAEA,iCAAiC;AACjC,MAAM;IACJ,eAAe,OAA0B,EAAoE;QAC3G,MAAM,WAAW,EAAE;QAEnB,IAAI,QAAQ,MAAM,GAAG,GAAG,OAAO;QAE/B,6CAA6C;QAC7C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,GAAG;YAC9C,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;QACF;QAEA,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,GAAG;YAC5C,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;QACF;QAEA,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,KAAK,CAAC,CAAC,KAAK;YACvC,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,YAAY;gBACZ,aAAa;YACf;QACF;QAEA,OAAO;IACT;IAEQ,SAAS,MAAuB,EAAW;QACjD,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;QACpD,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK,IAAI,OAAO,GAAG;QACpE,MAAM,cAAc,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,KAAK;QACpE,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;QAE3C,OAAO,WAAW,aAAa,OAAO,cAAc,WAAW,KAAK,cAAc,WAAW;IAC/F;IAEQ,OAAO,MAAuB,EAAW;QAC/C,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI;QACpD,MAAM,aAAa,OAAO,IAAI,GAAG,OAAO,GAAG;QAE3C,OAAO,WAAW,aAAa,OAAO,aAAa;IACrD;IAEQ,YAAY,OAA0B,EAAW;QACvD,IAAI,QAAQ,MAAM,GAAG,GAAG,OAAO;QAE/B,MAAM,CAAC,MAAM,KAAK,GAAG;QAErB,oBAAoB;QACpB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;YACpD,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;QACzD;QAEA,oBAAoB;QACpB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,EAAE;YACpD,OAAO,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;QACzD;QAEA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 2764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/charts/playback-chart.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { create<PERSON>hart, IChartApi, ISeriesApi, ColorType } from 'lightweight-charts'\nimport { ChartPlaybackEngine, type PlaybackState, type PredictionChallenge } from '@/lib/chart-playback/playback-engine'\nimport { CandlestickData } from '@/types'\nimport { useThemeColors } from '@/components/theme/theme-provider'\nimport { useUserStore } from '@/lib/stores/user-store'\n\ninterface PlaybackChartProps {\n  symbol: string\n  timeframe: string\n  startDate: Date\n  endDate: Date\n  onPredictionChallenge?: (challenge: PredictionChallenge) => void\n  onPatternDetected?: (pattern: any) => void\n  onMarketEvent?: (event: any) => void\n  className?: string\n}\n\nexport default function PlaybackChart({\n  symbol,\n  timeframe,\n  startDate,\n  endDate,\n  onPredictionChallenge,\n  onPatternDetected,\n  onMarketEvent,\n  className = '',\n}: PlaybackChartProps) {\n  const chartContainerRef = useRef<HTMLDivElement>(null)\n  const chartRef = useRef<IChartApi | null>(null)\n  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)\n  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)\n  const playbackEngineRef = useRef<ChartPlaybackEngine | null>(null)\n  \n  const [playbackState, setPlaybackState] = useState<PlaybackState | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [currentPrediction, setCurrentPrediction] = useState<PredictionChallenge | null>(null)\n  const [detectedPatterns, setDetectedPatterns] = useState<any[]>([])\n  const [marketEvents, setMarketEvents] = useState<any[]>([])\n  \n  const colors = useThemeColors()\n  const { interfaceMode } = useUserStore()\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  // Initialize playback engine and chart\n  useEffect(() => {\n    if (!chartContainerRef.current) return\n\n    const initializeChart = async () => {\n      setIsLoading(true)\n      \n      try {\n        // Create chart\n        const chart = createChart(chartContainerRef.current!, {\n          width: 800,\n          height: 500,\n          layout: {\n            background: { type: ColorType.Solid, color: colors.background },\n            textColor: colors.textPrimary,\n          },\n          grid: {\n            vertLines: { color: colors.chartGrid },\n            horzLines: { color: colors.chartGrid },\n          },\n          crosshair: { mode: 1 },\n          rightPriceScale: { borderColor: colors.border },\n          timeScale: {\n            borderColor: colors.border,\n            timeVisible: true,\n            secondsVisible: false,\n          },\n        })\n\n        chartRef.current = chart\n\n        // Add candlestick series\n        const candlestickSeries = chart.addCandlestickSeries({\n          upColor: colors.bullish,\n          downColor: colors.bearish,\n          borderDownColor: colors.bearish,\n          borderUpColor: colors.bullish,\n          wickDownColor: colors.bearish,\n          wickUpColor: colors.bullish,\n        })\n\n        candlestickSeriesRef.current = candlestickSeries\n\n        // Add volume series\n        const volumeSeries = chart.addHistogramSeries({\n          color: colors.chartVolume,\n          priceFormat: { type: 'volume' },\n          priceScaleId: '',\n          scaleMargins: { top: 0.7, bottom: 0 },\n        })\n\n        volumeSeriesRef.current = volumeSeries\n\n        // Initialize playback engine\n        const engine = new ChartPlaybackEngine()\n        playbackEngineRef.current = engine\n\n        // Set up event listeners\n        engine.on('state_change', (state: PlaybackState) => {\n          setPlaybackState(state)\n        })\n\n        engine.on('candle_update', ({ candle, visibleData }: { candle: CandlestickData, visibleData: CandlestickData[] }) => {\n          updateChart(visibleData)\n        })\n\n        engine.on('pattern_detected', (pattern: any) => {\n          setDetectedPatterns(prev => [...prev, pattern])\n          onPatternDetected?.(pattern)\n          addPatternMarker(pattern)\n        })\n\n        engine.on('market_event', (event: any) => {\n          setMarketEvents(prev => [...prev, event])\n          onMarketEvent?.(event)\n          addEventMarker(event)\n        })\n\n        engine.on('prediction_point', (prediction: PredictionChallenge) => {\n          setCurrentPrediction(prediction)\n          onPredictionChallenge?.(prediction)\n        })\n\n        // Initialize with data\n        await engine.initialize(symbol, timeframe, startDate, endDate)\n        \n        setIsLoading(false)\n      } catch (error) {\n        console.error('Failed to initialize playback chart:', error)\n        setIsLoading(false)\n      }\n    }\n\n    initializeChart()\n\n    // Cleanup\n    return () => {\n      if (chartRef.current) {\n        chartRef.current.remove()\n      }\n      if (playbackEngineRef.current) {\n        playbackEngineRef.current.stop()\n      }\n    }\n  }, [symbol, timeframe, startDate, endDate])\n\n  // Update chart with new data\n  const updateChart = (data: CandlestickData[]) => {\n    if (!candlestickSeriesRef.current || !volumeSeriesRef.current) return\n\n    const chartData = data.map(candle => ({\n      time: Math.floor(candle.timestamp / 1000) as any,\n      open: candle.open,\n      high: candle.high,\n      low: candle.low,\n      close: candle.close,\n    }))\n\n    const volumeData = data.map(candle => ({\n      time: Math.floor(candle.timestamp / 1000) as any,\n      value: candle.volume,\n      color: candle.close >= candle.open ? colors.bullish + '50' : colors.bearish + '50',\n    }))\n\n    candlestickSeriesRef.current.setData(chartData)\n    volumeSeriesRef.current.setData(volumeData)\n\n    // Auto-scroll to latest data\n    if (chartRef.current && data.length > 0) {\n      chartRef.current.timeScale().scrollToRealTime()\n    }\n  }\n\n  // Add pattern detection markers\n  const addPatternMarker = (pattern: any) => {\n    if (!candlestickSeriesRef.current || !pattern.timestamp) return\n\n    const markers = candlestickSeriesRef.current.markers() || []\n    markers.push({\n      time: Math.floor(pattern.timestamp / 1000) as any,\n      position: 'belowBar' as const,\n      color: colors.warning,\n      shape: 'circle' as const,\n      text: pattern.pattern.name,\n    })\n\n    candlestickSeriesRef.current.setMarkers(markers)\n  }\n\n  // Add market event markers\n  const addEventMarker = (event: any) => {\n    if (!candlestickSeriesRef.current || !event.timestamp) return\n\n    const markers = candlestickSeriesRef.current.markers() || []\n    const impactColors = {\n      low: colors.info,\n      medium: colors.warning,\n      high: colors.error,\n    }\n\n    markers.push({\n      time: Math.floor(event.timestamp / 1000) as any,\n      position: 'aboveBar' as const,\n      color: impactColors[event.impact] || colors.info,\n      shape: 'arrowDown' as const,\n      text: event.title.substring(0, 10) + '...',\n    })\n\n    candlestickSeriesRef.current.setMarkers(markers)\n  }\n\n  // Playback controls\n  const handlePlay = () => playbackEngineRef.current?.play()\n  const handlePause = () => playbackEngineRef.current?.pause()\n  const handleStop = () => playbackEngineRef.current?.stop()\n  const handleSpeedChange = (speed: number) => playbackEngineRef.current?.setSpeed(speed)\n  const handleJumpToIndex = (index: number) => playbackEngineRef.current?.jumpToIndex(index)\n\n  // Handle prediction response\n  const handlePredictionResponse = (answerIndex: number) => {\n    if (!currentPrediction) return\n\n    const isCorrect = answerIndex === currentPrediction.correctAnswer\n    \n    // Show result (you could emit an event or show a modal here)\n    console.log(`Prediction ${isCorrect ? 'correct' : 'incorrect'}: ${currentPrediction.explanation}`)\n    \n    setCurrentPrediction(null)\n    \n    // Resume playback\n    setTimeout(() => {\n      playbackEngineRef.current?.play()\n    }, 2000)\n  }\n\n  if (isLoading) {\n    return (\n      <div className={`${className} flex items-center justify-center h-96`}>\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-current mx-auto mb-4\"></div>\n          <p style={{ color: colors.textSecondary }}>\n            {isAdolescentMode ? '🔄 Loading historical data...' : 'LOADING_HISTORICAL_DATA...'}\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`${className} space-y-4`}>\n      {/* Chart Container */}\n      <div \n        ref={chartContainerRef}\n        className=\"rounded-lg border\"\n        style={{ borderColor: colors.border }}\n      />\n\n      {/* Playback Controls */}\n      {playbackState && (\n        <div \n          className=\"p-4 rounded-lg\"\n          style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n        >\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={handlePlay}\n                disabled={playbackState.isPlaying}\n                className=\"px-3 py-1 rounded text-sm font-bold transition-colors\"\n                style={{ \n                  backgroundColor: playbackState.isPlaying ? colors.disabled : colors.success,\n                  color: colors.background \n                }}\n              >\n                {isAdolescentMode ? '▶️ Play' : 'PLAY'}\n              </button>\n              \n              <button\n                onClick={handlePause}\n                disabled={!playbackState.isPlaying}\n                className=\"px-3 py-1 rounded text-sm font-bold transition-colors\"\n                style={{ \n                  backgroundColor: !playbackState.isPlaying ? colors.disabled : colors.warning,\n                  color: colors.background \n                }}\n              >\n                {isAdolescentMode ? '⏸️ Pause' : 'PAUSE'}\n              </button>\n              \n              <button\n                onClick={handleStop}\n                className=\"px-3 py-1 rounded text-sm font-bold transition-colors\"\n                style={{ backgroundColor: colors.error, color: colors.background }}\n              >\n                {isAdolescentMode ? '⏹️ Stop' : 'STOP'}\n              </button>\n            </div>\n\n            {/* Speed Control */}\n            <div className=\"flex items-center space-x-2\">\n              <span style={{ color: colors.textSecondary }} className=\"text-sm\">\n                {isAdolescentMode ? 'Speed:' : 'SPEED:'}\n              </span>\n              {[0.25, 0.5, 1.0, 2.0, 4.0].map(speed => (\n                <button\n                  key={speed}\n                  onClick={() => handleSpeedChange(speed)}\n                  className={`px-2 py-1 rounded text-xs transition-colors ${\n                    playbackState.speed === speed ? 'font-bold' : ''\n                  }`}\n                  style={{ \n                    backgroundColor: playbackState.speed === speed ? colors.primary : colors.backgroundTertiary,\n                    color: playbackState.speed === speed ? colors.background : colors.textSecondary\n                  }}\n                >\n                  {speed}x\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between text-sm\" style={{ color: colors.textSecondary }}>\n              <span>\n                {isAdolescentMode ? 'Progress:' : 'PROGRESS:'} {playbackState.currentIndex + 1} / {playbackState.totalCandles}\n              </span>\n              <span>\n                {isAdolescentMode ? 'Speed:' : 'SPEED:'} {playbackState.speed}x\n              </span>\n            </div>\n            \n            <div \n              className=\"w-full h-2 rounded-full\"\n              style={{ backgroundColor: colors.backgroundTertiary }}\n            >\n              <div\n                className=\"h-2 rounded-full transition-all duration-300\"\n                style={{ \n                  backgroundColor: colors.primary,\n                  width: `${(playbackState.currentIndex / Math.max(playbackState.totalCandles - 1, 1)) * 100}%`\n                }}\n              />\n            </div>\n            \n            <input\n              type=\"range\"\n              min=\"0\"\n              max={playbackState.totalCandles - 1}\n              value={playbackState.currentIndex}\n              onChange={(e) => handleJumpToIndex(parseInt(e.target.value))}\n              className=\"w-full\"\n            />\n          </div>\n        </div>\n      )}\n\n      {/* Prediction Challenge Modal */}\n      {currentPrediction && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div \n            className=\"max-w-md w-full p-6 rounded-lg\"\n            style={{ backgroundColor: colors.background, borderColor: colors.border }}\n          >\n            <h3 className=\"text-lg font-bold mb-4\" style={{ color: colors.textPrimary }}>\n              {isAdolescentMode ? '🔮 Prediction Challenge!' : '📊 MARKET_PREDICTION_CHALLENGE'}\n            </h3>\n            \n            <p className=\"mb-4\" style={{ color: colors.textSecondary }}>\n              {currentPrediction.question}\n            </p>\n            \n            <div className=\"space-y-2 mb-4\">\n              {currentPrediction.options.map((option, index) => (\n                <button\n                  key={index}\n                  onClick={() => handlePredictionResponse(index)}\n                  className=\"w-full p-3 rounded text-left transition-colors\"\n                  style={{ \n                    backgroundColor: colors.backgroundSecondary,\n                    borderColor: colors.border,\n                    color: colors.textPrimary\n                  }}\n                >\n                  {option}\n                </button>\n              ))}\n            </div>\n            \n            <div className=\"text-sm\" style={{ color: colors.textMuted }}>\n              {isAdolescentMode ? `💎 Points: ${currentPrediction.points}` : `POINTS: ${currentPrediction.points}`}\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Pattern Detection Panel */}\n      {detectedPatterns.length > 0 && (\n        <div \n          className=\"p-4 rounded-lg\"\n          style={{ backgroundColor: colors.backgroundSecondary, borderColor: colors.border }}\n        >\n          <h4 className=\"font-bold mb-2\" style={{ color: colors.textPrimary }}>\n            {isAdolescentMode ? '🔍 Detected Patterns' : '📊 PATTERN_DETECTION'}\n          </h4>\n          <div className=\"space-y-2\">\n            {detectedPatterns.slice(-3).map((pattern, index) => (\n              <div key={index} className=\"text-sm\">\n                <span className=\"font-medium\" style={{ color: colors.warning }}>\n                  {pattern.pattern.name}\n                </span>\n                <span style={{ color: colors.textSecondary }} className=\"ml-2\">\n                  - {pattern.pattern.description}\n                </span>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAoBe,SAAS,cAAc,EACpC,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,qBAAqB,EACrB,iBAAiB,EACjB,aAAa,EACb,YAAY,EAAE,EACK;IACnB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoC;IACtE,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkC;IAC/D,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA8B;IAE7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IACvF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE1D,MAAM,SAAS,CAAA,GAAA,gJAAA,CAAA,iBAAc,AAAD;IAC5B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,mBAAmB,kBAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,kBAAkB,OAAO,EAAE;QAEhC,MAAM,kBAAkB;YACtB,aAAa;YAEb,IAAI;gBACF,eAAe;gBACf,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD,EAAE,kBAAkB,OAAO,EAAG;oBACpD,OAAO;oBACP,QAAQ;oBACR,QAAQ;wBACN,YAAY;4BAAE,MAAM,sLAAA,CAAA,YAAS,CAAC,KAAK;4BAAE,OAAO,OAAO,UAAU;wBAAC;wBAC9D,WAAW,OAAO,WAAW;oBAC/B;oBACA,MAAM;wBACJ,WAAW;4BAAE,OAAO,OAAO,SAAS;wBAAC;wBACrC,WAAW;4BAAE,OAAO,OAAO,SAAS;wBAAC;oBACvC;oBACA,WAAW;wBAAE,MAAM;oBAAE;oBACrB,iBAAiB;wBAAE,aAAa,OAAO,MAAM;oBAAC;oBAC9C,WAAW;wBACT,aAAa,OAAO,MAAM;wBAC1B,aAAa;wBACb,gBAAgB;oBAClB;gBACF;gBAEA,SAAS,OAAO,GAAG;gBAEnB,yBAAyB;gBACzB,MAAM,oBAAoB,MAAM,oBAAoB,CAAC;oBACnD,SAAS,OAAO,OAAO;oBACvB,WAAW,OAAO,OAAO;oBACzB,iBAAiB,OAAO,OAAO;oBAC/B,eAAe,OAAO,OAAO;oBAC7B,eAAe,OAAO,OAAO;oBAC7B,aAAa,OAAO,OAAO;gBAC7B;gBAEA,qBAAqB,OAAO,GAAG;gBAE/B,oBAAoB;gBACpB,MAAM,eAAe,MAAM,kBAAkB,CAAC;oBAC5C,OAAO,OAAO,WAAW;oBACzB,aAAa;wBAAE,MAAM;oBAAS;oBAC9B,cAAc;oBACd,cAAc;wBAAE,KAAK;wBAAK,QAAQ;oBAAE;gBACtC;gBAEA,gBAAgB,OAAO,GAAG;gBAE1B,6BAA6B;gBAC7B,MAAM,SAAS,IAAI,qJAAA,CAAA,sBAAmB;gBACtC,kBAAkB,OAAO,GAAG;gBAE5B,yBAAyB;gBACzB,OAAO,EAAE,CAAC,gBAAgB,CAAC;oBACzB,iBAAiB;gBACnB;gBAEA,OAAO,EAAE,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,WAAW,EAA+D;oBAC9G,YAAY;gBACd;gBAEA,OAAO,EAAE,CAAC,oBAAoB,CAAC;oBAC7B,oBAAoB,CAAA,OAAQ;+BAAI;4BAAM;yBAAQ;oBAC9C,oBAAoB;oBACpB,iBAAiB;gBACnB;gBAEA,OAAO,EAAE,CAAC,gBAAgB,CAAC;oBACzB,gBAAgB,CAAA,OAAQ;+BAAI;4BAAM;yBAAM;oBACxC,gBAAgB;oBAChB,eAAe;gBACjB;gBAEA,OAAO,EAAE,CAAC,oBAAoB,CAAC;oBAC7B,qBAAqB;oBACrB,wBAAwB;gBAC1B;gBAEA,uBAAuB;gBACvB,MAAM,OAAO,UAAU,CAAC,QAAQ,WAAW,WAAW;gBAEtD,aAAa;YACf,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,aAAa;YACf;QACF;QAEA;QAEA,UAAU;QACV,OAAO;YACL,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,MAAM;YACzB;YACA,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,kBAAkB,OAAO,CAAC,IAAI;YAChC;QACF;IACF,GAAG;QAAC;QAAQ;QAAW;QAAW;KAAQ;IAE1C,6BAA6B;IAC7B,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAE/D,MAAM,YAAY,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACpC,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG;gBACpC,MAAM,OAAO,IAAI;gBACjB,MAAM,OAAO,IAAI;gBACjB,KAAK,OAAO,GAAG;gBACf,OAAO,OAAO,KAAK;YACrB,CAAC;QAED,MAAM,aAAa,KAAK,GAAG,CAAC,CAAA,SAAU,CAAC;gBACrC,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS,GAAG;gBACpC,OAAO,OAAO,MAAM;gBACpB,OAAO,OAAO,KAAK,IAAI,OAAO,IAAI,GAAG,OAAO,OAAO,GAAG,OAAO,OAAO,OAAO,GAAG;YAChF,CAAC;QAED,qBAAqB,OAAO,CAAC,OAAO,CAAC;QACrC,gBAAgB,OAAO,CAAC,OAAO,CAAC;QAEhC,6BAA6B;QAC7B,IAAI,SAAS,OAAO,IAAI,KAAK,MAAM,GAAG,GAAG;YACvC,SAAS,OAAO,CAAC,SAAS,GAAG,gBAAgB;QAC/C;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,QAAQ,SAAS,EAAE;QAEzD,MAAM,UAAU,qBAAqB,OAAO,CAAC,OAAO,MAAM,EAAE;QAC5D,QAAQ,IAAI,CAAC;YACX,MAAM,KAAK,KAAK,CAAC,QAAQ,SAAS,GAAG;YACrC,UAAU;YACV,OAAO,OAAO,OAAO;YACrB,OAAO;YACP,MAAM,QAAQ,OAAO,CAAC,IAAI;QAC5B;QAEA,qBAAqB,OAAO,CAAC,UAAU,CAAC;IAC1C;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,qBAAqB,OAAO,IAAI,CAAC,MAAM,SAAS,EAAE;QAEvD,MAAM,UAAU,qBAAqB,OAAO,CAAC,OAAO,MAAM,EAAE;QAC5D,MAAM,eAAe;YACnB,KAAK,OAAO,IAAI;YAChB,QAAQ,OAAO,OAAO;YACtB,MAAM,OAAO,KAAK;QACpB;QAEA,QAAQ,IAAI,CAAC;YACX,MAAM,KAAK,KAAK,CAAC,MAAM,SAAS,GAAG;YACnC,UAAU;YACV,OAAO,YAAY,CAAC,MAAM,MAAM,CAAC,IAAI,OAAO,IAAI;YAChD,OAAO;YACP,MAAM,MAAM,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM;QACvC;QAEA,qBAAqB,OAAO,CAAC,UAAU,CAAC;IAC1C;IAEA,oBAAoB;IACpB,MAAM,aAAa,IAAM,kBAAkB,OAAO,EAAE;IACpD,MAAM,cAAc,IAAM,kBAAkB,OAAO,EAAE;IACrD,MAAM,aAAa,IAAM,kBAAkB,OAAO,EAAE;IACpD,MAAM,oBAAoB,CAAC,QAAkB,kBAAkB,OAAO,EAAE,SAAS;IACjF,MAAM,oBAAoB,CAAC,QAAkB,kBAAkB,OAAO,EAAE,YAAY;IAEpF,6BAA6B;IAC7B,MAAM,2BAA2B,CAAC;QAChC,IAAI,CAAC,mBAAmB;QAExB,MAAM,YAAY,gBAAgB,kBAAkB,aAAa;QAEjE,6DAA6D;QAC7D,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY,YAAY,YAAY,EAAE,EAAE,kBAAkB,WAAW,EAAE;QAEjG,qBAAqB;QAErB,kBAAkB;QAClB,WAAW;YACT,kBAAkB,OAAO,EAAE;QAC7B,GAAG;IACL;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,GAAG,UAAU,sCAAsC,CAAC;sBAClE,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,OAAO;4BAAE,OAAO,OAAO,aAAa;wBAAC;kCACrC,mBAAmB,kCAAkC;;;;;;;;;;;;;;;;;IAKhE;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;0BAEtC,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,aAAa,OAAO,MAAM;gBAAC;;;;;;YAIrC,+BACC,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,OAAO,mBAAmB;oBAAE,aAAa,OAAO,MAAM;gBAAC;;kCAEjF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,UAAU,cAAc,SAAS;wCACjC,WAAU;wCACV,OAAO;4CACL,iBAAiB,cAAc,SAAS,GAAG,OAAO,QAAQ,GAAG,OAAO,OAAO;4CAC3E,OAAO,OAAO,UAAU;wCAC1B;kDAEC,mBAAmB,YAAY;;;;;;kDAGlC,8OAAC;wCACC,SAAS;wCACT,UAAU,CAAC,cAAc,SAAS;wCAClC,WAAU;wCACV,OAAO;4CACL,iBAAiB,CAAC,cAAc,SAAS,GAAG,OAAO,QAAQ,GAAG,OAAO,OAAO;4CAC5E,OAAO,OAAO,UAAU;wCAC1B;kDAEC,mBAAmB,aAAa;;;;;;kDAGnC,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAO;4CAAE,iBAAiB,OAAO,KAAK;4CAAE,OAAO,OAAO,UAAU;wCAAC;kDAEhE,mBAAmB,YAAY;;;;;;;;;;;;0CAKpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,OAAO;4CAAE,OAAO,OAAO,aAAa;wCAAC;wCAAG,WAAU;kDACrD,mBAAmB,WAAW;;;;;;oCAEhC;wCAAC;wCAAM;wCAAK;wCAAK;wCAAK;qCAAI,CAAC,GAAG,CAAC,CAAA,sBAC9B,8OAAC;4CAEC,SAAS,IAAM,kBAAkB;4CACjC,WAAW,CAAC,4CAA4C,EACtD,cAAc,KAAK,KAAK,QAAQ,cAAc,IAC9C;4CACF,OAAO;gDACL,iBAAiB,cAAc,KAAK,KAAK,QAAQ,OAAO,OAAO,GAAG,OAAO,kBAAkB;gDAC3F,OAAO,cAAc,KAAK,KAAK,QAAQ,OAAO,UAAU,GAAG,OAAO,aAAa;4CACjF;;gDAEC;gDAAM;;2CAVF;;;;;;;;;;;;;;;;;kCAiBb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAA+B,OAAO;oCAAE,OAAO,OAAO,aAAa;gCAAC;;kDACjF,8OAAC;;4CACE,mBAAmB,cAAc;4CAAY;4CAAE,cAAc,YAAY,GAAG;4CAAE;4CAAI,cAAc,YAAY;;;;;;;kDAE/G,8OAAC;;4CACE,mBAAmB,WAAW;4CAAS;4CAAE,cAAc,KAAK;4CAAC;;;;;;;;;;;;;0CAIlE,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,OAAO,kBAAkB;gCAAC;0CAEpD,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,iBAAiB,OAAO,OAAO;wCAC/B,OAAO,GAAG,AAAC,cAAc,YAAY,GAAG,KAAK,GAAG,CAAC,cAAc,YAAY,GAAG,GAAG,KAAM,IAAI,CAAC,CAAC;oCAC/F;;;;;;;;;;;0CAIJ,8OAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,KAAK,cAAc,YAAY,GAAG;gCAClC,OAAO,cAAc,YAAY;gCACjC,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;gCAC1D,WAAU;;;;;;;;;;;;;;;;;;YAOjB,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,iBAAiB,OAAO,UAAU;wBAAE,aAAa,OAAO,MAAM;oBAAC;;sCAExE,8OAAC;4BAAG,WAAU;4BAAyB,OAAO;gCAAE,OAAO,OAAO,WAAW;4BAAC;sCACvE,mBAAmB,6BAA6B;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;4BAAO,OAAO;gCAAE,OAAO,OAAO,aAAa;4BAAC;sCACtD,kBAAkB,QAAQ;;;;;;sCAG7B,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACtC,8OAAC;oCAEC,SAAS,IAAM,yBAAyB;oCACxC,WAAU;oCACV,OAAO;wCACL,iBAAiB,OAAO,mBAAmB;wCAC3C,aAAa,OAAO,MAAM;wCAC1B,OAAO,OAAO,WAAW;oCAC3B;8CAEC;mCATI;;;;;;;;;;sCAcX,8OAAC;4BAAI,WAAU;4BAAU,OAAO;gCAAE,OAAO,OAAO,SAAS;4BAAC;sCACvD,mBAAmB,CAAC,WAAW,EAAE,kBAAkB,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,kBAAkB,MAAM,EAAE;;;;;;;;;;;;;;;;;YAO3G,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,iBAAiB,OAAO,mBAAmB;oBAAE,aAAa,OAAO,MAAM;gBAAC;;kCAEjF,8OAAC;wBAAG,WAAU;wBAAiB,OAAO;4BAAE,OAAO,OAAO,WAAW;wBAAC;kCAC/D,mBAAmB,yBAAyB;;;;;;kCAE/C,8OAAC;wBAAI,WAAU;kCACZ,iBAAiB,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,sBACxC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC;wCAAK,WAAU;wCAAc,OAAO;4CAAE,OAAO,OAAO,OAAO;wCAAC;kDAC1D,QAAQ,OAAO,CAAC,IAAI;;;;;;kDAEvB,8OAAC;wCAAK,OAAO;4CAAE,OAAO,OAAO,aAAa;wCAAC;wCAAG,WAAU;;4CAAO;4CAC1D,QAAQ,OAAO,CAAC,WAAW;;;;;;;;+BALxB;;;;;;;;;;;;;;;;;;;;;;AAcxB", "debugId": null}}, {"offset": {"line": 3374, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/components/games/candle-strike-game.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { CandleStrikeGame } from '@/lib/game-engine/games/candle-strike'\nimport CandlestickChart, { PatternAnnotation, ChartSkeleton } from '@/components/charts/candlestick-chart'\nimport PlaybackChart from '@/components/charts/playback-chart'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport { marketDataService } from '@/lib/services/market-data'\n\ninterface CandleStrikeGameProps {\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  onGameEnd: (score: number) => void\n  usePlayback?: boolean\n  className?: string\n}\n\nexport default function CandleStrikeGameComponent({\n  difficulty,\n  onGameEnd,\n  usePlayback = false,\n  className = '',\n}: CandleStrikeGameProps) {\n  const { interfaceMode } = useUserStore()\n  const [game, setGame] = useState<CandleStrikeGame | null>(null)\n  const [gameState, setGameState] = useState<any>(null)\n  const [currentChallenge, setCurrentChallenge] = useState<any>(null)\n  const [gameData, setGameData] = useState<any>(null)\n  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)\n  const [showResult, setShowResult] = useState(false)\n  const [lastAnswerCorrect, setLastAnswerCorrect] = useState<boolean | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  useEffect(() => {\n    initializeGame()\n  }, [difficulty])\n\n  const initializeGame = async () => {\n    setIsLoading(true)\n    const newGame = new CandleStrikeGame(difficulty)\n    setGame(newGame)\n    \n    await newGame.start()\n    \n    // Update game state every second\n    const interval = setInterval(() => {\n      if (newGame.isGameActive()) {\n        setGameState(newGame.getState())\n        setCurrentChallenge(newGame.getCurrentChallenge())\n        setGameData(newGame.getGameSpecificData())\n      } else {\n        clearInterval(interval)\n        const finalScore = newGame.calculateScore()\n        onGameEnd(finalScore)\n      }\n    }, 1000)\n\n    setIsLoading(false)\n  }\n\n  const handleAnswerSubmit = async (answerIndex: number) => {\n    if (!game || selectedAnswer !== null || showResult) return\n\n    setSelectedAnswer(answerIndex)\n    const correct = await game.submitAnswer(answerIndex)\n    setLastAnswerCorrect(correct)\n    setShowResult(true)\n\n    // Update game state\n    setGameState(game.getState())\n    setGameData(game.getGameSpecificData())\n\n    // Auto-advance to next challenge after 2 seconds\n    setTimeout(() => {\n      setSelectedAnswer(null)\n      setShowResult(false)\n      setLastAnswerCorrect(null)\n      setCurrentChallenge(game.getCurrentChallenge())\n    }, 2000)\n  }\n\n  const getPatternHighlight = () => {\n    if (!currentChallenge) return undefined\n\n    return {\n      startIndex: currentChallenge.patternStartIndex,\n      endIndex: currentChallenge.patternEndIndex,\n      color: isAdolescentMode ? '#fbbf24' : '#10b981',\n    }\n  }\n\n  const getAnswerButtonStyle = (index: number) => {\n    const baseStyle = `p-3 rounded-lg font-bold transition-all duration-300 ${\n      isAdolescentMode\n        ? 'text-white border-2'\n        : 'text-gray-900 border-2'\n    }`\n\n    if (showResult && selectedAnswer !== null) {\n      if (index === currentChallenge?.correctAnswer) {\n        // Correct answer\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-green-500 border-green-400 shadow-lg shadow-green-500/50'\n            : 'bg-green-400 border-green-500 shadow-lg'\n        }`\n      } else if (index === selectedAnswer) {\n        // Wrong selected answer\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-red-500 border-red-400 shadow-lg shadow-red-500/50'\n            : 'bg-red-400 border-red-500 shadow-lg'\n        }`\n      } else {\n        // Other options\n        return `${baseStyle} ${\n          isAdolescentMode\n            ? 'bg-gray-600 border-gray-500 opacity-50'\n            : 'bg-gray-300 border-gray-400 opacity-50'\n        }`\n      }\n    } else {\n      // Normal state\n      return `${baseStyle} ${\n        isAdolescentMode\n          ? 'bg-purple-500 hover:bg-purple-600 border-purple-400 hover:shadow-lg hover:shadow-purple-500/30'\n          : 'bg-purple-400 hover:bg-purple-300 border-purple-500 hover:shadow-lg'\n      }`\n    }\n  }\n\n  if (isLoading) {\n    return (\n      <div className={`${className} space-y-6`}>\n        <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n          <h3 className=\"text-xl font-bold mb-2\">\n            {isAdolescentMode ? '🕯️ Loading CandleStrike...' : '📊 INITIALIZING_PATTERN_RECOGNITION'}\n          </h3>\n        </div>\n        <ChartSkeleton \n          width={800} \n          height={400} \n          theme={isAdolescentMode ? 'dark' : 'dark'} \n        />\n      </div>\n    )\n  }\n\n  if (!game || !gameState || !currentChallenge) {\n    return (\n      <div className={`${className} text-center`}>\n        <p className={isAdolescentMode ? 'text-white' : 'text-green-400'}>\n          {isAdolescentMode ? '🎮 Game not ready...' : 'SYSTEM_NOT_READY'}\n        </p>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`${className} space-y-6`}>\n      {/* Game Header */}\n      <div className={`text-center ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n        <h3 className=\"text-xl font-bold mb-2\">\n          {isAdolescentMode ? '🕯️ CandleStrike Challenge' : '📊 PATTERN_RECOGNITION_MODULE'}\n        </h3>\n        <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n          {isAdolescentMode \n            ? 'Identify the candlestick pattern in the highlighted area!' \n            : 'IDENTIFY_CANDLESTICK_PATTERN_IN_HIGHLIGHTED_REGION'\n          }\n        </p>\n      </div>\n\n      {/* Game Stats */}\n      <div className={`grid grid-cols-4 gap-4 p-4 rounded-lg ${\n        isAdolescentMode ? 'bg-white/10' : 'bg-gray-800 border border-green-400'\n      }`}>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Score' : 'SCORE'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-yellow-300' : 'text-green-400'}`}>\n            {gameState.score}\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Accuracy' : 'ACCURACY'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n            {gameData?.accuracy_percentage?.toFixed(1) || 0}%\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Streak' : 'STREAK'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-orange-300' : 'text-orange-400'}`}>\n            {gameData?.streak_count || 0}\n          </div>\n        </div>\n        <div className=\"text-center\">\n          <div className={`text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n            {isAdolescentMode ? 'Time Left' : 'TIME_REMAINING'}\n          </div>\n          <div className={`text-lg font-bold ${isAdolescentMode ? 'text-red-300' : 'text-red-400'}`}>\n            {gameState.time_remaining}s\n          </div>\n        </div>\n      </div>\n\n      {/* Chart Display */}\n      <div className=\"flex justify-center\">\n        {usePlayback ? (\n          <PlaybackChart\n            symbol=\"BTCUSD\"\n            timeframe=\"1h\"\n            startDate={new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)}\n            endDate={new Date()}\n            onPatternDetected={(pattern) => {\n              console.log('Pattern detected:', pattern)\n            }}\n            onPredictionChallenge={(challenge) => {\n              console.log('Prediction challenge:', challenge)\n            }}\n            className=\"w-full max-w-4xl\"\n          />\n        ) : (\n          <CandlestickChart\n            data={currentChallenge.candleData}\n            width={800}\n            height={400}\n            theme=\"dark\"\n            patternHighlight={getPatternHighlight()}\n            title={isAdolescentMode ? '📈 Trading Chart' : '📊 MARKET_DATA_VISUALIZATION'}\n            className=\"border rounded-lg\"\n          />\n        )}\n      </div>\n\n      {/* Pattern Information */}\n      {currentChallenge.pattern && (\n        <div className=\"flex justify-center\">\n          <PatternAnnotation \n            pattern={{\n              name: \"Pattern to Identify\",\n              description: isAdolescentMode \n                ? \"Look at the highlighted candles and identify the pattern!\"\n                : \"ANALYZE_HIGHLIGHTED_CANDLESTICKS_AND_IDENTIFY_PATTERN\",\n              bullish: true\n            }}\n            theme=\"dark\"\n          />\n        </div>\n      )}\n\n      {/* Answer Options */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        {currentChallenge.options.map((option: string, index: number) => (\n          <button\n            key={index}\n            onClick={() => handleAnswerSubmit(index)}\n            disabled={selectedAnswer !== null || showResult}\n            className={getAnswerButtonStyle(index)}\n          >\n            {option}\n          </button>\n        ))}\n      </div>\n\n      {/* Result Feedback */}\n      {showResult && lastAnswerCorrect !== null && (\n        <div className={`text-center p-4 rounded-lg ${\n          lastAnswerCorrect\n            ? (isAdolescentMode \n                ? 'bg-green-500/20 border border-green-400 text-green-100'\n                : 'bg-green-900/50 border border-green-400 text-green-300'\n              )\n            : (isAdolescentMode \n                ? 'bg-red-500/20 border border-red-400 text-red-100'\n                : 'bg-red-900/50 border border-red-400 text-red-300'\n              )\n        }`}>\n          <div className=\"text-2xl mb-2\">\n            {lastAnswerCorrect \n              ? (isAdolescentMode ? '🎉' : '✅') \n              : (isAdolescentMode ? '😅' : '❌')\n            }\n          </div>\n          <p className=\"font-bold\">\n            {lastAnswerCorrect \n              ? (isAdolescentMode ? 'Excellent! Correct pattern identified!' : 'CORRECT_PATTERN_IDENTIFICATION')\n              : (isAdolescentMode ? 'Not quite! The correct answer was highlighted.' : 'INCORRECT_PATTERN_IDENTIFICATION')\n            }\n          </p>\n          {!lastAnswerCorrect && currentChallenge.pattern && (\n            <p className=\"text-sm mt-2\">\n              {isAdolescentMode \n                ? `The correct pattern was: ${currentChallenge.options[currentChallenge.correctAnswer]}`\n                : `CORRECT_PATTERN: ${currentChallenge.options[currentChallenge.correctAnswer]}`\n              }\n            </p>\n          )}\n        </div>\n      )}\n\n      {/* Progress Indicator */}\n      <div className={`text-center text-sm ${isAdolescentMode ? 'text-white/70' : 'text-green-300'}`}>\n        {isAdolescentMode \n          ? `🎯 Patterns Identified: ${gameData?.patterns_identified || 0} | Correct: ${gameData?.correct_identifications || 0}`\n          : `PATTERNS_IDENTIFIED: ${gameData?.patterns_identified || 0} | CORRECT: ${gameData?.correct_identifications || 0}`\n        }\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAgBe,SAAS,0BAA0B,EAChD,UAAU,EACV,SAAS,EACT,cAAc,KAAK,EACnB,YAAY,EAAE,EACQ;IACtB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB,kBAAkB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB;QACrB,aAAa;QACb,MAAM,UAAU,IAAI,yJAAA,CAAA,mBAAgB,CAAC;QACrC,QAAQ;QAER,MAAM,QAAQ,KAAK;QAEnB,iCAAiC;QACjC,MAAM,WAAW,YAAY;YAC3B,IAAI,QAAQ,YAAY,IAAI;gBAC1B,aAAa,QAAQ,QAAQ;gBAC7B,oBAAoB,QAAQ,mBAAmB;gBAC/C,YAAY,QAAQ,mBAAmB;YACzC,OAAO;gBACL,cAAc;gBACd,MAAM,aAAa,QAAQ,cAAc;gBACzC,UAAU;YACZ;QACF,GAAG;QAEH,aAAa;IACf;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,QAAQ,mBAAmB,QAAQ,YAAY;QAEpD,kBAAkB;QAClB,MAAM,UAAU,MAAM,KAAK,YAAY,CAAC;QACxC,qBAAqB;QACrB,cAAc;QAEd,oBAAoB;QACpB,aAAa,KAAK,QAAQ;QAC1B,YAAY,KAAK,mBAAmB;QAEpC,iDAAiD;QACjD,WAAW;YACT,kBAAkB;YAClB,cAAc;YACd,qBAAqB;YACrB,oBAAoB,KAAK,mBAAmB;QAC9C,GAAG;IACL;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,kBAAkB,OAAO;QAE9B,OAAO;YACL,YAAY,iBAAiB,iBAAiB;YAC9C,UAAU,iBAAiB,eAAe;YAC1C,OAAO,mBAAmB,YAAY;QACxC;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,YAAY,CAAC,qDAAqD,EACtE,mBACI,wBACA,0BACJ;QAEF,IAAI,cAAc,mBAAmB,MAAM;YACzC,IAAI,UAAU,kBAAkB,eAAe;gBAC7C,iBAAiB;gBACjB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,gEACA,2CACJ;YACJ,OAAO,IAAI,UAAU,gBAAgB;gBACnC,wBAAwB;gBACxB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,0DACA,uCACJ;YACJ,OAAO;gBACL,gBAAgB;gBAChB,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,2CACA,0CACJ;YACJ;QACF,OAAO;YACL,eAAe;YACf,OAAO,GAAG,UAAU,CAAC,EACnB,mBACI,mGACA,uEACJ;QACJ;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;8BACtC,8OAAC;oBAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,eAAe,kBAAkB;8BACjF,cAAA,8OAAC;wBAAG,WAAU;kCACX,mBAAmB,gCAAgC;;;;;;;;;;;8BAGxD,8OAAC,oJAAA,CAAA,gBAAa;oBACZ,OAAO;oBACP,QAAQ;oBACR,OAAO,mBAAmB,SAAS;;;;;;;;;;;;IAI3C;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB;QAC5C,qBACE,8OAAC;YAAI,WAAW,GAAG,UAAU,YAAY,CAAC;sBACxC,cAAA,8OAAC;gBAAE,WAAW,mBAAmB,eAAe;0BAC7C,mBAAmB,yBAAyB;;;;;;;;;;;IAIrD;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;0BAEtC,8OAAC;gBAAI,WAAW,CAAC,YAAY,EAAE,mBAAmB,eAAe,kBAAkB;;kCACjF,8OAAC;wBAAG,WAAU;kCACX,mBAAmB,+BAA+B;;;;;;kCAErD,8OAAC;wBAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;kCAC7E,mBACG,8DACA;;;;;;;;;;;;0BAMR,8OAAC;gBAAI,WAAW,CAAC,sCAAsC,EACrD,mBAAmB,gBAAgB,uCACnC;;kCACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,UAAU;;;;;;0CAEhC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,kBAAkB;0CAC3F,UAAU,KAAK;;;;;;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,aAAa;;;;;;0CAEnC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,eAAe,kBAAkB;;oCACtF,UAAU,qBAAqB,QAAQ,MAAM;oCAAE;;;;;;;;;;;;;kCAGpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,WAAW;;;;;;0CAEjC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,oBAAoB,mBAAmB;0CAC5F,UAAU,gBAAgB;;;;;;;;;;;;kCAG/B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;0CAC/E,mBAAmB,cAAc;;;;;;0CAEpC,8OAAC;gCAAI,WAAW,CAAC,kBAAkB,EAAE,mBAAmB,iBAAiB,gBAAgB;;oCACtF,UAAU,cAAc;oCAAC;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC;gBAAI,WAAU;0BACZ,4BACC,8OAAC,iJAAA,CAAA,UAAa;oBACZ,QAAO;oBACP,WAAU;oBACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,KAAK;oBACpD,SAAS,IAAI;oBACb,mBAAmB,CAAC;wBAClB,QAAQ,GAAG,CAAC,qBAAqB;oBACnC;oBACA,uBAAuB,CAAC;wBACtB,QAAQ,GAAG,CAAC,yBAAyB;oBACvC;oBACA,WAAU;;;;;yCAGZ,8OAAC,oJAAA,CAAA,UAAgB;oBACf,MAAM,iBAAiB,UAAU;oBACjC,OAAO;oBACP,QAAQ;oBACR,OAAM;oBACN,kBAAkB;oBAClB,OAAO,mBAAmB,qBAAqB;oBAC/C,WAAU;;;;;;;;;;;YAMf,iBAAiB,OAAO,kBACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oJAAA,CAAA,oBAAiB;oBAChB,SAAS;wBACP,MAAM;wBACN,aAAa,mBACT,8DACA;wBACJ,SAAS;oBACX;oBACA,OAAM;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAC7C,8OAAC;wBAEC,SAAS,IAAM,mBAAmB;wBAClC,UAAU,mBAAmB,QAAQ;wBACrC,WAAW,qBAAqB;kCAE/B;uBALI;;;;;;;;;;YAWV,cAAc,sBAAsB,sBACnC,8OAAC;gBAAI,WAAW,CAAC,2BAA2B,EAC1C,oBACK,mBACG,2DACA,2DAEH,mBACG,qDACA,oDAER;;kCACA,8OAAC;wBAAI,WAAU;kCACZ,oBACI,mBAAmB,OAAO,MAC1B,mBAAmB,OAAO;;;;;;kCAGjC,8OAAC;wBAAE,WAAU;kCACV,oBACI,mBAAmB,2CAA2C,mCAC9D,mBAAmB,mDAAmD;;;;;;oBAG5E,CAAC,qBAAqB,iBAAiB,OAAO,kBAC7C,8OAAC;wBAAE,WAAU;kCACV,mBACG,CAAC,yBAAyB,EAAE,iBAAiB,OAAO,CAAC,iBAAiB,aAAa,CAAC,EAAE,GACtF,CAAC,iBAAiB,EAAE,iBAAiB,OAAO,CAAC,iBAAiB,aAAa,CAAC,EAAE;;;;;;;;;;;;0BAQ1F,8OAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,mBAAmB,kBAAkB,kBAAkB;0BAC3F,mBACG,CAAC,wBAAwB,EAAE,UAAU,uBAAuB,EAAE,YAAY,EAAE,UAAU,2BAA2B,GAAG,GACpH,CAAC,qBAAqB,EAAE,UAAU,uBAAuB,EAAE,YAAY,EAAE,UAAU,2BAA2B,GAAG;;;;;;;;;;;;AAK7H", "debugId": null}}, {"offset": {"line": 3813, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/TradeQuest/tradequest/src/app/demo/candle-strike/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport CandleStrikeGameComponent from '@/components/games/candle-strike-game'\nimport { useUserStore } from '@/lib/stores/user-store'\nimport Link from 'next/link'\n\nexport default function CandleStrikeDemoPage() {\n  const { interfaceMode, switchInterfaceMode } = useUserStore()\n  const [difficulty, setDifficulty] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner')\n  const [gameActive, setGameActive] = useState(false)\n  const [lastScore, setLastScore] = useState<number | null>(null)\n\n  const isAdolescentMode = interfaceMode === 'adolescent'\n\n  const handleGameEnd = (score: number) => {\n    setGameActive(false)\n    setLastScore(score)\n  }\n\n  const startNewGame = () => {\n    setGameActive(true)\n    setLastScore(null)\n  }\n\n  return (\n    <div className={`min-h-screen ${isAdolescentMode \n      ? 'bg-gradient-to-br from-purple-600 via-pink-500 to-blue-500' \n      : 'bg-gray-900'\n    }`}>\n      {/* Header */}\n      <header className={`p-6 ${isAdolescentMode ? 'text-white' : 'border-b border-green-400'}`}>\n        <div className=\"max-w-7xl mx-auto flex justify-between items-center\">\n          <div>\n            <Link \n              href=\"/\"\n              className={`text-sm hover:underline ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}\n            >\n              ← {isAdolescentMode ? 'Back to Quest Hub' : 'RETURN_TO_MAIN'}\n            </Link>\n            <h1 className={`text-3xl font-bold ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n              {isAdolescentMode ? '🕯️ CandleStrike: Pattern Master' : '📊 CANDLESTICK_PATTERN_RECOGNITION'}\n            </h1>\n          </div>\n          \n          <div className=\"flex items-center gap-4\">\n            <button\n              onClick={() => switchInterfaceMode(isAdolescentMode ? 'adult' : 'adolescent')}\n              className={`px-4 py-2 rounded-lg transition-colors ${\n                isAdolescentMode \n                  ? 'bg-white/20 hover:bg-white/30 text-white' \n                  : 'bg-green-400/20 hover:bg-green-400/30 text-green-400 border border-green-400'\n              }`}\n            >\n              {isAdolescentMode ? '🔄 Pro Mode' : 'ADV_MODE'}\n            </button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto p-6\">\n        {!gameActive ? (\n          <div className=\"space-y-8\">\n            {/* Game Introduction */}\n            <section className={`p-6 rounded-lg ${\n              isAdolescentMode \n                ? 'bg-white/10 backdrop-blur-sm text-white' \n                : 'bg-gray-800 border border-green-400'\n            }`}>\n              <h2 className={`text-2xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                {isAdolescentMode ? '🎯 Master the Art of Pattern Recognition!' : '📈 TECHNICAL_ANALYSIS_TRAINING_MODULE'}\n              </h2>\n              <p className={`text-lg mb-4 ${isAdolescentMode ? 'text-white/90' : 'text-green-300'}`}>\n                {isAdolescentMode \n                  ? 'Learn to identify candlestick patterns using real market data and professional trading charts. Each pattern tells a story about market sentiment and potential price movements!'\n                  : 'Advanced pattern recognition training using real historical market data with professional-grade charting tools. Develop skills in technical analysis and market sentiment interpretation.'\n                }\n              </p>\n              \n              {/* Features */}\n              <div className=\"grid md:grid-cols-3 gap-4 mb-6\">\n                {[\n                  {\n                    icon: isAdolescentMode ? '📊' : '📈',\n                    title: isAdolescentMode ? 'Real Market Data' : 'REAL_MARKET_DATA',\n                    description: isAdolescentMode \n                      ? 'Practice with actual historical price data from major cryptocurrencies and stocks'\n                      : 'Historical price data from major financial instruments'\n                  },\n                  {\n                    icon: isAdolescentMode ? '🎯' : '🔍',\n                    title: isAdolescentMode ? 'Pattern Recognition' : 'PATTERN_ANALYSIS',\n                    description: isAdolescentMode \n                      ? 'Learn to spot hammer, doji, engulfing, and star patterns like a pro trader'\n                      : 'Comprehensive candlestick pattern identification training'\n                  },\n                  {\n                    icon: isAdolescentMode ? '⚡' : '📊',\n                    title: isAdolescentMode ? 'Interactive Charts' : 'INTERACTIVE_VISUALIZATION',\n                    description: isAdolescentMode \n                      ? 'Professional trading charts with zoom, pan, and pattern highlighting'\n                      : 'Professional-grade charting with advanced visualization tools'\n                  }\n                ].map((feature, index) => (\n                  <div\n                    key={index}\n                    className={`p-4 rounded-lg ${\n                      isAdolescentMode \n                        ? 'bg-white/5 border border-white/20' \n                        : 'bg-gray-700 border border-green-400/50'\n                    }`}\n                  >\n                    <div className=\"text-2xl mb-2\">{feature.icon}</div>\n                    <h3 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                      {feature.title}\n                    </h3>\n                    <p className={`text-sm ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                      {feature.description}\n                    </p>\n                  </div>\n                ))}\n              </div>\n\n              {lastScore !== null && (\n                <div className={`p-4 rounded-lg mb-6 ${\n                  isAdolescentMode \n                    ? 'bg-yellow-500/20 border border-yellow-400 text-yellow-100' \n                    : 'bg-yellow-900/50 border border-yellow-400 text-yellow-300'\n                }`}>\n                  <h3 className=\"font-bold mb-2\">\n                    {isAdolescentMode ? '🏆 Last Game Results' : '📊 PREVIOUS_SESSION_RESULTS'}\n                  </h3>\n                  <p>\n                    {isAdolescentMode ? `Final Score: ${lastScore} points!` : `FINAL_SCORE: ${lastScore}`}\n                  </p>\n                </div>\n              )}\n            </section>\n\n            {/* Difficulty Selection */}\n            <section className={`p-6 rounded-lg ${\n              isAdolescentMode \n                ? 'bg-white/10 backdrop-blur-sm' \n                : 'bg-gray-800 border border-green-400'\n            }`}>\n              <h3 className={`text-xl font-bold mb-4 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                {isAdolescentMode ? '⚔️ Choose Your Challenge Level' : '🎯 SELECT_DIFFICULTY_LEVEL'}\n              </h3>\n              \n              <div className=\"grid md:grid-cols-3 gap-4 mb-6\">\n                {[\n                  {\n                    level: 'beginner' as const,\n                    title: isAdolescentMode ? '🌟 Apprentice Trader' : '📚 BEGINNER',\n                    description: isAdolescentMode \n                      ? 'Learn basic patterns: Hammer and Doji'\n                      : 'Basic patterns: Hammer, Doji',\n                    patterns: ['Hammer', 'Doji']\n                  },\n                  {\n                    level: 'intermediate' as const,\n                    title: isAdolescentMode ? '⚡ Skilled Trader' : '📈 INTERMEDIATE',\n                    description: isAdolescentMode \n                      ? 'Master engulfing patterns and reversals'\n                      : 'Engulfing patterns and reversals',\n                    patterns: ['Bullish Engulfing', 'Bearish Engulfing']\n                  },\n                  {\n                    level: 'advanced' as const,\n                    title: isAdolescentMode ? '🏆 Master Trader' : '🎯 ADVANCED',\n                    description: isAdolescentMode \n                      ? 'Complex three-candle star formations'\n                      : 'Complex multi-candle patterns',\n                    patterns: ['Morning Star', 'Evening Star']\n                  }\n                ].map((difficultyOption) => (\n                  <button\n                    key={difficultyOption.level}\n                    onClick={() => setDifficulty(difficultyOption.level)}\n                    className={`p-4 rounded-lg border-2 transition-all ${\n                      difficulty === difficultyOption.level\n                        ? (isAdolescentMode \n                            ? 'bg-purple-500/30 border-purple-400 shadow-lg shadow-purple-500/30'\n                            : 'bg-green-400/20 border-green-400 shadow-lg'\n                          )\n                        : (isAdolescentMode \n                            ? 'bg-white/5 border-white/20 hover:bg-white/10'\n                            : 'bg-gray-700 border-gray-600 hover:border-green-400/50'\n                          )\n                    }`}\n                  >\n                    <h4 className={`font-bold mb-2 ${isAdolescentMode ? 'text-white' : 'text-green-400'}`}>\n                      {difficultyOption.title}\n                    </h4>\n                    <p className={`text-sm mb-3 ${isAdolescentMode ? 'text-white/80' : 'text-green-300'}`}>\n                      {difficultyOption.description}\n                    </p>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {difficultyOption.patterns.map((pattern, i) => (\n                        <span\n                          key={i}\n                          className={`text-xs px-2 py-1 rounded ${\n                            isAdolescentMode \n                              ? 'bg-white/20 text-white' \n                              : 'bg-gray-600 text-green-300'\n                          }`}\n                        >\n                          {pattern}\n                        </span>\n                      ))}\n                    </div>\n                  </button>\n                ))}\n              </div>\n\n              <div className=\"text-center\">\n                <button\n                  onClick={startNewGame}\n                  className={`px-8 py-4 rounded-lg font-bold text-lg transition-colors ${\n                    isAdolescentMode\n                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white hover:from-yellow-500 hover:to-orange-600 shadow-lg'\n                      : 'bg-green-400 text-gray-900 hover:bg-green-300 shadow-lg'\n                  }`}\n                >\n                  {isAdolescentMode ? '🚀 Start Pattern Quest!' : '▶ INITIALIZE_TRAINING_SESSION'}\n                </button>\n              </div>\n            </section>\n          </div>\n        ) : (\n          <CandleStrikeGameComponent\n            difficulty={difficulty}\n            onGameEnd={handleGameEnd}\n            className=\"w-full\"\n          />\n        )}\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,mBAAmB,kBAAkB;IAE3C,MAAM,gBAAgB,CAAC;QACrB,cAAc;QACd,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,cAAc;QACd,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,aAAa,EAAE,mBAC5B,+DACA,eACF;;0BAEA,8OAAC;gBAAO,WAAW,CAAC,IAAI,EAAE,mBAAmB,eAAe,6BAA6B;0BACvF,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAW,CAAC,wBAAwB,EAAE,mBAAmB,kBAAkB,kBAAkB;;wCAC9F;wCACI,mBAAmB,sBAAsB;;;;;;;8CAE9C,8OAAC;oCAAG,WAAW,CAAC,mBAAmB,EAAE,mBAAmB,eAAe,kBAAkB;8CACtF,mBAAmB,qCAAqC;;;;;;;;;;;;sCAI7D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,oBAAoB,mBAAmB,UAAU;gCAChE,WAAW,CAAC,uCAAuC,EACjD,mBACI,6CACA,gFACJ;0CAED,mBAAmB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;0BAO5C,8OAAC;gBAAK,WAAU;0BACb,CAAC,2BACA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAQ,WAAW,CAAC,eAAe,EAClC,mBACI,4CACA,uCACJ;;8CACA,8OAAC;oCAAG,WAAW,CAAC,wBAAwB,EAAE,mBAAmB,eAAe,kBAAkB;8CAC3F,mBAAmB,8CAA8C;;;;;;8CAEpE,8OAAC;oCAAE,WAAW,CAAC,aAAa,EAAE,mBAAmB,kBAAkB,kBAAkB;8CAClF,mBACG,oLACA;;;;;;8CAKN,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,MAAM,mBAAmB,OAAO;4CAChC,OAAO,mBAAmB,qBAAqB;4CAC/C,aAAa,mBACT,sFACA;wCACN;wCACA;4CACE,MAAM,mBAAmB,OAAO;4CAChC,OAAO,mBAAmB,wBAAwB;4CAClD,aAAa,mBACT,+EACA;wCACN;wCACA;4CACE,MAAM,mBAAmB,MAAM;4CAC/B,OAAO,mBAAmB,uBAAuB;4CACjD,aAAa,mBACT,yEACA;wCACN;qCACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;4CAEC,WAAW,CAAC,eAAe,EACzB,mBACI,sCACA,0CACJ;;8DAEF,8OAAC;oDAAI,WAAU;8DAAiB,QAAQ,IAAI;;;;;;8DAC5C,8OAAC;oDAAG,WAAW,CAAC,eAAe,EAAE,mBAAmB,eAAe,kBAAkB;8DAClF,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAW,CAAC,QAAQ,EAAE,mBAAmB,kBAAkB,kBAAkB;8DAC7E,QAAQ,WAAW;;;;;;;2CAZjB;;;;;;;;;;gCAkBV,cAAc,sBACb,8OAAC;oCAAI,WAAW,CAAC,oBAAoB,EACnC,mBACI,8DACA,6DACJ;;sDACA,8OAAC;4CAAG,WAAU;sDACX,mBAAmB,yBAAyB;;;;;;sDAE/C,8OAAC;sDACE,mBAAmB,CAAC,aAAa,EAAE,UAAU,QAAQ,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW;;;;;;;;;;;;;;;;;;sCAO7F,8OAAC;4BAAQ,WAAW,CAAC,eAAe,EAClC,mBACI,iCACA,uCACJ;;8CACA,8OAAC;oCAAG,WAAW,CAAC,uBAAuB,EAAE,mBAAmB,eAAe,kBAAkB;8CAC1F,mBAAmB,mCAAmC;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,OAAO;4CACP,OAAO,mBAAmB,yBAAyB;4CACnD,aAAa,mBACT,0CACA;4CACJ,UAAU;gDAAC;gDAAU;6CAAO;wCAC9B;wCACA;4CACE,OAAO;4CACP,OAAO,mBAAmB,qBAAqB;4CAC/C,aAAa,mBACT,4CACA;4CACJ,UAAU;gDAAC;gDAAqB;6CAAoB;wCACtD;wCACA;4CACE,OAAO;4CACP,OAAO,mBAAmB,qBAAqB;4CAC/C,aAAa,mBACT,yCACA;4CACJ,UAAU;gDAAC;gDAAgB;6CAAe;wCAC5C;qCACD,CAAC,GAAG,CAAC,CAAC,iCACL,8OAAC;4CAEC,SAAS,IAAM,cAAc,iBAAiB,KAAK;4CACnD,WAAW,CAAC,uCAAuC,EACjD,eAAe,iBAAiB,KAAK,GAChC,mBACG,sEACA,+CAEH,mBACG,iDACA,yDAER;;8DAEF,8OAAC;oDAAG,WAAW,CAAC,eAAe,EAAE,mBAAmB,eAAe,kBAAkB;8DAClF,iBAAiB,KAAK;;;;;;8DAEzB,8OAAC;oDAAE,WAAW,CAAC,aAAa,EAAE,mBAAmB,kBAAkB,kBAAkB;8DAClF,iBAAiB,WAAW;;;;;;8DAE/B,8OAAC;oDAAI,WAAU;8DACZ,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,kBACvC,8OAAC;4DAEC,WAAW,CAAC,0BAA0B,EACpC,mBACI,2BACA,8BACJ;sEAED;2DAPI;;;;;;;;;;;2CAvBN,iBAAiB,KAAK;;;;;;;;;;8CAsCjC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAW,CAAC,yDAAyD,EACnE,mBACI,kHACA,2DACJ;kDAED,mBAAmB,4BAA4B;;;;;;;;;;;;;;;;;;;;;;yCAMxD,8OAAC,uJAAA,CAAA,UAAyB;oBACxB,YAAY;oBACZ,WAAW;oBACX,WAAU;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}]}